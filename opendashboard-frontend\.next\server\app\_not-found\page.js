/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "(rsc)/./node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \************************************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \************************************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@opentelemetry/instrumentation-http/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!***********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \***********************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!***********************************************************************************!*\
  !*** ./node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \***********************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/@prisma/instrumentation/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/@prisma/instrumentation/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \************************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@prisma/instrumentation/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/@prisma/instrumentation/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/@prisma/instrumentation/node_modules/@opentelemetry/instrumentation/build/esm/platform/node/ sync ***!
  \************************************************************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/@prisma/instrumentation/node_modules/@opentelemetry/instrumentation/build/esm/platform/node sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/require-in-the-middle sync recursive":
/*!**************************************************!*\
  !*** ./node_modules/require-in-the-middle/ sync ***!
  \**************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/require-in-the-middle sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(ssr)/./node_modules/require-in-the-middle sync recursive":
/*!**************************************************!*\
  !*** ./node_modules/require-in-the-middle/ sync ***!
  \**************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(ssr)/./node_modules/require-in-the-middle sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "postcss":
/*!**************************!*\
  !*** external "postcss" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("postcss");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "diagnostics_channel":
/*!**************************************!*\
  !*** external "diagnostics_channel" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("diagnostics_channel");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "module":
/*!*************************!*\
  !*** external "module" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("module");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "perf_hooks":
/*!*****************************!*\
  !*** external "perf_hooks" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("perf_hooks");

/***/ }),

/***/ "process":
/*!**************************!*\
  !*** external "process" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("process");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "worker_threads":
/*!*********************************!*\
  !*** external "worker_threads" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("worker_threads");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:child_process":
/*!*************************************!*\
  !*** external "node:child_process" ***!
  \*************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:child_process");

/***/ }),

/***/ "node:diagnostics_channel":
/*!*******************************************!*\
  !*** external "node:diagnostics_channel" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:diagnostics_channel");

/***/ }),

/***/ "node:fs":
/*!**************************!*\
  !*** external "node:fs" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:fs");

/***/ }),

/***/ "node:http":
/*!****************************!*\
  !*** external "node:http" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:http");

/***/ }),

/***/ "node:https":
/*!*****************************!*\
  !*** external "node:https" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:https");

/***/ }),

/***/ "node:inspector":
/*!*********************************!*\
  !*** external "node:inspector" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:inspector");

/***/ }),

/***/ "node:net":
/*!***************************!*\
  !*** external "node:net" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:net");

/***/ }),

/***/ "node:os":
/*!**************************!*\
  !*** external "node:os" ***!
  \**************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:os");

/***/ }),

/***/ "node:path":
/*!****************************!*\
  !*** external "node:path" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:path");

/***/ }),

/***/ "node:readline":
/*!********************************!*\
  !*** external "node:readline" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:readline");

/***/ }),

/***/ "node:stream":
/*!******************************!*\
  !*** external "node:stream" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:stream");

/***/ }),

/***/ "node:tls":
/*!***************************!*\
  !*** external "node:tls" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:tls");

/***/ }),

/***/ "node:util":
/*!****************************!*\
  !*** external "node:util" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:util");

/***/ }),

/***/ "node:worker_threads":
/*!**************************************!*\
  !*** external "node:worker_threads" ***!
  \**************************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:worker_threads");

/***/ }),

/***/ "node:zlib":
/*!****************************!*\
  !*** external "node:zlib" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:zlib");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ wrappedServerComponent$1),\n/* harmony export */   generateImageMetadata: () => (/* binding */ generateImageMetadata),\n/* harmony export */   generateMetadata: () => (/* binding */ generateMetadata),\n/* harmony export */   generateViewport: () => (/* binding */ generateViewport),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var _sentry_core__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @sentry/core */ \"(rsc)/./node_modules/@sentry/core/build/esm/utils-hoist/buildPolyfills/_optionalChain.js\");\n/* harmony import */ var _sentry_core__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @sentry/core */ \"(rsc)/./node_modules/@sentry/core/build/esm/utils-hoist/buildPolyfills/_nullishCoalesce.js\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @sentry/nextjs */ \"(rsc)/./node_modules/@sentry/nextjs/build/cjs/index.server.js\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var next_dist_client_components_request_async_storage_external_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/client/components/request-async-storage.external.js */ \"../../client/components/request-async-storage.external\");\n/* harmony import */ var next_dist_client_components_request_async_storage_external_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_request_async_storage_external_js__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_sonner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/sonner */ \"(rsc)/./src/components/ui/sonner.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/user */ \"(rsc)/./src/providers/user.tsx\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/alert */ \"(rsc)/./src/providers/alert.tsx\");\n/* harmony import */ var _providers_preview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/preview */ \"(rsc)/./src/providers/preview.tsx\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/providers/screenSize */ \"(rsc)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _utils_environment__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/utils/environment */ \"(rsc)/./src/utils/environment.ts\");\n/* harmony import */ var _components_tracking__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/tracking */ \"(rsc)/./src/components/tracking.tsx\");\n/* harmony import */ var _providers_broadcast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/providers/broadcast */ \"(rsc)/./src/providers/broadcast.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_12___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_12__);\n/* harmony import */ var _components_landing_registerReferral__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/landing/registerReferral */ \"(rsc)/./src/components/landing/registerReferral.tsx\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// const inter = Inter({ subsets: ['latin'] })\n// const jakarta = Plus_Jakarta_Sans({\n//   display: \"swap\",\n//   subsets: [\"latin\", \"cyrillic-ext\", \"vietnamese\", \"latin-ext\"],\n//   variable: \"--font-Jakarta\",\n//   weight: \"variable\",\n// });\nconst metadata = {\n    title: \"Opendashboard | Unified Platform for Your Business Data and Intelligence.\",\n    description: \"Streamline your data, Elevate your business\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"head\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"meta\", {\n                        name: \"viewport\",\n                        content: \"width=device-width,height=device-height,initial-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        rel: \"manifest\",\n                        href: \"/manifest.json\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 36,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"meta\", {\n                        name: \"theme-color\",\n                        content: \"#4292EB\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 37,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        rel: \"apple-touch-icon\",\n                        sizes: \"180x180\",\n                        href: \"/apple-touch-icon.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"32x32\",\n                        href: \"/favicon-32x32.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        rel: \"icon\",\n                        type: \"image/png\",\n                        sizes: \"16x16\",\n                        href: \"/favicon-16x16.png\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 49,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-capable\",\n                        content: \"yes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 55,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"meta\", {\n                        name: \"apple-mobile-web-app-status-bar-style\",\n                        content: \"default\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 13\n                    }, this),\n                    (0,_utils_environment__WEBPACK_IMPORTED_MODULE_9__.isProd)() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_tracking__WEBPACK_IMPORTED_MODULE_10__.Tracking, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"script\", {\n                        type: \"text/javascript\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        href: \"/assets/fonts/fontawesome/css/fontawesome.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        href: \"/assets/fonts/fontawesome/css/regular.min.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        href: \"/assets/fonts/fontawesome/css/light.min.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        href: \"/assets/fonts/fontawesome/css/sharp-regular.min.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"link\", {\n                        href: \"/assets/fonts/fontawesome/css/sharp-light.min.css\",\n                        rel: \"stylesheet\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"body\", {\n                suppressHydrationWarning: true,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(// \"min-h-screen bg-background font-sans antialiased\",\n                \"min-h-screen bg-background antialiased\"),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_12__.Suspense, {\n                    fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {}, void 0, false),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_providers_alert__WEBPACK_IMPORTED_MODULE_6__.AlertProvider, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_providers_screenSize__WEBPACK_IMPORTED_MODULE_8__.ScreenSizeProvider, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_providers_broadcast__WEBPACK_IMPORTED_MODULE_11__.BroadcastProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_providers_user__WEBPACK_IMPORTED_MODULE_5__.UserProvider, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_providers_preview__WEBPACK_IMPORTED_MODULE_7__.PreviewProvider, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(\"main\", {\n                                                    children: children\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 104,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_ui_sonner__WEBPACK_IMPORTED_MODULE_4__.Toaster, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                                                    lineNumber: 105,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                                            lineNumber: 103,\n                                            columnNumber: 29\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                                        lineNumber: 102,\n                                        columnNumber: 25\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxDEV)(_components_landing_registerReferral__WEBPACK_IMPORTED_MODULE_13__.RegisterReferral, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 21\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                        lineNumber: 99,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 33,\n        columnNumber: 9\n    }, this);\n}\n\nconst asyncStorageModule = { ...next_dist_client_components_request_async_storage_external_js__WEBPACK_IMPORTED_MODULE_0__ } ;\n\nconst requestAsyncStorage =\n  'workUnitAsyncStorage' in asyncStorageModule\n    ? asyncStorageModule.workUnitAsyncStorage\n    : 'requestAsyncStorage' in asyncStorageModule\n      ? asyncStorageModule.requestAsyncStorage\n      : undefined;\n\nconst serverComponent = RootLayout;\n\nlet wrappedServerComponent;\nif (typeof serverComponent === 'function') {\n  // For some odd Next.js magic reason, `headers()` will not work if used inside `wrapServerComponentsWithSentry`.\n  // Current assumption is that Next.js applies some loader magic to userfiles, but not files in node_modules. This file\n  // is technically a userfile so it gets the loader magic applied.\n  wrappedServerComponent = new Proxy(serverComponent, {\n    apply: (originalFunction, thisArg, args) => {\n      let sentryTraceHeader = undefined;\n      let baggageHeader = undefined;\n      let headers = undefined;\n\n      // We try-catch here just in `requestAsyncStorage` is undefined since it may not be defined\n      try {\n        const requestAsyncStore = (0,_sentry_core__WEBPACK_IMPORTED_MODULE_14__._optionalChain)([requestAsyncStorage, 'optionalAccess', _ => _.getStore, 'call', _2 => _2()]) ;\n        sentryTraceHeader = (0,_sentry_core__WEBPACK_IMPORTED_MODULE_15__._nullishCoalesce)((0,_sentry_core__WEBPACK_IMPORTED_MODULE_14__._optionalChain)([requestAsyncStore, 'optionalAccess', _3 => _3.headers, 'access', _4 => _4.get, 'call', _5 => _5('sentry-trace')]), () => ( undefined));\n        baggageHeader = (0,_sentry_core__WEBPACK_IMPORTED_MODULE_15__._nullishCoalesce)((0,_sentry_core__WEBPACK_IMPORTED_MODULE_14__._optionalChain)([requestAsyncStore, 'optionalAccess', _6 => _6.headers, 'access', _7 => _7.get, 'call', _8 => _8('baggage')]), () => ( undefined));\n        headers = (0,_sentry_core__WEBPACK_IMPORTED_MODULE_14__._optionalChain)([requestAsyncStore, 'optionalAccess', _9 => _9.headers]);\n      } catch (e) {\n        /** empty */\n      }\n\n      return _sentry_nextjs__WEBPACK_IMPORTED_MODULE_16__.wrapServerComponentWithSentry(originalFunction, {\n        componentRoute: '/',\n        componentType: 'Layout',\n        sentryTraceHeader,\n        baggageHeader,\n        headers,\n      }).apply(thisArg, args);\n    },\n  });\n} else {\n  wrappedServerComponent = serverComponent;\n}\n\nconst generateMetadata = undefined;\n\nconst generateImageMetadata = undefined;\n\nconst generateViewport = undefined;\n\nconst wrappedServerComponent$1 = wrappedServerComponent;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHomePC%5CDesktop%5Cad%5Copendashboard-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHomePC%5CDesktop%5Cad%5Copendashboard-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHomePC%5CDesktop%5Cad%5Copendashboard-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHomePC%5CDesktop%5Cad%5Copendashboard-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport safe */ C_Users_HomePC_Desktop_ad_opendashboard_frontend_src_app_global_error_tsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?f86a\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var C_Users_HomePC_Desktop_ad_opendashboard_frontend_src_app_global_error_tsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./src/app/global-error.tsx */ \"(rsc)/./src/app/global-error.tsx\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHomePC%5CDesktop%5Cad%5Copendashboard-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHomePC%5CDesktop%5Cad%5Copendashboard-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/global-error.tsx */ \"(ssr)/./src/app/global-error.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hvbWVQQyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZCU1QyU1Q29wZW5kYXNoYm9hcmQtZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWwtZXJyb3IudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnS0FBdUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLz8zM2E3Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiQzpcXFxcVXNlcnNcXFxcSG9tZVBDXFxcXERlc2t0b3BcXFxcYWRcXFxcb3BlbmRhc2hib2FyZC1mcm9udGVuZFxcXFxzcmNcXFxcYXBwXFxcXGdsb2JhbC1lcnJvci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobal-error.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CregisterReferral.tsx%22%2C%22ids%22%3A%5B%22RegisterReferral%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctracking.tsx%22%2C%22ids%22%3A%5B%22Tracking%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Calert.tsx%22%2C%22ids%22%3A%5B%22AlertProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cbroadcast.tsx%22%2C%22ids%22%3A%5B%22BroadcastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cpreview.tsx%22%2C%22ids%22%3A%5B%22PreviewProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5CscreenSize.tsx%22%2C%22ids%22%3A%5B%22ScreenSizeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cuser.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CregisterReferral.tsx%22%2C%22ids%22%3A%5B%22RegisterReferral%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctracking.tsx%22%2C%22ids%22%3A%5B%22Tracking%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Calert.tsx%22%2C%22ids%22%3A%5B%22AlertProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cbroadcast.tsx%22%2C%22ids%22%3A%5B%22BroadcastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cpreview.tsx%22%2C%22ids%22%3A%5B%22PreviewProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5CscreenSize.tsx%22%2C%22ids%22%3A%5B%22ScreenSizeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cuser.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/landing/registerReferral.tsx */ \"(ssr)/./src/components/landing/registerReferral.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/tracking.tsx */ \"(ssr)/./src/components/tracking.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/ui/sonner.tsx */ \"(ssr)/./src/components/ui/sonner.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/alert.tsx */ \"(ssr)/./src/providers/alert.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/broadcast.tsx */ \"(ssr)/./src/providers/broadcast.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/preview.tsx */ \"(ssr)/./src/providers/preview.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/screenSize.tsx */ \"(ssr)/./src/providers/screenSize.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/providers/user.tsx */ \"(ssr)/./src/providers/user.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hvbWVQQyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZCU1QyU1Q29wZW5kYXNoYm9hcmQtZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNIb21lUEMlNUMlNUNEZXNrdG9wJTVDJTVDYWQlNUMlNUNvcGVuZGFzaGJvYXJkLWZyb250ZW5kJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q2xhbmRpbmclNUMlNUNyZWdpc3RlclJlZmVycmFsLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlJlZ2lzdGVyUmVmZXJyYWwlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSG9tZVBDJTVDJTVDRGVza3RvcCU1QyU1Q2FkJTVDJTVDb3BlbmRhc2hib2FyZC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q2NvbXBvbmVudHMlNUMlNUN0cmFja2luZy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJUcmFja2luZyUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNIb21lUEMlNUMlNUNEZXNrdG9wJTVDJTVDYWQlNUMlNUNvcGVuZGFzaGJvYXJkLWZyb250ZW5kJTVDJTVDc3JjJTVDJTVDY29tcG9uZW50cyU1QyU1Q3VpJTVDJTVDc29ubmVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlRvYXN0ZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSG9tZVBDJTVDJTVDRGVza3RvcCU1QyU1Q2FkJTVDJTVDb3BlbmRhc2hib2FyZC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q2FsZXJ0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkFsZXJ0UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSG9tZVBDJTVDJTVDRGVza3RvcCU1QyU1Q2FkJTVDJTVDb3BlbmRhc2hib2FyZC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q2Jyb2FkY2FzdC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJCcm9hZGNhc3RQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNIb21lUEMlNUMlNUNEZXNrdG9wJTVDJTVDYWQlNUMlNUNvcGVuZGFzaGJvYXJkLWZyb250ZW5kJTVDJTVDc3JjJTVDJTVDcHJvdmlkZXJzJTVDJTVDcHJldmlldy50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJQcmV2aWV3UHJvdmlkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDSG9tZVBDJTVDJTVDRGVza3RvcCU1QyU1Q2FkJTVDJTVDb3BlbmRhc2hib2FyZC1mcm9udGVuZCU1QyU1Q3NyYyU1QyU1Q3Byb3ZpZGVycyU1QyU1Q3NjcmVlblNpemUudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyU2NyZWVuU2l6ZVByb3ZpZGVyJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0hvbWVQQyU1QyU1Q0Rlc2t0b3AlNUMlNUNhZCU1QyU1Q29wZW5kYXNoYm9hcmQtZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNwcm92aWRlcnMlNUMlNUN1c2VyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMlVzZXJQcm92aWRlciUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsc01BQWlMO0FBQ2pMO0FBQ0Esc0tBQXdKO0FBQ3hKO0FBQ0Esd0tBQXlKO0FBQ3pKO0FBQ0EsOEpBQXlKO0FBQ3pKO0FBQ0Esc0tBQWlLO0FBQ2pLO0FBQ0Esa0tBQTZKO0FBQzdKO0FBQ0Esd0tBQW1LO0FBQ25LO0FBQ0EsNEpBQXVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8/OWE5MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlJlZ2lzdGVyUmVmZXJyYWxcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxIb21lUENcXFxcRGVza3RvcFxcXFxhZFxcXFxvcGVuZGFzaGJvYXJkLWZyb250ZW5kXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXGxhbmRpbmdcXFxccmVnaXN0ZXJSZWZlcnJhbC50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRyYWNraW5nXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcSG9tZVBDXFxcXERlc2t0b3BcXFxcYWRcXFxcb3BlbmRhc2hib2FyZC1mcm9udGVuZFxcXFxzcmNcXFxcY29tcG9uZW50c1xcXFx0cmFja2luZy50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRvYXN0ZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxIb21lUENcXFxcRGVza3RvcFxcXFxhZFxcXFxvcGVuZGFzaGJvYXJkLWZyb250ZW5kXFxcXHNyY1xcXFxjb21wb25lbnRzXFxcXHVpXFxcXHNvbm5lci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkFsZXJ0UHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxIb21lUENcXFxcRGVza3RvcFxcXFxhZFxcXFxvcGVuZGFzaGJvYXJkLWZyb250ZW5kXFxcXHNyY1xcXFxwcm92aWRlcnNcXFxcYWxlcnQudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJCcm9hZGNhc3RQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEhvbWVQQ1xcXFxEZXNrdG9wXFxcXGFkXFxcXG9wZW5kYXNoYm9hcmQtZnJvbnRlbmRcXFxcc3JjXFxcXHByb3ZpZGVyc1xcXFxicm9hZGNhc3QudHN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcmV2aWV3UHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxIb21lUENcXFxcRGVza3RvcFxcXFxhZFxcXFxvcGVuZGFzaGJvYXJkLWZyb250ZW5kXFxcXHNyY1xcXFxwcm92aWRlcnNcXFxccHJldmlldy50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlNjcmVlblNpemVQcm92aWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXEhvbWVQQ1xcXFxEZXNrdG9wXFxcXGFkXFxcXG9wZW5kYXNoYm9hcmQtZnJvbnRlbmRcXFxcc3JjXFxcXHByb3ZpZGVyc1xcXFxzY3JlZW5TaXplLnRzeFwiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiVXNlclByb3ZpZGVyXCJdICovIFwiQzpcXFxcVXNlcnNcXFxcSG9tZVBDXFxcXERlc2t0b3BcXFxcYWRcXFxcb3BlbmRhc2hib2FyZC1mcm9udGVuZFxcXFxzcmNcXFxccHJvdmlkZXJzXFxcXHVzZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Clanding%5C%5CregisterReferral.tsx%22%2C%22ids%22%3A%5B%22RegisterReferral%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Ctracking.tsx%22%2C%22ids%22%3A%5B%22Tracking%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Ccomponents%5C%5Cui%5C%5Csonner.tsx%22%2C%22ids%22%3A%5B%22Toaster%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Calert.tsx%22%2C%22ids%22%3A%5B%22AlertProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cbroadcast.tsx%22%2C%22ids%22%3A%5B%22BroadcastProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cpreview.tsx%22%2C%22ids%22%3A%5B%22PreviewProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5CscreenSize.tsx%22%2C%22ids%22%3A%5B%22ScreenSizeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CHomePC%5C%5CDesktop%5C%5Cad%5C%5Copendashboard-frontend%5C%5Csrc%5C%5Cproviders%5C%5Cuser.tsx%22%2C%22ids%22%3A%5B%22UserProvider%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/api/account.ts":
/*!****************************!*\
  !*** ./src/api/account.ts ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DeRegisterFCMToken: () => (/* binding */ DeRegisterFCMToken),\n/* harmony export */   EventType: () => (/* binding */ EventType),\n/* harmony export */   RegisterFCMToken: () => (/* binding */ RegisterFCMToken),\n/* harmony export */   createApiKey: () => (/* binding */ createApiKey),\n/* harmony export */   deleteSession: () => (/* binding */ deleteSession),\n/* harmony export */   getAccount: () => (/* binding */ getAccount),\n/* harmony export */   getApiKeys: () => (/* binding */ getApiKeys),\n/* harmony export */   getSessions: () => (/* binding */ getSessions),\n/* harmony export */   getSettings: () => (/* binding */ getSettings),\n/* harmony export */   pingSession: () => (/* binding */ pingSession),\n/* harmony export */   pushEvent: () => (/* binding */ pushEvent),\n/* harmony export */   regenerateApiKey: () => (/* binding */ regenerateApiKey),\n/* harmony export */   updateApiKey: () => (/* binding */ updateApiKey),\n/* harmony export */   updatePhoto: () => (/* binding */ updatePhoto),\n/* harmony export */   updateProfile: () => (/* binding */ updateProfile),\n/* harmony export */   updateSettings: () => (/* binding */ updateSettings)\n/* harmony export */ });\n/* harmony import */ var _api_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/common */ \"(ssr)/./src/api/common.ts\");\n/* harmony import */ var _utils_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/http */ \"(ssr)/./src/utils/http.ts\");\n\n\nconst getAccount = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updatePhoto = (token, file, callback)=>{\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/photo`;\n    const oldCb = callback.onComplete;\n    callback.onComplete = (res)=>{\n        const response = (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(res);\n        oldCb(response);\n    };\n    (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.UploadFile)(\"patch\", endpoint, token, \"file\", file, callback);\n};\nconst updateProfile = async (token, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst pingSession = async (token, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/ping`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getSessions = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/sessions`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteSession = async (token, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/sessions`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getSettings = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/settings`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateSettings = async (token, type, settings)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const body = {\n        type,\n        settings\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/settings`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getApiKeys = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/api-keys`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateApiKey = async (token, id, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/api-keys/${id}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst createApiKey = async (token, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/api-keys`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst regenerateApiKey = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/api-keys/${id}/regenerate`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, {});\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nvar EventType;\n(function(EventType) {\n    EventType[EventType[\"View\"] = 1] = \"View\";\n})(EventType || (EventType = {}));\nconst pushEvent = async (token, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/event`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst RegisterFCMToken = async (token, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/push-tokens`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst DeRegisterFCMToken = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/account/push-tokens`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, {});\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/account.ts\n");

/***/ }),

/***/ "(ssr)/./src/api/admin.ts":
/*!**************************!*\
  !*** ./src/api/admin.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   adminAddTopPick: () => (/* binding */ adminAddTopPick),\n/* harmony export */   adminApproveAffiliate: () => (/* binding */ adminApproveAffiliate),\n/* harmony export */   adminGetAffiliates: () => (/* binding */ adminGetAffiliates),\n/* harmony export */   adminRespondToTemplate: () => (/* binding */ adminRespondToTemplate),\n/* harmony export */   getAdminMember: () => (/* binding */ getAdminMember),\n/* harmony export */   getAdminTemplatesSubmitted: () => (/* binding */ getAdminTemplatesSubmitted),\n/* harmony export */   queryObjectToString: () => (/* binding */ queryObjectToString)\n/* harmony export */ });\n/* harmony import */ var _api_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/common */ \"(ssr)/./src/api/common.ts\");\n/* harmony import */ var _utils_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/http */ \"(ssr)/./src/utils/http.ts\");\n\n\nconst getAdminMember = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/admin`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getAdminTemplatesSubmitted = async (token, queryData = {})=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    // turn query object to url query string\n    const queryStr = queryObjectToString(queryData);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/admin/templates/submitted?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nfunction queryObjectToString(query) {\n    const params = [];\n    for(const key in query){\n        if (query.hasOwnProperty(key)) {\n            const value = query[key];\n            if (value === undefined || value === null) continue;\n            if (typeof value === \"object\") {\n                for(const subKey in value){\n                    if (value.hasOwnProperty(subKey)) {\n                        params.push(`${encodeURIComponent(key)}[${encodeURIComponent(subKey)}]=${encodeURIComponent(value[subKey])}`);\n                    }\n                }\n            } else {\n                params.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);\n            }\n        }\n    }\n    return params.join(\"&\");\n}\nconst adminRespondToTemplate = async (token, submissionId, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/admin/templates/submitted/${submissionId}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst adminAddTopPick = async (token, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/admin/templates/top-picks`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst adminGetAffiliates = async (token, params)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const queryStr = queryObjectToString(params);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/admin/affiliates?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst adminApproveAffiliate = async (token, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/admin/affiliates/approve`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/admin.ts\n");

/***/ }),

/***/ "(ssr)/./src/api/affiliate.ts":
/*!******************************!*\
  !*** ./src/api/affiliate.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getAffiliatePayouts: () => (/* binding */ getAffiliatePayouts),\n/* harmony export */   getAffiliateStats: () => (/* binding */ getAffiliateStats),\n/* harmony export */   getAffiliates: () => (/* binding */ getAffiliates),\n/* harmony export */   registerReferral: () => (/* binding */ registerReferral),\n/* harmony export */   signupForAffiliate: () => (/* binding */ signupForAffiliate),\n/* harmony export */   updateAffiliateCode: () => (/* binding */ updateAffiliateCode)\n/* harmony export */ });\n/* harmony import */ var _api_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/common */ \"(ssr)/./src/api/common.ts\");\n/* harmony import */ var _utils_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/http */ \"(ssr)/./src/utils/http.ts\");\n/* harmony import */ var _api_admin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/api/admin */ \"(ssr)/./src/api/admin.ts\");\n\n\n\nconst registerReferral = async (referralCode)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.publicApiUrl)()}/v0/referral`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, {\n        referralCode\n    });\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getAffiliates = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/affiliates`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getAffiliateStats = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/affiliates/stats`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getAffiliatePayouts = async (token, params = {})=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const queryStr = (0,_api_admin__WEBPACK_IMPORTED_MODULE_2__.queryObjectToString)(params);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/affiliates/payouts?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst signupForAffiliate = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/affiliates`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateAffiliateCode = async (token, referralCode)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/affiliates`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, {\n        code: referralCode\n    });\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/affiliate.ts\n");

/***/ }),

/***/ "(ssr)/./src/api/auth.ts":
/*!*************************!*\
  !*** ./src/api/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __VARS__: () => (/* binding */ __VARS__),\n/* harmony export */   clearAuthTokenIds: () => (/* binding */ clearAuthTokenIds),\n/* harmony export */   clearToken: () => (/* binding */ clearToken),\n/* harmony export */   exchangeToken: () => (/* binding */ exchangeToken),\n/* harmony export */   getAuthTokenIds: () => (/* binding */ getAuthTokenIds),\n/* harmony export */   getRedirectLocation: () => (/* binding */ getRedirectLocation),\n/* harmony export */   getReferralCode: () => (/* binding */ getReferralCode),\n/* harmony export */   getSessionLastPing: () => (/* binding */ getSessionLastPing),\n/* harmony export */   getToken: () => (/* binding */ getToken),\n/* harmony export */   initiateSignIn: () => (/* binding */ initiateSignIn),\n/* harmony export */   removeRedirectLocation: () => (/* binding */ removeRedirectLocation),\n/* harmony export */   removeReferralCode: () => (/* binding */ removeReferralCode),\n/* harmony export */   saveAuthTokenId: () => (/* binding */ saveAuthTokenId),\n/* harmony export */   saveLastSessionPing: () => (/* binding */ saveLastSessionPing),\n/* harmony export */   saveReferralCode: () => (/* binding */ saveReferralCode),\n/* harmony export */   saveToken: () => (/* binding */ saveToken),\n/* harmony export */   setRedirectLocation: () => (/* binding */ setRedirectLocation)\n/* harmony export */ });\n/* harmony import */ var _common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./common */ \"(ssr)/./src/api/common.ts\");\n/* harmony import */ var _utils_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/http */ \"(ssr)/./src/utils/http.ts\");\n\n\nconst initiateSignIn = async (email)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    const body = {\n        email\n    };\n    const endpoint = `${(0,_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/auth/sign-in`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst exchangeToken = async (hash)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\"\n    };\n    const body = {\n        hash\n    };\n    const endpoint = `${(0,_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/auth/exchange-token`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nvar __VARS__;\n(function(__VARS__) {\n    __VARS__[\"__SESSION_TOKEN\"] = \"__session_token\";\n    __VARS__[\"__SESSION_LAST_PING\"] = \"__session_last_ping\";\n    __VARS__[\"__SESSION_AUTH_REDIRECT_URL\"] = \"__session_auth_redirect_url\";\n    __VARS__[__VARS__[\"__SESSION_PING_TIMEOUT_MS\"] = 600000] = \"__SESSION_PING_TIMEOUT_MS\";\n    __VARS__[\"__SESSION_REFERRAL_ATTRIBUTION\"] = \"__session_referral_attribution\";\n    __VARS__[__VARS__[\"__SESSION_REFERRAL_TIMEOUT_DAYS\"] = 30] = \"__SESSION_REFERRAL_TIMEOUT_DAYS\";\n    __VARS__[\"__SESSION_AUTH_TOKEN_IDS\"] = \"__session_auth_token_ids\";\n})(__VARS__ || (__VARS__ = {}));\nconst saveToken = (token)=>{\n    localStorage.setItem(\"__session_token\", JSON.stringify(token));\n};\nconst getToken = ()=>{\n    if (false) {}\n    return null;\n};\nconst clearToken = ()=>{\n    localStorage.removeItem(\"__session_token\");\n};\nconst getSessionLastPing = ()=>{\n    // __SESSION_LAST_PING\n    if (false) {}\n    return 0;\n};\nconst saveLastSessionPing = ()=>{\n    if (false) {}\n};\nconst setRedirectLocation = (location)=>{\n    if (false) {}\n};\nconst getRedirectLocation = ()=>{\n    if (false) {}\n    return null;\n};\nconst saveAuthTokenId = (tokenId)=>{\n    if (false) {}\n};\nconst getAuthTokenIds = ()=>{\n    if (false) {}\n    return \"\";\n};\nconst clearAuthTokenIds = ()=>{\n    if (false) {}\n    return;\n};\nconst removeRedirectLocation = ()=>{\n    if (false) {}\n};\nconst saveReferralCode = (referralCode)=>{\n    if (false) {}\n};\nconst getReferralCode = ()=>{\n    if (false) {}\n    return undefined;\n};\nconst removeReferralCode = ()=>{\n    if (false) {}\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/auth.ts\n");

/***/ }),

/***/ "(ssr)/./src/api/common.ts":
/*!***************************!*\
  !*** ./src/api/common.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadFile: () => (/* binding */ UploadFile),\n/* harmony export */   apiUrl: () => (/* binding */ apiUrl),\n/* harmony export */   collabServiceHash: () => (/* binding */ collabServiceHash),\n/* harmony export */   collabServiceUrl: () => (/* binding */ collabServiceUrl),\n/* harmony export */   defaultAPIMessage: () => (/* binding */ defaultAPIMessage),\n/* harmony export */   googleRecaptchaSiteKey: () => (/* binding */ googleRecaptchaSiteKey),\n/* harmony export */   normalizeResponse: () => (/* binding */ normalizeResponse),\n/* harmony export */   publicApiUrl: () => (/* binding */ publicApiUrl)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! opendb-app-db-utils/lib */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/index.js\");\n/* harmony import */ var opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst apiUrl = ()=>{\n    return \"http://localhost:3033/api/v1\";\n};\nconst publicApiUrl = ()=>(0,opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_0__.strReplaceAll)(\"/api/v1\", apiUrl() || \"\", \"\");\nconst googleRecaptchaSiteKey = ()=>{\n    return process.env.NEXT_PUBLIC_RECAPTCHA_SITEKEY;\n};\nconst collabServiceUrl = ()=>{\n    return \"http://localhost:4000/\";\n};\nconst collabServiceHash = ()=>{\n    return \"ccHash12345\";\n};\nconst defaultAPIMessage = ()=>{\n    return \"Error occurred while making request\";\n};\nconst normalizeResponse = (response)=>{\n    if (!response.data || response.data.status !== \"ok\") {\n        response.error = response.data?.error || response.data?.message || defaultAPIMessage();\n    }\n    return response;\n};\nconst UploadFile = (method, url, token, fileKey, file, callback, body)=>{\n    const data = new FormData();\n    data.append(fileKey, file, file.name);\n    if (body) {\n        for (let key of Object.keys(body)){\n            data.append(key, body[key]);\n        }\n    }\n    const onProgress = (event)=>{\n        const percent = Math.round(100 * event.loaded / event.total);\n        callback.onProgress(percent);\n    };\n    const headers = {\n        \"Content-Type\": \"multipart/form-data\",\n        Authorization: `Bearer ${token}`\n    };\n    callback.onStart();\n    (0,axios__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n        method,\n        url,\n        data,\n        headers,\n        onUploadProgress: onProgress\n    }).then((raw)=>{\n        const isSuccess = true;\n        const res = {\n            status: raw ? raw.status : 0,\n            data: raw ? raw.data : undefined,\n            isSuccess\n        };\n        callback.onComplete(res);\n    }).catch((e)=>{\n        const ex = e;\n        const raw = ex.response;\n        const error = ex.message;\n        const exception = ex;\n        const res = {\n            status: raw ? raw.status : 0,\n            data: raw ? raw.data : undefined,\n            isSuccess: false,\n            exception,\n            error\n        };\n        callback.onComplete(res);\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/common.ts\n");

/***/ }),

/***/ "(ssr)/./src/api/workspace.ts":
/*!******************************!*\
  !*** ./src/api/workspace.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   acceptInvitation: () => (/* binding */ acceptInvitation),\n/* harmony export */   addWorkspaceSender: () => (/* binding */ addWorkspaceSender),\n/* harmony export */   completeWorkspaceSetup: () => (/* binding */ completeWorkspaceSetup),\n/* harmony export */   createNote: () => (/* binding */ createNote),\n/* harmony export */   createNotification: () => (/* binding */ createNotification),\n/* harmony export */   createReminder: () => (/* binding */ createReminder),\n/* harmony export */   createSecret: () => (/* binding */ createSecret),\n/* harmony export */   createWorkspace: () => (/* binding */ createWorkspace),\n/* harmony export */   createWorkspaceViaOnboarding: () => (/* binding */ createWorkspaceViaOnboarding),\n/* harmony export */   declineInvitation: () => (/* binding */ declineInvitation),\n/* harmony export */   deleteFutureSubscription: () => (/* binding */ deleteFutureSubscription),\n/* harmony export */   deleteIntegrationConnection: () => (/* binding */ deleteIntegrationConnection),\n/* harmony export */   deleteInvitation: () => (/* binding */ deleteInvitation),\n/* harmony export */   deleteNote: () => (/* binding */ deleteNote),\n/* harmony export */   deleteReminder: () => (/* binding */ deleteReminder),\n/* harmony export */   deleteSecret: () => (/* binding */ deleteSecret),\n/* harmony export */   deleteSubscription: () => (/* binding */ deleteSubscription),\n/* harmony export */   deleteWorkspace: () => (/* binding */ deleteWorkspace),\n/* harmony export */   deleteWorkspaceDomain: () => (/* binding */ deleteWorkspaceDomain),\n/* harmony export */   deleteWorkspaceSenderEmail: () => (/* binding */ deleteWorkspaceSenderEmail),\n/* harmony export */   executeIntegrationAction: () => (/* binding */ executeIntegrationAction),\n/* harmony export */   fetchIntegrationConnections: () => (/* binding */ fetchIntegrationConnections),\n/* harmony export */   getCheckoutSessionUrl: () => (/* binding */ getCheckoutSessionUrl),\n/* harmony export */   getCustomerPortalUrl: () => (/* binding */ getCustomerPortalUrl),\n/* harmony export */   getDocumentHistory: () => (/* binding */ getDocumentHistory),\n/* harmony export */   getInstalledTemplates: () => (/* binding */ getInstalledTemplates),\n/* harmony export */   getIntegrationDropdownOptions: () => (/* binding */ getIntegrationDropdownOptions),\n/* harmony export */   getInvitation: () => (/* binding */ getInvitation),\n/* harmony export */   getMemberSettings: () => (/* binding */ getMemberSettings),\n/* harmony export */   getNotes: () => (/* binding */ getNotes),\n/* harmony export */   getNotificationStats: () => (/* binding */ getNotificationStats),\n/* harmony export */   getNotifications: () => (/* binding */ getNotifications),\n/* harmony export */   getPlans: () => (/* binding */ getPlans),\n/* harmony export */   getReminders: () => (/* binding */ getReminders),\n/* harmony export */   getSecrets: () => (/* binding */ getSecrets),\n/* harmony export */   getWorkspace: () => (/* binding */ getWorkspace),\n/* harmony export */   getWorkspaceMembers: () => (/* binding */ getWorkspaceMembers),\n/* harmony export */   getWorkspaceRiskLog: () => (/* binding */ getWorkspaceRiskLog),\n/* harmony export */   getWorkspaceSenders: () => (/* binding */ getWorkspaceSenders),\n/* harmony export */   getWorkspaceUsage: () => (/* binding */ getWorkspaceUsage),\n/* harmony export */   getWorkspaces: () => (/* binding */ getWorkspaces),\n/* harmony export */   inviteToPage: () => (/* binding */ inviteToPage),\n/* harmony export */   inviteWorkspaceMember: () => (/* binding */ inviteWorkspaceMember),\n/* harmony export */   makeWorkspaceMemberOwner: () => (/* binding */ makeWorkspaceMemberOwner),\n/* harmony export */   modifyAddOns: () => (/* binding */ modifyAddOns),\n/* harmony export */   purchaseWorkspaceCredit: () => (/* binding */ purchaseWorkspaceCredit),\n/* harmony export */   removeWorkspaceMember: () => (/* binding */ removeWorkspaceMember),\n/* harmony export */   resendInvitation: () => (/* binding */ resendInvitation),\n/* harmony export */   resendWorkspaceSenderVerificationEmail: () => (/* binding */ resendWorkspaceSenderVerificationEmail),\n/* harmony export */   resolveReminder: () => (/* binding */ resolveReminder),\n/* harmony export */   revokePagePermission: () => (/* binding */ revokePagePermission),\n/* harmony export */   saveIntegrationConnection: () => (/* binding */ saveIntegrationConnection),\n/* harmony export */   searchWorkspaces: () => (/* binding */ searchWorkspaces),\n/* harmony export */   startIntegrationOAuth2Redirect: () => (/* binding */ startIntegrationOAuth2Redirect),\n/* harmony export */   suggestImportMapping: () => (/* binding */ suggestImportMapping),\n/* harmony export */   switchToWorkspace: () => (/* binding */ switchToWorkspace),\n/* harmony export */   updateNote: () => (/* binding */ updateNote),\n/* harmony export */   updateNotification: () => (/* binding */ updateNotification),\n/* harmony export */   updatePagePermission: () => (/* binding */ updatePagePermission),\n/* harmony export */   updateReminder: () => (/* binding */ updateReminder),\n/* harmony export */   updateSecret: () => (/* binding */ updateSecret),\n/* harmony export */   updateWorkspace: () => (/* binding */ updateWorkspace),\n/* harmony export */   updateWorkspaceLogo: () => (/* binding */ updateWorkspaceLogo),\n/* harmony export */   updateWorkspaceMember: () => (/* binding */ updateWorkspaceMember),\n/* harmony export */   updateWorkspaceSupportAccess: () => (/* binding */ updateWorkspaceSupportAccess),\n/* harmony export */   uploadFile: () => (/* binding */ uploadFile),\n/* harmony export */   uploadRecordCoverImage: () => (/* binding */ uploadRecordCoverImage),\n/* harmony export */   uploadRecordProfileImage: () => (/* binding */ uploadRecordProfileImage),\n/* harmony export */   verifyWorkspaceDomain: () => (/* binding */ verifyWorkspaceDomain)\n/* harmony export */ });\n/* harmony import */ var _api_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/api/common */ \"(ssr)/./src/api/common.ts\");\n/* harmony import */ var _utils_http__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/utils/http */ \"(ssr)/./src/utils/http.ts\");\n/* harmony import */ var _api_admin__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/api/admin */ \"(ssr)/./src/api/admin.ts\");\n\n\n\nconst searchWorkspaces = async (token, workspaceId, query, page = 1, pageSize = 25)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}/search?query=${query}&page=${page}&pageSize=${pageSize}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getWorkspaces = async (token)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst createWorkspace = async (token, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst createWorkspaceViaOnboarding = async (token, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/v2`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getWorkspace = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateWorkspace = async (token, id, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getWorkspaceMembers = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/members`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst inviteWorkspaceMember = async (token, id, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/members`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getCustomerPortalUrl = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/customer-portal`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getCheckoutSessionUrl = async (token, id, planId, priceId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/checkout-session?planId=${planId}&priceId=${priceId}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteFutureSubscription = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/future-subscription`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteSubscription = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/subscription`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateWorkspaceLogo = (token, workspaceId, file, callback)=>{\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}/logo`;\n    const oldCb = callback.onComplete;\n    callback.onComplete = (res)=>{\n        const response = (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(res);\n        oldCb(response);\n    };\n    (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.UploadFile)(\"patch\", endpoint, token, \"file\", file, callback);\n};\nconst deleteWorkspace = async (token, workspaceId, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst completeWorkspaceSetup = async (token, workspaceId, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}/complete-setup`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst switchToWorkspace = async (token, workspaceId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}/switch-to`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, {});\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateWorkspaceSupportAccess = async (token, workspaceId, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}/support-access`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getWorkspaceUsage = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/usage`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getWorkspaceRiskLog = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/risklog`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst modifyAddOns = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/modify-addons`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getPlans = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/plans`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst purchaseWorkspaceCredit = async (token, id, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/purchase-credit`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteInvitation = async (token, id, inviteId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/invitations/${inviteId}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst resendInvitation = async (token, id, inviteId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/invitations/${inviteId}/resend`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst removeWorkspaceMember = async (token, id, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/members`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateWorkspaceMember = async (token, id, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/members`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst makeWorkspaceMemberOwner = async (token, id, userId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/members/${userId}/make-owner`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getInvitation = async (token, id, inviteToken)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/invitation?token=${inviteToken}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst acceptInvitation = async (token, id, inviteToken)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/invitation/accept?token=${inviteToken}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst declineInvitation = async (token, id, inviteToken)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/invitation/decline?token=${inviteToken}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getWorkspaceSenders = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/senders`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst addWorkspaceSender = async (token, id, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/senders`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst verifyWorkspaceDomain = async (token, id, domainId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/domains/${domainId}/verify`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst resendWorkspaceSenderVerificationEmail = async (token, id, senderId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/senders/${senderId}/prompt-verification`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteWorkspaceSenderEmail = async (token, id, senderId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/senders/${senderId}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteWorkspaceDomain = async (token, id, domainId)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/domains/${domainId}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getMemberSettings = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/settings`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst inviteToPage = async (token, id, pageId, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    // /workspaces/{id}/pages/{pageId}/permissions\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/pages/${pageId}/permissions`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updatePagePermission = async (token, id, pageId, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/pages/${pageId}/permissions`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst revokePagePermission = async (token, id, pageId, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/pages/${pageId}/permissions`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst uploadFile = (token, id, file, callback)=>{\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/uploads`;\n    const oldCb = callback.onComplete;\n    callback.onComplete = (res)=>{\n        const response = (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(res);\n        oldCb(response);\n    };\n    (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.UploadFile)(\"post\", endpoint, token, \"file\", file, callback);\n};\nconst uploadRecordCoverImage = (token, workspaceId, recordId, databaseId, file, callback)=>{\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}/databases/${databaseId}/records/${recordId}/cover-image`;\n    const oldCb = callback.onComplete;\n    callback.onComplete = (res)=>{\n        const response = (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(res);\n        oldCb(response);\n    };\n    (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.UploadFile)(\"patch\", endpoint, token, \"file\", file, callback);\n};\nconst uploadRecordProfileImage = (token, workspaceId, recordId, databaseId, file, callback)=>{\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}/databases/${databaseId}/records/${recordId}/image`;\n    const oldCb = callback.onComplete;\n    callback.onComplete = (res)=>{\n        const response = (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(res);\n        oldCb(response);\n    };\n    (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.UploadFile)(\"patch\", endpoint, token, \"file\", file, callback);\n};\nconst suggestImportMapping = async (token, id, body)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/suggest-import-map`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, body);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getInstalledTemplates = async (token, id, params = {})=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const queryStr = (0,_api_admin__WEBPACK_IMPORTED_MODULE_2__.queryObjectToString)(params);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/templates?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getNotifications = async (token, id, params = {})=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const queryStr = (0,_api_admin__WEBPACK_IMPORTED_MODULE_2__.queryObjectToString)(params);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/notifications?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateNotification = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/notifications`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst createNotification = async (token, workspaceId, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${workspaceId}/notifications/create`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getNotes = async (token, id, params = {})=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const queryStr = (0,_api_admin__WEBPACK_IMPORTED_MODULE_2__.queryObjectToString)(params);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/notes?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst createNote = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/notes`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteNote = async (token, id, params)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/notes`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, params);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateNote = async (token, id, params)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/notes`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, params);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getNotificationStats = async (token, id)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/notifications/stats`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getReminders = async (token, id, params = {})=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const queryStr = (0,_api_admin__WEBPACK_IMPORTED_MODULE_2__.queryObjectToString)(params);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/reminders?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst createReminder = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/reminders`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateReminder = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/reminders`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteReminder = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/reminders`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst resolveReminder = async (token, id, reminderId, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/reminders/${reminderId}/resolve`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getDocumentHistory = async (token, id, params)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const queryStr = (0,_api_admin__WEBPACK_IMPORTED_MODULE_2__.queryObjectToString)(params);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/document-history?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getSecrets = async (token, id, params)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const queryStr = (0,_api_admin__WEBPACK_IMPORTED_MODULE_2__.queryObjectToString)(params);\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/secrets?${queryStr}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst createSecret = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/secrets`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst updateSecret = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/secrets`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"patch\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteSecret = async (token, id, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/secrets`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst fetchIntegrationConnections = async (token, id, integration)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/integrations/${integration}/connections`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"get\", endpoint, headers);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst saveIntegrationConnection = async (token, id, integration, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/integrations/${integration}/connections`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst deleteIntegrationConnection = async (token, id, integration, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/integrations/${integration}/connections`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"delete\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst startIntegrationOAuth2Redirect = async (token, id, integration, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/integrations/${integration}/oauth2/redirect`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst getIntegrationDropdownOptions = async (token, id, integration, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/integrations/${integration}/options`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\nconst executeIntegrationAction = async (token, id, integration, data)=>{\n    const headers = {\n        \"Content-Type\": \"application/json\",\n        Authorization: `Bearer ${token}`\n    };\n    const endpoint = `${(0,_api_common__WEBPACK_IMPORTED_MODULE_0__.apiUrl)()}/workspaces/${id}/integrations/${integration}/${data.type}`;\n    const response = await (0,_utils_http__WEBPACK_IMPORTED_MODULE_1__.httpRequest)(\"post\", endpoint, headers, data);\n    return (0,_api_common__WEBPACK_IMPORTED_MODULE_0__.normalizeResponse)(response);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/api/workspace.ts\n");

/***/ }),

/***/ "(ssr)/./src/app/global-error.tsx":
/*!**********************************!*\
  !*** ./src/app/global-error.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ GlobalError)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @sentry/nextjs */ \"(ssr)/./node_modules/@sentry/nextjs/build/cjs/index.server.js\");\n/* harmony import */ var _sentry_nextjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_sentry_nextjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/error */ \"(ssr)/./node_modules/next/error.js\");\n/* harmony import */ var next_error__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_error__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction GlobalError({ error }) {\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        _sentry_nextjs__WEBPACK_IMPORTED_MODULE_3__.captureException(error);\n    }, [\n        error\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_error__WEBPACK_IMPORTED_MODULE_1___default()), {\n                statusCode: 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\global-error.tsx\",\n                lineNumber: 19,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\global-error.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\app\\\\global-error.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvYXBwL2dsb2JhbC1lcnJvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUV5QztBQUNOO0FBQ0Q7QUFFbkIsU0FBU0csWUFBWSxFQUFFQyxLQUFLLEVBQTBDO0lBQ25GRixnREFBU0EsQ0FBQztRQUNSRiw0REFBdUIsQ0FBQ0k7SUFDMUIsR0FBRztRQUFDQTtLQUFNO0lBRVYscUJBQ0UsOERBQUNFO2tCQUNDLDRFQUFDQztzQkFLQyw0RUFBQ04sbURBQVNBO2dCQUFDTyxZQUFZOzs7Ozs7Ozs7Ozs7Ozs7O0FBSS9CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFsLWVycm9yLnRzeD8yOTMyIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgKiBhcyBTZW50cnkgZnJvbSBcIkBzZW50cnkvbmV4dGpzXCI7XG5pbXBvcnQgTmV4dEVycm9yIGZyb20gXCJuZXh0L2Vycm9yXCI7XG5pbXBvcnQgeyB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gR2xvYmFsRXJyb3IoeyBlcnJvciB9OiB7IGVycm9yOiBFcnJvciAmIHsgZGlnZXN0Pzogc3RyaW5nIH0gfSkge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIFNlbnRyeS5jYXB0dXJlRXhjZXB0aW9uKGVycm9yKTtcbiAgfSwgW2Vycm9yXSk7XG5cbiAgcmV0dXJuIChcbiAgICA8aHRtbD5cbiAgICAgIDxib2R5PlxuICAgICAgICB7LyogYE5leHRFcnJvcmAgaXMgdGhlIGRlZmF1bHQgTmV4dC5qcyBlcnJvciBwYWdlIGNvbXBvbmVudC4gSXRzIHR5cGVcbiAgICAgICAgZGVmaW5pdGlvbiByZXF1aXJlcyBhIGBzdGF0dXNDb2RlYCBwcm9wLiBIb3dldmVyLCBzaW5jZSB0aGUgQXBwIFJvdXRlclxuICAgICAgICBkb2VzIG5vdCBleHBvc2Ugc3RhdHVzIGNvZGVzIGZvciBlcnJvcnMsIHdlIHNpbXBseSBwYXNzIDAgdG8gcmVuZGVyIGFcbiAgICAgICAgZ2VuZXJpYyBlcnJvciBtZXNzYWdlLiAqL31cbiAgICAgICAgPE5leHRFcnJvciBzdGF0dXNDb2RlPXswfSAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn0iXSwibmFtZXMiOlsiU2VudHJ5IiwiTmV4dEVycm9yIiwidXNlRWZmZWN0IiwiR2xvYmFsRXJyb3IiLCJlcnJvciIsImNhcHR1cmVFeGNlcHRpb24iLCJodG1sIiwiYm9keSIsInN0YXR1c0NvZGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/app/global-error.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/authentication/pingSessionHeadLess.tsx":
/*!***************************************************************!*\
  !*** ./src/components/authentication/pingSessionHeadLess.tsx ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PingSessionHeadLess: () => (/* binding */ PingSessionHeadLess)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/providers/user */ \"(ssr)/./src/providers/user.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_platform__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/utils/platform */ \"(ssr)/./src/utils/platform.ts\");\n/* harmony import */ var _api_account__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/api/account */ \"(ssr)/./src/api/account.ts\");\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/api/auth */ \"(ssr)/./src/api/auth.ts\");\n\n\n\n\n\n\nconst PingSessionHeadLess = ()=>{\n    const { token, isAuthenticated } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_1__.useAuth)();\n    const pingInterval = _api_auth__WEBPACK_IMPORTED_MODULE_5__.__VARS__.__SESSION_PING_TIMEOUT_MS;\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        let pingTimer;\n        const ping = async ()=>{\n            const client = (0,_utils_platform__WEBPACK_IMPORTED_MODULE_3__.browserProps)();\n            if (!client || !token) return;\n            const name = `${client.browser.name} on ${client.os.name}`;\n            console.log(\"Ping: \", {\n                client,\n                name\n            });\n            const res = await (0,_api_account__WEBPACK_IMPORTED_MODULE_4__.pingSession)(token.token, {\n                client,\n                name\n            });\n            if (res.error) {\n                // Handle errors if needed\n                console.error(\"Ping session error:\", res.error);\n            } else {\n                (0,_api_auth__WEBPACK_IMPORTED_MODULE_5__.saveLastSessionPing)();\n            }\n        };\n        const lastPingTS = (0,_api_auth__WEBPACK_IMPORTED_MODULE_5__.getSessionLastPing)();\n        const timeSinceLastPing = Date.now() - lastPingTS;\n        if (isAuthenticated() && timeSinceLastPing > 120000) {\n            ping().then(); // Send ping immediately\n            pingTimer = setInterval(ping, pingInterval); // Schedule periodic pings\n        }\n        return ()=>{\n            if (pingTimer) clearInterval(pingTimer); // Clear the interval when component unmounts\n        };\n    }, [\n        isAuthenticated,\n        pingInterval,\n        token\n    ]);\n    // empty component\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/authentication/pingSessionHeadLess.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/landing/registerReferral.tsx":
/*!*****************************************************!*\
  !*** ./src/components/landing/registerReferral.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RegisterReferral: () => (/* binding */ RegisterReferral)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _api_affiliate__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/affiliate */ \"(ssr)/./src/api/affiliate.ts\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/alert */ \"(ssr)/./src/providers/alert.tsx\");\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/api/auth */ \"(ssr)/./src/api/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ RegisterReferral auto */ \n\n\n\n\n\nconst RegisterReferral = ()=>{\n    const path = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const refCode = searchParams.get(\"refCode\");\n    const { choice } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_4__.useAlert)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    const registerClick = async ()=>{\n        if (!refCode) return;\n        const res = await (0,_api_affiliate__WEBPACK_IMPORTED_MODULE_3__.registerReferral)(refCode);\n        if (res.error) {\n            return;\n        }\n        // setOffer(res.data.data.offer)\n        const offer = res.data.data.offer;\n        (0,_api_auth__WEBPACK_IMPORTED_MODULE_5__.saveReferralCode)(refCode);\n        if (path === \"/\") {\n            const btn = {\n                label: \"Claim Offer\",\n                onClick: ()=>{\n                    router.push(\"/auth/sign-in\");\n                }\n            };\n            choice(\"You have been invited to try Opendashboard!\", `Sign and get ${offer.referralDiscountPercent}% on your subscription and credit purchase for the next ${offer.referralExpiryMonths} months.`, [\n                btn\n            ]);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (refCode) {\n            registerClick().then();\n        }\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {}, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy9sYW5kaW5nL3JlZ2lzdGVyUmVmZXJyYWwudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFHd0U7QUFDeEM7QUFDaUI7QUFDUTtBQUNiO0FBRXJDLE1BQU1PLG1CQUFtQjtJQUM1QixNQUFNQyxPQUFPUiw0REFBV0E7SUFDeEIsTUFBTVMsZUFBZVAsZ0VBQWVBO0lBQ3BDLE1BQU1RLFVBQVVELGFBQWFFLEdBQUcsQ0FBQztJQUVqQyxNQUFNLEVBQUNDLE1BQU0sRUFBQyxHQUFHUCwwREFBUUE7SUFDekIsTUFBTVEsU0FBU1osMERBQVNBO0lBRXhCLE1BQU1hLGdCQUFnQjtRQUNsQixJQUFJLENBQUNKLFNBQVM7UUFDZCxNQUFNSyxNQUFNLE1BQU1YLGdFQUFnQkEsQ0FBQ007UUFDbkMsSUFBSUssSUFBSUMsS0FBSyxFQUFFO1lBQ1g7UUFDSjtRQUNBLGdDQUFnQztRQUVoQyxNQUFNQyxRQUFRRixJQUFJRyxJQUFJLENBQUNBLElBQUksQ0FBQ0QsS0FBSztRQUVqQ1gsMkRBQWdCQSxDQUFDSTtRQUVqQixJQUFJRixTQUFTLEtBQUs7WUFDZCxNQUFNVyxNQUFvQjtnQkFDdEJDLE9BQU87Z0JBQ1BDLFNBQVM7b0JBQ0xSLE9BQU9TLElBQUksQ0FBQztnQkFDaEI7WUFDSjtZQUNBVixPQUNJLCtDQUNBLENBQUMsYUFBYSxFQUFFSyxNQUFNTSx1QkFBdUIsQ0FBQyx3REFBd0QsRUFBRU4sTUFBTU8sb0JBQW9CLENBQUMsUUFBUSxDQUFDLEVBQzVJO2dCQUFDTDthQUFJO1FBRWI7SUFDSjtJQUVBaEIsZ0RBQVNBLENBQUM7UUFDTixJQUFJTyxTQUFTO1lBQ1RJLGdCQUFnQlcsSUFBSTtRQUN4QjtJQUNKLEdBQUcsRUFBRTtJQUVMLHFCQUFPO0FBQ1gsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy9sYW5kaW5nL3JlZ2lzdGVyUmVmZXJyYWwudHN4PzI5OWIiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuXG5pbXBvcnQge3VzZVBhdGhuYW1lLCB1c2VSb3V0ZXIsIHVzZVNlYXJjaFBhcmFtc30gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xuaW1wb3J0IHt1c2VFZmZlY3R9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHtyZWdpc3RlclJlZmVycmFsfSBmcm9tIFwiQC9hcGkvYWZmaWxpYXRlXCI7XG5pbXBvcnQge0lBbGVydENob2ljZSwgdXNlQWxlcnR9IGZyb20gXCJAL3Byb3ZpZGVycy9hbGVydFwiO1xuaW1wb3J0IHtzYXZlUmVmZXJyYWxDb2RlfSBmcm9tIFwiQC9hcGkvYXV0aFwiO1xuXG5leHBvcnQgY29uc3QgUmVnaXN0ZXJSZWZlcnJhbCA9ICgpID0+IHtcbiAgICBjb25zdCBwYXRoID0gdXNlUGF0aG5hbWUoKVxuICAgIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpXG4gICAgY29uc3QgcmVmQ29kZSA9IHNlYXJjaFBhcmFtcy5nZXQoXCJyZWZDb2RlXCIpXG5cbiAgICBjb25zdCB7Y2hvaWNlfSA9IHVzZUFsZXJ0KClcbiAgICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuXG4gICAgY29uc3QgcmVnaXN0ZXJDbGljayA9IGFzeW5jICgpID0+IHtcbiAgICAgICAgaWYgKCFyZWZDb2RlKSByZXR1cm5cbiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgcmVnaXN0ZXJSZWZlcnJhbChyZWZDb2RlKVxuICAgICAgICBpZiAocmVzLmVycm9yKSB7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgLy8gc2V0T2ZmZXIocmVzLmRhdGEuZGF0YS5vZmZlcilcblxuICAgICAgICBjb25zdCBvZmZlciA9IHJlcy5kYXRhLmRhdGEub2ZmZXJcblxuICAgICAgICBzYXZlUmVmZXJyYWxDb2RlKHJlZkNvZGUpXG5cbiAgICAgICAgaWYgKHBhdGggPT09ICcvJykge1xuICAgICAgICAgICAgY29uc3QgYnRuOiBJQWxlcnRDaG9pY2UgPSB7XG4gICAgICAgICAgICAgICAgbGFiZWw6IFwiQ2xhaW0gT2ZmZXJcIixcbiAgICAgICAgICAgICAgICBvbkNsaWNrOiAoKSA9PiB7XG4gICAgICAgICAgICAgICAgICAgIHJvdXRlci5wdXNoKCcvYXV0aC9zaWduLWluJylcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjaG9pY2UoXG4gICAgICAgICAgICAgICAgXCJZb3UgaGF2ZSBiZWVuIGludml0ZWQgdG8gdHJ5IE9wZW5kYXNoYm9hcmQhXCIsXG4gICAgICAgICAgICAgICAgYFNpZ24gYW5kIGdldCAke29mZmVyLnJlZmVycmFsRGlzY291bnRQZXJjZW50fSUgb24geW91ciBzdWJzY3JpcHRpb24gYW5kIGNyZWRpdCBwdXJjaGFzZSBmb3IgdGhlIG5leHQgJHtvZmZlci5yZWZlcnJhbEV4cGlyeU1vbnRoc30gbW9udGhzLmAsXG4gICAgICAgICAgICAgICAgW2J0bl1cbiAgICAgICAgICAgIClcbiAgICAgICAgfVxuICAgIH1cblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIGlmIChyZWZDb2RlKSB7XG4gICAgICAgICAgICByZWdpc3RlckNsaWNrKCkudGhlbigpXG4gICAgICAgIH1cbiAgICB9LCBbXSk7XG5cbiAgICByZXR1cm4gPD48Lz5cbn1cbiJdLCJuYW1lcyI6WyJ1c2VQYXRobmFtZSIsInVzZVJvdXRlciIsInVzZVNlYXJjaFBhcmFtcyIsInVzZUVmZmVjdCIsInJlZ2lzdGVyUmVmZXJyYWwiLCJ1c2VBbGVydCIsInNhdmVSZWZlcnJhbENvZGUiLCJSZWdpc3RlclJlZmVycmFsIiwicGF0aCIsInNlYXJjaFBhcmFtcyIsInJlZkNvZGUiLCJnZXQiLCJjaG9pY2UiLCJyb3V0ZXIiLCJyZWdpc3RlckNsaWNrIiwicmVzIiwiZXJyb3IiLCJvZmZlciIsImRhdGEiLCJidG4iLCJsYWJlbCIsIm9uQ2xpY2siLCJwdXNoIiwicmVmZXJyYWxEaXNjb3VudFBlcmNlbnQiLCJyZWZlcnJhbEV4cGlyeU1vbnRocyIsInRoZW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/components/landing/registerReferral.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/tracking.tsx":
/*!*************************************!*\
  !*** ./src/components/tracking.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tracking: () => (/* binding */ Tracking)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_script__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/script */ \"(ssr)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* __next_internal_client_entry_do_not_use__ Tracking auto */ \n\n\nconst Tracking = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PWAServiceWorker, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\tracking.tsx\",\n                lineNumber: 8,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MicrosoftClarity, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\tracking.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GoogleAnalytics, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\tracking.tsx\",\n                lineNumber: 10,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(HotJar, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\tracking.tsx\",\n                lineNumber: 11,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst MicrosoftClarity = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        id: \"microsoft-clarity-init\",\n        strategy: \"afterInteractive\",\n        dangerouslySetInnerHTML: {\n            __html: `\n                (function(c,l,a,r,i,t,y){\n                    c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};\n                    t=l.createElement(r);t.async=1;t.src=\"https://www.clarity.ms/tag/\"+i;\n                    y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);\n                })(window, document, \"clarity\", \"script\", \"${process.env.NEXT_PUBLIC_MICROSOFT_CLARITY}\");\n                `\n        }\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\tracking.tsx\",\n        lineNumber: 17,\n        columnNumber: 9\n    }, undefined);\n};\nconst GoogleAnalytics = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                strategy: \"afterInteractive\",\n                src: `https://www.googletagmanager.com/gtag/js?id=${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS}`\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\tracking.tsx\",\n                lineNumber: 36,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                id: \"google-analytics-init\",\n                strategy: \"afterInteractive\",\n                dangerouslySetInnerHTML: {\n                    __html: `\n                    window.dataLayer = window.dataLayer || [];\n                    function gtag(){dataLayer.push(arguments);}\n                    gtag('js', new Date());\n                    gtag('config', '${process.env.NEXT_PUBLIC_GOOGLE_ANALYTICS}');\n                    `\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\tracking.tsx\",\n                lineNumber: 39,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\nconst HotJar = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_script__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            id: \"hotjar-init\",\n            strategy: \"afterInteractive\",\n            dangerouslySetInnerHTML: {\n                __html: `\n                    (function(h,o,t,j,a,r){\n                        h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};\n                        h._hjSettings={hjid:${process.env.NEXT_PUBLIC_HOTJAR || 0},hjsv:6};\n                        a=o.getElementsByTagName('head')[0];\n                        r=o.createElement('script');r.async=1;\n                        r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;\n                        a.appendChild(r);\n                    })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');\n                    `\n            }\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\tracking.tsx\",\n            lineNumber: 59,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false);\n};\nconst PWAServiceWorker = ()=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (\"serviceWorker\" in navigator) {\n            navigator.serviceWorker.register(\"/sw.js\").then(function() {\n                console.log(\"✅ Service Worker Registered\");\n            }).catch(function(error) {\n                console.error(\"❌ Service Worker Registration Failed:\", error);\n            });\n        }\n    }, []);\n    return null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/tracking.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n            outline: \"border border-neutral-300 border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-8 px-4 py-2\",\n            sm: \"h-7 rounded-md px-3 text-xs\",\n            lg: \"h-10 rounded-md px-8\",\n            icon: \"h-8 w-8\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 47,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7QUFBOEI7QUFDYTtBQUNzQjtBQUVqQztBQUVoQyxNQUFNSSxpQkFBaUJGLDZEQUFHQSxDQUN4Qix1T0FDQTtJQUNFRyxVQUFVO1FBQ1JDLFNBQVM7WUFDUEMsU0FDRTtZQUNGQyxhQUNFO1lBQ0ZDLFNBQ0U7WUFDRkMsV0FDRTtZQUNGQyxPQUFPO1lBQ1BDLE1BQU07UUFDUjtRQUNBQyxNQUFNO1lBQ0pOLFNBQVM7WUFDVE8sSUFBSTtZQUNKQyxJQUFJO1lBQ0pDLE1BQU07UUFDUjtJQUNGO0lBQ0FDLGlCQUFpQjtRQUNmWCxTQUFTO1FBQ1RPLE1BQU07SUFDUjtBQUNGO0FBU0YsTUFBTUssdUJBQVNsQiw2Q0FBZ0IsQ0FDN0IsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFZCxPQUFPLEVBQUVPLElBQUksRUFBRVEsVUFBVSxLQUFLLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUN4RCxNQUFNQyxPQUFPSCxVQUFVcEIsc0RBQUlBLEdBQUc7SUFDOUIscUJBQ0UsOERBQUN1QjtRQUNDSixXQUFXakIsOENBQUVBLENBQUNDLGVBQWU7WUFBRUU7WUFBU087WUFBTU87UUFBVTtRQUN4REcsS0FBS0E7UUFDSixHQUFHRCxLQUFLOzs7Ozs7QUFHZjtBQUVGSixPQUFPTyxXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4PzZhMGEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFNsb3QgfSBmcm9tIFwiQHJhZGl4LXVpL3JlYWN0LXNsb3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXG5cbmNvbnN0IGJ1dHRvblZhcmlhbnRzID0gY3ZhKFxuICBcImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB3aGl0ZXNwYWNlLW5vd3JhcCByb3VuZGVkLW1kIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXMtdmlzaWJsZTpvdXRsaW5lLW5vbmUgZm9jdXMtdmlzaWJsZTpyaW5nLTEgZm9jdXMtdmlzaWJsZTpyaW5nLXJpbmcgZGlzYWJsZWQ6cG9pbnRlci1ldmVudHMtbm9uZSBkaXNhYmxlZDpvcGFjaXR5LTUwXCIsXG4gIHtcbiAgICB2YXJpYW50czoge1xuICAgICAgdmFyaWFudDoge1xuICAgICAgICBkZWZhdWx0OlxuICAgICAgICAgIFwiYmctcHJpbWFyeSB0ZXh0LXByaW1hcnktZm9yZWdyb3VuZCBzaGFkb3cgaG92ZXI6YmctcHJpbWFyeS85MFwiLFxuICAgICAgICBkZXN0cnVjdGl2ZTpcbiAgICAgICAgICBcImJnLWRlc3RydWN0aXZlIHRleHQtZGVzdHJ1Y3RpdmUtZm9yZWdyb3VuZCBzaGFkb3ctc20gaG92ZXI6YmctZGVzdHJ1Y3RpdmUvOTBcIixcbiAgICAgICAgb3V0bGluZTpcbiAgICAgICAgICBcImJvcmRlciBib3JkZXItbmV1dHJhbC0zMDAgYm9yZGVyLWlucHV0IGJnLWJhY2tncm91bmQgc2hhZG93LXNtIGhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIHNlY29uZGFyeTpcbiAgICAgICAgICBcImJnLXNlY29uZGFyeSB0ZXh0LXNlY29uZGFyeS1mb3JlZ3JvdW5kIHNoYWRvdy1zbSBob3ZlcjpiZy1zZWNvbmRhcnkvODBcIixcbiAgICAgICAgZ2hvc3Q6IFwiaG92ZXI6YmctYWNjZW50IGhvdmVyOnRleHQtYWNjZW50LWZvcmVncm91bmRcIixcbiAgICAgICAgbGluazogXCJ0ZXh0LXByaW1hcnkgdW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZVwiLFxuICAgICAgfSxcbiAgICAgIHNpemU6IHtcbiAgICAgICAgZGVmYXVsdDogXCJoLTggcHgtNCBweS0yXCIsXG4gICAgICAgIHNtOiBcImgtNyByb3VuZGVkLW1kIHB4LTMgdGV4dC14c1wiLFxuICAgICAgICBsZzogXCJoLTEwIHJvdW5kZWQtbWQgcHgtOFwiLFxuICAgICAgICBpY29uOiBcImgtOCB3LThcIixcbiAgICAgIH0sXG4gICAgfSxcbiAgICBkZWZhdWx0VmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6IFwiZGVmYXVsdFwiLFxuICAgICAgc2l6ZTogXCJkZWZhdWx0XCIsXG4gICAgfSxcbiAgfVxuKVxuXG5leHBvcnQgaW50ZXJmYWNlIEJ1dHRvblByb3BzXG4gIGV4dGVuZHMgUmVhY3QuQnV0dG9uSFRNTEF0dHJpYnV0ZXM8SFRNTEJ1dHRvbkVsZW1lbnQ+LFxuICAgIFZhcmlhbnRQcm9wczx0eXBlb2YgYnV0dG9uVmFyaWFudHM+IHtcbiAgYXNDaGlsZD86IGJvb2xlYW5cbn1cblxuY29uc3QgQnV0dG9uID0gUmVhY3QuZm9yd2FyZFJlZjxIVE1MQnV0dG9uRWxlbWVudCwgQnV0dG9uUHJvcHM+KFxuICAoeyBjbGFzc05hbWUsIHZhcmlhbnQsIHNpemUsIGFzQ2hpbGQgPSBmYWxzZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgY29uc3QgQ29tcCA9IGFzQ2hpbGQgPyBTbG90IDogXCJidXR0b25cIlxuICAgIHJldHVybiAoXG4gICAgICA8Q29tcFxuICAgICAgICBjbGFzc05hbWU9e2NuKGJ1dHRvblZhcmlhbnRzKHsgdmFyaWFudCwgc2l6ZSwgY2xhc3NOYW1lIH0pKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIHsuLi5wcm9wc31cbiAgICAgIC8+XG4gICAgKVxuICB9XG4pXG5CdXR0b24uZGlzcGxheU5hbWUgPSBcIkJ1dHRvblwiXG5cbmV4cG9ydCB7IEJ1dHRvbiwgYnV0dG9uVmFyaWFudHMgfVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiU2xvdCIsImN2YSIsImNuIiwiYnV0dG9uVmFyaWFudHMiLCJ2YXJpYW50cyIsInZhcmlhbnQiLCJkZWZhdWx0IiwiZGVzdHJ1Y3RpdmUiLCJvdXRsaW5lIiwic2Vjb25kYXJ5IiwiZ2hvc3QiLCJsaW5rIiwic2l6ZSIsInNtIiwibGciLCJpY29uIiwiZGVmYXVsdFZhcmlhbnRzIiwiQnV0dG9uIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsImFzQ2hpbGQiLCJwcm9wcyIsInJlZiIsIkNvbXAiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/dialog.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/dialog.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Dialog: () => (/* binding */ Dialog),\n/* harmony export */   DialogClose: () => (/* binding */ DialogClose),\n/* harmony export */   DialogContent: () => (/* binding */ DialogContent),\n/* harmony export */   DialogDescription: () => (/* binding */ DialogDescription),\n/* harmony export */   DialogFooter: () => (/* binding */ DialogFooter),\n/* harmony export */   DialogHeader: () => (/* binding */ DialogHeader),\n/* harmony export */   DialogOverlay: () => (/* binding */ DialogOverlay),\n/* harmony export */   DialogPortal: () => (/* binding */ DialogPortal),\n/* harmony export */   DialogTitle: () => (/* binding */ DialogTitle),\n/* harmony export */   DialogTrigger: () => (/* binding */ DialogTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dialog */ \"(ssr)/./node_modules/@radix-ui/react-dialog/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-icons */ \"(ssr)/./node_modules/@radix-ui/react-icons/dist/react-icons.esm.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Dialog,DialogPortal,DialogOverlay,DialogTrigger,DialogClose,DialogContent,DialogHeader,DialogFooter,DialogTitle,DialogDescription auto */ \n\n\n\n\nconst Dialog = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DialogTrigger = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DialogPortal = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DialogClose = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close;\nconst DialogOverlay = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 21,\n        columnNumber: 3\n    }, undefined));\nDialogOverlay.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Overlay.displayName;\nconst DialogContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, hideCloseBtn, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogPortal, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DialogOverlay, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                ref: ref,\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\", className),\n                ...props,\n                children: [\n                    children,\n                    !hideCloseBtn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Close, {\n                        className: \"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_icons__WEBPACK_IMPORTED_MODULE_4__.Cross2Icon, {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"sr-only\",\n                                children: \"Close\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 27\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n                lineNumber: 38,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nDialogContent.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DialogHeader = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 text-center sm:text-left\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 61,\n        columnNumber: 3\n    }, undefined);\nDialogHeader.displayName = \"DialogHeader\";\nconst DialogFooter = ({ className, ...props })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 75,\n        columnNumber: 3\n    }, undefined);\nDialogFooter.displayName = \"DialogFooter\";\nconst DialogTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-lg font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 89,\n        columnNumber: 3\n    }, undefined));\nDialogTitle.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Title.displayName;\nconst DialogDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\dialog.tsx\",\n        lineNumber: 104,\n        columnNumber: 3\n    }, undefined));\nDialogDescription.displayName = _radix_ui_react_dialog__WEBPACK_IMPORTED_MODULE_3__.Description.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/dialog.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/ui/sonner.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/sonner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Toaster auto */ \n\n\nconst Toaster = ({ ...props })=>{\n    const { theme = \"system\" } = (0,next_themes__WEBPACK_IMPORTED_MODULE_1__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(sonner__WEBPACK_IMPORTED_MODULE_2__.Toaster, {\n        theme: theme,\n        className: \"toaster group\",\n        toastOptions: {\n            classNames: {\n                toast: \"group toast group-[.toaster]:bg-background group-[.toaster]:text-foreground group-[.toaster]:border-border group-[.toaster]:shadow-lg\",\n                description: \"group-[.toast]:text-muted-foreground\",\n                actionButton: \"group-[.toast]:bg-primary group-[.toast]:text-primary-foreground\",\n                cancelButton: \"group-[.toast]:bg-muted group-[.toast]:text-muted-foreground\"\n            }\n        },\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\ui\\\\sonner.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ui/sonner.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   isDesktop: () => (/* binding */ isDesktop)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction isDesktop() {\n    return  false && 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEM7QUFDSjtBQUUvQixTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEI7QUFFTyxTQUFTQztJQUNkLE9BQU8sTUFBa0IsSUFBZUMsQ0FBeUI7QUFDbkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dHlwZSBDbGFzc1ZhbHVlLCBjbHN4fSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQge3R3TWVyZ2V9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0Rlc2t0b3AoKTogYm9vbGVhbiB7XG4gIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cuaW5uZXJXaWR0aCA+PSAxMDI0O1xufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJpc0Rlc2t0b3AiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(ssr)/./src/providers/alert.tsx":
/*!*********************************!*\
  !*** ./src/providers/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertProvider: () => (/* binding */ AlertProvider),\n/* harmony export */   AlertProviderContext: () => (/* binding */ AlertProviderContext),\n/* harmony export */   useAlert: () => (/* binding */ useAlert)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_confirm_alert__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-confirm-alert */ \"(ssr)/./node_modules/react-confirm-alert/lib/index.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sonner */ \"(ssr)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dialog */ \"(ssr)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./src/components/ui/button.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ AlertProviderContext,AlertProvider,useAlert auto */ \n\n\n\n\n\n\nconst AlertProviderContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().createContext(null);\nconst AlertProvider = (props)=>{\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const showAlert = (title, message, buttons, options)=>{\n        (0,react_confirm_alert__WEBPACK_IMPORTED_MODULE_2__.confirmAlert)({\n            title: title,\n            message: \"\",\n            buttons: buttons,\n            overlayClassName: \"\",\n            customUI: (options)=>{\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.Dialog, {\n                        open: true,\n                        onOpenChange: ()=>options.onClose(),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogContent, {\n                            className: \"max-w-[600px] !rounded-none p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogTitle, {\n                                        className: \"font-bold\",\n                                        children: options.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 py-2 text-xs font-medium\",\n                                        children: message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 33\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_4__.DialogFooter, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-row-reverse gap-1\",\n                                        children: buttons.map((b, i)=>{\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                className: `text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1 ${b.variant === \"danger\" ? \"bg-red-600\" : \"\"}`,\n                                                variant: b.variant === \"ghost\" ? \"ghost\" : undefined,\n                                                onClick: (e)=>{\n                                                    if (b.onClick) {\n                                                        const result = b.onClick();\n                                                        if (result === false) {\n                                                            return;\n                                                        }\n                                                    }\n                                                    options.onClose();\n                                                },\n                                                children: b.label\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                                                lineNumber: 79,\n                                                columnNumber: 49\n                                            }, undefined);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 37\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 33\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                            lineNumber: 66,\n                            columnNumber: 29\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false);\n            },\n            ...options ? options : {}\n        });\n    };\n    const data = {\n        alert: (title, message, onDismiss, options)=>{\n            const button = {\n                label: \"Ok\",\n                onClick: ()=>onDismiss ? onDismiss() : undefined\n            };\n            showAlert(title, message, [\n                button\n            ], options);\n        },\n        choice: (title, message, choices, options)=>{\n            showAlert(title, message, choices, options);\n        },\n        promptUpgrade: (message, domain)=>{\n            let title = `Upgrade Needed!`;\n            const button = {\n                label: \"Cancel\",\n                variant: \"ghost\",\n                onClick: ()=>undefined\n            };\n            const onConfirm = ()=>{\n                router.push(`/${domain}/settings/plans`);\n            };\n            const confirmBtn = {\n                label: \"Upgrade\",\n                onClick: onConfirm\n            };\n            showAlert(title, message, [\n                confirmBtn,\n                button\n            ]);\n        },\n        confirm: (title, message, onConfirm, onCancel, options, isDangerAction)=>{\n            const button = {\n                label: \"Cancel\",\n                variant: \"ghost\",\n                onClick: ()=>onCancel ? onCancel() : undefined\n            };\n            const confirmBtn = {\n                label: \"Confirm\",\n                onClick: onConfirm\n            };\n            if (isDangerAction) confirmBtn.variant = \"danger\";\n            showAlert(title, message, [\n                confirmBtn,\n                button\n            ], options);\n        },\n        toast: sonner__WEBPACK_IMPORTED_MODULE_3__.toast\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AlertProviderContext.Provider, {\n            value: data,\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\alert.tsx\",\n            lineNumber: 163,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false);\n};\nfunction useAlert() {\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AlertProviderContext);\n} // export const showUpgradeAlert = (confirm: IAlertProviderContext['confirm'], message: string, domain:string) => {\n //     let title = `Upgrade Needed!`\n //\n //     const cb = async () => {\n //        router\n //         return\n //     }\n //\n //     confirm(title, message, cb, undefined, undefined, true)\n // }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/alert.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/broadcast.tsx":
/*!*************************************!*\
  !*** ./src/providers/broadcast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BroadcastNamespaces: () => (/* binding */ BroadcastNamespaces),\n/* harmony export */   BroadcastProvider: () => (/* binding */ BroadcastProvider),\n/* harmony export */   useBroadcast: () => (/* binding */ useBroadcast)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ BroadcastNamespaces,BroadcastProvider,useBroadcast auto */ \n// BroadcastContext.tsx\n\nvar BroadcastNamespaces;\n(function(BroadcastNamespaces) {\n    BroadcastNamespaces[\"DatabaseTableView\"] = \"database-table-view\";\n    BroadcastNamespaces[\"Workspace\"] = \"workspace\";\n})(BroadcastNamespaces || (BroadcastNamespaces = {}));\nconst BroadcastContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst BroadcastProvider = ({ children })=>{\n    const listenersRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    const sendMessage = (namespace, key, data)=>{\n        const keyListeners = listenersRef.current.get(namespace)?.get(key);\n        if (keyListeners) {\n            keyListeners.forEach((callback)=>callback(data));\n        }\n    };\n    const registerListener = (namespace, key, callback)=>{\n        if (!listenersRef.current.has(namespace)) {\n            listenersRef.current.set(namespace, new Map());\n        }\n        const namespaceMap = listenersRef.current.get(namespace);\n        if (!namespaceMap.has(key)) {\n            namespaceMap.set(key, []);\n        }\n        namespaceMap.get(key).push(callback);\n        return ()=>{\n            const listeners = namespaceMap.get(key);\n            if (listeners) {\n                namespaceMap.set(key, listeners.filter((cb)=>cb !== callback));\n            }\n        };\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BroadcastContext.Provider, {\n        value: {\n            sendMessage,\n            registerListener\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\broadcast.tsx\",\n        lineNumber: 54,\n        columnNumber: 9\n    }, undefined);\n};\nconst useBroadcast = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(BroadcastContext);\n    if (!context) {\n        throw new Error(\"useBroadcast must be used within a BroadcastProvider\");\n    }\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/broadcast.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/preview.tsx":
/*!***********************************!*\
  !*** ./src/providers/preview.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PreviewProvider: () => (/* binding */ PreviewProvider),\n/* harmony export */   usePreview: () => (/* binding */ usePreview)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react_photo_view__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-photo-view */ \"(ssr)/./node_modules/react-photo-view/dist/react-photo-view.module.js\");\n/* __next_internal_client_entry_do_not_use__ PreviewProvider,usePreview auto */ \n\n\nconst PreviewProvider = (props)=>{\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const previewFiles = (files, index = 0)=>{\n        setIndex(index);\n        setFiles(files);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(PreviewContext.Provider, {\n        value: {\n            previewFiles\n        },\n        children: [\n            props.children,\n            files && files.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"fixed top-0 right-0 bottom-0 left-0 z-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_photo_view__WEBPACK_IMPORTED_MODULE_2__.PhotoSlider, {\n                        images: files.map((item)=>({\n                                src: item.link,\n                                key: item.id\n                            })),\n                        onClose: ()=>setFiles(undefined),\n                        visible: true,\n                        index: index,\n                        onIndexChange: setIndex\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\preview.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\preview.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\preview.tsx\",\n        lineNumber: 20,\n        columnNumber: 12\n    }, undefined);\n};\nconst PreviewContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst usePreview = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PreviewContext);\n    if (!context) throw new Error(\"usePreview must be used within a PreviewProvider\");\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/preview.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/screenSize.tsx":
/*!**************************************!*\
  !*** ./src/providers/screenSize.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScreenSizeProvider: () => (/* binding */ ScreenSizeProvider),\n/* harmony export */   useScreenSize: () => (/* binding */ useScreenSize)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useScreenSize,ScreenSizeProvider auto */ \n\nconst ScreenSizeContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n    isMobile: false,\n    isCollapsed: false,\n    setCollapsed: (b)=>{}\n});\nconst useScreenSize = ()=>(0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ScreenSizeContext);\nconst ScreenSizeProvider = ({ children })=>{\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)( false && 0);\n    const [isCollapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleResize = ()=>{\n            setIsMobile(window.innerWidth < 1024);\n        };\n        window.addEventListener(\"resize\", handleResize);\n        handleResize();\n        return ()=>{\n            window.removeEventListener(\"resize\", handleResize);\n        };\n    }, []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ScreenSizeContext.Provider, {\n        value: {\n            isMobile,\n            isCollapsed,\n            setCollapsed\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\screenSize.tsx\",\n        lineNumber: 37,\n        columnNumber: 9\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/screenSize.tsx\n");

/***/ }),

/***/ "(ssr)/./src/providers/user.tsx":
/*!********************************!*\
  !*** ./src/providers/user.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserProvider: () => (/* binding */ UserProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/api/auth */ \"(ssr)/./src/api/auth.ts\");\n/* harmony import */ var _api_account__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/api/account */ \"(ssr)/./src/api/account.ts\");\n/* harmony import */ var _api_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/api/common */ \"(ssr)/./src/api/common.ts\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/api/workspace */ \"(ssr)/./src/api/workspace.ts\");\n/* harmony import */ var _components_authentication_pingSessionHeadLess__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/authentication/pingSessionHeadLess */ \"(ssr)/./src/components/authentication/pingSessionHeadLess.tsx\");\n/* __next_internal_client_entry_do_not_use__ UserProvider,useAuth auto */ \n\n\n\n\n\n\nconst UserProvider = (props)=>{\n    const firstLoadRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(true);\n    const loadingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [token, setToken] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [workspaces, setWorkspaces] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(undefined);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const login = (user, token, callback)=>{\n        initContext(token).then((r)=>{\n            (0,_api_auth__WEBPACK_IMPORTED_MODULE_2__.saveToken)(token);\n            callback?.();\n        });\n    };\n    const logout = async ()=>{\n        setToken(undefined);\n        setWorkspaces(undefined);\n        setUser(undefined);\n        (0,_api_auth__WEBPACK_IMPORTED_MODULE_2__.clearToken)();\n    };\n    const updateUser = (update)=>{\n        const updated = {\n            ...user,\n            ...update\n        };\n        setUser(updated);\n    };\n    const initContext = async (token)=>{\n        const r1 = await (0,_api_account__WEBPACK_IMPORTED_MODULE_3__.getAccount)(token.token);\n        const user = r1.data?.data?.user;\n        if (!user) {\n            setError(r1.error || _api_common__WEBPACK_IMPORTED_MODULE_4__.defaultAPIMessage);\n            return;\n        }\n        const r2 = await (0,_api_workspace__WEBPACK_IMPORTED_MODULE_5__.getWorkspaces)(token.token);\n        const workspaces = r2.data?.data?.workspaces;\n        if (!workspaces) {\n            setError(r2.error || _api_common__WEBPACK_IMPORTED_MODULE_4__.defaultAPIMessage);\n            return;\n        }\n        setUser(user);\n        setWorkspaces(workspaces);\n        setToken(token);\n        setError(\"\");\n        return {\n            user,\n            workspaces\n        };\n    };\n    const isAuthenticated = ()=>!!(user && workspaces && token);\n    const reloadContext = async ()=>{\n        if (loadingRef.current) return;\n        loadingRef.current = true;\n        const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_2__.getToken)();\n        if (!token) {\n            // clearToken()\n            // setToken(undefined)\n            // setUser(undefined)\n            // setWorkspaces(undefined)\n            loadingRef.current = false;\n            setError(\"Token not available..\");\n            return;\n        }\n        const res = await initContext(token);\n        loadingRef.current = false;\n    };\n    const addWorkspace = async (workspace)=>{\n        if (!workspaces) return;\n        setWorkspaces([\n            ...workspaces,\n            workspace\n        ]);\n    };\n    const removeWorkspace = async (workspace)=>{\n        if (!workspaces) return;\n        setWorkspaces([\n            ...workspaces\n        ].filter((w)=>w.workspace.id !== workspace.workspace.id));\n    };\n    const updateWorkspaces = (update)=>{\n        setWorkspaces([\n            ...update\n        ]);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        reloadContext().then().finally();\n        firstLoadRef.current = false;\n        return;\n    }, []);\n    const isLoading = loadingRef.current;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(UserContext.Provider, {\n        value: {\n            isAuthenticated,\n            login,\n            logout,\n            workspaces,\n            user,\n            token,\n            isLoading,\n            error,\n            updateUser,\n            addWorkspace,\n            updateWorkspaces,\n            removeWorkspace\n        },\n        children: [\n            props.children,\n            isAuthenticated() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_authentication_pingSessionHeadLess__WEBPACK_IMPORTED_MODULE_6__.PingSessionHeadLess, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\user.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 33\n                }, undefined)\n            }, void 0, false)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\user.tsx\",\n        lineNumber: 110,\n        columnNumber: 12\n    }, undefined);\n};\nconst UserContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nconst useAuth = ()=>{\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(UserContext);\n    if (!context) throw new Error(\"useAuth must be used within a UserProvider\");\n    return context;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvcHJvdmlkZXJzL3VzZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7OztBQUdnRztBQUVyQztBQUVsQjtBQUNNO0FBQ0Q7QUFDc0M7QUFNN0UsTUFBTVksZUFBZSxDQUFDQztJQUN6QixNQUFNQyxlQUFlWCw2Q0FBTUEsQ0FBQztJQUM1QixNQUFNWSxhQUFhWiw2Q0FBTUEsQ0FBQztJQUUxQixNQUFNLENBQUNhLE1BQU1DLFFBQVEsR0FBR2IsK0NBQVFBLENBQW1CYztJQUNuRCxNQUFNLENBQUNDLE9BQU9DLFNBQVMsR0FBR2hCLCtDQUFRQSxDQUFvQmM7SUFDdEQsTUFBTSxDQUFDRyxZQUFZQyxjQUFjLEdBQUdsQiwrQ0FBUUEsQ0FBNEJjO0lBQ3hFLE1BQU0sQ0FBQ0ssT0FBT0MsU0FBUyxHQUFHcEIsK0NBQVFBLENBQVM7SUFFM0MsTUFBTXFCLFFBQVEsQ0FBQ1QsTUFBWUcsT0FBY087UUFDckNDLFlBQVlSLE9BQU9TLElBQUksQ0FBQ0MsQ0FBQUE7WUFDcEJ0QixvREFBU0EsQ0FBQ1k7WUFDVk87UUFDSjtJQUNKO0lBRUEsTUFBTUksU0FBUztRQUNYVixTQUFTRjtRQUNUSSxjQUFjSjtRQUNkRCxRQUFRQztRQUVSYixxREFBVUE7SUFDZDtJQUVBLE1BQU0wQixhQUFhLENBQUNDO1FBQ2hCLE1BQU1DLFVBQWdCO1lBQUMsR0FBR2pCLElBQUk7WUFBRSxHQUFHZ0IsTUFBTTtRQUFBO1FBQ3pDZixRQUFRZ0I7SUFDWjtJQUVBLE1BQU1OLGNBQWMsT0FBT1I7UUFDdkIsTUFBTWUsS0FBSyxNQUFNMUIsd0RBQVVBLENBQUNXLE1BQU1BLEtBQUs7UUFDdkMsTUFBTUgsT0FBYWtCLEdBQUdDLElBQUksRUFBRUEsTUFBTW5CO1FBQ2xDLElBQUksQ0FBQ0EsTUFBTTtZQUNQUSxTQUFTVSxHQUFHWCxLQUFLLElBQUlkLDBEQUFpQkE7WUFDdEM7UUFDSjtRQUNBLE1BQU0yQixLQUFLLE1BQU0xQiw2REFBYUEsQ0FBQ1MsTUFBTUEsS0FBSztRQUMxQyxNQUFNRSxhQUE0QmUsR0FBR0QsSUFBSSxFQUFFQSxNQUFNZDtRQUNqRCxJQUFJLENBQUNBLFlBQVk7WUFDYkcsU0FBU1ksR0FBR2IsS0FBSyxJQUFJZCwwREFBaUJBO1lBQ3RDO1FBQ0o7UUFDQVEsUUFBUUQ7UUFDUk0sY0FBY0Q7UUFDZEQsU0FBU0Q7UUFDVEssU0FBUztRQUVULE9BQU87WUFDSFI7WUFBTUs7UUFDVjtJQUNKO0lBRUEsTUFBTWdCLGtCQUFrQixJQUFNLENBQUMsQ0FBRXJCLENBQUFBLFFBQVFLLGNBQWNGLEtBQUk7SUFFM0QsTUFBTW1CLGdCQUFnQjtRQUNsQixJQUFJdkIsV0FBV3dCLE9BQU8sRUFBRTtRQUN4QnhCLFdBQVd3QixPQUFPLEdBQUc7UUFDckIsTUFBTXBCLFFBQVFiLG1EQUFRQTtRQUV0QixJQUFJLENBQUNhLE9BQU87WUFDUixlQUFlO1lBQ2Ysc0JBQXNCO1lBQ3RCLHFCQUFxQjtZQUNyQiwyQkFBMkI7WUFDM0JKLFdBQVd3QixPQUFPLEdBQUc7WUFDckJmLFNBQVM7WUFDVDtRQUNKO1FBQ0EsTUFBTWdCLE1BQU0sTUFBTWIsWUFBWVI7UUFDOUJKLFdBQVd3QixPQUFPLEdBQUc7SUFDekI7SUFFQSxNQUFNRSxlQUFlLE9BQU9DO1FBQ3hCLElBQUksQ0FBQ3JCLFlBQVk7UUFDakJDLGNBQWM7ZUFBSUQ7WUFBWXFCO1NBQVU7SUFDNUM7SUFFQSxNQUFNQyxrQkFBa0IsT0FBT0Q7UUFDM0IsSUFBSSxDQUFDckIsWUFBWTtRQUNqQkMsY0FBYztlQUFJRDtTQUFXLENBQUN1QixNQUFNLENBQUNDLENBQUFBLElBQUtBLEVBQUVILFNBQVMsQ0FBQ0ksRUFBRSxLQUFLSixVQUFVQSxTQUFTLENBQUNJLEVBQUU7SUFDdkY7SUFFQSxNQUFNQyxtQkFBbUIsQ0FBQ2Y7UUFDdEJWLGNBQWM7ZUFBSVU7U0FBTztJQUM3QjtJQUVBOUIsZ0RBQVNBLENBQUM7UUFDTm9DLGdCQUFnQlYsSUFBSSxHQUFHb0IsT0FBTztRQUM5QmxDLGFBQWF5QixPQUFPLEdBQUc7UUFDdkI7SUFDSixHQUFHLEVBQUU7SUFFTCxNQUFNVSxZQUFZbEMsV0FBV3dCLE9BQU87SUFDcEMscUJBQU8sOERBQUNXLFlBQVlDLFFBQVE7UUFDeEJDLE9BQU87WUFDSGY7WUFDQVo7WUFDQUs7WUFDQVQ7WUFDQUw7WUFDQUc7WUFDQThCO1lBQ0ExQjtZQUNBUTtZQUNBVTtZQUNBTTtZQUNBSjtRQUNKOztZQUNDOUIsTUFBTXdDLFFBQVE7WUFDZGhCLG1DQUFxQjswQkFBRSw0RUFBQzFCLCtGQUFtQkE7Ozs7Ozs7Ozs7OztBQUVwRCxFQUFDO0FBaUJELE1BQU11Qyw0QkFBY2xELG9EQUFhQSxDQUM3QmtCO0FBR0csTUFBTW9DLFVBQVU7SUFDbkIsTUFBTUMsVUFBVXRELGlEQUFVQSxDQUFDaUQ7SUFDM0IsSUFBSSxDQUFDSyxTQUFTLE1BQU0sSUFBSUMsTUFBTTtJQUM5QixPQUFPRDtBQUNYLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vc3JjL3Byb3ZpZGVycy91c2VyLnRzeD8zYjlhIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cblxuaW1wb3J0IHtjcmVhdGVDb250ZXh0LCBQcm9wc1dpdGhDaGlsZHJlbiwgdXNlQ29udGV4dCwgdXNlRWZmZWN0LCB1c2VSZWYsIHVzZVN0YXRlfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7VG9rZW4sIFVzZXJ9IGZyb20gXCJAL3R5cGluZ3MvdXNlclwiO1xuaW1wb3J0IHtjbGVhclRva2VuLCBnZXRUb2tlbiwgc2F2ZVRva2VufSBmcm9tIFwiQC9hcGkvYXV0aFwiO1xuaW1wb3J0IHtNeVdvcmtzcGFjZX0gZnJvbSBcIkAvdHlwaW5ncy93b3Jrc3BhY2VcIjtcbmltcG9ydCB7Z2V0QWNjb3VudH0gZnJvbSBcIkAvYXBpL2FjY291bnRcIjtcbmltcG9ydCB7ZGVmYXVsdEFQSU1lc3NhZ2V9IGZyb20gXCJAL2FwaS9jb21tb25cIjtcbmltcG9ydCB7Z2V0V29ya3NwYWNlc30gZnJvbSBcIkAvYXBpL3dvcmtzcGFjZVwiO1xuaW1wb3J0IHtQaW5nU2Vzc2lvbkhlYWRMZXNzfSBmcm9tIFwiQC9jb21wb25lbnRzL2F1dGhlbnRpY2F0aW9uL3BpbmdTZXNzaW9uSGVhZExlc3NcIjtcblxuZXhwb3J0IGludGVyZmFjZSBVc2VyUHJvdmlkZXJQcm9wcyB7XG5cbn1cblxuZXhwb3J0IGNvbnN0IFVzZXJQcm92aWRlciA9IChwcm9wczogUHJvcHNXaXRoQ2hpbGRyZW48VXNlclByb3ZpZGVyUHJvcHM+KSA9PiB7XG4gICAgY29uc3QgZmlyc3RMb2FkUmVmID0gdXNlUmVmKHRydWUpO1xuICAgIGNvbnN0IGxvYWRpbmdSZWYgPSB1c2VSZWYoZmFsc2UpO1xuXG4gICAgY29uc3QgW3VzZXIsIHNldFVzZXJdID0gdXNlU3RhdGU8VXNlciB8IHVuZGVmaW5lZD4odW5kZWZpbmVkKTtcbiAgICBjb25zdCBbdG9rZW4sIHNldFRva2VuXSA9IHVzZVN0YXRlPFRva2VuIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuICAgIGNvbnN0IFt3b3Jrc3BhY2VzLCBzZXRXb3Jrc3BhY2VzXSA9IHVzZVN0YXRlPE15V29ya3NwYWNlW10gfCB1bmRlZmluZWQ+KHVuZGVmaW5lZCk7XG4gICAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTxzdHJpbmc+KFwiXCIpO1xuXG4gICAgY29uc3QgbG9naW4gPSAodXNlcjogVXNlciwgdG9rZW46IFRva2VuLCBjYWxsYmFjaz86ICgpID0+IHZvaWQpID0+IHtcbiAgICAgICAgaW5pdENvbnRleHQodG9rZW4pLnRoZW4ociA9PiB7XG4gICAgICAgICAgICBzYXZlVG9rZW4odG9rZW4pXG4gICAgICAgICAgICBjYWxsYmFjaz8uKClcbiAgICAgICAgfSlcbiAgICB9XG5cbiAgICBjb25zdCBsb2dvdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgICAgIHNldFRva2VuKHVuZGVmaW5lZClcbiAgICAgICAgc2V0V29ya3NwYWNlcyh1bmRlZmluZWQpXG4gICAgICAgIHNldFVzZXIodW5kZWZpbmVkKVxuXG4gICAgICAgIGNsZWFyVG9rZW4oKVxuICAgIH1cblxuICAgIGNvbnN0IHVwZGF0ZVVzZXIgPSAodXBkYXRlOiBQYXJ0aWFsPFVzZXI+KSA9PiB7XG4gICAgICAgIGNvbnN0IHVwZGF0ZWQ6IFVzZXIgPSB7Li4udXNlciwgLi4udXBkYXRlfSBhcyBVc2VyXG4gICAgICAgIHNldFVzZXIodXBkYXRlZClcbiAgICB9XG5cbiAgICBjb25zdCBpbml0Q29udGV4dCA9IGFzeW5jICh0b2tlbjogVG9rZW4pID0+IHtcbiAgICAgICAgY29uc3QgcjEgPSBhd2FpdCBnZXRBY2NvdW50KHRva2VuLnRva2VuKTtcbiAgICAgICAgY29uc3QgdXNlcjogVXNlciA9IHIxLmRhdGE/LmRhdGE/LnVzZXJcbiAgICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgICAgICBzZXRFcnJvcihyMS5lcnJvciB8fCBkZWZhdWx0QVBJTWVzc2FnZSlcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIGNvbnN0IHIyID0gYXdhaXQgZ2V0V29ya3NwYWNlcyh0b2tlbi50b2tlbilcbiAgICAgICAgY29uc3Qgd29ya3NwYWNlczogTXlXb3Jrc3BhY2VbXSA9IHIyLmRhdGE/LmRhdGE/LndvcmtzcGFjZXNcbiAgICAgICAgaWYgKCF3b3Jrc3BhY2VzKSB7XG4gICAgICAgICAgICBzZXRFcnJvcihyMi5lcnJvciB8fCBkZWZhdWx0QVBJTWVzc2FnZSlcbiAgICAgICAgICAgIHJldHVyblxuICAgICAgICB9XG4gICAgICAgIHNldFVzZXIodXNlcilcbiAgICAgICAgc2V0V29ya3NwYWNlcyh3b3Jrc3BhY2VzKVxuICAgICAgICBzZXRUb2tlbih0b2tlbilcbiAgICAgICAgc2V0RXJyb3IoXCJcIilcblxuICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgdXNlciwgd29ya3NwYWNlc1xuICAgICAgICB9XG4gICAgfVxuXG4gICAgY29uc3QgaXNBdXRoZW50aWNhdGVkID0gKCkgPT4gISEodXNlciAmJiB3b3Jrc3BhY2VzICYmIHRva2VuKTtcblxuICAgIGNvbnN0IHJlbG9hZENvbnRleHQgPSBhc3luYyAoKSA9PiB7XG4gICAgICAgIGlmIChsb2FkaW5nUmVmLmN1cnJlbnQpIHJldHVybjtcbiAgICAgICAgbG9hZGluZ1JlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICAgICAgY29uc3QgdG9rZW4gPSBnZXRUb2tlbigpO1xuXG4gICAgICAgIGlmICghdG9rZW4pIHtcbiAgICAgICAgICAgIC8vIGNsZWFyVG9rZW4oKVxuICAgICAgICAgICAgLy8gc2V0VG9rZW4odW5kZWZpbmVkKVxuICAgICAgICAgICAgLy8gc2V0VXNlcih1bmRlZmluZWQpXG4gICAgICAgICAgICAvLyBzZXRXb3Jrc3BhY2VzKHVuZGVmaW5lZClcbiAgICAgICAgICAgIGxvYWRpbmdSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgICAgICAgc2V0RXJyb3IoXCJUb2tlbiBub3QgYXZhaWxhYmxlLi5cIik7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgaW5pdENvbnRleHQodG9rZW4pO1xuICAgICAgICBsb2FkaW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICB9XG5cbiAgICBjb25zdCBhZGRXb3Jrc3BhY2UgPSBhc3luYyAod29ya3NwYWNlOiBNeVdvcmtzcGFjZSkgPT4ge1xuICAgICAgICBpZiAoIXdvcmtzcGFjZXMpIHJldHVyblxuICAgICAgICBzZXRXb3Jrc3BhY2VzKFsuLi53b3Jrc3BhY2VzLCB3b3Jrc3BhY2VdKVxuICAgIH1cblxuICAgIGNvbnN0IHJlbW92ZVdvcmtzcGFjZSA9IGFzeW5jICh3b3Jrc3BhY2U6IE15V29ya3NwYWNlKSA9PiB7XG4gICAgICAgIGlmICghd29ya3NwYWNlcykgcmV0dXJuXG4gICAgICAgIHNldFdvcmtzcGFjZXMoWy4uLndvcmtzcGFjZXNdLmZpbHRlcih3ID0+IHcud29ya3NwYWNlLmlkICE9PSB3b3Jrc3BhY2Uud29ya3NwYWNlLmlkKSlcbiAgICB9XG5cbiAgICBjb25zdCB1cGRhdGVXb3Jrc3BhY2VzID0gKHVwZGF0ZTogTXlXb3Jrc3BhY2VbXSkgPT4ge1xuICAgICAgICBzZXRXb3Jrc3BhY2VzKFsuLi51cGRhdGVdKVxuICAgIH1cblxuICAgIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgICAgIHJlbG9hZENvbnRleHQoKS50aGVuKCkuZmluYWxseSgpO1xuICAgICAgICBmaXJzdExvYWRSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgICByZXR1cm47XG4gICAgfSwgW10pO1xuXG4gICAgY29uc3QgaXNMb2FkaW5nID0gbG9hZGluZ1JlZi5jdXJyZW50XG4gICAgcmV0dXJuIDxVc2VyQ29udGV4dC5Qcm92aWRlclxuICAgICAgICB2YWx1ZT17e1xuICAgICAgICAgICAgaXNBdXRoZW50aWNhdGVkLFxuICAgICAgICAgICAgbG9naW4sXG4gICAgICAgICAgICBsb2dvdXQsXG4gICAgICAgICAgICB3b3Jrc3BhY2VzLFxuICAgICAgICAgICAgdXNlcixcbiAgICAgICAgICAgIHRva2VuLFxuICAgICAgICAgICAgaXNMb2FkaW5nLFxuICAgICAgICAgICAgZXJyb3IsXG4gICAgICAgICAgICB1cGRhdGVVc2VyLFxuICAgICAgICAgICAgYWRkV29ya3NwYWNlLFxuICAgICAgICAgICAgdXBkYXRlV29ya3NwYWNlcyxcbiAgICAgICAgICAgIHJlbW92ZVdvcmtzcGFjZVxuICAgICAgICB9fT5cbiAgICAgICAge3Byb3BzLmNoaWxkcmVufVxuICAgICAgICB7aXNBdXRoZW50aWNhdGVkKCkgJiYgPD48UGluZ1Nlc3Npb25IZWFkTGVzcy8+PC8+fVxuICAgIDwvVXNlckNvbnRleHQuUHJvdmlkZXI+XG59XG5cbmludGVyZmFjZSBVc2VyQ29udGV4dFByb3BzIGV4dGVuZHMgVXNlclByb3ZpZGVyUHJvcHMge1xuICAgIHVzZXI/OiBVc2VyO1xuICAgIHdvcmtzcGFjZXM/OiBNeVdvcmtzcGFjZVtdO1xuICAgIHRva2VuPzogVG9rZW47XG4gICAgZXJyb3I/OiBzdHJpbmc7XG4gICAgaXNMb2FkaW5nOiBib29sZWFuXG4gICAgaXNBdXRoZW50aWNhdGVkOiAoKSA9PiBib29sZWFuO1xuICAgIGxvZ2luOiAodXNlcjogVXNlciwgdG9rZW46IFRva2VuLCBjYWxsYmFjaz86ICgpID0+IHZvaWQpID0+IHZvaWRcbiAgICBsb2dvdXQ6ICgpID0+IHZvaWRcbiAgICB1cGRhdGVVc2VyOiAodXBkYXRlOiBQYXJ0aWFsPFVzZXI+KSA9PiB2b2lkXG4gICAgdXBkYXRlV29ya3NwYWNlczogKHVwZGF0ZTogTXlXb3Jrc3BhY2VbXSkgPT4gdm9pZFxuICAgIGFkZFdvcmtzcGFjZTogKHdvcmtzcGFjZTogTXlXb3Jrc3BhY2UpID0+IHZvaWRcbiAgICByZW1vdmVXb3Jrc3BhY2U6ICh3b3Jrc3BhY2U6IE15V29ya3NwYWNlKSA9PiB2b2lkXG59XG5cbmNvbnN0IFVzZXJDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxVc2VyQ29udGV4dFByb3BzIHwgdW5kZWZpbmVkPihcbiAgICB1bmRlZmluZWRcbik7XG5cbmV4cG9ydCBjb25zdCB1c2VBdXRoID0gKCkgPT4ge1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KFVzZXJDb250ZXh0KVxuICAgIGlmICghY29udGV4dCkgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYSBVc2VyUHJvdmlkZXInKTtcbiAgICByZXR1cm4gY29udGV4dCBhcyBVc2VyQ29udGV4dFByb3BzO1xufVxuXG5cbiJdLCJuYW1lcyI6WyJjcmVhdGVDb250ZXh0IiwidXNlQ29udGV4dCIsInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwiY2xlYXJUb2tlbiIsImdldFRva2VuIiwic2F2ZVRva2VuIiwiZ2V0QWNjb3VudCIsImRlZmF1bHRBUElNZXNzYWdlIiwiZ2V0V29ya3NwYWNlcyIsIlBpbmdTZXNzaW9uSGVhZExlc3MiLCJVc2VyUHJvdmlkZXIiLCJwcm9wcyIsImZpcnN0TG9hZFJlZiIsImxvYWRpbmdSZWYiLCJ1c2VyIiwic2V0VXNlciIsInVuZGVmaW5lZCIsInRva2VuIiwic2V0VG9rZW4iLCJ3b3Jrc3BhY2VzIiwic2V0V29ya3NwYWNlcyIsImVycm9yIiwic2V0RXJyb3IiLCJsb2dpbiIsImNhbGxiYWNrIiwiaW5pdENvbnRleHQiLCJ0aGVuIiwiciIsImxvZ291dCIsInVwZGF0ZVVzZXIiLCJ1cGRhdGUiLCJ1cGRhdGVkIiwicjEiLCJkYXRhIiwicjIiLCJpc0F1dGhlbnRpY2F0ZWQiLCJyZWxvYWRDb250ZXh0IiwiY3VycmVudCIsInJlcyIsImFkZFdvcmtzcGFjZSIsIndvcmtzcGFjZSIsInJlbW92ZVdvcmtzcGFjZSIsImZpbHRlciIsInciLCJpZCIsInVwZGF0ZVdvcmtzcGFjZXMiLCJmaW5hbGx5IiwiaXNMb2FkaW5nIiwiVXNlckNvbnRleHQiLCJQcm92aWRlciIsInZhbHVlIiwiY2hpbGRyZW4iLCJ1c2VBdXRoIiwiY29udGV4dCIsIkVycm9yIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./src/providers/user.tsx\n");

/***/ }),

/***/ "(ssr)/./src/utils/http.ts":
/*!***************************!*\
  !*** ./src/utils/http.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   httpRequest: () => (/* binding */ httpRequest)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n\nconst httpRequest = async (method, url, headers = {}, data = {})=>{\n    let isSuccess = false;\n    let raw;\n    let error = undefined;\n    let exception = undefined;\n    try {\n        switch(method.toLowerCase() || \"\"){\n            case \"post\":\n                console.log(\"POST request to:\", url);\n                console.log(\"POST data:\", JSON.stringify(data, null, 2));\n                raw = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, data, {\n                    headers\n                });\n                break;\n            case \"patch\":\n                raw = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(url, data, {\n                    headers\n                });\n                break;\n            case \"delete\":\n                raw = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(url, {\n                    data,\n                    headers\n                });\n                break;\n            case \"put\":\n                raw = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, data, {\n                    headers\n                });\n                break;\n            default:\n                raw = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, {\n                    // data,\n                    headers\n                });\n                break;\n        }\n        isSuccess = true;\n    } catch (e) {\n        const ex = e;\n        raw = ex.response;\n        error = ex.message;\n        exception = ex;\n    }\n    return {\n        status: raw ? raw.status : 0,\n        data: raw ? raw.data : undefined,\n        isSuccess,\n        error,\n        exception\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvaHR0cC50cyIsIm1hcHBpbmdzIjoiOzs7OztBQUErRDtBQWN4RCxNQUFNQyxjQUFjLE9BQU9DLFFBQWdCQyxLQUFhQyxVQUFrQixDQUFDLENBQUMsRUFBRUMsT0FBd0IsQ0FBQyxDQUFDO0lBQzNHLElBQUlDLFlBQVk7SUFDaEIsSUFBSUM7SUFDSixJQUFJQyxRQUE0QkM7SUFDaEMsSUFBSUMsWUFBK0JEO0lBRW5DLElBQUk7UUFDQSxPQUFRUCxPQUFPUyxXQUFXLE1BQU07WUFDNUIsS0FBSztnQkFDREMsUUFBUUMsR0FBRyxDQUFDLG9CQUFvQlY7Z0JBQ2hDUyxRQUFRQyxHQUFHLENBQUMsY0FBY0MsS0FBS0MsU0FBUyxDQUFDVixNQUFNLE1BQU07Z0JBQ3JERSxNQUFNLE1BQU1QLDZDQUFLQSxDQUFDZ0IsSUFBSSxDQUFDYixLQUFLRSxNQUFNO29CQUM5QkQ7Z0JBQ0o7Z0JBQ0E7WUFDSixLQUFLO2dCQUNERyxNQUFNLE1BQU1QLDZDQUFLQSxDQUFDaUIsS0FBSyxDQUFDZCxLQUFLRSxNQUFNO29CQUMvQkQ7Z0JBQ0o7Z0JBQ0E7WUFDSixLQUFLO2dCQUNERyxNQUFNLE1BQU1QLDZDQUFLQSxDQUFDa0IsTUFBTSxDQUFDZixLQUFLO29CQUMxQkU7b0JBQ0FEO2dCQUNKO2dCQUNBO1lBQ0osS0FBSztnQkFDREcsTUFBTSxNQUFNUCw2Q0FBS0EsQ0FBQ21CLEdBQUcsQ0FBQ2hCLEtBQUtFLE1BQU07b0JBQzdCRDtnQkFDSjtnQkFDQTtZQUNKO2dCQUNJRyxNQUFNLE1BQU1QLDZDQUFLQSxDQUFDb0IsR0FBRyxDQUFDakIsS0FBSztvQkFDdkIsUUFBUTtvQkFDUkM7Z0JBQ0o7Z0JBQ0E7UUFDUjtRQUVBRSxZQUFZO0lBQ2hCLEVBQUUsT0FBT2UsR0FBRztRQUNSLE1BQU1DLEtBQUtEO1FBQ1hkLE1BQU1lLEdBQUdDLFFBQVE7UUFDakJmLFFBQVFjLEdBQUdFLE9BQU87UUFDbEJkLFlBQVlZO0lBQ2hCO0lBQ0EsT0FBTztRQUNIRyxRQUFRbEIsTUFBTUEsSUFBSWtCLE1BQU0sR0FBRztRQUMzQnBCLE1BQU1FLE1BQU1BLElBQUlGLElBQUksR0FBR0k7UUFDdkJIO1FBQ0FFO1FBQ0FFO0lBQ0o7QUFDSixFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL3NyYy91dGlscy9odHRwLnRzPzJlMzciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zLCB7QXhpb3NFcnJvciwgQXhpb3NSZXNwb25zZSwgTWV0aG9kfSBmcm9tIFwiYXhpb3NcIjtcblxuZXhwb3J0IGludGVyZmFjZSBVcHN0cmVhbUFwaVJlc3BvbnNlIHtcbiAgICBzdGF0dXM6IG51bWJlclxuICAgIGRhdGE6IGFueVxuICAgIGlzU3VjY2VzczogYm9vbGVhblxuICAgIGVycm9yPzogc3RyaW5nXG4gICAgZXhjZXB0aW9uPzogRXJyb3Jcbn1cblxuZXhwb3J0IGludGVyZmFjZSBUeXBlZFVwc3RyZWFtQXBpUmVzcG9uc2U8VD4gZXh0ZW5kcyBVcHN0cmVhbUFwaVJlc3BvbnNlIHtcbiAgICBkYXRhOiBUXG59XG5cbmV4cG9ydCBjb25zdCBodHRwUmVxdWVzdCA9IGFzeW5jIChtZXRob2Q6IE1ldGhvZCwgdXJsOiBzdHJpbmcsIGhlYWRlcnM6IG9iamVjdCA9IHt9LCBkYXRhOiBvYmplY3QgfCBzdHJpbmcgPSB7fSk6IFByb21pc2U8VXBzdHJlYW1BcGlSZXNwb25zZT4gPT4ge1xuICAgIGxldCBpc1N1Y2Nlc3MgPSBmYWxzZVxuICAgIGxldCByYXc6IEF4aW9zUmVzcG9uc2U8YW55LCBhbnk+IHwgdW5kZWZpbmVkXG4gICAgbGV0IGVycm9yOiBzdHJpbmcgfCB1bmRlZmluZWQgPSB1bmRlZmluZWRcbiAgICBsZXQgZXhjZXB0aW9uOiBFcnJvciB8IHVuZGVmaW5lZCA9IHVuZGVmaW5lZFxuXG4gICAgdHJ5IHtcbiAgICAgICAgc3dpdGNoIChtZXRob2QudG9Mb3dlckNhc2UoKSB8fCBcIlwiKSB7XG4gICAgICAgICAgICBjYXNlIFwicG9zdFwiOlxuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKCdQT1NUIHJlcXVlc3QgdG86JywgdXJsKTtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZygnUE9TVCBkYXRhOicsIEpTT04uc3RyaW5naWZ5KGRhdGEsIG51bGwsIDIpKTtcbiAgICAgICAgICAgICAgICByYXcgPSBhd2FpdCBheGlvcy5wb3N0KHVybCwgZGF0YSwge1xuICAgICAgICAgICAgICAgICAgICBoZWFkZXJzXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICBicmVha1xuICAgICAgICAgICAgY2FzZSBcInBhdGNoXCI6XG4gICAgICAgICAgICAgICAgcmF3ID0gYXdhaXQgYXhpb3MucGF0Y2godXJsLCBkYXRhLCB7XG4gICAgICAgICAgICAgICAgICAgIGhlYWRlcnNcbiAgICAgICAgICAgICAgICB9KVxuICAgICAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgICBjYXNlIFwiZGVsZXRlXCI6XG4gICAgICAgICAgICAgICAgcmF3ID0gYXdhaXQgYXhpb3MuZGVsZXRlKHVybCwge1xuICAgICAgICAgICAgICAgICAgICBkYXRhLFxuICAgICAgICAgICAgICAgICAgICBoZWFkZXJzXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICBicmVha1xuICAgICAgICAgICAgY2FzZSBcInB1dFwiOlxuICAgICAgICAgICAgICAgIHJhdyA9IGF3YWl0IGF4aW9zLnB1dCh1cmwsIGRhdGEsIHtcbiAgICAgICAgICAgICAgICAgICAgaGVhZGVyc1xuICAgICAgICAgICAgICAgIH0pXG4gICAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICAgIGRlZmF1bHQ6XG4gICAgICAgICAgICAgICAgcmF3ID0gYXdhaXQgYXhpb3MuZ2V0KHVybCwge1xuICAgICAgICAgICAgICAgICAgICAvLyBkYXRhLFxuICAgICAgICAgICAgICAgICAgICBoZWFkZXJzXG4gICAgICAgICAgICAgICAgfSlcbiAgICAgICAgICAgICAgICBicmVha1xuICAgICAgICB9XG5cbiAgICAgICAgaXNTdWNjZXNzID0gdHJ1ZVxuICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgY29uc3QgZXggPSBlIGFzIEF4aW9zRXJyb3JcbiAgICAgICAgcmF3ID0gZXgucmVzcG9uc2VcbiAgICAgICAgZXJyb3IgPSBleC5tZXNzYWdlXG4gICAgICAgIGV4Y2VwdGlvbiA9IGV4XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICAgIHN0YXR1czogcmF3ID8gcmF3LnN0YXR1cyA6IDAsXG4gICAgICAgIGRhdGE6IHJhdyA/IHJhdy5kYXRhIDogdW5kZWZpbmVkLFxuICAgICAgICBpc1N1Y2Nlc3MsXG4gICAgICAgIGVycm9yLFxuICAgICAgICBleGNlcHRpb25cbiAgICB9O1xufVxuXG5cblxuIl0sIm5hbWVzIjpbImF4aW9zIiwiaHR0cFJlcXVlc3QiLCJtZXRob2QiLCJ1cmwiLCJoZWFkZXJzIiwiZGF0YSIsImlzU3VjY2VzcyIsInJhdyIsImVycm9yIiwidW5kZWZpbmVkIiwiZXhjZXB0aW9uIiwidG9Mb3dlckNhc2UiLCJjb25zb2xlIiwibG9nIiwiSlNPTiIsInN0cmluZ2lmeSIsInBvc3QiLCJwYXRjaCIsImRlbGV0ZSIsInB1dCIsImdldCIsImUiLCJleCIsInJlc3BvbnNlIiwibWVzc2FnZSIsInN0YXR1cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/http.ts\n");

/***/ }),

/***/ "(ssr)/./src/utils/platform.ts":
/*!*******************************!*\
  !*** ./src/utils/platform.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   browserProps: () => (/* binding */ browserProps)\n/* harmony export */ });\n/* harmony import */ var bowser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bowser */ \"(ssr)/./node_modules/bowser/src/bowser.js\");\n\nconst browserProps = ()=>{\n    if (false) {}\n    return null;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvdXRpbHMvcGxhdGZvcm0udHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBaUM7QUFHMUIsTUFBTUMsZUFBZTtJQUN4QixJQUFJLEtBQTZCLEVBQUUsRUFBOEM7SUFDakYsT0FBTztBQUNYLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vc3JjL3V0aWxzL3BsYXRmb3JtLnRzPzdjZjgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgQm93c2VyIGZyb20gXCJib3dzZXJcIjtcbmltcG9ydCB7UGFyc2VyfSBmcm9tIFwiYm93c2VyXCI7XG5cbmV4cG9ydCBjb25zdCBicm93c2VyUHJvcHMgPSAoKTogUGFyc2VyLlBhcnNlZFJlc3VsdCB8IG51bGwgPT4ge1xuICAgIGlmICh0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiKSByZXR1cm4gQm93c2VyLnBhcnNlKHdpbmRvdy5uYXZpZ2F0b3IudXNlckFnZW50KVxuICAgIHJldHVybiBudWxsXG59XG5cbiJdLCJuYW1lcyI6WyJCb3dzZXIiLCJicm93c2VyUHJvcHMiLCJwYXJzZSIsIndpbmRvdyIsIm5hdmlnYXRvciIsInVzZXJBZ2VudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/platform.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"64fa2e21fe4d\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/YjJmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjY0ZmEyZTIxZmU0ZFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/global-error.tsx":
/*!**********************************!*\
  !*** ./src/app/global-error.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\app\global-error.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/components/landing/registerReferral.tsx":
/*!*****************************************************!*\
  !*** ./src/components/landing/registerReferral.tsx ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   RegisterReferral: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\components\landing\registerReferral.tsx#RegisterReferral`);


/***/ }),

/***/ "(rsc)/./src/components/tracking.tsx":
/*!*************************************!*\
  !*** ./src/components/tracking.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Tracking: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\components\tracking.tsx#Tracking`);


/***/ }),

/***/ "(rsc)/./src/components/ui/sonner.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/sonner.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\components\ui\sonner.tsx#Toaster`);


/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   isDesktop: () => (/* binding */ isDesktop)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction isDesktop() {\n    return  false && 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3V0aWxzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBMEM7QUFDSjtBQUUvQixTQUFTRSxHQUFHLEdBQUdDLE1BQW9CO0lBQ3hDLE9BQU9GLHVEQUFPQSxDQUFDRCwwQ0FBSUEsQ0FBQ0c7QUFDdEI7QUFFTyxTQUFTQztJQUNkLE9BQU8sTUFBa0IsSUFBZUMsQ0FBeUI7QUFDbkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vc3JjL2xpYi91dGlscy50cz83YzFjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7dHlwZSBDbGFzc1ZhbHVlLCBjbHN4fSBmcm9tIFwiY2xzeFwiXG5pbXBvcnQge3R3TWVyZ2V9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBpc0Rlc2t0b3AoKTogYm9vbGVhbiB7XG4gIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB3aW5kb3cuaW5uZXJXaWR0aCA+PSAxMDI0O1xufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiLCJpc0Rlc2t0b3AiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/providers/alert.tsx":
/*!*********************************!*\
  !*** ./src/providers/alert.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AlertProvider: () => (/* binding */ e1),\n/* harmony export */   AlertProviderContext: () => (/* binding */ e0),\n/* harmony export */   useAlert: () => (/* binding */ e2)\n/* harmony export */ });\n/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js\");\n\n\nconst e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Desktop\\ad\\opendashboard-frontend\\src\\providers\\alert.tsx#AlertProviderContext`);\n\nconst e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Desktop\\ad\\opendashboard-frontend\\src\\providers\\alert.tsx#AlertProvider`);\n\nconst e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\\Users\\<USER>\\Desktop\\ad\\opendashboard-frontend\\src\\providers\\alert.tsx#useAlert`);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/providers/alert.tsx\n");

/***/ }),

/***/ "(rsc)/./src/providers/broadcast.tsx":
/*!*************************************!*\
  !*** ./src/providers/broadcast.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   BroadcastNamespaces: () => (/* binding */ e0),
/* harmony export */   BroadcastProvider: () => (/* binding */ e1),
/* harmony export */   useBroadcast: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\broadcast.tsx#BroadcastNamespaces`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\broadcast.tsx#BroadcastProvider`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\broadcast.tsx#useBroadcast`);


/***/ }),

/***/ "(rsc)/./src/providers/preview.tsx":
/*!***********************************!*\
  !*** ./src/providers/preview.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   PreviewProvider: () => (/* binding */ e0),
/* harmony export */   usePreview: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\preview.tsx#PreviewProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\preview.tsx#usePreview`);


/***/ }),

/***/ "(rsc)/./src/providers/screenSize.tsx":
/*!**************************************!*\
  !*** ./src/providers/screenSize.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ScreenSizeProvider: () => (/* binding */ e1),
/* harmony export */   useScreenSize: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\screenSize.tsx#useScreenSize`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\screenSize.tsx#ScreenSizeProvider`);


/***/ }),

/***/ "(rsc)/./src/providers/user.tsx":
/*!********************************!*\
  !*** ./src/providers/user.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   UserProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\user.tsx#UserProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Desktop\ad\opendashboard-frontend\src\providers\user.tsx#useAuth`);


/***/ }),

/***/ "(rsc)/./src/utils/environment.ts":
/*!**********************************!*\
  !*** ./src/utils/environment.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   isCloud: () => (/* binding */ isCloud),\n/* harmony export */   isLocal: () => (/* binding */ isLocal),\n/* harmony export */   isProd: () => (/* binding */ isProd),\n/* harmony export */   isStage: () => (/* binding */ isStage)\n/* harmony export */ });\nconst argEnvIndex = process.argv.indexOf(\"--env\");\nlet argEnv = argEnvIndex !== -1 && process.argv[argEnvIndex + 1] || \"\";\nconst RUN_ENV_MAP = [\n    \"local\",\n    \"dev\",\n    \"prod\"\n];\nif (!RUN_ENV_MAP.includes(argEnv)) {\n    argEnv = \"dev\";\n}\nconst isDevMode = \"development\" === \"development\";\nconst applicationEnv = \"stage\" || 0;\nconst isProd = ()=>{\n    // return !isDevMode && argEnv === 'prod'\n    return applicationEnv === \"production\";\n};\nconst isStage = ()=>{\n    return applicationEnv === \"stage\";\n};\nconst isCloud = ()=>isProd() || isStage();\n// export const isDev = () => {\n//     return argEnv === 'dev'\n// }\nconst isLocal = ()=>{\n    // console.log('ArgEnv', argEnv)\n    return !isStage() && !isProd();\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/environment.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@opentelemetry","vendor-chunks/@sentry","vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/opendb-app-db-utils","vendor-chunks/tailwind-merge","vendor-chunks/mime-db","vendor-chunks/axios","vendor-chunks/@prisma","vendor-chunks/semver","vendor-chunks/entities","vendor-chunks/htmlparser2","vendor-chunks/resolve","vendor-chunks/bowser","vendor-chunks/sonner","vendor-chunks/color-convert","vendor-chunks/debug","vendor-chunks/domutils","vendor-chunks/sanitize-html","vendor-chunks/require-in-the-middle","vendor-chunks/jsep","vendor-chunks/prop-types","vendor-chunks/domhandler","vendor-chunks/follow-redirects","vendor-chunks/chalk","vendor-chunks/react-photo-view","vendor-chunks/tslib","vendor-chunks/react-remove-scroll","vendor-chunks/get-intrinsic","vendor-chunks/is-core-module","vendor-chunks/form-data","vendor-chunks/forwarded-parse","vendor-chunks/import-in-the-middle","vendor-chunks/dayjs","vendor-chunks/parse-srcset","vendor-chunks/react-confirm-alert","vendor-chunks/dom-serializer","vendor-chunks/color-name","vendor-chunks/ansi-styles","vendor-chunks/asynckit","vendor-chunks/react-is","vendor-chunks/stacktrace-parser","vendor-chunks/aria-hidden","vendor-chunks/ms","vendor-chunks/shimmer","vendor-chunks/supports-color","vendor-chunks/next-themes","vendor-chunks/react-remove-scroll-bar","vendor-chunks/combined-stream","vendor-chunks/function-bind","vendor-chunks/deepmerge","vendor-chunks/path-parse","vendor-chunks/mime-types","vendor-chunks/use-callback-ref","vendor-chunks/proxy-from-env","vendor-chunks/use-sidecar","vendor-chunks/class-variance-authority","vendor-chunks/react-style-singleton","vendor-chunks/has-symbols","vendor-chunks/delayed-stream","vendor-chunks/@swc","vendor-chunks/object-assign","vendor-chunks/domelementtype","vendor-chunks/module-details-from-path","vendor-chunks/es-set-tostringtag","vendor-chunks/get-proto","vendor-chunks/call-bind-apply-helpers","vendor-chunks/dunder-proto","vendor-chunks/is-plain-object","vendor-chunks/math-intrinsics","vendor-chunks/clsx","vendor-chunks/has-flag","vendor-chunks/es-errors","vendor-chunks/escape-string-regexp","vendor-chunks/hasown","vendor-chunks/gopd","vendor-chunks/get-nonce","vendor-chunks/es-define-property","vendor-chunks/has-tostringtag","vendor-chunks/es-object-atoms"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5CUsers%5CHomePC%5CDesktop%5Cad%5Copendashboard-frontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CHomePC%5CDesktop%5Cad%5Copendashboard-frontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();