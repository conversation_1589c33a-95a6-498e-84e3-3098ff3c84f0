"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@yudiel";
exports.ids = ["vendor-chunks/@yudiel"];
exports.modules = {

/***/ "(ssr)/./node_modules/@yudiel/react-qr-scanner/dist/index.esm.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@yudiel/react-qr-scanner/dist/index.esm.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Scanner: () => (/* binding */ k),\n/* harmony export */   boundingBox: () => (/* binding */ f),\n/* harmony export */   centerText: () => (/* binding */ u),\n/* harmony export */   outline: () => (/* binding */ N),\n/* harmony export */   prepareZXingModule: () => (/* reexport safe */ barcode_detector__WEBPACK_IMPORTED_MODULE_2__.prepareZXingModule),\n/* harmony export */   setZXingModuleOverrides: () => (/* reexport safe */ barcode_detector__WEBPACK_IMPORTED_MODULE_2__.setZXingModuleOverrides),\n/* harmony export */   useDevices: () => (/* binding */ K)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var webrtc_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! webrtc-adapter */ \"(ssr)/./node_modules/webrtc-adapter/src/js/adapter_core.js\");\n/* harmony import */ var barcode_detector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! barcode-detector */ \"(ssr)/./node_modules/barcode-detector/dist/es/index.js\");\nfunction a(o){const{onClick:e,disabled:g,className:t}=o,i={cursor:g?\"default\":\"pointer\",stroke:g?\"grey\":\"yellow\",strokeLineJoin:\"round\",strokeLineCap:\"round\",strokeWidth:1.5,...o.style};return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\",{onClick:g?void 0:e,className:t,style:i,width:\"28px\",height:\"28px\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\"},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\",{d:\"M3 3L6.00007 6.00007M21 21L19.8455 19.8221M9.74194 4.06811C9.83646 4.04279 9.93334 4.02428 10.0319 4.01299C10.1453 4 10.2683 4 10.5141 4H13.5327C13.7786 4 13.9015 4 14.015 4.01299C14.6068 4.08078 15.1375 4.40882 15.4628 4.90782C15.5252 5.00345 15.5802 5.11345 15.6901 5.33333C15.7451 5.44329 15.7726 5.49827 15.8037 5.54609C15.9664 5.79559 16.2318 5.95961 16.5277 5.9935C16.5844 6 16.6459 6 16.7688 6H17.8234C18.9435 6 19.5036 6 19.9314 6.21799C20.3077 6.40973 20.6137 6.71569 20.8055 7.09202C21.0234 7.51984 21.0234 8.0799 21.0234 9.2V15.3496M19.8455 19.8221C19.4278 20 18.8702 20 17.8234 20H6.22344C5.10333 20 4.54328 20 4.11546 19.782C3.73913 19.5903 3.43317 19.2843 3.24142 18.908C3.02344 18.4802 3.02344 17.9201 3.02344 16.8V9.2C3.02344 8.0799 3.02344 7.51984 3.24142 7.09202C3.43317 6.71569 3.73913 6.40973 4.11546 6.21799C4.51385 6.015 5.0269 6.00103 6.00007 6.00007M19.8455 19.8221L14.5619 14.5619M14.5619 14.5619C14.0349 15.4243 13.0847 16 12 16C10.3431 16 9 14.6569 9 13C9 11.9153 9.57566 10.9651 10.4381 10.4381M14.5619 14.5619L10.4381 10.4381M10.4381 10.4381L6.00007 6.00007\"}))}function B(o){const{onClick:e,disabled:g,className:t}=o,i={cursor:g?\"default\":\"pointer\",stroke:g?\"grey\":\"yellow\",strokeLineJoin:\"round\",strokeLineCap:\"round\",strokeWidth:1.5,...o.style};return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\",{onClick:g?void 0:e,className:t,style:i,width:\"28px\",height:\"28px\",viewBox:\"0 0 24 24\",fill:\"none\",xmlns:\"http://www.w3.org/2000/svg\"},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\",{d:\"M12 16C13.6569 16 15 14.6569 15 13C15 11.3431 13.6569 10 12 10C10.3431 10 9 11.3431 9 13C9 14.6569 10.3431 16 12 16Z\"}),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\",{d:\"M3 16.8V9.2C3 8.0799 3 7.51984 3.21799 7.09202C3.40973 6.71569 3.71569 6.40973 4.09202 6.21799C4.51984 6 5.0799 6 6.2 6H7.25464C7.37758 6 7.43905 6 7.49576 5.9935C7.79166 5.95961 8.05705 5.79559 8.21969 5.54609C8.25086 5.49827 8.27836 5.44328 8.33333 5.33333C8.44329 5.11342 8.49827 5.00346 8.56062 4.90782C8.8859 4.40882 9.41668 4.08078 10.0085 4.01299C10.1219 4 10.2448 4 10.4907 4H13.5093C13.7552 4 13.8781 4 13.9915 4.01299C14.5833 4.08078 15.1141 4.40882 15.4394 4.90782C15.5017 5.00345 15.5567 5.11345 15.6667 5.33333C15.7216 5.44329 15.7491 5.49827 15.7803 5.54609C15.943 5.79559 16.2083 5.95961 16.5042 5.9935C16.561 6 16.6224 6 16.7454 6H17.8C18.9201 6 19.4802 6 19.908 6.21799C20.2843 6.40973 20.5903 6.71569 20.782 7.09202C21 7.51984 21 8.0799 21 9.2V16.8C21 17.9201 21 18.4802 20.782 18.908C20.5903 19.2843 20.2843 19.5903 19.908 19.782C19.4802 20 18.9201 20 17.8 20H6.2C5.0799 20 4.51984 20 4.09202 19.782C3.71569 19.5903 3.40973 19.2843 3.21799 18.908C3 18.4802 3 17.9201 3 16.8Z\"}))}function r(e){const{scanning:g,startScanning:t,stopScanning:i}=e,[w,n]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!1);function r(){n(!0),g?i():t(),setTimeout((()=>n(!1)),1e3)}return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:{bottom:85,right:8,position:\"absolute\",zIndex:2,cursor:w?\"default\":\"pointer\"}},g?react__WEBPACK_IMPORTED_MODULE_0__.createElement(a,{disabled:w,onClick:r}):react__WEBPACK_IMPORTED_MODULE_0__.createElement(B,{disabled:w,onClick:r}))}function s(o){const{onClick:e,className:g,style:t}=o;return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\",{onClick:e,width:\"30px\",height:\"30px\",viewBox:\"0 0 24 24\",className:g,style:t,xmlns:\"http://www.w3.org/2000/svg\"},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\",{strokeWidth:.2,stroke:\"yellow\",fill:\"yellow\",d:\"M13.225 9l5.025-7h-7.972l-3.3 11h5.359l-2.452 8.648.75.364L20.374 9zm.438 3H8.322l2.7-9H16.3l-5.025 7h7.101l-6.7 8.953z\"}))}function C(o){const{onClick:e,className:g,style:t}=o;return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\",{onClick:e,width:\"30px\",height:\"30px\",viewBox:\"0 0 24 24\",className:g,style:t,xmlns:\"http://www.w3.org/2000/svg\"},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\",{strokeWidth:.2,stroke:\"yellow\",fill:\"yellow\",d:\"M14.516 15.158l.714.714-4.595 6.14-.75-.364L12.337 13H6.978L8.22 8.861l.803.803L8.322 12h3.036l1.793 1.792-1.475 5.16zm5.984 4.05L4.793 3.5l.707-.707 3.492 3.492L10.278 2h7.972l-5.025 7h7.149l-3.71 4.957 4.543 4.543zM12.707 10l3.243 3.243L18.376 10zM9.795 7.088l2.079 2.079L16.3 3h-5.278z\"}))}function E(o){const{status:e,scanning:g,torchToggle:t}=o;function i(A){t(A)}return g&&t?react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:{bottom:35,right:8,position:\"absolute\",zIndex:2,cursor:\"pointer\"}},e?react__WEBPACK_IMPORTED_MODULE_0__.createElement(C,{onClick:()=>i(!1)}):react__WEBPACK_IMPORTED_MODULE_0__.createElement(s,{onClick:()=>i(!0)})):null}function c(o){const{onClick:e,className:g,disabled:t=!1}=o,i={cursor:t?\"default\":\"pointer\",stroke:t?\"grey\":\"yellow\",fill:t?\"grey\":\"yellow\",...o.style};return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\",{onClick:t?void 0:e,width:\"30px\",height:\"30px\",viewBox:\"0 0 24 24\",className:g,style:i,xmlns:\"http://www.w3.org/2000/svg\"},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\",{strokeWidth:.3,d:\"M16.279,17.039c-1.396,1.209 -3.216,1.941 -5.206,1.941c-4.393,0 -7.96,-3.567 -7.96,-7.96c-0,-4.393 3.567,-7.96 7.96,-7.96c4.393,0 7.96,3.567 7.96,7.96c-0,2.044 -0.772,3.909 -2.04,5.319l0.165,0.165c1.194,1.194 2.388,2.388 3.583,3.582c0.455,0.456 -0.252,1.163 -0.707,0.708l-3.755,-3.755Zm1.754,-6.019c-0,-3.841 -3.119,-6.96 -6.96,-6.96c-3.842,0 -6.96,3.119 -6.96,6.96c-0,3.841 3.118,6.96 6.96,6.96c3.841,0 6.96,-3.119 6.96,-6.96Zm-7.46,0.5l-1.5,0c-0.645,0 -0.643,-1 -0,-1l1.5,0l-0,-1.5c-0,-0.645 1,-0.643 1,0l-0,1.5l1.5,0c0.645,0 0.643,1 -0,1l-1.5,0l-0,1.5c-0,0.645 -1,0.643 -1,0l-0,-1.5Z\"}))}function h(o){const{onClick:e,className:g,disabled:t=!1}=o,i={cursor:t?\"default\":\"pointer\",stroke:t?\"grey\":\"yellow\",fill:t?\"grey\":\"yellow\",...o.style};return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\",{onClick:t?void 0:e,width:\"30px\",height:\"30px\",viewBox:\"0 0 24 24\",className:g,style:i,xmlns:\"http://www.w3.org/2000/svg\"},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\",{strokeWidth:.3,d:\"M16.279,17.039c-1.396,1.209 -3.216,1.941 -5.206,1.941c-4.393,0 -7.96,-3.567 -7.96,-7.96c-0,-4.393 3.567,-7.96 7.96,-7.96c4.393,0 7.96,3.567 7.96,7.96c-0,2.044 -0.772,3.909 -2.04,5.319l0.165,0.165c1.194,1.194 2.388,2.388 3.583,3.582c0.455,0.456 -0.252,1.163 -0.707,0.708l-3.755,-3.755Zm1.754,-6.019c-0,-3.841 -3.119,-6.96 -6.96,-6.96c-3.842,0 -6.96,3.119 -6.96,6.96c-0,3.841 3.118,6.96 6.96,6.96c3.841,0 6.96,-3.119 6.96,-6.96Zm-4.96,-0.5c0.645,0 0.643,1 -0,1l-4,0c-0.645,0 -0.643,-1 -0,-1l4,0Z\"}))}function q(o){const{scanning:g,capabilities:t,onZoom:i,value:w}=o;if(!g||!i)return null;const n=(t.max-t.min)/3;return react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment,null,react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:{bottom:130,right:8,position:\"absolute\",zIndex:2,cursor:\"pointer\"}},react__WEBPACK_IMPORTED_MODULE_0__.createElement(h,{disabled:w<=t.min,onClick:function(){i(Math.max(w-n,t.min))}})),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:{bottom:180,right:8,position:\"absolute\",zIndex:2,cursor:\"pointer\"}},react__WEBPACK_IMPORTED_MODULE_0__.createElement(c,{disabled:w>=t.max,onClick:function(){i(Math.min(w+n,t.max))}})))}const l={fullContainer:{width:\"100%\",height:\"100%\",position:\"relative\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",overflow:\"hidden\"},innerContainer:{width:\"100%\",height:\"100%\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",position:\"relative\"},overlay:{position:\"absolute\",top:0,right:0,bottom:0,left:0,pointerEvents:\"none\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\"},borderBox:{position:\"relative\",width:\"70%\",aspectRatio:\"1 / 1\",border:\"2px dashed rgba(239, 68, 68, 0.4)\",borderRadius:\"0.5rem\"},cornerTopLeft:{position:\"absolute\",width:\"15%\",height:\"15%\",border:\"4px solid #ef4444\",top:0,left:0,borderBottomColor:\"transparent\",borderRightColor:\"transparent\",borderTopLeftRadius:\"0.5rem\"},cornerTopRight:{position:\"absolute\",width:\"15%\",height:\"15%\",border:\"4px solid #ef4444\",top:0,right:0,borderBottomColor:\"transparent\",borderLeftColor:\"transparent\",borderTopRightRadius:\"0.5rem\"},cornerBottomLeft:{position:\"absolute\",width:\"15%\",height:\"15%\",border:\"4px solid #ef4444\",bottom:0,left:0,borderTopColor:\"transparent\",borderRightColor:\"transparent\",borderBottomLeftRadius:\"0.5rem\"},cornerBottomRight:{position:\"absolute\",width:\"15%\",height:\"15%\",border:\"4px solid #ef4444\",bottom:0,right:0,borderTopColor:\"transparent\",borderLeftColor:\"transparent\",borderBottomRightRadius:\"0.5rem\"}};function M(o){const{scanning:e,capabilities:g,onOff:t,torch:i,zoom:w,startScanning:n,stopScanning:a}=o;return react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:l.fullContainer},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:l.innerContainer},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:l.overlay},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:l.borderBox},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:l.cornerTopLeft}),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:l.cornerTopRight}),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:l.cornerBottomLeft}),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:l.cornerBottomRight}))),t&&react__WEBPACK_IMPORTED_MODULE_0__.createElement(r,{scanning:e,startScanning:n,stopScanning:a}),i&&g.torch&&react__WEBPACK_IMPORTED_MODULE_0__.createElement(E,{scanning:e,status:i.status,torchToggle:i.toggle}),w&&g.zoom&&react__WEBPACK_IMPORTED_MODULE_0__.createElement(q,{scanning:e,capabilities:g.zoom,value:w.value,onZoom:w.onChange})))}const G=\"data:audio/mp3;base64,//PkZAAhghE0AKToAJra/n0FQygAAAGIkYJgmCYXBMAAGCTJz3zhCEM//z//1hz//8MMMMN08ssV6e5DDWIQreAgCvFKy8bXgIKMkUDDBgzJwIBtkRMQAocxIFdxghQGKDoEziAzQxOBOdH92i/iGi+zDCAEIX46a73HrSybZw1x3JZjXp7dSNy/P68rjcbt7p7fakMP5LVMyzCaj1pjvejYYAIDgDGzECjEAk1Jl3559HIon8hzlfPVTCvGJZzfcKSxXfyMWM88//9VKSxXdtnb9vomOuuRyiWVYbf+X8zp6fKGHIdycuWMMMMMMKSnp+6wsYc/9f/7z7rPPWHN556p6fP8MMMP///PPP/7+GHK9PT6p7f/unldP2np7YeHjweiYA4GLNAgAiI7u57n5//oc5/yfk6znOcPhwOBwggHCMpA4HA4KEyHOc5znO+hPIc5//+fqJh8XQPh90JU5xQinOf//87/zvP+ggAYuhCKHxdA+Hxd0EA4KKHA4ciB3kOXfXB/gmf8p/B96lAMKAgAADU+BujARHgwdisgHMfAUHAiceBg4ASBgZBiBIBH4ZaHOJsLhf8R+HYEciIgYSwj/+Bi7EqBh+AcBn5P6Bh4TuBmrAMBiZH7gaLEWgew//PkZFMlyek60MpYAShzqqZhm6gCUWeEUWAewEWYGN4X4GDEPwGE8S4GDoOIGAYKgIQOkz//gGARAUB+CwGxTwMAACAEgyAwdAlAxKhzAxXiZ///AxcEwAwJjDAziCAAwQgdAwRgdAsJQDAmAcGzYDwAhZIAKAcIQB4GT9TQMJ9/4Gi1Fv/AcAYUqKBAwGgNAwVBAAwGhwAwdBlAxFg1AwlgzAwNBuAkJQDBgEEDEqGECgChFgBgL//CIswYYH//+HKCpk4K0C9AaKKCAOBeMcR4X9C44BwABCgGAsGYCgTwHAcAwXAiAwSAQV///CJP9lwMBQAwAAAWGo5lVLCcaeneVhJAVGai3//ioaUEf//gaTAYGCj8BnEwfrIqDcsIQb/vmhU/8fAs0G8YGGwKST8Igj4GCATipksVzY8p//90FWJwh45AkX//4fCF9wMEgkL3uQc+gbGJ8t4MBAMBP/hEXf9FRuWBcAfIFjYzQdoLCBwh7IWVlxaX/w8oMCP/+EQT5poGB1Ir90DhiV6af/jFYBpT2BgoQyyt2M0ToBdEaZyzt8nTo3xdNDCTSd//o6F06CjooxRr4jVF/0bOKD6OMUNDRxiMUVFR0FFQPhGXRjDpr4MAEA4wIQUhYOIw//PkZE4nrg08sOx4ACQ0CopD2aAAlwTTBxQeMcwd8w8gZTAgABXwGABOkzpI0wAQAExnWfP4x8ZjKunWdZ1o1Qe6lFGKCNULORQCELAAPnRUf/GIx/0FHGfo3SdZ0qP2cukXKLlRtBKFgAQwCkwJgFRYBEEATmDsLSY2QiQYCEnEra+UZo6F1aKMRr6GhoaP/+j//ov+i+hjLqBgMkYoqChoo1GqCjoY06TpRqjjLOYzRRmMuizmgdKhdF8kjTAAAPMCwGswzglgwAJfzqxmioKH/////////////+ijLOAwFmN0f/////////Q///8ZjP/9DGYyFwIDBeB3MA4AlBWidKM5spykAUAQOCakK+udGqN8VDYjTRuN//0bVX//6Kio4xQFv//nf//kKOoAEIB6SY6hcxenp85///4/DEPf/84Xvl4vF4dwtxfEJgCYQHMSgKCROp4+fn////L4tQf0unz89OecLxeL0ul09y6enzp4dIhKAQFA5hKfLu1nU7/Wr71rRKJUBQSUVt////l08GUPVWgAAYAeNKsxbQNAAhQx3/oKGMM6dZ1Yy6sajMbfKio7lNcv0sScZ47lNcw32ekVFMP9fp/f+mklI6at6u/dBMQuW6y+C5BcgwAA//PkZEomGg1HYGe0riWsCqbEwiY0BJFJMxrCcxGgQ2VK8xxCF1wsABgQBBgQBAYAFCkYCgALkK4fJ00E5ctJN1Uxy5au3XoGcM4dSN0EYZwzhW0WCh1PZwzhnFFQxtnbO2d0Kt6CdXbruggkTEX5Rs4TEQUVsdVXaCcuWXIBQUGmwmgoAGcJiM4dSioHQdBnEY+hjcbov//jcb/43G43G/jCRibfxugdBnDqULO2duvQumztncbo4xGKKNxugooxGIxQULoOgFgUMTA/jbrxiMDmETL+eLhcPThzOeeOZfL5fIcBYuXD/OHv8t//LJFxjxQYHHKDGFkZMtlmKM0RgQfbVtxFIHbQBDRuzRguEI1c9XZ/rWmrZfVU3Oc/9utNNygRQkCoHlAYstNN6DJ1IINpoINppv+WG/+g2mmnUmmmpBAoJIJhnwB5xkymn606Df/t000xhU03UXC5Ol8uFyXy+Xy/l8v5w4cLk4XAvwAYUvl/QL5fTdlKZkEEEEGumaKaX00zIOemf6CH///ywI3LKpiQ2Ef4xYWMSDyI0XwzikpL0ajVHZ3j9DGIjeuXcNfjn96/S/TUz/X/vX////5K/r+v7Jn+LlAoGZOIQIwMKBQM/yppKYURmhGZkR2b//PkZE0mHg1HEG8wyiD7mlwAyDU8nJHi5htakZmZGNiRcpkJhBmUmZYQcM1VDEEBggNDV/ASEZRYKFZIhkXKLlKmk7IVTKmZNJ5KhiqVUqpX+k7+qlVKyV/ZIqZDFDWTJlFyi5TJpKhiXKBQyZzIS5JhBoayV/AQWVpGWkcYTV5O/rIVSv8/0nkr+v6/vyR/X9f1/ZN8kf5/n99/n+f5/lTKmf9/mRP8yFUqpWSv4yJUqpX9kj/MmZNJ5K/z/P9J5P/v6yFUz/CEoywgUMqZkz/FssECIsVq0aPRrRRatFFFykQZJ2Ukl///5KjmksOaA7ALROJLDnEsLbq3GTMVrwb/0dGzmM0DlfBlHQOmrdR0MYi92TX6b/cmDHLclyUVYO/4O8sFTKFTKFf8yhUyhUyhXzKlDKlCsr/+ZUoVlSsr/+ZQqZUoZUoceMfcoZUoZUoVlfOPGcmDHKcpynKg9yXJcr3JclyXJclyYO+DXJclyfgxFZVb3KcqD/g2MxzsY72L9LubPR0dCyf5K5NNd+Tv9GXRdV0XJclyfcl/n+k8kf6SX/iVynvXf///4Mg/1OkxguYzmM5jMcLHMxwwGhgNCwXMLAcwMXTLi1MD7w7tmTdBvMmE0xQFzCwHMDAd//PkZGMpigkmAGOUvCfTgcgAuC+ITswMFzC4oMLhYMByn0xvU+p9Tr0xUxf/0xv///1PBYDhgNTFTFTGTGTGTGTGTGTGTGC4GTFMDAcLAdMVMVMULAcMBpgYDhcDGFwsYXFBikLGFwMGA4MBxgcDKfTEMUoAx2BisD+mKp5Tv1OkxkxkxkxkxkxkxkxlO0xVPJi/6nv////U7CwHTFTFTGTGTGTGU+p71PpjJipihYDpipi/6n1Ov//C4GC4GTGTGTGTFU8WBSZdLhWB//yKlotyLZFiLf8tyyRUipFSKkVGRBZQMiWC3yx//wxUGKsSvDFQGHUAL3QMsHDFIYpCYy2Bmagy6BjLgy6Bh3od58DCLAizBgRb+EQiyEQiwGBFuDAi3/////20QqSBJf/qcumv/////wMZdGXAYMu1gYy6MuBEZdBgy4BjLgy6BjLgy6BjLgy6BjLQy6DBlz2cJDLgRGXAMZdGXAYMuhMZcCIy6sJGaoGMuppwNRl3WBjLgy63wiEWAwIt/+EQi0IhFsDCLAi0GBFkGBFgMJCqaqYAgAYIhBBvwf7kwcqRq7V3IctacvlN6NxqgooxRRuNRiijEZjMGRuMvz9FGozQ+1X2qe1T1S//+VhaWAtKwtML//PkZEIkbgcaAHc1xCJrjeAAtfuUAtLAWf///mWePH95ZFgsyssvM88+jzO6K+z7OM48sH+WDiweZx5YOK+zxWLGJWsa6xadAv02fTZLSpsFpvLSga0tMgWgUgWmwgUmyWl8tMgWWmLSoFJsoFlpSwsmyBg4HhEdgwHAYPBwGDgeBg8HwMHg4DB4PBgOAwcOwMHLwDn5lAwcDgiDgYDwMHA/4MB3//+EQeDAdwiDwYLPhEWQYLODBYERZBgt8Ii34GLBaB5pfAZ0FvEAQ/UfwxULlx+IXx+8f5CELH8fxKhAIDFoHBEDRc3////gwHgaRXgMHYMB2EU/gwZ/gYbw3gY3QbgYNgbgYiAbgwN3/+EQbBEG4MBuDAbAwG4RBtAwbg2////+B9B6F//////////hEdD//////////8DRahEQGCInMNg3LBElgNjDciCwG/lgNiwGxWG///mG4bf/+WA3Kw3KyIMNw2Kw3MNg3MNg3MNiIMiQ2OK0xMiA3KyJLAEZgRARmBEDEYFwBBgEgEeomowol5YAJKwCDAIAJ8sAE+gGBgCJYAQUZ/13LubMu5dyiYOAhKwEFGf9AOoz/g4CFAIol/lgAjysAgsAElYBP////lgJksBM//lgRUxF//PkZGApggsIAHt2wiNLoggAqCz8RFSwIqYioipkjEjHCCSN/+WJg5iYOYmPK5krmTmZk5mZ8sTJYmf8yJiNiIisi/ywRmRkZkZGZGRmRERkREZERGRERkREZERGxEf///hETIMEyBiZEyBiZEzwiJgDEwU4DV2gQGFOBgmP8GAiAwRAiBgIgYCIGAi/4MBHAwRgjAwRgjwMEQIgMcgY8DBGCMDBGCMDEyJn///+BiYQIBlObj4eYLIwsjAOBGFkYBoEQ84eULIw88LIA88PKHnw8sPKFkeHlANAiAaFUDAiMYPIHmDzBZBhZB///8GBOA1sH/Awni7AwnhPCITwYE/CKzBgt8Ig8GA4Ig6EQdww4Yb+F1sIgtwYCwGAsAwWAsBgLAYCwDBYCwDBaHUDF+VEDKgC2BgtBaDAWgYLQW////gYLAWQMFgLAMFoLcGAt////4RMABmBSj4NgwMOGHBsHhdcLrwbB4XXC6+F14YcLrww4Ng8MOGHBsHww4Ng8AUC4AoMQMC4fgw3wutV4QIsAgFYIBWCCVgHeVgHeWABCwAIVgH+YBwB5aRAstKgV6Ba1oMg9avtUVN/qdLWciD/cqD3Jg6DFPuTB/////lYAn//mC8C+YLwL5gvgvGC//PkZFEpLgMKYXt2xB5LogAArbdc+C8VgvlYL/mC8C+YLwL5YBfMNgNkwXixDWoHPMc4VUxVA2TBeDZPYXzXl815fK14sLxYXvK1//NeXzXl815fMtdDLSw3UsMtLCstMtLDLSwsFhWWlgt//8sFhW6f/////+WF7zXl8sL5ry+Vrxry+WF/wMLwX4MC+EQv/AwvBfAxsjYAyqpKA0lpLAwvBfBgXv///4RA2DANAYGgNgYGgNgYGwN+EQvf/BgXwYF//8DC+F4Dd6NgGBf4AgFwbB0MMF1wuuDYMBsGwusDYOBsHA2Dvg2DQuuDYOhdcMMF14YYDGYDEGwfhhgbB0MN///4RBaEQWgZByoYRWYMFvwiDsGwaF1gbB34GDwdC64XW/w1aKxDVgatir4YcLrQbBwNg7////////////+ESfgZPyff/////////4MFmBvvMAERZ8CC5adNj02E2S0qbCBSBZaYtN///oFlpU2QMXIFAYuQK9AtAo7IxLTpsIFJs/5aZRxQawaxGfIg9CsrI+PUqkWdOHp7ODsOl6cOHZ4dsulfLI9/8BAAgMBACsBLACVgBjoAWAE5OTM6WDAR0zodMdHCsJLASWAjzCQgsBJhASWAjysI8wkJMICU//PkZFkeqgcaYDd1th6DogAAqCuIxQwtDA5MdMULgyY6YqnXqdep5TpMdMVRNRL/UT9AN6jCiSjKAZRNRhAMomgFQDfCIJwYCQiCAiCMGAkGAgDBJUA3eVAMEgj////8SrDFIYqh5g8kPMHkDzB5OFkYeYPJ+HlDzB5+AYVQDibCyIPLLEZIsjIliWS0WywWyyWuWCyWJaLZbLJYDHgLAItFvy3///4eYPKBlQTh5QYEODBb//hhoNg7/C62DYPFUKwKsVgNWBq8VkVcVkVUVYrH//hGff/8Iz8GT7//////wiX4ML3gwvgwvf4ML3gZeqoHVGyBl4vgZfL2F1oNg4Lrg2DcLrg2DQw0Lrhh8Lr4YcMNBsGhdcGBcMOGGAFGAGMQsF1guuGG4XXV/4uQXOLmF1kL/8lCXkuSk4O8/FzHzuP0hIucf/CIFAMCgFPgYFAKAwCgGBQCgGHYO4GBVM4GgQI4RCMEQKQOtQjUI1CNcI14MqDKwDIAGQDzh5wsjDyw8uHmDzYeUPN+DEYRRhFEIowYgDQgIhWDApgwKAwKQYFAYFQMKkYDOxHAzsRgMKBUGBX/h5v+Hm4ecPNh5A8wBwjhZHhZAHlDzBZAFkAeQPKHnCyILIAsih5QsgDz//PkZLQgIgsMAFp1xCGLogAAA2rkB5w8oeYLIQ8kPMHkCyGHnAMKoGmAgFkUPMHmDYAtfHPHNkr/JYl8c8lCWHPHMkqSo5onMAkBDnfJX//8IgDBgBhEdAaxSQMHUGAEIgAV4risCcipBOMVRVBO////hEvAxsAwvf////8MPDDww4XWC64XXDDww4YcMPC6wYb/wYGgiGwMplMDYhTCIaCIaBga/////8GA+EQdgYPB4MB4GDwdhEH8Ig8Ig8GA6EQcEQf8DBxlBjoAweDuFwgCQXiL//iLiKCKCL4XCiKRFwEgoRT4iqpMQU1FMy4xMDCqqqqqqqr////xuDd8fv/DVwrArEIg6EQdhEHAYPBwMB+ERYBi0WAwWAZ0FoGLYOB+JfgZ0FoGdBaDBYbn+WHm9xW7/Nzzc83PLDzc8reWlA7S0oHamx//6BflpU2C0qbBaf/8yk/ysn+VkMhTKQrKVk8yE8rIVkKyYsAn/5WC+YICmCApWCGTkxtKMcVWlgFMEBTBAX////0C0C0C/////////LTFpvTZLTlpv/0Ci0xaYtOgWmymyWkAxcWmTZTYTY8tMmygUWmLTIFpsFpv9NnywLmLCx2RgWmQKTZ98UjnzZ1/++D4M6/////3//PkZOgjIg8IAFcbxCaTogAAoCuIzfJ8vfJ8Wcs5fBnD5//++L5f////////6nCKv//+o0o0FUYwqDNGCwgUMKC0VVGkVv//4Yf8LrxVCriqDVoasDVuKsNWw1bisBqwVkVkVX//gy/4YYMMGGhhgwwNg4LrhdcLrAYWGAA0zBsH4Ng3C6/////4MAoGBQIDAKBgUCAwCgYEAsDAoEAwKBAYBYMAoGBQIBgUCYGBQIBgUCwiBAYBYMAoRAgRAoGBBMBooCAYFAkIgTEUxFv/EWxFxFhFoi0LhQuFiLCL/EWqTEFNRTMuMTAwqqqq//////8TWJriV/hERAwRgYiEYGIhGBmIRgYiEYMEQMEcDMSiA1GYwMR+UDMTkA7KIgMRGIGGM0SIrRGjRlhEV4jxov8rR/5YRFhEdQSViCsR5iRJWIKxPlgQYgSViSsR5WJMQJLC78IiKERF4REeERGEREERHwMRGOBmIRhERhERAwRAYiEQGIhGBmIRAajkgG5TEEREDBHwYIwYI4REXCIV/4MCmDArgwKAYUO/gYVCkGBUGBTgYUCsGBUIhUDCoUCIVBgVBgVBgUhEKQMKhUDCoUBgVAwoFAYFcGBQDI6oA5+qQiFAMjBUGBXCyEPIFkOH//PkZPMj6gr6AFdVwifkBdwAoC2Ilh5/8PPh5Qsih5IeUA0TAZrE8PP////wMRiIIqID8piAxEIwMxCMDEQjCIj//BiIDRo8Iogii/4MKwYVhEoESkDEiIGJEgYlfBgmDBIMEAYgSBiBIREAwSERH/8I7wPfuCO4I7v/8GBvAw3Bv/////gwN34RBuBg2BuBg2BuBg2BuEQbhEG+EQbAwG4MBtwiDaDAbhEGwMBuDAbAwG4GDYG8DBsDcDBuNwDRWDcIg2AwbA34RBv///CINvCINgMRIN/////wiG4DDeG+TEFNRTMuMTAwqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq/zB4OLAP8rBxWDisHlYOKxb5WLTFgsMWiw18LDOnjPxZoxadDOgsLAtLAbKymVhsrDZWG/LAb8w2GywGysNlgNFgNGFwuBQuWnTZLToFFpisLJsFp0Cy0voFAQLFZkMCAQwIBCsC/5WBCwBCwBCwBCsCeVgUwIBCwBDEwEMCAQrAhgUClgCFgTlgCGJgJ5gQTGJwKWAKYFApgQTGBQL5kY0HMjQYnAhWJvKwIWAIVgQrApYAhWBPBgECIEBgE/8IgXhECQiBAMCkcDEwEwMCAQGAQIhoIhsGBsIhvgwN//PkZN4irgr2AAOVqiUThegAnCtMgwNwiG/8IhuDA1hENhENgYbKYHb2IBhoNhENAwN////8IhsDKZS////8IhuBhspgc0KQRDWEQ3///CNf4GAIMDAwgAwAAwhAwAwYAIgwiHBgAMAYGAAGHv///CIBqBqESDEIgRYGoGHBiDEIgGoGgMAYgxgwgxCIETwYFYMCoMCuDApBgU8DCoU//gY3G//wYFOBhUKBEKQYFfBgV8IhSEQoBkYK8IhT/wMKhXBgUCIU4RCoRCvgYVCsDCp3A1SqAYFAiFP////gY3G1TEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVgwCAwCBEC///////8IgUIgWEQIBgUCAwTQMCAQDAoEhECAYFIwGaRMBotWAasdwH/KqBmhFgaKI4GRgKVpjCBTCBPMJHNMFMIFKwnlgJ5hUxhUwGWlpAMuMuWLTFpvQKQLQKLTIFJsoFIFFZb/8woQwoT///MIF8rC+WAphAphAppgpYClgsmwBC6bHoFFpUCi0wGXFguWCwFLgUsgX6BZaVNj0Ci03//+EQKEQKEQLCIFAxMBcDAgFAwIBQYBODAKBgUCAYEAuDAKBgUCAYFAgRAngwCgwCwiBIRAmE//PkZN4iogj2AFdVxiUrhewAhOkwQIBgQ0gbOI4GRhOBiYCgYEAv////CIFAyMBQYBf///CIF8IgUDAgFBgEAwIRwMCCcDEwFAwIBf///CIEQGMDGEXCKBpCIDCEQDCAsYInDFQMMAuYSoTQMVfBlf//+DKfBgAiGDAQMIMIgCIQYADAEGAgwP////8ItgNu2/8DAAAiABgHhECBgQGEQARA8GAeEQIMABECEQIMAwYBBgCDAARA4GAAgYACDAARAwiAgwDhED4RABEBCIGBgDoH2dgZwCBgAP//BhX/+DCiTEFNRTMuMTAwqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqF1ww////////hdeGGDDBhwjeF102E2fTYAowMLjE0zTDfyyMYjEDJTwMLSwFi0/gQLoFIFpsegWmyBQsWlQL9ApNgtIWn8tMgX6BaBX+WmQL9Nn02f9NktIgUgWmwWl9Avy06bCBSbJaUtImyWkQKLTIFgYWoFFpAKFwMLzZgwAwvLTIFlpU2fQLQK///4Yf4YaF1sLrg2DAuuDYNDDQw4NgwLrhh4XXC64Ng3C60MNwbB4XWC64YcMODYPCJYDlMQBlwRLg2DOF1v/////PkZM0eKgz0AE+UuCnLFfAAk3Fsg2DAYX/ww//4YaGGDDhdbDDACFwMuXA2DADlygusDYOBsGhhv//wNU/CKQusF1oXX+F1wuuGGC6wXWDDBdbC6wXXhHv//4AHIFgC0BZAA4Ba4FkADwFsC3AtQAOcC0BYAtgAcAA9+BYAsgW4FvgWOABwC1oq+it4RT0VVG0VFGkVywo1/RWU4U59TlFb1GvRX////QK9Nj/9AtNn02fTY9NhNlAr/QK//TYTZLSJsIFemx///oF/5aZNj02U2C0yBaBfoFAS6bKBfoFVTEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVQiA8GAO/4RAf//////wYA4GAO+EQHAwB4GDoB4GA4HQRB0BhlDIBpEAcBgPDIBgPAeDAHIFAYuLTpspsJsFpi0haRApAtAstIYKClgF/ysF/ysE/ysF//LAJ6plTlYC1ZUypmqtWVK1VqyplSFgBaqqcwAAVIqcrEBAAKmMBERCQhwD5iICIQEyABMAABBVGXF4gIA4DLAAWAEQgLVSsBaq1ZUv+YKClgELAJ////5YBP//LAL5WC+Vgn+WAX/LT+gW//PkZMIhLgrsAFt1yiD7FiB6A9qwmwViybCbCbCBSBabCBSBabPpsegWmwWn9ApApApNgCGBmEwWlLSpsf////hEHgYOB3////4GDkiBxMHhEdgYOB4GDwcBg4HW23gooFYHd/+BnAz8O8Vv//FaBm4rBWh38es2zbAtD1m2PVBqBrBqBrAF7AF3/5el84MkuHTgyy+dL0dRnEajMIyOn8RsZ5HGG/kbIpHIkYUiD0y2WctlZaWSwrKx6FZbxnL54ul04eLhw9Ol84dl86cOHT3Ipb5Z8qKpVx6/j0EkLctVTEFNRTMuMTAwgWQLQAHgLf/////4Fn+EQESEcIgInCIRW9TkrCzH0YIjjWtI+YeCD0woLCBQwsKRWUaRVCBZFZRtRtFRTlTlThRtAr0C0C//02E2fTZQK/0Ck2f9TlFRTlTlFT/9TlFVFRFb/9ThThynIQbWkAREue5S11rKdlkDEVABh61ExYPU+tOD4MQgcn4iwigivxFRFoXDcRQLhoiwioXCeAkFBcNiL8RURQBIKEVEWiLBcKIqIpEXC4QReIoIpCIKAQMxFAFAsIuIr/4YYLr//ww4XWBgwC64XX/hhgw3hhwuv8MNhhguvhdcAUYgaYTAAoWDDg2DQbBg//PkZPYe6gzuADd1uDKsBhBAe89cYYdGjtDT2hDmlD2leaOv9oX0OX/0NNLplNf8ewakNWGrTfTRpphNJtfaeh7T1/tStdtTpqN532lpX/1/kmQ5eaf2hoaV9paGleJAvd0rer2pWu1ar2vk56vVrV5Hj1Nop+/lfv5Zpkem5kX/2vtata2rq783nbvtbW6VjW6a2rq13+rO6VysV58K7tbp0ru6Vrtqa1a67tr6sau1ulYrnfa2v927Vrt21NSuN5X927/D4diAQANh4gEIfxB+IQG/D/4gDw/iGIRD+HRATEFNRTMuMTAwqqqqqqqqqqqqqqqqqiIEK/ititFX///8VcVuK38VsVhXBOQTpq/qm8QCAcBmIxgdfFZCHECpxFBFAuFEUC4cLhsRTEVEUDVoDQD+KrFYirFX+KwKoVgVUVUVeKwGrhWRWBViqFYDV4rMViKyGroauBg/A0YThVgNAEVQqw1fisis4quKxxWYrAatxWIqoqg1dFYxVxWIrAavAaAArIrAqoqhWYrIqvxWMVYrARCADhcKoVYavDVgqhV4qorIrHFWKuKxFViscVUVYasAaAfFY/4YcMNhdfwuuGGC64YfDDA2DwusANygbBwAxhAECwNg8MOF1hD4//PkZOkc4g7yBjd1sDNkDhgqYwtcYoUyiQ0NCkxQ0NCQkSIbGKIyRGEo5mUaNEhGKEHMmaGhI0UokORvMmZmZSZSiMZQkRnKKZkzRoSNCQpQ0NEZoaNFJkhh3KOUJEhIcoSJCQkIzlDlEjMDMyMpQ0JCQzNCRGaNCRISEZoZihoaIwlEHKNGiMzFEjRmSIwlHMyZmcyiGyMxRyhIkIxRSZokSEYGBkiRojAPESNGjQxvKHMmMoSKTJCQkIwlFKLDYWGhoZhYWAAUGBsAwsMDYUAcK4XDPhQVwwLAH/DfgHC6TEFNRaqqBgjwYI//wYI/8GCL///+DBFhERgwRcIiPAzGYgMRKIDcskBgjAxEIwYI4GiRwNEjhFH4MRwYjA0aOEUf8Io/BiLgxGEUYRRQij/BiKDEYMR+DBGBiMRQiIgYIwiIwYIgiIgMRGIDUSjhEReEREBiIRAwR/wYI/8IiP4MEcGCIIiOERH8GCIIiMIiIIiODBHBgjBgjwiIoREcIiOEREDBFCKiAxHJcIiMGCMGCLwYI8IiPhERgwRAwRAYjEUIiIGCP/AxGIgiIsGCL//4REYMEXhERBERhExhExAzyAZiEWDBGERGeKsVsVRVFcVwTnFcVxVgnYqipFUV//PkZPkcfgzeAFaVxDgUBgAgatvIYrgnYqgnQJyK4riqK4rwTsVor4rQAjABAgnQritFSKoqQTsE5C1haxfF7F4XQtULSFqC1i4LwWkXIWkXRdF+LwqAnME4iqCdwToVuK+HAERWAkAlgJB0OB3Doc8OYc8VCsVgIB3DuHAERUAkAkHMOBwOAIisO4dwEAEAEg6AkAl4CeHA4AkKhXioBIOiv8OB3F+LgWkXxeF8XouxcF0XBeF8XBdF8XhdAdouC8AEsXYWsXOKsVBU/FaKmK/FTxXFbFUVPiv/8VfxXitVTEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVAXX/8MN/wwwYf//DDBh4Yf/+DYODDfCIFAwKBQiaANFgUDAgFAwIBcADwFuBY/At8C1wLfgAdAtwLGBawLcC3AsgWeBYwLX4XW8LrBhgiFgMlhYLrhh4XWhdb8MOGGhdcMP4YaF1gw8LrwuvhhoYYLrYXWDDBdcGwfC60MN/ww8GwZ4XXC64GZQsF1wbBnDD4XXg2DcLrBdaDYPhhgusF1uGGC64YfhhoNg0LrQwwYcMNC62F1/g2DYXW4XXC6+GH8DTIW4YeB/wR/wjwR+DP/y06bJaRAtAtAtAs//PkZNwY2grqBFW1xDhTRegABLBstL6BRaVAstN5aYtN/psIFf5aQtIgWgUWk8tImyWkQL9Avy0qbKbKbJaf02f/0CkCy05adAsC3K7+mx5aVAv0C/TZ8tOmymwB2pspsFpPLSlpy0qbCBSBXlpk2E2E2C0paUtP6Bfpslp0CkCy0ibJaZAosWQLLTJsemwgUmz6BZaYtOgUWmLTpsJsoFJslpPLT+mwmz//6Bfpsf6BSBSbCBXoFeWk9NhNhAr0Cy0/+WnTZ8tJ/oFf6bP+myWn//8tMmz/lpf//////8sPTEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVVBgJwiCYMBP//gwE//////wiCAMEgkIggIqIDKpUCIIgwE8IzA4j+EYhGAZMIyDJA5gGRhGcIyDIhEQiIGAgwIRDwiAGAAwYMHBghGAZMGRCMhGYRgGRA5gIyDIwZPBk/wZIRmEY/gyOBxIMgIzCMgcTgwRBgjwYJBgngwTwYJhEQBiBGERMIrwOouAxAgGCQYIhERhEQDBGDBAREgwTCIgGCQYJgwSDBHwiI4MEAwSBiRHCIkGCYGJEQiI/Bgn4REYREeBiBIH6XhERhETwogoy04mpZibFmJuWYmx8n//PkZN4ZrgrkAFZUyDcjofgAA9qc0HafZ8HwNrjaG3xtjaDtJwTo+D55Ow7fxNRNSyLPlqJsAr/lmA/FmWYm5acTQVgTkVQTnBOQTgVxVBOBVFWK4J3FUE7gBABOwToVQTsVhUFcVRXBOhUBORWiuKkVYrCqK+KoqgnQrAnEV+K8E7FQE5xWBOQToVQTsVRW8V4qRUFYVgTsVhXioKgrCqKwqAnYqCtBOhUioK+CcisK8VRUFSKnirFQVBVFcE6FcVoJwKoqCrFXFUE7gnAqiuCcQTsE7xU8VP8VYrgnYrCuTEFNRTMuMTAwqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqn3/l/J/8vHL5f/BhwCETgGpIDOATgEIOFoDcHzgEIDgC+pG7eve1S/6tat7rXupDXSStWoIpGS4TSMQNIySMwpIyAaRkkZ8GJGdBtbwYkZ63tCKRkoIpGWDEjLCKRmEkjOvsEUjJOsIpGa6bOrgxIzsEkjIGJGYUkZVKCKRmqoDkgEjMJJGSYMSMAikZJ3qhp0dq9ru3X1I36FujPbXuxwKbr//waABfg0AC6DUDUDXBqBoAmQawJiGrhqDUGoN//PkZL8USgKcAAblrroTgfQAbhtQAag1QawaYNIAuQa12Lv9si7F2NnXa2Vsi7i/DZl2tlL9l+vL8tlUTUTUTUYQDIBlGUAyifqJKJqJ+p5Mf/U7TETEU7CxkxVO1PKdqeU8u4vq2ddnrsbM2Zs/tl9s67V2NlbIu5sgaA1Q14ExAmAaA0BrDVDXhpgTPDQGuBMA0gTKGgCYgTHDQGrDUGgNcNQaw1hoAmcNIaRIAtALVEgC1gteI8FpBaBIAtcRwkAWgFpBaILVBaIkAWgRwjhIiPEeDXwa/4NUGr+DRwaKBgX/8IhP//wiF8GBMGBP/wYEhEKEQmEQuDAoGFTAwLgwLhG/4MvCM8GSDJCNwZQOXg2DwbB8MPDDhhuGGBsHhh8Lrf4MCQYFCIUGJgiE8GBQMIFhELDDhdaGHhdYGwaGGDDhhuDYMBsHg2D8MMGHBsH8GwYDYMhEKEQgMCeDAsIhAiFBgQIhAYFhEKDAkGBMIhYGEC4GFCBELgYUKDAuEQsGBQMKEwiEBgUGBcIhQYFwYFAwgWDAoRC8GBAYE4RCgwKEQvhEJgYUIBhQkIhAiECIUGBIMC4RCgwJCIUGBAiFgaYLwiEA5cGXCNhGBGwZAw4Ng0MPBkqNqcoqorlh//PkZP8cygrkAFI0wjo7lfAAjhq4SnPor//psoFemygWmwWm/0C0Ci05adApNn/TZQLU5RU9FRFVFdTn1OFG1Gywv/UbUa9Ff0VoFgCwBbAtQLUCzAtgAchHAN8ImAbgBvwDd4RwjQDeAN8I0ImEQEQAbwRgiQjBHhHAN0A3vgG6Ab/CIhEgG5AN6EQESAbmEYI4RABvYRoRABuYRgDehEQiAiIRABvcA3QiQDfwjwDfCICPCJCJCJ4RwiYRwjBEwDehEYRgDfCNCOEQEQEcI+BagAd4FrAscCxAswLfwLAFgC1wLNVMQU1FMy4xMDBVVVVVVVVVBlBkwO3wZAZMIz//////+DJwO0IwI0DsA7cGUI34Rn4RgMuEZgywZQjAZP/hGAcmDKEYDIB28GQGQGXhGgcgRoMgRoHYBywZAZYMmBygyQYFCIUDChQYE4MCgaYJ/gwKEQsGBAiEBgXAwgQLrhdcGwcGHBsGA2Dgw4YYGwZC6/hdYMMF14YcLrYYcMPDDhdYGweF1oYb4MCwYEwYEgwJBgTCIQGBIRC/BgQDCBcIhAMKFhEKDAsGBQYFAwgTgwKDAoMCQiEgwIBhAnCIUGBQiFwiEBicIhAiFhEIgIoBooGieDPA+4GeDPCP//PkZOobXgrkAEY0jjazieQgBKKIBGgyBGhGhGgyAdoMmEaDIB2gyhGwZYMsI0GSDIEaDKB2QOWEaDKB2hGgygcoHYBygdkDtgygcngyAyhG4MgMgRgRmDJCNBkA7QjAZIRoRoMuByhGgyAyAyhGwZYHKByAcgMgRoHIEbCNCNA7fCNA7AZAO0GSEYEaDLA7cDthGgyAyAyQjAOwGUIyDKEYEaEYEYEaEbA5AZQZAZAZIMgRkGXBlBkCMgyAyQZYMngcmEb8GUGQIyDIBywZAZOEZhGBGwO2EbwjcI3hGYMqTEFNRTMuMTAwqqqqqqqqqqqqqqqqqqqqquDqz8GMDQIvBj///+DGDEGAMAifBj8IuEThFCIEQGPCLwiQYBFCKESDCETBhhGAZGEZ4MjgcSEY4GoRQigxCIEQGPCKBiBrgxwiBEwMIRAY4MQNMIkImDEGIRYMIRYRAYhFBjwYwNQYgwBgBh4GAMQiwYgxA0gahFBgBjgYBFhFhFCKEWBgEWEUGEIgMQYQMYRAMQigxhFBh4GkIgRIRYRAY4RIMQYgYhFA18IoMQYQihFhEgwhFCIDEGIRQieBiEUIoRQYhEwYDAFwGrBog0waAaQawacGsGiDQp9MZMVT3piJjJjK//PkZOYZBgjoaUJQ0DprofQgbhq0dep71PqeU69TpMdMVT6n1OlPKdJjpiKdFZkxlPKdJiJj+mKp/0xFPBoAmYaw0BpwJhDSBMQ1BqAmIEzDSGgNXDRDRhp+GsNMNAEyAmYagJkBMA1BpDXAmECZhoDXhqAmYawJjgTICZBrDSGsNAExDVDQGqGgNHDWBMIaQ0BrAmcCYgTHAmIEzDWGgNYaOBMw0BqDSBMYaA04aA1hqwJhw0ATKGqBM8NYEz4aQJkGoNMNIag1+GsCY+BMQJlDVw0cCYfDXw1ho4aQ0gTCTEFNRTMuMTAwqqoD37wjuhHf/////hGf////hHf/gycEZwRn4MnYMncIzsGTwOdOA507A507hGeEZ8GTuDJ4Rn4HPn4Mn4RnhGcDJwM3YM3gzdBm6DNwR3QjvA927Bm//Bm7Bm/Bm8GbgZvBm4GboR3gzeEd+DN8GbvBm6EZ+Bzp4Mn4HOnBGfA588IzsDnTgZPCM8Iz+EZ/CM4DnTgOdPBk8Iz4RJwGTidBi7AycTgiT4RJ8GE6ESdhEngZPJwGTieDCcESfwYToMJ3AycTwiTsDJ5OhEnwibwYb+ETdwibwibvCJuwYb/4RN/Btf/jYGyNv8bHLUtSzE0/LQBW//PkZPQcHgjQAFKVrjfDmgAge1eMLITcshNy1LX8si0LUTQtCyLQTUteWQmvLItSz4mgmnTJops0umEymDSPsnR9HwfB8k7Pvn0fAvwtcLXF/F+L4vQtQWuFoC0BaReF7AeBfwtcXBdF4XheFwXAtAui9C1haRdi6LwvirgnUE6FcVBVisKgrCvioK+KoqiqAhh3AQFXhwVYrFYCWHMOioVAI4dFWHPDgCAdDgdDmHcBIBEO+HRWAiKsBMO4dFQCAdATw6HBWHBWAgHBUHMOf/gICsOCoV4qw7+KxWHA6KhVTEFNRTMuMTAwVVVVVVVVVVVVVVVVVVVVC11Fz1+NP+/Xkqpr9OfuhQ6lZNWf+XsZc/si+KNoOj/Ntai9j7IISgZAklTqQhjToNTeQoreRdaSDODWvYAVFaWWkQmVuZPP8bRUzq896vc+XS6mm5gnnW7JLnV4z9N5XnW4k86x+uyHyxnWkmdborzraff2SvOtvljOtjd/5XRW8MivOsry/K3gUzrFPS1eFTOttTsul1+lLGdbV3c53h51mdbK7xPOr1SpnWlM63kedZnV4XPK86onnW7myrnWTmRTOqascoabXd/L+yuzS95dv1efjhl92ntYyTuIb6wuWRZFmWRa//PkZOcb/gqMACDf1LTLofggA9qc/gKRZiblqWYm3E24moSQnR9BJj6JwfBaFqWZZFoWvLUshNPxNuWn6bTZHgozRTSaNAY5oGjFQVgTkE6FUVorioK8VBXBO8VRUioAhxVioK4qAnIrYrCqKsVRVBORXBORVFcVcVwTgVBUBORUisK4rCoKwrxXFYE4FYVATjFcVhX4rcVBVFQV4rxVFSKwqCpFcV+K8VhXFYV4qivFUE4FaCdivBORUBOfBOgToE6FUVYqisKgqCtxWFbFQVxXisKgqRU4qioKuKwqCvxVBkH/4Mgf4Rg4RgfgxZBizhFYEVuDFvhFbBi3CKyEVsGLf/4MgAxaDFoGsWYMWgaxYBrFngxaDFnCKwIrAYswNas+DFgRWYGtWYMWBFZgxbgaxbgxZCKwGLIRWgxZCK2EVmDFmDFgMW4GtW8IrAYtBi3Bi0GLAYsCK0IrQit4MWeEVgRW+DFsI9cDW9AYtgxZhFaBrVgMWgxaDFvCKwIrIGtWhFaBrFuEVvBi3hFZA1i0IrYMWYMWAxaEVkGLAisCKzBizCK0DWrIRg+EYIRgQjABkHwODABkDhGDwODBgyAEYIRoRvww4YcKILYKILfhRg5htjY4OQbQ2hNAH8su//PkZP8cRgjWAFAUYjwDofQAi9pcJry1E1LUsuWvE2E0LQTcB+E2LMTctQFEEcWnFYVwTkAI4rRWFYE6BOhWwToBBAQgnIrisETANwIiAb/COEQEbgG+EcA38A3wjhGCMEeEaEeKoJwK4J2KoARBVBOIJ1FWKoJ0CcCuCdRWFcV4rioK4rAnYJ2K0V8VYr8E4xWFUVBVFQE6FXBORWFSKorxVFcVwToE5wTvBOoqwToVIJziuKwJxAQgnUVBUgnAqQCaCdxWirFaKwrRWFcE7FfFeK4rgnIrAnYrCoKoqgnAqCrip4qgnCr/////8Iu8BjvP4Ry5/8Izggc4Zw4RnD/hGcP8GThBk4eDJwgycIMnDwjOD4MnBA3eu9hF3kGO8gx3vBjvAN3jvMGO8CLvAY72DHeAbvXe+DHehF3kGO8gbvHeBF3oG713gG713vgx3oMd5BjvAi7wGO9A3eu8BjvMDd470Iu9wN3jvQY7wGO9wN3jvIRd4Bu9d6Bu9d6DHeAbvHewi7yEXecGO9BjvQN3rvIG7zykGO9A3eO9CLvYMd6EXegx3gG7x3kDd470Dd472DHewY7yDHeAKomwmhZFkAof8tCy4m5aCbcTUswFYsy0/LQTX9NBqBgJgYaY//PkZPcaTdCuAFgWlkI0DfQAe9r8Jxz6CShr8++JsArgjxNBNC0LQsyyLMsgRxallyyLIsi0LUTUtS0AVwFPia8si0LMTXiacsxNizLMTYTcsi1LMsy0E2LUsuJqJvxNyy/E1LMteWgm4mpZlkJuWRZFmWnLQTfgncVATkVcVQTsVIqQToVxWFeK4rwTmATCsCcAnQJyKoriqKwrxWiqKoqwToVRWBORWBOBVFSKgq4qAnAqAnArRVgnIJ3FYVQAgCriqK4qYqivBOsVoJwKgJ3FYVBWFfBOIqCoCcivipxUFSCd4qRV4rCvFaCc/BOhW8VxWFUVhX4r1eX/+DHgaYMOEX/CJgwwYQY8GODH/wYf4RAiQiAxCKBj4GAMYRQiwYcGEGMIoMfhECIBgEXAwCJhEA1BjhF+BhCIDEGARYGoRAYgxCLwYeESEUGMDEIoGAMAiAwBiBrCJ8GMGGEWEUGIRYMAYAxCKBiBgBqDDA1wiBEBiDAIsGIMANANMIoMAYhEBh4GMGMGARfAxBj8GAMANYRAYBFAwCIBhgYhEwNQMYGIRAMAiwNYRQi4RQYwYgahEBiBqBgESEUIqa6Z6bTBpGkmUwmkwaZpJlNCkifmkaaaBymgm0wmRPxSDTTa//PkZOYYTgboZUAQIkCEFgAAe898YTZpJtMJhNilphNdMJhMGgmemDSNJNmkaX6aNJN9MJhMc0E2mzSNJMpg0umU2mDS5oJlNJtNJnpg0DSTBoml0wmzTE8TRpGgm00mkzzRNNMJg0jQTSb/NFNdNGl02KTzRTfTPTSbTKZNLphNJpNmgmUymkwaaaTKZ/TSYTBoJr/ifptMpr9MJlNptMpg0kymumEwmf+mEwmOmkymTT5ppv80eaHTCYTHTSaNBNJtN80/0yaBoJo0k2aPTPNA0OaSaTJoplMJv9MgBAB4MBUGwbwVwUBkFAYDAU4NBoMBqhAELk//h/y1/vz6/6zMnEglwqnIsVSHBzAMMIqXiLgxS8ucoLCKl7VwipeupwYpeAxS8Z22hSl4U+qE1L2goKUva1GUJqXqwkpeAxS81IQpS8smBqXql6E1LyEVLxdEJqXoSUvAmpeJhFS8rpH3CKl6EVLxPWFKXgDUvVL1LCal4kuDFL2EVLxFlPClLyEVL2FKXhdwYpeswGpeqXkGKXqLAxS8wYpeqWk8KUvATUvQYpehJS9Bil6yoUpegipeZT2x1vN5NR2X3KrRuXjFmaS0AUCyE1BHflmWZacTYsvy05ZgAIs+WQCqJoWZ//PkZOwZ+gaYBgbm6EQMAfQAe9r8ZCbiaFkWv5ZibialoArlryyDtCSnyfR8hrE7PpNmiaIxDTGKafTJalqJoJoWQm4mnLUtSzE1LUsxNyy4mpZlqJpy14mgD8WpagKvLIEcJuWnLItBNhNhNxNgH8tSyLMTflmA/iaCaFqJqWf4mxZFmCdCvFWKwqivgnQJzFfwToAIwqgnOKgqCoK4J2KwqisKwJwKwqCqKgJwK4rYqirgnIJ2K8VhVwTmKorYrioK4JwKwrRWFQE5FUE6BOwTgV4qYqiuCdAnYqAnMVBVFUVATkVBVirioKoqCviuK8VwTjxWFfivFUVxXBOcVuKvFaKoqiuCdeEW3Bjb/gxvhFvCLYGN8GNwi3CLf8DbtuEW//CM7+DG+EW+DG8DbNgi3CLfA2zYItwY3gbZuDG8GT/Bk+DJ+DJ/hGdCM+EZwMnQZP8GT8GToRngc6dBk4IzwZOBk8GTvhGdBk+DG8Dbt8DbtgY2CLbBjYGNwi2A2zYDbN4RbhFtA2zcDbNwNu3CLaEW4RbeEW2DG8DbNwY3BjYDbNgY3A2zYItwi3CLbgbdvA27YGN/CLaDG4RbgxsDG4Rb8ItsI7oR3cGb00m0wmTT6aTZoJgUpMc0kwaa//PkZNcYqeLUAAAUHjw8DgQAe88cYTXTXNBpXiQdfLVpLJoaCSJs0TQNAbRpJpDWleaGlDmlfQ9fQ7tBZoa0IeOw3Ccq44TcHgrufDVzQNA0Om0wmDTNE0EymkymumU2mU0aSY6ZE8NFMmiaaYNDptMGn+aJpdNptNps0OmU0J50wNtMGmmEz/zQTJpppNplMJlM9M8FAYCoKAAApgqDIMACwAQbBsAMGQAQaCkGg0GgyCkGeCgNABwAAUgrwA+CoAQNAeHCAOAeAyIQHBwgDg4QYDAGgMEEOEAeA0Q4DxAHh0PEMGYMBsFQYDPgoDMGQV4NBqryAZfhGfwjMDl4Rv/wjcGT/4R/CPBH//hH4H3gzv4M7A+/gzoR4GdBnAf+DPBnwj4M7BnwivA1QIpCKcIpgaqDFgaqBogMWBqgGqgxQinA0XBi8IqEUCKBFcD7/CPwj0I+DPA/8GfBnQZ8I+EeCPwZ4R6EfhGcI0GSDIDJCNCMBkgdsIwGQDkBk4HIEZgy4RoHZwZQjcDtCMgcoRgRoRgHKDJBl4RgHIDKDIEYEaEZgyYMoRsIwGQGQIwGUGUI3A5AZMI2EaDLBlwjQZQjAjAZQoqIqqN+pypx/qNIqoqqNKNKcqchwGrCAPqN//PkZOsaCgjiBUZRnkhEBfAgw9tw+iu1RUqp2qNUNalOVG1GkV/U58sLU48sLLCiwFU4hAYA+qQQgasVhVIYQNWKwmAKpvasNoHIDm4OQHIDlGwNgbH4D+JuWQm4mhZFoJsWgm4I4TcbA2xtA5ODnGyNoHJ+NgHKDl/LMBXLUtS04mvLQteAqCaCbFoWQmha8bQ2Acg2ht8bI2RsjaGyNrjYGwNvjbByja/GxFYE4ipBOIrwTiK4J2KuK8BAK4rAnAJwKkE4FYE7FUVgEIqgIRVBO4qRXFQVBVBOMVwCcE5itBOoqAnQqCpBOIrxVBOoJ0KwJxgnQrCuKsVBXioCcCqCdAnYJyKsVYqipBOgjwiAj/8InhH4RMIkIioAC4RiJCMRP////CNk//Blkv4M6F8I9C/8GWT/8I2SwjZOEbJQZZIGWShGyYMsnwj0P///CPQ/hGyQRsn8I2SCNkgOyVkoMsnhGyQMslCNkoMsl4MsnhGyXCNk4MsmEbJ/wZZP8I2SA7JWTgyyQMiKDIieEYiAcRIihGIkIxFwZEUIxEA4iREgcRYihGIoHESIkDiJEUIxFwjEQIxEwZEWDIihGIoRiKEYiHcYJRIr9kzDmO5cIaApXE/NA0TTTKZGym/0//PkZMQWjdCsAwAWCsQ8Cfwoe89cyaXTKZACgKxsmybQFQCyPQPUbRtA9x6QAoegHKmRtClpk0RSkwaCYE/GyaaZ6ZFJTaaTCbTabTBomgmk2mxS0x0waSaTI2gcgOcT0T5MiedM9M9NCfCeGgNhMJlNJoT1MdNJhMjaNPptNGkmhtpnmgmv+KQmRSE0aaY6ZTKZTRoJg0U0aBopg0emU1/xPzTTaa5o80DRTZoJs0TRNEUhMikJs0P0x0wmkwmUymkwaKb5pppNJg0OaInhpGgmeaSbTBp9N/ptNJo0DTNAbOCkGAwGABgA4NgwAAAMAPBgAQKAyDAZgoCkFINAABWAEDAUBTAABgMBTgqACDcGAAQYDAUqLUlVt///+Bj4MIRcGH/hE//hEwY/wi8GPBj8GH8IgRcIv4RYMQi+DAIsIsIn8GHCLCJCJ/BhBgBpgwwifhFhEhF/gwCIDAGIMIMAMQYBFhFBgDH4GAMQiQNAYAwwYBECIAMIAMAAiHBgAMIAiAIg4MABgABhADAhEAGAARCBhCEQhEIMABh6EQAwIMABgCEQwMAQYD4MDCIcGACIQiHwYGDAhEOEQ4MAENB8hJD7PsnXJ0fZ98+D6LItQR3PgnZHmgPYYRopk0xj//PkZMkU/gLsKkITrkL0CfzIetvgJtNdNGkmkwCPLITf8s+WpaFmApAjgFUsyz49jSTCaGMmkyaRZlkJsWpaiagKBZlmWomh8c+CdcnROj4Pk+Akh9E6DsJ0fJ8c+OTs+idk4J2Tg+j7Pr8nB9nwfAa59k7598+D74Sk+eEkFQdDmKsOhwBEVYCeAiAmAiHcBEV4CYCIWmCHF6FqC0haRcF4XYvxfF2LwvC+FqC0xcC0C/FwLSFoF0XBfi7C0xei8LwvcLQLoWoXheC0BaIWoXBdiNiMY6cZ8RkZxnjqOojAz4zx0HUdBGozjoMw6iMjpxdi+L4uC5F/xd/haIui/bZ/+DGBp4RYMQYgw8Ivgw/CJ4RfBh/hECKBgEUGODH8InhFwY4GoMcIgRIMcInCIBrBhCIESESBp+DEGARfhEA1gxBjBj4RAYQYgxgYwiYRcIgMAiAwBjCJgYAawYYRQMQMAYhFgagYhEA0AxA1A1CJBhBgEXwMQNAihFhFAxwYAwgaYMPCJA1hEBiBrwYQiwYwiBFwYhFgxgawihFCLA0gYAwCKESBoBpCJ4MIRcGOEVB9ALR8BJj7/PonB9HyEoJwfZ9E4Dt5OeTnlqJtxNAH7nwfHPonJ8E759E6PoJI//PkZOAWKgjoZUAQYka0Ffgge9VcfZOCdE7J0To+Cc8JWTo+D7J0EqPoO0+T5PsJSfJ9n0TkJUfJOeTknPPo+z6J2Ts+ycE759/k7Pg+z5J2TonBOeTkJUTg+T5DtJ0Tvk5Pjk5DXPg+idH2HYfR9E6PgnR9k5JyfZ9cnR9cNYJSfROidE6Ps+Cdn0fB8E6DtPs+CdHwfH5OAlR8nyfB98+SdE4J0ff5OidH2TknfPkJMfYSg+Cd8+D6PsnP/JwTk++fYSQO0+D5J0fZ8HwTjn2AWDgdAL8ApALQCkO8GACwMgyAVBiAVhzwY4BeDIMgFMOh0ArDgc4BeAV4cgFsOhz/wP225HGw7wCsO4M8AqHP/+DEGQ7/8OgzgFoM4Bf4MhwO4BT4cDmHcGfDnw78GcVhUFTFXFeKoqYJ3isKuKkVgTsVoqCsKwrCoKsVQTqKorYWoXhfxeF/xdi58XOL4WmLnFYVQToE4FXiuK0VwTnFQVRVFcVhU4JzFfDgdhzBnDuHfALgyDAMQZBgGQCgBUAoDAMh3BkAsDMGcGAYgxBn+HAC0ArwCwMAS5adAry0yBSBfoFBVaKqKwVUVrRXUa9FUOEqVUjV3ySPLkvkzlNlNn02E2S06pvVKWACECpW//PkZN8VDgj+zymq5krb1ewAxJvkqe1XysDVFSqlVIo2o0ispyFVoqKNIrKcqNKcKcFhSjaKiKyKhYWiv6nCnCKijajSnPorKNKcKcqcIrqNoqKNqcKNoqKNKNqNKcIqKN//orf6BSbKbKBSBXlp0CvQKTYTZ/0C/LTxFxFoigioigCLC4cRYRWItEXC4cRcI0IoFwwXCiKwjQXC8LhRFcRcRQRSFwoikRcLhxFRFguHiKRF8E7FYVhVBOgCYVRUACKKgqgE0E6ACJFYVoJ0CcgnQrAnMVwTgE6ioKoRwDdhHhG4RARgDcCMEaEYIiAbwRIRuESEcIwRwiYRwjQjBEgWoFuBa4FngWkIQQigwhEwigx/CL/4RfCKEQGP/wYiEUwYHwYGDAwYAGA4RCEQBEGB0rCNcGU4RrBlcIsGGDCESBiEWDCBhCIEWBiDAGHCKEUGEGEGMIgMAiwigxCIDADQGIRfCIEQDXgYYRfBgBoESEXwY/CJCKEQIsGIRODEGIRQYgw8IgRYMMGAGGDEDGDAIoMcDQIkGMGGBrgwCKDAIsIkDUDWDGDDA1hE4RQMQYgwBhhFBhBh4MAMYRMIoRAiAxBjhF4mhZFpxNBNy0LQTUTXhKglR9FqWpaAP5Zi//PkZNYXkgTkGEJwlkXcDfQAe9r8bCa/k5JwTriacsizBHE6CSH1w7efHLPgjuJqWnLITYTX8tRNAH/ialqJuJuWZaFkCO4m5aE7J2TonB8E559nyEnJyfB8FmJrxNxNhNS1E3LT8sxNC0E0LTlqJuWQm3E15ZiaFqWRZiaCagP5aCblkKoqCsCdioKoqwToE6FcVwAhRWBOIrCpFbgnEVRVFUVIJ2K4rAnIqioKgrcV4riuK0E4FcVQTsVxUBOoJ3FUE7itFYE7FYVRXFUVwTkVoqAnYJxBO4rReF4XYWsLWLoui4LwuC9AdsLQL0LQA9C8Lgvi6LguBahfFwXAtIvQQ4vCqKwrxX/8E5xXit4JwKzBnwj+DF+F1guvBiQYkDRIR8I/gz8Gd4R/wYsGLBi8I8DPBneDPBnwZ/8I8DP8I8B/0Gd+DPA+8I/+Ef+DOgzvwj4R/hHoM+Ee4M4D74M+Ef/A/7gfcDPBn4R7BnfCPgz4M7Bngf8DPwjwR6B/3CPwj0Gfgz+EfBnhH8GfwioRQGIBqgRUDVAYoGiBFcIoBqoMWEUA1UDRYRUIqEUA1WBooGiAaqBogRUGJwigM8I/gz+DOCPwj/Bnwj3BnBHgZ4M4I+DOhHhNCz5aiaCb//PkZM0YugrgAAASBEUEBfQAe9rclmWompZ8shNyzLMsy1LMBUAfhNxNicBJCcE5J0JpyyLMTcBVLXloCO5aCagKACiAqflqWYmvE3E3BHlmArFnxNRNC1LUBS5aFoWhZFmWR8k5CTnxz4PonZ8E5Pg+j4LUshNQH4TYtSzLMshNBNhNSyAViyE0FQE4FYVoJ0CcRUgnYqisKoJxFWAhiuKgrCtFUE7FQE5iqKgqRVFWKorwTsVxVFYE7FTFTBOxVgnAqRXBOPisK4BOK0VoriuK0E5BOxUivFcE5FYE5FQVBXFQE6FQVBVFYVxWioCcCoLovi4LoWiL0LSL+LouBaIvBahdi6LoWgEMFoC1hacXxdC14uipiqKn8VPFbiqKsVb//Bm/+EXgx4RdhFb///hE1/8GLP//CKwIrQjoGa/wPewZqEdYM1wZqDNAetgzWEd8GagzYM2DN4M1wjoI7Bm+DNhHcD3rA9awZuEdwZvCOwjsGaA964M1A978I7hHQR0DNge98I6hHYR1wZrBmwPWgPWwZsI7wjqB63COvgzQR3BmwZsGaget8D3rCOgZoGa4R1gzYM0EdwZsI6CO+DN4R1wZsD3oGaBmwjqEdAzXCO/4R0DNAzYR0WgI8TQT//PkZL4XFgTcAE6Tekf8EfQAe9t8YTXiagKwCsJuWXLMTUtC1LMTT8TYTUJP+fAdomomoD8JuWoCgWoI7iaFkJqJqfZ8hrk4CSE5Pk+Cdk5PgnBOT7JwTg+uWZaiaCbcsgFAsy0LPlmJsWYmom5ZFmWpaibiaAjy15ZFkWQm5Zll+JsJqJvy1E0CThJz65OQlJ8H2TknZOT759k6J3z4PgTQTcsy0LITflmWfLUsiyLITYTcTT8syzLQsi0LMEcWZa8tSyE1LTibibFqWYI/8shNCyE1LQsy1LUTUBTLLloWXLUsiyLUtBN+JqJvy1E05Z8TcsxNeWn/LTnxydHwfZ9n1ydk4PsnHJyfR9E4JwTgnB8k4588nB9cnR8c+D7PjxV4q4r8VsVgTgE4/itFShX/xV4rirxVFWKoq4qfxX8E6xVxUBOYrCtwTvwTj/xV8E5/irBOsVuKwqRXFTFQVorCrFf/xWioKwrCtioCcxUFUVAToE4FUVxWFQVhUBO4rgnUVIJyKkVgToE4ipFUVoq4qisKsE64qCoCdxUFUVxXFUE78VxWFQV4qiuKgqiuK4CCK2KoqirBOATsE4FWK4qxVFUVhVFYE4gnIriuCcQTiK4rCoCdCtirBORUiqK2//PkZLAWsgzuADQNNkZj/fjIe9r8K/FXFUVwSE5aiaAKgmhaFpxNQkp8n3z5PsJOGsffLQsyyE1LItS1E1Af+CPLMTUTfibFmJoWQmxanzydHwTsnASknBaCalqWom5ZcsxNyc8nASsnHDt59BrhJgkpOicBKg7D7JyfZ8E5Pk+Cd8EeWXE0LQsiyLQtSyLIsi0LUTYsy0E3LITQshNiyLUsiy4I78shNy0LMVcE6ipgnArRVgncE4wTsVIrCqCcCtBOYrwTgE7iuKwqCoKwrcE7BOQTqKgqiqFpBEC1gPYD3F0X4WoLTF0Xxei6FqC0BaAtQWgXxdwtWFqC0haBW/FQVRUBOBUFcE6ioCcAnEVRX4rYrxWFUVBViuLsXgtAuC6FoBDBaYvi7F8XxexdF8XhcFyI//gx//CJBhgxhEwY/+EXwYgx8ImDH8GPhE8GPhEwigwhFwNQNQNfhE+DGDAGHCIBrhEhEBh4RQMAY/gYBFBhhEAxCIDCEUDEDHBiEUIoRQY+DCBp4McGEIgGgMcGIGMGIGIGgRQYgxCKBiBjBgDGESDADWBgEUDEIuDADWBpBiEUDEDDgYgwBgDEDQDUGIGgRAYQYAxCKBhCKEThEBgBiEUGIRQMQiQiwNAY//PkZKwWNgjoZUAQREakFfQAe9t8YMfwi4GBaFoJsWpaFlyyBHctCzE25ZiaFoAocsy1E24Sk+idk6LMTYTYTQsyyLUtCyLQTUswFYJMGuTg+CdHwErPotAFAsuJtxNCz4m4mvBHiafloWZZ8YppBqzTNI0jTNBMpg0jTTZZFqWfE3Af/xNf+WomhZ8suWZZCb8tC1LQtS14m/LQtBNhNOfR8hKCc8nHJ2EqCUE5CSHxydk6Pg+D7Ps+Sdk6LUTQTcTcTctSz4mwmha8TXlmJvyy5ZiaFl+WpZFoWZa8TQshNCz4mhalmJsWfLQsuWRactBNRNC1E1LLlly0E2PgJWfR8h2H2fJOSdnx+fXPs+T5PgNbnzz6Po+T7JwTk+z4PonROglJ84qCvxXxUirFX4q4qeCd1f8InCI/4R/CP//4RMI/CMEYI4RAR8I8IjCJ4REI/gG8ESEfCOERCP4R/+EaERCPCNCNCI4BvQjYRMHINobIOfjZBzDZG3xtja42gcw2PwcvG2Nng5+NgbY2wcn42htDYGwNrjYGwNsbX42vxscbI2gc3Bzg5Qcg2uNvjaGwNgbI2+Dl4OUbI2Qc42RsjbBy//g5eAboR4RwjQDeCP+EcI8IwBuwjwjBEBEA//PkZKoZMgrqADXt9EV8FfQAe9scG+EThEBGCMESESEbhEQjwjwjwjBHCIwiQDcCNCI/hHE0/LQBSLITf/lqWoSvk7LT8BWLMtRNAk58E7Po+CyLQTcsyyLUsi0Afi1LUTUTUtBNS05aFkWpZHzwkoSgnB9HwfQSsTYsizLQtRNRNgFf/8NcJWEmAWCdn0To+T4CUHyGtxNiyLT/lmJqJqJqWgmpZFqJsWgmwmwmwmv5ZiaibiactBNv/yyLTiaFkWZagKgCmJqWQmgmom3E0LTibAKfE0LLiuKgrYqipBO4JzFUE5BORXBOhVioKwqRWBOYJyKkE6BOIrYqCqCcxVxVFUV8E4FSCdcVgAgCuKkLTBDC6CHi+LwuRfha4uC+FpF8XQtAWgB2wtAWoXBchaBfwtQWnFSCdxVxXFUVhUFYV4rgnOK3wTiKir//1eDCsf//7/+wOYd///ei/f6lmiqGXpyOlCSRm+yP3UtDhFIzqBhwCFHAGFHABYGcAnAAMOAAYcAAw4BYKOAIROANgicAQYcABE4AAzgE4B+1wmcAwYcAK+sDOADgFLCJwAlWBnABwABnABwDWraETgEDOADgDBiRkgoJpGaP+64RSMoRSMGBiRkkwMSMlq7AxIy4//PkZJUYEgieBFw3fkL8FgAied8AROAAicA8DOATgAGHAAROAFwM4BOAGBhwADDgAGHANQUcAgicAYROAfgw4AIkbPNNNpo0TSNBNJgUs0l7lomEwmRSOaRp80zQFK4nvTInnTEGgwGwAwYmOmkyaInppJpMJg0k1+KUaaaTCb42U0mBsGiaSaNNNpo0OaKZNE0jTTSY5oGmaZpJrphM9MGkmDQTCZBzpg0OKR+mBtdMJobfTSbTfG2KSaBpGgaZomiaBomn02mOmkwmjSNPpjmmaKZNA0zSNFNCeClJkT40DTTCY5oJk0kyaJpdMppMdMdN80TR6ZTXTfNJNmimumem+aKZTKaTaaTRops0kwaZppk0+aSa6ZTKYTfTfTPTHTKa5omgmEyaJo9NmkaRp/pnmmaf6aNNMplMplMdMpv8TxMJlNplNJn9MdM1OM6hWa/EjiRiRiO/xIcSP+GgCZQ1+GvDRDV//4aP///4I8EwTwTBLwS/4AHgnBHBHgiwR4JgAgS4IwAYPA8ADg+AAAAFwfB4L4WADC2AGAGFwsFwuD2FgAcAPC+F/B8L4X/B7B4L4WwuD+AGDwXACAAC2D2AEFgfCwAAAAXB8LhcLeDwXCwPYPA+ACACD+AH4XwA//PkZJMVIgb2ejVLxkBMEgTIe898vwv4X/ACB/C4WwtgBg/gA4PA9gBeFhLRTXTRomkmP0yJ6J+mPy0NA0OvibNDQmfzTNNMjaFKNATxMiemiaRoGkmE2mkwmTRTHNBMGiaCZTSZTZomh0PaV9fX2hoX14kzSmOaCY5pJr80U0aPTfNFMps0jSNJMmiaSaTKb6aTKYNLphMpg0eaJoGkJ8aBoppMppMJg00xzTTabNFNJpNJpNpnpg0DT6bTRoppoJMWrSh/690NQ5paV5D2le/LND2hoQxDF/rzR1/r/aWhfaWleQxfXmheaevNDS0tLQ0ryGoch/X15D+vLy+0oc0ry/19DUOXuvIc0oav9oXkNX1/r3X18QCGHQGQHB8PiAPAaHgPDsBgcH+Hqv//+DJ0GNuEW4MbQY2wZO4Rn///4R34R3Qjv//8Gbv//8GbgZuwZvCO4GbsD37+Ed3A586DJ4Rn8DnToRn8GTsGT8GToRn4HOngyf4HOnBGeDJ0IzgjOCM8DnTwjOgydwOdP4RnYMncDbtwY2A27cItgNs3CLeDGwMbwY3CLaEWwRbQi3A27eDG0GN4G2bBFvA2zcItgY3A27YGN4G2bAxtA58/gc6eDJwHOnAyfCM7A588//PkZLMYngTSAFAUhEYcDfggw9V8DnzwOdOBk8IzuEZ4MnAyfgychsy7F3oE13Nl9sy7myrsL6qqOU5anCqkHqNDI3KVUTEU8GNU7U6gxyHKchyVV/Xa2Zspfds3rubK2VsjZmztk9djZi/TZV2NnXYu0vsm0yaBoDZNEbA2k0aKZNjmwbH/Nvm2bRtGyBVArD1G0bQPUevg9h6/+PRzZNkCrzZNgesegesermyPQPXzbHpNvm0PQPUPSbJs82TaHqAtj082h6DaNn8eo2DbHrHpNjj1m1zYHqNo2jYNnmwbPNgek2ja5sj0D1m0bJs80xtJlMdMCeps0U0meaaa6b5p/mmmU3zRTfNFN/pgHPzQNPmkaaaTXTKbTRoJk0zS6b/NBNdNJlNGj02aH6Z4IgAEEYJ4I+CMEQJcEUEXBOpHDX/w1Q0Q1Q1cNWGvw0Q0/hp/AmIaA0cCYhow0w1/w0hoDRhpw1w1BrgTLDXw1BrhrDQGgNQEzAmOGuGiGgNfgTHDQGoCZw1w0BrDWBMIEzDVDTDUGgNfAFwGjBrg1A1YNcGsAXAaQaABcBqg1g1QagaINYaoEyw1Bow1YaA1hoDSBMw1gTICYQJkGrDQGoNGGmGsNcNQawJkGuBM4aIa//PkZKAYngTsATQNQkKMFgDifh7AYagJkGsCZ+GkNWGiGsNAaYaOGgCYgTENQaw0Yaw1Q1Q0/hqw1BkAfmkaCb6ZTCY6bNBNLwmqGA5fzSTCb/lgwXOmMmMmTQ5pmkmUwmkymfzRTaZFJ6aNM002NrppMpg0kwKQmE2m0yKUaKaE95o9MJj9N9NJk0UymOaSaE/TabNNNmmmk2mU2aHTKZTCaNDjaTJoGimkyNvpk0TRTHTf6bTRopg001+mU0aXNBMjYNA0k2mjR5pfps0U100mUz0ym02mDRTHTaaTRpplMc0kz0ySA000mDSTKbNI002mzQTHTaYTf//NA0zQTBodNJk0UyKSaaZNJMprmkaP5oGmKUmEymUyaCbTHNBMJlN80DRNBMmkm/00mUwaZpprplNpvmiaJo80+aSa//TaY/6ZaH//wY4ReEX4RIMfwiAxBh/BhCJ/+ESDDwYcIn+EQIoMAiQYAw8IsGIGGETBj4MP4MQiAxhEAwAxAxBjBiEUIgMIMcDCDAGIMcGMDEGGBgEUDTwNfAx4MAiwiQYgwBgEUIgMIRAiQYhFwYAwBhA1CKDEIoGoGgMQi4MeDADGEQIkIgGMDCDAIoMAiQiAYwYYMQNQigwCJBiDEGAR//PkZJwV+gzoZUAQRkZsCfQAe9tcQYgx8GEGIMQiwMAY4MANAMcIoMSyE1E1/LMTUtCz5OD5PkJJz5Pk+uA/FqWRaib8TUTUTQtC05alqWYm4momgI8TYsxNCzE3LUtQFEsgFM+z6J0fB8nz+TsNcnB9BK+Tk+g7CdhJT6LTiaFqWYI7lqWoCvy05ZlnxNy0/LUshNBNvwR4moCj+Wv5ZlkJqWpalkCOE1E1LQsiyLITUBULLlqWZZlmJsWhZcsi1/4CiWpalkJqJuWvLXlkJsJpyy4mwm5ZlmJuWXLUTQteWgmgmwCsJoWfE3LMsiyLP8TTlkJuAolqJryzE1LTibCbloJuWhZFoWYmxZ8TcXIvxeC0i+FrF0B2i4L8LSL4uBaIDyCHF8XRfF6FqgiwtIuYWoXIrYqAncVvxVip8VIqCvUxPqtFbFf/4q/8V/8V/iririr8VATjxXipxWgnYq+KwqfFTFQVoq+KsVBW8VhUisKuK0VRXivFYE4FTBOhVFcVRV4riuCdCuCdCtwTiCcCqCcgnYqxWFcVIJwKsVxWisK8VxVgnArYqRWFeK0V4qgnQrgnYrfBOoqAnAq4J0CciqKviuK3BOoJyKsE4FUVoqisK2KwrAnArgBAFSK4//PkZJ0VagjyJgGtbkOkFfwAbh4AqRViuK4J0KgrCrFUVxVjOEeDQgjlcT4T40xtjaNI0uaI2QciGNJIl4kJZtCGIaSckIm6GL3TQ2zRTY2UwmjSNE0fxS02aY2BsJs0/+mzSE8TQpHTKZTaaG0aQpApZp/mmm+aHTfTCZGwNgUk0E0aRp/ilJlNJgT5MmkJ9zQNA0TQTH6ZG2mk0mk0mk3zQTKY/G3zQTHTabTBpGgmU0aKaTJpJpNJtMphNjYTfNE0emkymE2mEyaCbNBNfmkaJpdNJnpg0k0mDTTCbTaZTBp80k2aCbTfNE00yaCb6bTZoplMmmaKZTKaTJpmiaHTXNJNdMDbTab6Y6Z6ZNNMJhMJhN9NJrmim02m0ymU300aPTSa6Y//5pJpNdN/9NppxWxU4Jx/xWxU/xU4qcV/ipiuKwqxWFbioK/8VIqivFTwTkVgTkVor4JxFXxX8VcVxUFQE4FaCcwTvBOIqwToVuKwqCtFUVYqxXxWFeK0E7FQVYrwTnwTsV4rCqCcfFaCcRVFQE5gnAr4JyK4r4Jz4J0Koqip4rCvisKoJ3xWBOME7iuKgriuKgrAnAqgnArxXiqCcgnYJzFQVQTmAEIVYrwToAIYrwTiKkVQTkE5//PkZK4XGgzsADQNAkIcCfwAe898FXFTFYVxXxUFQVkzzS5oilg5TTTY2kyaJpJtNGgJ8aKaTQ2k2aCaNI0eaSYNJM/tKHEiQxfJG09MmgaSYTRppg0TRNNMJlMptMCfppNprmiaA2jQNLmmaRppk0jRFLNP9NJhMGmaQpZpJhNCkGkmE0J+mxSk0m0x02mjRTKbTCZ42EwaQpJpptNprmgaRpmim00J4mzSNM0kymkyKWmE0muaKYGwNjmgafTPNE0U0mUwJ7+mem01+J8mhSk2mjRTSb6aTCbNBM9MpjpjppMpk0E2mE2mE2mjSTfTaZ6YNFMcT1MpnprptNmim0wmE100aSaTRo9MGjzTTHTaaTfTSaNLpoFMAAFYNBQGwAvwAYM4NgqqkjaAN3d3d3etERNK5CZ/3Ctf8AaiAc/u7vaIn/+8XPv//+GDKv8W1/+AC8K4Yw+94xe940jgceOBwOcRaxGwH4EuNIjBfxazB8HhkwVjIAII/vDkjG/wAcJPeF5e98hNgSYLWMFvhZ73pX06aoehpmh01hMJtM9K3TKY9E10x+memvlIpZM9MUTCbShbUrtM+n5ophNZuXK+0wmemumMCemkmvc0Uwm/lJJlLe5oJZI0SyW6R0mE//PkZLcXlgrzECFvrEmMEfgAfh7k2aORpbTYUo9SBNsekGYNVKgqg6hrUXK4Q42DOwPWbILEzBSumEgkWJPU8S72G5wUuGDwguEKa1Q0UykS3Bn7FJNgg5qoEgpsGabBtGabZt7NrKCNgLYes1idmabBtm2bZsBRm0PQbF9mwPSPUBbMwzB6QojYM42ePTzbMwzR6TaNs1eBVNv81yDUJ2QYhJtD0m0PVe+x6T2M8HsbFwsh6B6D7HpugT2uZhO6HqZhmE7ISZhhkFNcghtmybBnnsD1Ho49ZtG2bZs5PVBc2EEepCaEHNjj1EJHq5smabZtmwbNEEPR+bJse9z2PY2T1SN0rdNJq6yXI06COvNJj9MGmkkGmUj7pI0TToNGlEsNEnCW10ymi5FtNFKpvps0kz0uW3aaTF00mTSzdNJu0KFYz1KjaGAhyoQCFGM//zFARgIUZ/oZpdCttay6M5drW1joSj6Ekk1b7J7R7aoQNlPrWBKLTTzOsGT5yYxLrtHTy2iUxEkES0HIBVD066VjGj31odPkkSjJtbWs6OIjKmlXHR9K5MTv5pdZolE10kk2lly74TFx5KTXeTGLrS6ExiucvJSSDUdXZm3NLn1rtYBCUEoRj77WOhCPc+g5//PkZJ8XPgzoAABMTrz8BfQyY9ONGxJULutaa1TE5LHxWEo+XecmMa3p0kqBCR6qOFaFUGpNJIIjqVi0ylBqJIkiSJIkmJj605EkSRJPetWAyVacmodBUVhGfrAZPF0AUrgRLNiqDU4VHo8h6HRObJJNdqlRBMFT0qdNE0WZhmay2tCuOY6oieWieianCnVSbzjd7SGhrLi6eUSeLcZTWqdML5PM50uOGJms9VqtYU6hrgrm4V0frUzTvYsiegR2FOkqUxSKdVCpmRCSkIAUZ4VPhqEKgFDU2VhUiIhUaIg0RAkTBYEjYBUbBMuCJgLAkCUBUinABgBE3/9EQqFRM0s0qzktk9ZFqqFC6RULE11JVDkd8Y1vVFOx9oUIpZ8Vnq5klkxBTUUzLjEwMKqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqqq\";var I=A=>A instanceof Date,Y=A=>null==A;const D=A=>\"object\"==typeof A;var F=A=>!Y(A)&&!Array.isArray(A)&&D(A)&&!I(A),T=A=>Y(A)||!D(A);function m(A,o){if(T(A)||T(o))return A===o;if(I(A)&&I(o))return A.getTime()===o.getTime();const e=Object.keys(A),g=Object.keys(o);if(e.length!==g.length)return!1;for(const t of e){const e=A[t];if(!g.includes(t))return!1;if(\"ref\"!==t){const A=o[t];if(I(e)&&I(A)||F(e)&&F(A)||Array.isArray(e)&&Array.isArray(A)?!m(e,A):e!==A)return!1}}return!0}const V={facingMode:\"environment\",width:{min:640,ideal:720,max:1920},height:{min:640,ideal:720,max:1080}},Q={finder:!0,torch:!0,tracker:void 0,onOff:!1,zoom:!1},R={width:\"100%\",height:\"100%\",position:\"relative\",display:\"flex\",alignItems:\"center\",justifyContent:\"center\",overflow:\"hidden\",aspectRatio:\"1/1\"},d={width:\"100%\",height:\"100%\",objectFit:\"cover\",overflow:\"hidden\"};function N(A,o){for(const e of A){const[A,...g]=e.cornerPoints;o.lineWidth=2,o.strokeStyle=\"yellow\",o.beginPath(),o.moveTo(A.x,A.y);for(const{x:A,y:e}of g)o.lineTo(A,e);o.lineTo(A.x,A.y),o.closePath(),o.stroke()}}function f(A,o){for(const e of A){const{boundingBox:{x:A,y:g,width:t,height:i}}=e;o.lineWidth=2,o.strokeStyle=\"yellow\",o.strokeRect(A,g,t,i)}}function u(A,o){A.forEach((A=>{const{boundingBox:e,rawValue:g}=A,t=e.x+e.width/2,i=e.y+e.height/2,w=Math.max(12,50*e.width/o.canvas.width),n=w;let a;o.font=`${w}px sans-serif`,o.textAlign=\"left\";try{a=JSON.stringify(JSON.parse(g),null,2)}catch(A){a=g}const B=a.split(\"\\n\"),r=Math.max(...B.map((A=>o.measureText(A).width))),s=B.length*n,C=t-r/2-10,E=i-s/2-10,c=r+20,h=s+10;o.beginPath(),o.moveTo(C+8,E),o.lineTo(C+c-8,E),o.quadraticCurveTo(C+c,E,C+c,E+8),o.lineTo(C+c,E+h-8),o.quadraticCurveTo(C+c,E+h,C+c-8,E+h),o.lineTo(C+8,E+h),o.quadraticCurveTo(C,E+h,C,E+h-8),o.lineTo(C,E+8),o.quadraticCurveTo(C,E,C+8,E),o.closePath(),o.fillStyle=\"rgba(255, 255, 0, 0.9)\",o.fill(),B.forEach(((A,e)=>{const g=i+e*n-(B.length-1)*n/2;let w=t-r/2,a=0;const s=[...A.matchAll(/\"([^\"]+)\":/g)],C=[...A.matchAll(/:\\s*(\"[^\"]*\"|\\d+|true|false|null)/g)];s.forEach(((e,t)=>{var i,n;const B=e[0].replace(\":\",\"\"),r=A.substring(a,e.index);if(o.fillStyle=\"black\",o.fillText(r,w,g),w+=o.measureText(r).width,o.fillStyle=\"blue\",o.fillText(B,w,g),w+=o.measureText(B).width,a=e.index+B.length,o.fillStyle=\"black\",o.fillText(\": \",w,g),w+=o.measureText(\": \").width,t<C.length){const e=C[t],B=A.substring(a,e.index);o.fillStyle=\"black\",o.fillText(B,w,g),w+=o.measureText(B).width;const r=null!==(n=null===(i=e[0].match(/:\\s*(.*)/))||void 0===i?void 0:i[1])&&void 0!==n?n:\"\";o.fillStyle=\"green\",o.fillText(r,w,g),w+=o.measureText(r).width,a=e.index+e[0].length}})),o.fillStyle=\"black\";const E=A.substring(a);o.fillText(E,w,g)}))}))}function p(A){if(null===A)throw new Error(\"Canvas should always be defined when component is mounted.\");const o=A.getContext(\"2d\");if(null===o)throw new Error(\"Canvas 2D context should be non-null\");o.clearRect(0,0,A.width,A.height)}function k(e){var a;const{onScan:B,constraints:r,formats:s=[\"qr_code\"],paused:C=!1,components:E,children:c,styles:h,classNames:q,allowMultiple:l,scanDelay:I,onError:Y,sound:D}=e,F=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),T=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),N=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),f=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)((()=>({...V,...r})),[r]),u=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)((()=>({...Q,...E})),[E]),[k,K]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!1),[y,L]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(!0),[J,x]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(f),U=function(){const A=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(Promise.resolve({type:\"stop\",data:{}})),e=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),w=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),[n,a]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}),[B,r]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({}),s=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((async(A,o)=>{var g,t,i;if(!window.isSecureContext)throw new Error(\"camera access is only permitted in secure context. Use HTTPS or localhost rather than HTTP.\");if(void 0===(null===(g=null===navigator||void 0===navigator?void 0:navigator.mediaDevices)||void 0===g?void 0:g.getUserMedia))throw new Error(\"this browser has no Stream API support\");const n=await navigator.mediaDevices.getUserMedia({audio:!1,video:o});void 0!==A.srcObject?A.srcObject=n:void 0!==A.mozSrcObject?A.mozSrcObject=n:window.URL.createObjectURL?A.src=window.URL.createObjectURL(n):window.webkitURL?A.src=window.webkitURL.createObjectURL(n):A.src=n.id,await Promise.race([A.play(),new Promise((A=>setTimeout(A,3e3))).then((()=>{throw new Error(\"Loading camera stream timed out after 3 seconds.\")}))]),await new Promise((A=>setTimeout(A,500)));const[B]=n.getVideoTracks();return r(B.getSettings()),a(null!==(i=null===(t=null==B?void 0:B.getCapabilities)||void 0===t?void 0:t.call(B))&&void 0!==i?i:{}),e.current=n,w.current=B,{type:\"start\",data:{videoEl:A,stream:n,constraints:o}}}),[]),C=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((async(A,o)=>{A.src=\"\",A.srcObject=null,A.load();for(const A of o.getTracks())o.removeTrack(A),A.stop();return e.current=null,w.current=null,r({}),{type:\"stop\",data:{}}}),[]),E=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((async(o,{constraints:e,restart:g=!1})=>{if(A.current=A.current.then((A=>{if(\"start\"===A.type){const{data:{videoEl:t,stream:i,constraints:w}}=A;return g||o!==t||e!==w?C(t,i).then((()=>s(o,e))):A}return s(o,e)})),\"stop\"===(await A.current).type)throw new Error(\"Something went wrong with the camera task queue (start task).\")}),[s,C]),c=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((async()=>{if(A.current=A.current.then((A=>{if(\"stop\"===A.type)return A;const{data:{videoEl:o,stream:e}}=A;return C(o,e)})),\"start\"===(await A.current).type)throw new Error(\"Something went wrong with the camera task queue (stop task).\")}),[C]),h=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((async A=>{const o=w.current;if(!o)throw new Error(\"No active video track found.\");{A.advanced&&A.advanced[0].zoom&&o.getCapabilities().torch&&await o.applyConstraints({advanced:[{torch:!1}]}),await o.applyConstraints(A);const e=o.getCapabilities(),g=o.getSettings();a(e),r(g)}}),[]);return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>()=>{(async()=>{await c()})()}),[c]),{capabilities:n,settings:B,startCamera:E,stopCamera:c,updateConstraints:h}}(),{startScanning:Z,stopScanning:W}=function(A){const{videoElementRef:o,onScan:e,onFound:w,retryDelay:a=100,scanDelay:B=0,formats:r=[],allowMultiple:s=!1,sound:C=!0}=A,E=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new barcode_detector__WEBPACK_IMPORTED_MODULE_2__.BarcodeDetector({formats:r})),c=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null),h=(0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{E.current=new barcode_detector__WEBPACK_IMPORTED_MODULE_2__.BarcodeDetector({formats:r})}),[r]),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{\"undefined\"!=typeof window&&C&&(c.current=new Audio(\"string\"==typeof C?C:G))}),[C]);const q=(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((A=>async g=>{if(null!==o.current&&o.current.readyState>1){const{lastScan:t,contentBefore:i,lastScanHadContent:n}=A;if(g-t<a)h.current=window.requestAnimationFrame(q(A));else{const t=await E.current.detect(o.current),a=t.some((A=>!i.includes(A.rawValue))),r=t.length>0;let l=A.lastOnScan;(a||s&&r&&g-l>=B)&&(C&&c.current&&c.current.paused&&c.current.play().catch((A=>console.error(\"Error playing the sound\",A))),l=g,e(t)),r&&w(t),!r&&n&&w(t);const M={lastScan:g,lastOnScan:l,lastScanHadContent:r,contentBefore:a?t.map((A=>A.rawValue)):i};h.current=window.requestAnimationFrame(q(M))}}}),[o.current,e,w,a]);return{startScanning:(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((()=>{const A=performance.now(),o={lastScan:A,lastOnScan:A,contentBefore:[],lastScanHadContent:!1};h.current=window.requestAnimationFrame(q(o))}),[q]),stopScanning:(0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((()=>{null!==h.current&&(window.cancelAnimationFrame(h.current),h.current=null)}),[])}}({videoElementRef:F,onScan:B,onFound:A=>function(A,o,e,g){const t=e;if(null==t)throw new Error(\"onFound handler should only be called when component is mounted. Thus tracking canvas is always defined.\");const i=o;if(null==i)throw new Error(\"onFound handler should only be called when component is mounted. Thus video element is always defined.\");if(0===A.length||void 0===g)p(t);else{const o=i.offsetWidth,e=i.offsetHeight,w=i.videoWidth,n=i.videoHeight,a=Math.max(o/w,e/n),B=w*a,r=n*a,s=B/w,C=r/n,E=(o-B)/2,c=(e-r)/2,h=({x:A,y:o})=>({x:Math.floor(A*s),y:Math.floor(o*C)}),q=({x:A,y:o})=>({x:Math.floor(A+E),y:Math.floor(o+c)}),l=A.map((A=>{const{boundingBox:o,cornerPoints:e}=A,{x:g,y:t}=q(h({x:o.x,y:o.y})),{x:i,y:w}=h({x:o.width,y:o.height});return{...A,cornerPoints:e.map((A=>q(h(A)))),boundingBox:DOMRectReadOnly.fromRect({x:g,y:t,width:i,height:w})}}));t.width=i.offsetWidth,t.height=i.offsetHeight;const M=t.getContext(\"2d\");if(null===M)throw new Error(\"onFound handler should only be called when component is mounted. Thus tracking canvas 2D context is always defined.\");g(l,M)}}(A,F.current,N.current,u.tracker),formats:s,retryDelay:void 0===u.tracker?500:10,scanDelay:I,allowMultiple:l,sound:D});(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>(K(!0),()=>{K(!1)})),[]),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{k&&(W(),Z())}),[null==E?void 0:E.tracker]),(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{if(!m(f,J)){const A=f;(null==r?void 0:r.deviceId)&&delete A.facingMode,x(A)}}),[r]);const v=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)((()=>({constraints:J,shouldStream:k&&!C})),[J,k,C]),b=async()=>{const A=F.current;if(null==A)throw new Error(\"Video should be defined when component is mounted.\");const o=T.current;if(null==o)throw new Error(\"Canvas should be defined when component is mounted.\");const e=o.getContext(\"2d\");if(null==e)throw new Error(\"Canvas should be defined when component is mounted.\");if(v.shouldStream){await U.stopCamera(),L(!1);try{await U.startCamera(A,v),A?L(!0):await U.stopCamera()}catch(A){null==Y||Y(A),console.error(\"error\",A)}}else o.width=A.videoWidth,o.height=A.videoHeight,e.drawImage(A,0,0,A.videoWidth,A.videoHeight),await U.stopCamera(),L(!1)};(0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{(async()=>{await b()})()}),[v]);const O=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)((()=>v.shouldStream&&y),[v.shouldStream,y]);return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{if(O){if(void 0===T.current)throw new Error(\"shouldScan effect should only be triggered when component is mounted. Thus pause frame canvas is defined\");if(p(T.current),void 0===N.current)throw new Error(\"shouldScan effect should only be triggered when component is mounted. Thus tracking canvas is defined\");p(N.current);const A=F.current;if(null==A)throw new Error(\"shouldScan effect should only be triggered when component is mounted. Thus video element is defined\");Z()}}),[O]),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:{...R,...null==h?void 0:h.container},className:null==q?void 0:q.container},react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"video\",{ref:F,style:{...d,...null==h?void 0:h.video,visibility:C?\"hidden\":\"visible\"},className:null==q?void 0:q.video,autoPlay:!0,muted:!0,playsInline:!0}),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"canvas\",{ref:T,style:{display:C?\"block\":\"none\",position:\"absolute\",width:\"100%\",height:\"100%\"}}),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"canvas\",{ref:N,style:{position:\"absolute\",width:\"100%\",height:\"100%\"}}),react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\",{style:{position:\"absolute\",width:\"100%\",height:\"100%\"}},u.finder&&react__WEBPACK_IMPORTED_MODULE_0__.createElement(M,{scanning:y,capabilities:U.capabilities,onOff:u.onOff,zoom:u.zoom&&U.settings.zoom?{value:U.settings.zoom,onChange:async A=>{const o={...J,advanced:[{zoom:A}]};await U.updateConstraints(o)}}:void 0,torch:u.torch?{status:null!==(a=U.settings.torch)&&void 0!==a&&a,toggle:async A=>{const o={...J,advanced:[{torch:A}]};await U.updateConstraints(o)}}:void 0,startScanning:async()=>await b(),stopScanning:async()=>{await U.stopCamera(),p(N.current),L(!1)}}),c))}function K(){const[A,e]=(0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);return (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)((()=>{(async()=>{e(await async function(){return(await navigator.mediaDevices.enumerateDevices()).filter((({kind:A})=>\"videoinput\"===A))}())})()}),[]),A}\n//# sourceMappingURL=index.esm.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@yudiel/react-qr-scanner/dist/index.esm.mjs\n");

/***/ })

};
;