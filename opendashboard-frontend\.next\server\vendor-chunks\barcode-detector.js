"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/barcode-detector";
exports.ids = ["vendor-chunks/barcode-detector"];
exports.modules = {

/***/ "(ssr)/./node_modules/barcode-detector/dist/es/index.js":
/*!********************************************************!*\
  !*** ./node_modules/barcode-detector/dist/es/index.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarcodeDetector: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_1__.BarcodeDetector),\n/* harmony export */   ZXING_CPP_COMMIT: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_1__.ZXING_CPP_COMMIT),\n/* harmony export */   ZXING_WASM_SHA256: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_1__.ZXING_WASM_SHA256),\n/* harmony export */   ZXING_WASM_VERSION: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_1__.ZXING_WASM_VERSION),\n/* harmony export */   prepareZXingModule: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_1__.prepareZXingModule),\n/* harmony export */   purgeZXingModule: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_1__.purgeZXingModule),\n/* harmony export */   setZXingModuleOverrides: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_1__.setZXingModuleOverrides)\n/* harmony export */ });\n/* harmony import */ var _polyfill_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./polyfill.js */ \"(ssr)/./node_modules/barcode-detector/dist/es/polyfill.js\");\n/* harmony import */ var _ponyfill_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ponyfill.js */ \"(ssr)/./node_modules/barcode-detector/dist/es/ponyfill.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmFyY29kZS1kZXRlY3Rvci9kaXN0L2VzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUF1QjtBQUNvTDtBQVN6TSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvYmFyY29kZS1kZXRlY3Rvci9kaXN0L2VzL2luZGV4LmpzPzk0YTMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IFwiLi9wb2x5ZmlsbC5qc1wiO1xuaW1wb3J0IHsgQmFyY29kZURldGVjdG9yIGFzIE0sIFpYSU5HX0NQUF9DT01NSVQgYXMgWCwgWlhJTkdfV0FTTV9TSEEyNTYgYXMgWiwgWlhJTkdfV0FTTV9WRVJTSU9OIGFzIF8sIHByZXBhcmVaWGluZ01vZHVsZSBhcyBkLCBwdXJnZVpYaW5nTW9kdWxlIGFzIGksIHNldFpYaW5nTW9kdWxlT3ZlcnJpZGVzIGFzIHAgfSBmcm9tIFwiLi9wb255ZmlsbC5qc1wiO1xuZXhwb3J0IHtcbiAgTSBhcyBCYXJjb2RlRGV0ZWN0b3IsXG4gIFggYXMgWlhJTkdfQ1BQX0NPTU1JVCxcbiAgWiBhcyBaWElOR19XQVNNX1NIQTI1NixcbiAgXyBhcyBaWElOR19XQVNNX1ZFUlNJT04sXG4gIGQgYXMgcHJlcGFyZVpYaW5nTW9kdWxlLFxuICBpIGFzIHB1cmdlWlhpbmdNb2R1bGUsXG4gIHAgYXMgc2V0WlhpbmdNb2R1bGVPdmVycmlkZXNcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/barcode-detector/dist/es/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/barcode-detector/dist/es/polyfill.js":
/*!***********************************************************!*\
  !*** ./node_modules/barcode-detector/dist/es/polyfill.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ZXING_CPP_COMMIT: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_0__.ZXING_CPP_COMMIT),\n/* harmony export */   ZXING_WASM_SHA256: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_0__.ZXING_WASM_SHA256),\n/* harmony export */   ZXING_WASM_VERSION: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_0__.ZXING_WASM_VERSION),\n/* harmony export */   prepareZXingModule: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_0__.prepareZXingModule),\n/* harmony export */   purgeZXingModule: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_0__.purgeZXingModule),\n/* harmony export */   setZXingModuleOverrides: () => (/* reexport safe */ _ponyfill_js__WEBPACK_IMPORTED_MODULE_0__.setZXingModuleOverrides)\n/* harmony export */ });\n/* harmony import */ var _ponyfill_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ponyfill.js */ \"(ssr)/./node_modules/barcode-detector/dist/es/ponyfill.js\");\n\n\nvar e;\n(e = globalThis.BarcodeDetector) != null || (globalThis.BarcodeDetector = _ponyfill_js__WEBPACK_IMPORTED_MODULE_0__.BarcodeDetector);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvYmFyY29kZS1kZXRlY3Rvci9kaXN0L2VzL3BvbHlmaWxsLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBcUQ7QUFDZ0k7QUFDckw7QUFDQSwwRUFBMEUseURBQUM7QUFRekUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2JhcmNvZGUtZGV0ZWN0b3IvZGlzdC9lcy9wb2x5ZmlsbC5qcz82YjExIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEJhcmNvZGVEZXRlY3RvciBhcyByIH0gZnJvbSBcIi4vcG9ueWZpbGwuanNcIjtcbmltcG9ydCB7IFpYSU5HX0NQUF9DT01NSVQgYXMgZCwgWlhJTkdfV0FTTV9TSEEyNTYgYXMgaSwgWlhJTkdfV0FTTV9WRVJTSU9OIGFzIFgsIHByZXBhcmVaWGluZ01vZHVsZSBhcyBaLCBwdXJnZVpYaW5nTW9kdWxlIGFzIF8sIHNldFpYaW5nTW9kdWxlT3ZlcnJpZGVzIGFzIGcgfSBmcm9tIFwiLi9wb255ZmlsbC5qc1wiO1xudmFyIGU7XG4oZSA9IGdsb2JhbFRoaXMuQmFyY29kZURldGVjdG9yKSAhPSBudWxsIHx8IChnbG9iYWxUaGlzLkJhcmNvZGVEZXRlY3RvciA9IHIpO1xuZXhwb3J0IHtcbiAgZCBhcyBaWElOR19DUFBfQ09NTUlULFxuICBpIGFzIFpYSU5HX1dBU01fU0hBMjU2LFxuICBYIGFzIFpYSU5HX1dBU01fVkVSU0lPTixcbiAgWiBhcyBwcmVwYXJlWlhpbmdNb2R1bGUsXG4gIF8gYXMgcHVyZ2VaWGluZ01vZHVsZSxcbiAgZyBhcyBzZXRaWGluZ01vZHVsZU92ZXJyaWRlc1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/barcode-detector/dist/es/polyfill.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/barcode-detector/dist/es/ponyfill.js":
/*!***********************************************************!*\
  !*** ./node_modules/barcode-detector/dist/es/ponyfill.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarcodeDetector: () => (/* binding */ Eo),\n/* harmony export */   ZXING_CPP_COMMIT: () => (/* binding */ bo),\n/* harmony export */   ZXING_WASM_SHA256: () => (/* binding */ Po),\n/* harmony export */   ZXING_WASM_VERSION: () => (/* binding */ $o),\n/* harmony export */   prepareZXingModule: () => (/* binding */ Be),\n/* harmony export */   purgeZXingModule: () => (/* binding */ Co),\n/* harmony export */   setZXingModuleOverrides: () => (/* binding */ To)\n/* harmony export */ });\nvar Ae = (o) => {\n  throw TypeError(o);\n};\nvar Se = (o, f, c) => f.has(o) || Ae(\"Cannot \" + c);\nvar Ie = (o, f, c) => (Se(o, f, \"read from private field\"), c ? c.call(o) : f.get(o)), De = (o, f, c) => f.has(o) ? Ae(\"Cannot add the same private member more than once\") : f instanceof WeakSet ? f.add(o) : f.set(o, c), Me = (o, f, c, T) => (Se(o, f, \"write to private field\"), T ? T.call(o, c) : f.set(o, c), c);\nconst Dt = [\n  [\"Aztec\", \"M\"],\n  [\"Codabar\", \"L\"],\n  [\"Code39\", \"L\"],\n  [\"Code93\", \"L\"],\n  [\"Code128\", \"L\"],\n  [\"DataBar\", \"L\"],\n  [\"DataBarExpanded\", \"L\"],\n  [\"DataMatrix\", \"M\"],\n  [\"EAN-8\", \"L\"],\n  [\"EAN-13\", \"L\"],\n  [\"ITF\", \"L\"],\n  [\"MaxiCode\", \"M\"],\n  [\"PDF417\", \"M\"],\n  [\"QRCode\", \"M\"],\n  [\"UPC-A\", \"L\"],\n  [\"UPC-E\", \"L\"],\n  [\"MicroQRCode\", \"M\"],\n  [\"rMQRCode\", \"M\"],\n  [\"DXFilmEdge\", \"L\"],\n  [\"DataBarLimited\", \"L\"]\n], Mt = Dt.map(([o]) => o), La = Mt.filter(\n  (o, f) => Dt[f][1] === \"L\"\n), Ba = Mt.filter(\n  (o, f) => Dt[f][1] === \"M\"\n);\nfunction Yt(o) {\n  switch (o) {\n    case \"Linear-Codes\":\n      return La.reduce((f, c) => f | Yt(c), 0);\n    case \"Matrix-Codes\":\n      return Ba.reduce((f, c) => f | Yt(c), 0);\n    case \"Any\":\n      return (1 << Dt.length) - 1;\n    case \"None\":\n      return 0;\n    default:\n      return 1 << Mt.indexOf(o);\n  }\n}\nfunction Wa(o) {\n  if (o === 0)\n    return \"None\";\n  const f = 31 - Math.clz32(o);\n  return Mt[f];\n}\nfunction Ua(o) {\n  return o.reduce((f, c) => f | Yt(c), 0);\n}\nconst Va = [\n  \"LocalAverage\",\n  \"GlobalHistogram\",\n  \"FixedThreshold\",\n  \"BoolCast\"\n];\nfunction ka(o) {\n  return Va.indexOf(o);\n}\nconst Fe = [\n  \"Unknown\",\n  \"ASCII\",\n  \"ISO8859_1\",\n  \"ISO8859_2\",\n  \"ISO8859_3\",\n  \"ISO8859_4\",\n  \"ISO8859_5\",\n  \"ISO8859_6\",\n  \"ISO8859_7\",\n  \"ISO8859_8\",\n  \"ISO8859_9\",\n  \"ISO8859_10\",\n  \"ISO8859_11\",\n  \"ISO8859_13\",\n  \"ISO8859_14\",\n  \"ISO8859_15\",\n  \"ISO8859_16\",\n  \"Cp437\",\n  \"Cp1250\",\n  \"Cp1251\",\n  \"Cp1252\",\n  \"Cp1256\",\n  \"Shift_JIS\",\n  \"Big5\",\n  \"GB2312\",\n  \"GB18030\",\n  \"EUC_JP\",\n  \"EUC_KR\",\n  \"UTF16BE\",\n  /**\n   * UnicodeBig [[deprecated]]\n   */\n  \"UTF16BE\",\n  \"UTF8\",\n  \"UTF16LE\",\n  \"UTF32BE\",\n  \"UTF32LE\",\n  \"BINARY\"\n];\nfunction Ha(o) {\n  return o === \"UnicodeBig\" ? Fe.indexOf(\"UTF16BE\") : Fe.indexOf(o);\n}\nconst Na = [\n  \"Text\",\n  \"Binary\",\n  \"Mixed\",\n  \"GS1\",\n  \"ISO15434\",\n  \"UnknownECI\"\n];\nfunction za(o) {\n  return Na[o];\n}\nconst Ga = [\"Ignore\", \"Read\", \"Require\"];\nfunction Xa(o) {\n  return Ga.indexOf(o);\n}\nconst qa = [\"Plain\", \"ECI\", \"HRI\", \"Hex\", \"Escaped\"];\nfunction Ya(o) {\n  return qa.indexOf(o);\n}\nconst It = {\n  formats: [],\n  tryHarder: !0,\n  tryRotate: !0,\n  tryInvert: !0,\n  tryDownscale: !0,\n  tryDenoise: !1,\n  binarizer: \"LocalAverage\",\n  isPure: !1,\n  downscaleFactor: 3,\n  downscaleThreshold: 500,\n  minLineCount: 2,\n  maxNumberOfSymbols: 255,\n  tryCode39ExtendedMode: !0,\n  returnErrors: !1,\n  eanAddOnSymbol: \"Ignore\",\n  textMode: \"HRI\",\n  characterSet: \"Unknown\"\n};\nfunction je(o) {\n  return {\n    ...o,\n    formats: Ua(o.formats),\n    binarizer: ka(o.binarizer),\n    eanAddOnSymbol: Xa(o.eanAddOnSymbol),\n    textMode: Ya(o.textMode),\n    characterSet: Ha(o.characterSet)\n  };\n}\nfunction Za(o) {\n  return {\n    ...o,\n    format: Wa(o.format),\n    contentType: za(o.contentType),\n    eccLevel: o.ecLevel\n  };\n}\nconst $o = \"2.1.2\", bo = \"a1516b34167cff504bf3c83698ea841e13a8f7f1\", Qa = {\n  locateFile: (o, f) => {\n    const c = o.match(/_(.+?)\\.wasm$/);\n    return c ? `https://fastly.jsdelivr.net/npm/zxing-wasm@2.1.2/dist/${c[1]}/${o}` : f + o;\n  }\n}, St = /* @__PURE__ */ new WeakMap();\nfunction Ja(o, f) {\n  return Object.is(o, f) || Object.keys(o).length === Object.keys(f).length && Object.keys(o).every(\n    (c) => Object.prototype.hasOwnProperty.call(f, c) && o[c] === f[c]\n  );\n}\nfunction Le(o, {\n  overrides: f,\n  equalityFn: c = Ja,\n  fireImmediately: T = !1\n} = {}) {\n  var $;\n  const [x, D] = ($ = St.get(o)) != null ? $ : [Qa], R = f != null ? f : x;\n  let O;\n  if (T) {\n    if (D && (O = c(x, R)))\n      return D;\n    const M = o({\n      ...R\n    });\n    return St.set(o, [R, M]), M;\n  }\n  (O != null ? O : c(x, R)) || St.set(o, [R]);\n}\nfunction Ka(o) {\n  St.delete(o);\n}\nasync function to(o, f, c = It) {\n  const T = {\n    ...It,\n    ...c\n  }, $ = await Le(o, {\n    fireImmediately: !0\n  });\n  let x, D;\n  if (\"width\" in f && \"height\" in f && \"data\" in f) {\n    const {\n      data: O,\n      data: { byteLength: M },\n      width: F,\n      height: q\n    } = f;\n    D = $._malloc(M), $.HEAPU8.set(O, D), x = $.readBarcodesFromPixmap(\n      D,\n      F,\n      q,\n      je(T)\n    );\n  } else {\n    let O, M;\n    if (\"buffer\" in f)\n      [O, M] = [f.byteLength, f];\n    else if (\"byteLength\" in f)\n      [O, M] = [f.byteLength, new Uint8Array(f)];\n    else if (\"size\" in f)\n      [O, M] = [f.size, new Uint8Array(await f.arrayBuffer())];\n    else\n      throw new TypeError(\"Invalid input type\");\n    D = $._malloc(O), $.HEAPU8.set(M, D), x = $.readBarcodesFromImage(\n      D,\n      O,\n      je(T)\n    );\n  }\n  $._free(D);\n  const R = [];\n  for (let O = 0; O < x.size(); ++O)\n    R.push(\n      Za(x.get(O))\n    );\n  return R;\n}\n({\n  ...It,\n  formats: [...It.formats]\n});\nvar Qt = async function(o = {}) {\n  var f, c = o, T, $, x = new Promise((e, t) => {\n    T = e, $ = t;\n  }), D = typeof window == \"object\", R = typeof Bun < \"u\", O = typeof WorkerGlobalScope < \"u\";\n  typeof process == \"object\" && typeof process.versions == \"object\" && typeof process.versions.node == \"string\" && process.type != \"renderer\";\n  var M = \"./this.program\", F = \"\";\n  function q(e) {\n    return c.locateFile ? c.locateFile(e, F) : F + e;\n  }\n  var rt, Y;\n  (D || O || R) && (O ? F = self.location.href : typeof document < \"u\" && document.currentScript && (F = document.currentScript.src), F.startsWith(\"blob:\") ? F = \"\" : F = F.slice(0, F.replace(/[?#].*/, \"\").lastIndexOf(\"/\") + 1), O && (Y = (e) => {\n    var t = new XMLHttpRequest();\n    return t.open(\"GET\", e, !1), t.responseType = \"arraybuffer\", t.send(null), new Uint8Array(t.response);\n  }), rt = async (e) => {\n    var t = await fetch(e, {\n      credentials: \"same-origin\"\n    });\n    if (t.ok)\n      return t.arrayBuffer();\n    throw new Error(t.status + \" : \" + t.url);\n  });\n  var st = console.log.bind(console), k = console.error.bind(console), Z, nt, Jt = !1, Q, B, ut, vt, at, P, Kt, te;\n  function ee() {\n    var e = nt.buffer;\n    Q = new Int8Array(e), ut = new Int16Array(e), c.HEAPU8 = B = new Uint8Array(e), vt = new Uint16Array(e), at = new Int32Array(e), P = new Uint32Array(e), Kt = new Float32Array(e), te = new Float64Array(e);\n  }\n  function qe() {\n    if (c.preRun)\n      for (typeof c.preRun == \"function\" && (c.preRun = [c.preRun]); c.preRun.length; )\n        sr(c.preRun.shift());\n    re(ae);\n  }\n  function Ye() {\n    E.ya();\n  }\n  function Ze() {\n    if (c.postRun)\n      for (typeof c.postRun == \"function\" && (c.postRun = [c.postRun]); c.postRun.length; )\n        ir(c.postRun.shift());\n    re(ne);\n  }\n  var J = 0, ct = null;\n  function Qe(e) {\n    var t;\n    J++, (t = c.monitorRunDependencies) === null || t === void 0 || t.call(c, J);\n  }\n  function Je(e) {\n    var t;\n    if (J--, (t = c.monitorRunDependencies) === null || t === void 0 || t.call(c, J), J == 0 && ct) {\n      var r = ct;\n      ct = null, r();\n    }\n  }\n  function Ft(e) {\n    var t;\n    (t = c.onAbort) === null || t === void 0 || t.call(c, e), e = \"Aborted(\" + e + \")\", k(e), Jt = !0, e += \". Build with -sASSERTIONS for more info.\";\n    var r = new WebAssembly.RuntimeError(e);\n    throw $(r), r;\n  }\n  var yt;\n  function Ke() {\n    return q(\"zxing_reader.wasm\");\n  }\n  function tr(e) {\n    if (e == yt && Z)\n      return new Uint8Array(Z);\n    if (Y)\n      return Y(e);\n    throw \"both async and sync fetching of the wasm failed\";\n  }\n  async function er(e) {\n    if (!Z)\n      try {\n        var t = await rt(e);\n        return new Uint8Array(t);\n      } catch {\n      }\n    return tr(e);\n  }\n  async function rr(e, t) {\n    try {\n      var r = await er(e), n = await WebAssembly.instantiate(r, t);\n      return n;\n    } catch (a) {\n      k(`failed to asynchronously prepare wasm: ${a}`), Ft(a);\n    }\n  }\n  async function nr(e, t, r) {\n    if (!e && typeof WebAssembly.instantiateStreaming == \"function\")\n      try {\n        var n = fetch(t, {\n          credentials: \"same-origin\"\n        }), a = await WebAssembly.instantiateStreaming(n, r);\n        return a;\n      } catch (i) {\n        k(`wasm streaming compile failed: ${i}`), k(\"falling back to ArrayBuffer instantiation\");\n      }\n    return rr(t, r);\n  }\n  function ar() {\n    return {\n      a: Hn\n    };\n  }\n  async function or() {\n    function e(i, u) {\n      return E = i.exports, nt = E.xa, ee(), pe = E.Ba, Je(), E;\n    }\n    Qe();\n    function t(i) {\n      return e(i.instance);\n    }\n    var r = ar();\n    if (c.instantiateWasm)\n      return new Promise((i, u) => {\n        c.instantiateWasm(r, (s, l) => {\n          i(e(s));\n        });\n      });\n    yt != null || (yt = Ke());\n    try {\n      var n = await nr(Z, yt, r), a = t(n);\n      return a;\n    } catch (i) {\n      return $(i), Promise.reject(i);\n    }\n  }\n  var re = (e) => {\n    for (; e.length > 0; )\n      e.shift()(c);\n  }, ne = [], ir = (e) => ne.push(e), ae = [], sr = (e) => ae.push(e), y = (e) => Xn(e), m = () => qn(), mt = [], gt = 0, ur = (e) => {\n    var t = new jt(e);\n    return t.get_caught() || (t.set_caught(!0), gt--), t.set_rethrown(!1), mt.push(t), Zn(e), zn(e);\n  }, H = 0, cr = () => {\n    v(0, 0);\n    var e = mt.pop();\n    Yn(e.excPtr), H = 0;\n  };\n  class jt {\n    constructor(t) {\n      this.excPtr = t, this.ptr = t - 24;\n    }\n    set_type(t) {\n      P[this.ptr + 4 >> 2] = t;\n    }\n    get_type() {\n      return P[this.ptr + 4 >> 2];\n    }\n    set_destructor(t) {\n      P[this.ptr + 8 >> 2] = t;\n    }\n    get_destructor() {\n      return P[this.ptr + 8 >> 2];\n    }\n    set_caught(t) {\n      t = t ? 1 : 0, Q[this.ptr + 12] = t;\n    }\n    get_caught() {\n      return Q[this.ptr + 12] != 0;\n    }\n    set_rethrown(t) {\n      t = t ? 1 : 0, Q[this.ptr + 13] = t;\n    }\n    get_rethrown() {\n      return Q[this.ptr + 13] != 0;\n    }\n    init(t, r) {\n      this.set_adjusted_ptr(0), this.set_type(t), this.set_destructor(r);\n    }\n    set_adjusted_ptr(t) {\n      P[this.ptr + 16 >> 2] = t;\n    }\n    get_adjusted_ptr() {\n      return P[this.ptr + 16 >> 2];\n    }\n  }\n  var wt = (e) => Gn(e), Rt = (e) => {\n    var t = H;\n    if (!t)\n      return wt(0), 0;\n    var r = new jt(t);\n    r.set_adjusted_ptr(t);\n    var n = r.get_type();\n    if (!n)\n      return wt(0), t;\n    for (var a of e) {\n      if (a === 0 || a === n)\n        break;\n      var i = r.ptr + 16;\n      if (Qn(a, n, i))\n        return wt(a), t;\n    }\n    return wt(n), t;\n  }, lr = () => Rt([]), fr = (e) => Rt([e]), dr = (e, t) => Rt([e, t]), hr = () => {\n    var e = mt.pop();\n    e || Ft(\"no exception to throw\");\n    var t = e.excPtr;\n    throw e.get_rethrown() || (mt.push(e), e.set_rethrown(!0), e.set_caught(!1), gt++), H = t, H;\n  }, pr = (e, t, r) => {\n    var n = new jt(e);\n    throw n.init(t, r), H = e, gt++, H;\n  }, vr = () => gt, yr = (e) => {\n    throw H || (H = e), H;\n  }, mr = () => Ft(\"\"), $t = {}, Lt = (e) => {\n    for (; e.length; ) {\n      var t = e.pop(), r = e.pop();\n      r(t);\n    }\n  };\n  function lt(e) {\n    return this.fromWireType(P[e >> 2]);\n  }\n  var ot = {}, K = {}, bt = {}, gr = c.InternalError = class extends Error {\n    constructor(e) {\n      super(e), this.name = \"InternalError\";\n    }\n  }, Ct = (e) => {\n    throw new gr(e);\n  }, tt = (e, t, r) => {\n    e.forEach((s) => bt[s] = t);\n    function n(s) {\n      var l = r(s);\n      l.length !== e.length && Ct(\"Mismatched type converter count\");\n      for (var d = 0; d < e.length; ++d)\n        V(e[d], l[d]);\n    }\n    var a = new Array(t.length), i = [], u = 0;\n    t.forEach((s, l) => {\n      K.hasOwnProperty(s) ? a[l] = K[s] : (i.push(s), ot.hasOwnProperty(s) || (ot[s] = []), ot[s].push(() => {\n        a[l] = K[s], ++u, u === i.length && n(a);\n      }));\n    }), i.length === 0 && n(a);\n  }, wr = (e) => {\n    var t = $t[e];\n    delete $t[e];\n    var r = t.rawConstructor, n = t.rawDestructor, a = t.fields, i = a.map((u) => u.getterReturnType).concat(a.map((u) => u.setterArgumentType));\n    tt([e], i, (u) => {\n      var s = {};\n      return a.forEach((l, d) => {\n        var h = l.fieldName, p = u[d], w = u[d].optional, b = l.getter, _ = l.getterContext, S = u[d + a.length], A = l.setter, I = l.setterContext;\n        s[h] = {\n          read: (X) => p.fromWireType(b(_, X)),\n          write: (X, L) => {\n            var W = [];\n            A(I, X, S.toWireType(W, L)), Lt(W);\n          },\n          optional: w\n        };\n      }), [{\n        name: t.name,\n        fromWireType: (l) => {\n          var d = {};\n          for (var h in s)\n            d[h] = s[h].read(l);\n          return n(l), d;\n        },\n        toWireType: (l, d) => {\n          for (var h in s)\n            if (!(h in d) && !s[h].optional)\n              throw new TypeError(`Missing field: \"${h}\"`);\n          var p = r();\n          for (h in s)\n            s[h].write(p, d[h]);\n          return l !== null && l.push(n, p), p;\n        },\n        argPackAdvance: N,\n        readValueFromPointer: lt,\n        destructorFunction: n\n      }];\n    });\n  }, $r = (e, t, r, n, a) => {\n  }, br = () => {\n    for (var e = new Array(256), t = 0; t < 256; ++t)\n      e[t] = String.fromCharCode(t);\n    oe = e;\n  }, oe, j = (e) => {\n    for (var t = \"\", r = e; B[r]; )\n      t += oe[B[r++]];\n    return t;\n  }, ft = c.BindingError = class extends Error {\n    constructor(e) {\n      super(e), this.name = \"BindingError\";\n    }\n  }, C = (e) => {\n    throw new ft(e);\n  };\n  function Cr(e, t) {\n    let r = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    var n = t.name;\n    if (e || C(`type \"${n}\" must have a positive integer typeid pointer`), K.hasOwnProperty(e)) {\n      if (r.ignoreDuplicateRegistrations)\n        return;\n      C(`Cannot register type '${n}' twice`);\n    }\n    if (K[e] = t, delete bt[e], ot.hasOwnProperty(e)) {\n      var a = ot[e];\n      delete ot[e], a.forEach((i) => i());\n    }\n  }\n  function V(e, t) {\n    let r = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n    return Cr(e, t, r);\n  }\n  var N = 8, Tr = (e, t, r, n) => {\n    t = j(t), V(e, {\n      name: t,\n      fromWireType: function(a) {\n        return !!a;\n      },\n      toWireType: function(a, i) {\n        return i ? r : n;\n      },\n      argPackAdvance: N,\n      readValueFromPointer: function(a) {\n        return this.fromWireType(B[a]);\n      },\n      destructorFunction: null\n    });\n  }, Pr = (e) => ({\n    count: e.count,\n    deleteScheduled: e.deleteScheduled,\n    preservePointerOnDelete: e.preservePointerOnDelete,\n    ptr: e.ptr,\n    ptrType: e.ptrType,\n    smartPtr: e.smartPtr,\n    smartPtrType: e.smartPtrType\n  }), Bt = (e) => {\n    function t(r) {\n      return r.$$.ptrType.registeredClass.name;\n    }\n    C(t(e) + \" instance already deleted\");\n  }, Wt = !1, ie = (e) => {\n  }, Er = (e) => {\n    e.smartPtr ? e.smartPtrType.rawDestructor(e.smartPtr) : e.ptrType.registeredClass.rawDestructor(e.ptr);\n  }, se = (e) => {\n    e.count.value -= 1;\n    var t = e.count.value === 0;\n    t && Er(e);\n  }, dt = (e) => typeof FinalizationRegistry > \"u\" ? (dt = (t) => t, e) : (Wt = new FinalizationRegistry((t) => {\n    se(t.$$);\n  }), dt = (t) => {\n    var r = t.$$, n = !!r.smartPtr;\n    if (n) {\n      var a = {\n        $$: r\n      };\n      Wt.register(t, a, t);\n    }\n    return t;\n  }, ie = (t) => Wt.unregister(t), dt(e)), _r = () => {\n    let e = Tt.prototype;\n    Object.assign(e, {\n      isAliasOf(r) {\n        if (!(this instanceof Tt) || !(r instanceof Tt))\n          return !1;\n        var n = this.$$.ptrType.registeredClass, a = this.$$.ptr;\n        r.$$ = r.$$;\n        for (var i = r.$$.ptrType.registeredClass, u = r.$$.ptr; n.baseClass; )\n          a = n.upcast(a), n = n.baseClass;\n        for (; i.baseClass; )\n          u = i.upcast(u), i = i.baseClass;\n        return n === i && a === u;\n      },\n      clone() {\n        if (this.$$.ptr || Bt(this), this.$$.preservePointerOnDelete)\n          return this.$$.count.value += 1, this;\n        var r = dt(Object.create(Object.getPrototypeOf(this), {\n          $$: {\n            value: Pr(this.$$)\n          }\n        }));\n        return r.$$.count.value += 1, r.$$.deleteScheduled = !1, r;\n      },\n      delete() {\n        this.$$.ptr || Bt(this), this.$$.deleteScheduled && !this.$$.preservePointerOnDelete && C(\"Object already scheduled for deletion\"), ie(this), se(this.$$), this.$$.preservePointerOnDelete || (this.$$.smartPtr = void 0, this.$$.ptr = void 0);\n      },\n      isDeleted() {\n        return !this.$$.ptr;\n      },\n      deleteLater() {\n        return this.$$.ptr || Bt(this), this.$$.deleteScheduled && !this.$$.preservePointerOnDelete && C(\"Object already scheduled for deletion\"), this.$$.deleteScheduled = !0, this;\n      }\n    });\n    const t = Symbol.dispose;\n    t && (e[t] = e.delete);\n  };\n  function Tt() {\n  }\n  var Ut = (e, t) => Object.defineProperty(t, \"name\", {\n    value: e\n  }), ue = {}, ce = (e, t, r) => {\n    if (e[t].overloadTable === void 0) {\n      var n = e[t];\n      e[t] = function() {\n        for (var a = arguments.length, i = new Array(a), u = 0; u < a; u++)\n          i[u] = arguments[u];\n        return e[t].overloadTable.hasOwnProperty(i.length) || C(`Function '${r}' called with an invalid number of arguments (${i.length}) - expects one of (${e[t].overloadTable})!`), e[t].overloadTable[i.length].apply(this, i);\n      }, e[t].overloadTable = [], e[t].overloadTable[n.argCount] = n;\n    }\n  }, le = (e, t, r) => {\n    c.hasOwnProperty(e) ? ((r === void 0 || c[e].overloadTable !== void 0 && c[e].overloadTable[r] !== void 0) && C(`Cannot register public name '${e}' twice`), ce(c, e, e), c[e].overloadTable.hasOwnProperty(r) && C(`Cannot register multiple overloads of a function with the same number of arguments (${r})!`), c[e].overloadTable[r] = t) : (c[e] = t, c[e].argCount = r);\n  }, Or = 48, xr = 57, Ar = (e) => {\n    e = e.replace(/[^a-zA-Z0-9_]/g, \"$\");\n    var t = e.charCodeAt(0);\n    return t >= Or && t <= xr ? `_${e}` : e;\n  };\n  function Sr(e, t, r, n, a, i, u, s) {\n    this.name = e, this.constructor = t, this.instancePrototype = r, this.rawDestructor = n, this.baseClass = a, this.getActualType = i, this.upcast = u, this.downcast = s, this.pureVirtualFunctions = [];\n  }\n  var Vt = (e, t, r) => {\n    for (; t !== r; )\n      t.upcast || C(`Expected null or instance of ${r.name}, got an instance of ${t.name}`), e = t.upcast(e), t = t.baseClass;\n    return e;\n  };\n  function Ir(e, t) {\n    if (t === null)\n      return this.isReference && C(`null is not a valid ${this.name}`), 0;\n    t.$$ || C(`Cannot pass \"${embindRepr(t)}\" as a ${this.name}`), t.$$.ptr || C(`Cannot pass deleted object as a pointer of type ${this.name}`);\n    var r = t.$$.ptrType.registeredClass, n = Vt(t.$$.ptr, r, this.registeredClass);\n    return n;\n  }\n  function Dr(e, t) {\n    var r;\n    if (t === null)\n      return this.isReference && C(`null is not a valid ${this.name}`), this.isSmartPointer ? (r = this.rawConstructor(), e !== null && e.push(this.rawDestructor, r), r) : 0;\n    (!t || !t.$$) && C(`Cannot pass \"${embindRepr(t)}\" as a ${this.name}`), t.$$.ptr || C(`Cannot pass deleted object as a pointer of type ${this.name}`), !this.isConst && t.$$.ptrType.isConst && C(`Cannot convert argument of type ${t.$$.smartPtrType ? t.$$.smartPtrType.name : t.$$.ptrType.name} to parameter type ${this.name}`);\n    var n = t.$$.ptrType.registeredClass;\n    if (r = Vt(t.$$.ptr, n, this.registeredClass), this.isSmartPointer)\n      switch (t.$$.smartPtr === void 0 && C(\"Passing raw pointer to smart pointer is illegal\"), this.sharingPolicy) {\n        case 0:\n          t.$$.smartPtrType === this ? r = t.$$.smartPtr : C(`Cannot convert argument of type ${t.$$.smartPtrType ? t.$$.smartPtrType.name : t.$$.ptrType.name} to parameter type ${this.name}`);\n          break;\n        case 1:\n          r = t.$$.smartPtr;\n          break;\n        case 2:\n          if (t.$$.smartPtrType === this)\n            r = t.$$.smartPtr;\n          else {\n            var a = t.clone();\n            r = this.rawShare(r, G.toHandle(() => a.delete())), e !== null && e.push(this.rawDestructor, r);\n          }\n          break;\n        default:\n          C(\"Unsupporting sharing policy\");\n      }\n    return r;\n  }\n  function Mr(e, t) {\n    if (t === null)\n      return this.isReference && C(`null is not a valid ${this.name}`), 0;\n    t.$$ || C(`Cannot pass \"${embindRepr(t)}\" as a ${this.name}`), t.$$.ptr || C(`Cannot pass deleted object as a pointer of type ${this.name}`), t.$$.ptrType.isConst && C(`Cannot convert argument of type ${t.$$.ptrType.name} to parameter type ${this.name}`);\n    var r = t.$$.ptrType.registeredClass, n = Vt(t.$$.ptr, r, this.registeredClass);\n    return n;\n  }\n  var fe = (e, t, r) => {\n    if (t === r)\n      return e;\n    if (r.baseClass === void 0)\n      return null;\n    var n = fe(e, t, r.baseClass);\n    return n === null ? null : r.downcast(n);\n  }, Fr = {}, jr = (e, t) => {\n    for (t === void 0 && C(\"ptr should not be undefined\"); e.baseClass; )\n      t = e.upcast(t), e = e.baseClass;\n    return t;\n  }, Rr = (e, t) => (t = jr(e, t), Fr[t]), Pt = (e, t) => {\n    (!t.ptrType || !t.ptr) && Ct(\"makeClassHandle requires ptr and ptrType\");\n    var r = !!t.smartPtrType, n = !!t.smartPtr;\n    return r !== n && Ct(\"Both smartPtrType and smartPtr must be specified\"), t.count = {\n      value: 1\n    }, dt(Object.create(e, {\n      $$: {\n        value: t,\n        writable: !0\n      }\n    }));\n  };\n  function Lr(e) {\n    var t = this.getPointee(e);\n    if (!t)\n      return this.destructor(e), null;\n    var r = Rr(this.registeredClass, t);\n    if (r !== void 0) {\n      if (r.$$.count.value === 0)\n        return r.$$.ptr = t, r.$$.smartPtr = e, r.clone();\n      var n = r.clone();\n      return this.destructor(e), n;\n    }\n    function a() {\n      return this.isSmartPointer ? Pt(this.registeredClass.instancePrototype, {\n        ptrType: this.pointeeType,\n        ptr: t,\n        smartPtrType: this,\n        smartPtr: e\n      }) : Pt(this.registeredClass.instancePrototype, {\n        ptrType: this,\n        ptr: e\n      });\n    }\n    var i = this.registeredClass.getActualType(t), u = ue[i];\n    if (!u)\n      return a.call(this);\n    var s;\n    this.isConst ? s = u.constPointerType : s = u.pointerType;\n    var l = fe(t, this.registeredClass, s.registeredClass);\n    return l === null ? a.call(this) : this.isSmartPointer ? Pt(s.registeredClass.instancePrototype, {\n      ptrType: s,\n      ptr: l,\n      smartPtrType: this,\n      smartPtr: e\n    }) : Pt(s.registeredClass.instancePrototype, {\n      ptrType: s,\n      ptr: l\n    });\n  }\n  var Br = () => {\n    Object.assign(Et.prototype, {\n      getPointee(e) {\n        return this.rawGetPointee && (e = this.rawGetPointee(e)), e;\n      },\n      destructor(e) {\n        var t;\n        (t = this.rawDestructor) === null || t === void 0 || t.call(this, e);\n      },\n      argPackAdvance: N,\n      readValueFromPointer: lt,\n      fromWireType: Lr\n    });\n  };\n  function Et(e, t, r, n, a, i, u, s, l, d, h) {\n    this.name = e, this.registeredClass = t, this.isReference = r, this.isConst = n, this.isSmartPointer = a, this.pointeeType = i, this.sharingPolicy = u, this.rawGetPointee = s, this.rawConstructor = l, this.rawShare = d, this.rawDestructor = h, !a && t.baseClass === void 0 ? n ? (this.toWireType = Ir, this.destructorFunction = null) : (this.toWireType = Mr, this.destructorFunction = null) : this.toWireType = Dr;\n  }\n  var de = (e, t, r) => {\n    c.hasOwnProperty(e) || Ct(\"Replacing nonexistent public symbol\"), c[e].overloadTable !== void 0 && r !== void 0 ? c[e].overloadTable[r] = t : (c[e] = t, c[e].argCount = r);\n  }, he = [], pe, g = (e) => {\n    var t = he[e];\n    return t || (he[e] = t = pe.get(e)), t;\n  }, Wr = function(e, t) {\n    let r = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [];\n    if (e.includes(\"j\"))\n      return dynCallLegacy(e, t, r);\n    var n = g(t), a = n(...r);\n    return a;\n  }, Ur = function(e, t) {\n    let r = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : !1;\n    return function() {\n      for (var n = arguments.length, a = new Array(n), i = 0; i < n; i++)\n        a[i] = arguments[i];\n      return Wr(e, t, a, r);\n    };\n  }, U = function(e, t) {\n    e = j(e);\n    function r() {\n      if (e.includes(\"j\"))\n        return Ur(e, t);\n      var a = g(t);\n      return a;\n    }\n    var n = r();\n    return typeof n != \"function\" && C(`unknown function pointer with signature ${e}: ${t}`), n;\n  };\n  class Vr extends Error {\n  }\n  var ve = (e) => {\n    var t = Nn(e), r = j(t);\n    return et(t), r;\n  }, _t = (e, t) => {\n    var r = [], n = {};\n    function a(i) {\n      if (!n[i] && !K[i]) {\n        if (bt[i]) {\n          bt[i].forEach(a);\n          return;\n        }\n        r.push(i), n[i] = !0;\n      }\n    }\n    throw t.forEach(a), new Vr(`${e}: ` + r.map(ve).join([\", \"]));\n  }, kr = (e, t, r, n, a, i, u, s, l, d, h, p, w) => {\n    h = j(h), i = U(a, i), s && (s = U(u, s)), d && (d = U(l, d)), w = U(p, w);\n    var b = Ar(h);\n    le(b, function() {\n      _t(`Cannot construct ${h} due to unbound types`, [n]);\n    }), tt([e, t, r], n ? [n] : [], (_) => {\n      _ = _[0];\n      var S, A;\n      n ? (S = _.registeredClass, A = S.instancePrototype) : A = Tt.prototype;\n      var I = Ut(h, function() {\n        if (Object.getPrototypeOf(this) !== X)\n          throw new ft(`Use 'new' to construct ${h}`);\n        if (L.constructor_body === void 0)\n          throw new ft(`${h} has no accessible constructor`);\n        for (var Oe = arguments.length, xt = new Array(Oe), At = 0; At < Oe; At++)\n          xt[At] = arguments[At];\n        var xe = L.constructor_body[xt.length];\n        if (xe === void 0)\n          throw new ft(`Tried to invoke ctor of ${h} with invalid number of parameters (${xt.length}) - expected (${Object.keys(L.constructor_body).toString()}) parameters instead!`);\n        return xe.apply(this, xt);\n      }), X = Object.create(A, {\n        constructor: {\n          value: I\n        }\n      });\n      I.prototype = X;\n      var L = new Sr(h, I, X, w, S, i, s, d);\n      if (L.baseClass) {\n        var W, Ot;\n        (Ot = (W = L.baseClass).__derivedClasses) !== null && Ot !== void 0 || (W.__derivedClasses = []), L.baseClass.__derivedClasses.push(L);\n      }\n      var Ra = new Et(h, L, !0, !1, !1), Ee = new Et(h + \"*\", L, !1, !1, !1), _e = new Et(h + \" const*\", L, !1, !0, !1);\n      return ue[e] = {\n        pointerType: Ee,\n        constPointerType: _e\n      }, de(b, I), [Ra, Ee, _e];\n    });\n  }, kt = (e, t) => {\n    for (var r = [], n = 0; n < e; n++)\n      r.push(P[t + n * 4 >> 2]);\n    return r;\n  };\n  function Hr(e) {\n    for (var t = 1; t < e.length; ++t)\n      if (e[t] !== null && e[t].destructorFunction === void 0)\n        return !0;\n    return !1;\n  }\n  function Ht(e, t, r, n, a, i) {\n    var u = t.length;\n    u < 2 && C(\"argTypes array size mismatch! Must at least get return value and 'this' types!\");\n    var s = t[1] !== null && r !== null, l = Hr(t), d = t[0].name !== \"void\", h = u - 2, p = new Array(h), w = [], b = [], _ = function() {\n      b.length = 0;\n      var S;\n      w.length = s ? 2 : 1, w[0] = a, s && (S = t[1].toWireType(b, this), w[1] = S);\n      for (var A = 0; A < h; ++A)\n        p[A] = t[A + 2].toWireType(b, A < 0 || arguments.length <= A ? void 0 : arguments[A]), w.push(p[A]);\n      var I = n(...w);\n      function X(L) {\n        if (l)\n          Lt(b);\n        else\n          for (var W = s ? 1 : 2; W < t.length; W++) {\n            var Ot = W === 1 ? S : p[W - 2];\n            t[W].destructorFunction !== null && t[W].destructorFunction(Ot);\n          }\n        if (d)\n          return t[0].fromWireType(L);\n      }\n      return X(I);\n    };\n    return Ut(e, _);\n  }\n  var Nr = (e, t, r, n, a, i) => {\n    var u = kt(t, r);\n    a = U(n, a), tt([], [e], (s) => {\n      s = s[0];\n      var l = `constructor ${s.name}`;\n      if (s.registeredClass.constructor_body === void 0 && (s.registeredClass.constructor_body = []), s.registeredClass.constructor_body[t - 1] !== void 0)\n        throw new ft(`Cannot register multiple constructors with identical number of parameters (${t - 1}) for class '${s.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);\n      return s.registeredClass.constructor_body[t - 1] = () => {\n        _t(`Cannot construct ${s.name} due to unbound types`, u);\n      }, tt([], u, (d) => (d.splice(1, 0, null), s.registeredClass.constructor_body[t - 1] = Ht(l, d, null, a, i), [])), [];\n    });\n  }, ye = (e) => {\n    e = e.trim();\n    const t = e.indexOf(\"(\");\n    return t === -1 ? e : e.slice(0, t);\n  }, zr = (e, t, r, n, a, i, u, s, l, d) => {\n    var h = kt(r, n);\n    t = j(t), t = ye(t), i = U(a, i), tt([], [e], (p) => {\n      p = p[0];\n      var w = `${p.name}.${t}`;\n      t.startsWith(\"@@\") && (t = Symbol[t.substring(2)]), s && p.registeredClass.pureVirtualFunctions.push(t);\n      function b() {\n        _t(`Cannot call ${w} due to unbound types`, h);\n      }\n      var _ = p.registeredClass.instancePrototype, S = _[t];\n      return S === void 0 || S.overloadTable === void 0 && S.className !== p.name && S.argCount === r - 2 ? (b.argCount = r - 2, b.className = p.name, _[t] = b) : (ce(_, t, w), _[t].overloadTable[r - 2] = b), tt([], h, (A) => {\n        var I = Ht(w, A, p, i, u);\n        return _[t].overloadTable === void 0 ? (I.argCount = r - 2, _[t] = I) : _[t].overloadTable[r - 2] = I, [];\n      }), [];\n    });\n  }, Nt = [], z = [], zt = (e) => {\n    e > 9 && --z[e + 1] === 0 && (z[e] = void 0, Nt.push(e));\n  }, Gr = () => z.length / 2 - 5 - Nt.length, Xr = () => {\n    z.push(0, 1, void 0, 1, null, 1, !0, 1, !1, 1), c.count_emval_handles = Gr;\n  }, G = {\n    toValue: (e) => (e || C(`Cannot use deleted val. handle = ${e}`), z[e]),\n    toHandle: (e) => {\n      switch (e) {\n        case void 0:\n          return 2;\n        case null:\n          return 4;\n        case !0:\n          return 6;\n        case !1:\n          return 8;\n        default: {\n          const t = Nt.pop() || z.length;\n          return z[t] = e, z[t + 1] = 1, t;\n        }\n      }\n    }\n  }, me = {\n    name: \"emscripten::val\",\n    fromWireType: (e) => {\n      var t = G.toValue(e);\n      return zt(e), t;\n    },\n    toWireType: (e, t) => G.toHandle(t),\n    argPackAdvance: N,\n    readValueFromPointer: lt,\n    destructorFunction: null\n  }, qr = (e) => V(e, me), Yr = (e, t) => {\n    switch (t) {\n      case 4:\n        return function(r) {\n          return this.fromWireType(Kt[r >> 2]);\n        };\n      case 8:\n        return function(r) {\n          return this.fromWireType(te[r >> 3]);\n        };\n      default:\n        throw new TypeError(`invalid float width (${t}): ${e}`);\n    }\n  }, Zr = (e, t, r) => {\n    t = j(t), V(e, {\n      name: t,\n      fromWireType: (n) => n,\n      toWireType: (n, a) => a,\n      argPackAdvance: N,\n      readValueFromPointer: Yr(t, r),\n      destructorFunction: null\n    });\n  }, Qr = (e, t, r, n, a, i, u, s) => {\n    var l = kt(t, r);\n    e = j(e), e = ye(e), a = U(n, a), le(e, function() {\n      _t(`Cannot call ${e} due to unbound types`, l);\n    }, t - 1), tt([], l, (d) => {\n      var h = [d[0], null].concat(d.slice(1));\n      return de(e, Ht(e, h, null, a, i), t - 1), [];\n    });\n  }, Jr = (e, t, r) => {\n    switch (t) {\n      case 1:\n        return r ? (n) => Q[n] : (n) => B[n];\n      case 2:\n        return r ? (n) => ut[n >> 1] : (n) => vt[n >> 1];\n      case 4:\n        return r ? (n) => at[n >> 2] : (n) => P[n >> 2];\n      default:\n        throw new TypeError(`invalid integer width (${t}): ${e}`);\n    }\n  }, Kr = (e, t, r, n, a) => {\n    t = j(t);\n    var i = (h) => h;\n    if (n === 0) {\n      var u = 32 - 8 * r;\n      i = (h) => h << u >>> u;\n    }\n    var s = t.includes(\"unsigned\"), l = (h, p) => {\n    }, d;\n    s ? d = function(h, p) {\n      return l(p, this.name), p >>> 0;\n    } : d = function(h, p) {\n      return l(p, this.name), p;\n    }, V(e, {\n      name: t,\n      fromWireType: i,\n      toWireType: d,\n      argPackAdvance: N,\n      readValueFromPointer: Jr(t, r, n !== 0),\n      destructorFunction: null\n    });\n  }, tn = (e, t, r) => {\n    var n = [Int8Array, Uint8Array, Int16Array, Uint16Array, Int32Array, Uint32Array, Float32Array, Float64Array], a = n[t];\n    function i(u) {\n      var s = P[u >> 2], l = P[u + 4 >> 2];\n      return new a(Q.buffer, l, s);\n    }\n    r = j(r), V(e, {\n      name: r,\n      fromWireType: i,\n      argPackAdvance: N,\n      readValueFromPointer: i\n    }, {\n      ignoreDuplicateRegistrations: !0\n    });\n  }, en = Object.assign({\n    optional: !0\n  }, me), rn = (e, t) => {\n    V(e, en);\n  }, nn = (e, t, r, n) => {\n    if (!(n > 0)) return 0;\n    for (var a = r, i = r + n - 1, u = 0; u < e.length; ++u) {\n      var s = e.charCodeAt(u);\n      if (s >= 55296 && s <= 57343) {\n        var l = e.charCodeAt(++u);\n        s = 65536 + ((s & 1023) << 10) | l & 1023;\n      }\n      if (s <= 127) {\n        if (r >= i) break;\n        t[r++] = s;\n      } else if (s <= 2047) {\n        if (r + 1 >= i) break;\n        t[r++] = 192 | s >> 6, t[r++] = 128 | s & 63;\n      } else if (s <= 65535) {\n        if (r + 2 >= i) break;\n        t[r++] = 224 | s >> 12, t[r++] = 128 | s >> 6 & 63, t[r++] = 128 | s & 63;\n      } else {\n        if (r + 3 >= i) break;\n        t[r++] = 240 | s >> 18, t[r++] = 128 | s >> 12 & 63, t[r++] = 128 | s >> 6 & 63, t[r++] = 128 | s & 63;\n      }\n    }\n    return t[r] = 0, r - a;\n  }, it = (e, t, r) => nn(e, B, t, r), ge = (e) => {\n    for (var t = 0, r = 0; r < e.length; ++r) {\n      var n = e.charCodeAt(r);\n      n <= 127 ? t++ : n <= 2047 ? t += 2 : n >= 55296 && n <= 57343 ? (t += 4, ++r) : t += 3;\n    }\n    return t;\n  }, we = typeof TextDecoder < \"u\" ? new TextDecoder() : void 0, $e = function(e) {\n    let t = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, r = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : NaN;\n    for (var n = t + r, a = t; e[a] && !(a >= n); ) ++a;\n    if (a - t > 16 && e.buffer && we)\n      return we.decode(e.subarray(t, a));\n    for (var i = \"\"; t < a; ) {\n      var u = e[t++];\n      if (!(u & 128)) {\n        i += String.fromCharCode(u);\n        continue;\n      }\n      var s = e[t++] & 63;\n      if ((u & 224) == 192) {\n        i += String.fromCharCode((u & 31) << 6 | s);\n        continue;\n      }\n      var l = e[t++] & 63;\n      if ((u & 240) == 224 ? u = (u & 15) << 12 | s << 6 | l : u = (u & 7) << 18 | s << 12 | l << 6 | e[t++] & 63, u < 65536)\n        i += String.fromCharCode(u);\n      else {\n        var d = u - 65536;\n        i += String.fromCharCode(55296 | d >> 10, 56320 | d & 1023);\n      }\n    }\n    return i;\n  }, an = (e, t) => e ? $e(B, e, t) : \"\", on = (e, t) => {\n    t = j(t), V(e, {\n      name: t,\n      fromWireType(r) {\n        for (var n = P[r >> 2], a = r + 4, i, s, u = a, s = 0; s <= n; ++s) {\n          var l = a + s;\n          if (s == n || B[l] == 0) {\n            var d = l - u, h = an(u, d);\n            i === void 0 ? i = h : (i += \"\\0\", i += h), u = l + 1;\n          }\n        }\n        return et(r), i;\n      },\n      toWireType(r, n) {\n        n instanceof ArrayBuffer && (n = new Uint8Array(n));\n        var a, i = typeof n == \"string\";\n        i || ArrayBuffer.isView(n) && n.BYTES_PER_ELEMENT == 1 || C(\"Cannot pass non-string to std::string\"), i ? a = ge(n) : a = n.length;\n        var u = Pe(4 + a + 1), s = u + 4;\n        return P[u >> 2] = a, i ? it(n, s, a + 1) : B.set(n, s), r !== null && r.push(et, u), u;\n      },\n      argPackAdvance: N,\n      readValueFromPointer: lt,\n      destructorFunction(r) {\n        et(r);\n      }\n    });\n  }, be = typeof TextDecoder < \"u\" ? new TextDecoder(\"utf-16le\") : void 0, sn = (e, t) => {\n    for (var r = e, n = r >> 1, a = n + t / 2; !(n >= a) && vt[n]; ) ++n;\n    if (r = n << 1, r - e > 32 && be) return be.decode(B.subarray(e, r));\n    for (var i = \"\", u = 0; !(u >= t / 2); ++u) {\n      var s = ut[e + u * 2 >> 1];\n      if (s == 0) break;\n      i += String.fromCharCode(s);\n    }\n    return i;\n  }, un = (e, t, r) => {\n    if (r != null || (r = 2147483647), r < 2) return 0;\n    r -= 2;\n    for (var n = t, a = r < e.length * 2 ? r / 2 : e.length, i = 0; i < a; ++i) {\n      var u = e.charCodeAt(i);\n      ut[t >> 1] = u, t += 2;\n    }\n    return ut[t >> 1] = 0, t - n;\n  }, cn = (e) => e.length * 2, ln = (e, t) => {\n    for (var r = 0, n = \"\"; !(r >= t / 4); ) {\n      var a = at[e + r * 4 >> 2];\n      if (a == 0) break;\n      if (++r, a >= 65536) {\n        var i = a - 65536;\n        n += String.fromCharCode(55296 | i >> 10, 56320 | i & 1023);\n      } else\n        n += String.fromCharCode(a);\n    }\n    return n;\n  }, fn = (e, t, r) => {\n    if (r != null || (r = 2147483647), r < 4) return 0;\n    for (var n = t, a = n + r - 4, i = 0; i < e.length; ++i) {\n      var u = e.charCodeAt(i);\n      if (u >= 55296 && u <= 57343) {\n        var s = e.charCodeAt(++i);\n        u = 65536 + ((u & 1023) << 10) | s & 1023;\n      }\n      if (at[t >> 2] = u, t += 4, t + 4 > a) break;\n    }\n    return at[t >> 2] = 0, t - n;\n  }, dn = (e) => {\n    for (var t = 0, r = 0; r < e.length; ++r) {\n      var n = e.charCodeAt(r);\n      n >= 55296 && n <= 57343 && ++r, t += 4;\n    }\n    return t;\n  }, hn = (e, t, r) => {\n    r = j(r);\n    var n, a, i, u;\n    t === 2 ? (n = sn, a = un, u = cn, i = (s) => vt[s >> 1]) : t === 4 && (n = ln, a = fn, u = dn, i = (s) => P[s >> 2]), V(e, {\n      name: r,\n      fromWireType: (s) => {\n        for (var l = P[s >> 2], d, h = s + 4, p = 0; p <= l; ++p) {\n          var w = s + 4 + p * t;\n          if (p == l || i(w) == 0) {\n            var b = w - h, _ = n(h, b);\n            d === void 0 ? d = _ : (d += \"\\0\", d += _), h = w + t;\n          }\n        }\n        return et(s), d;\n      },\n      toWireType: (s, l) => {\n        typeof l != \"string\" && C(`Cannot pass non-string to C++ string type ${r}`);\n        var d = u(l), h = Pe(4 + d + t);\n        return P[h >> 2] = d / t, a(l, h + 4, d + t), s !== null && s.push(et, h), h;\n      },\n      argPackAdvance: N,\n      readValueFromPointer: lt,\n      destructorFunction(s) {\n        et(s);\n      }\n    });\n  }, pn = (e, t, r, n, a, i) => {\n    $t[e] = {\n      name: j(t),\n      rawConstructor: U(r, n),\n      rawDestructor: U(a, i),\n      fields: []\n    };\n  }, vn = (e, t, r, n, a, i, u, s, l, d) => {\n    $t[e].fields.push({\n      fieldName: j(t),\n      getterReturnType: r,\n      getter: U(n, a),\n      getterContext: i,\n      setterArgumentType: u,\n      setter: U(s, l),\n      setterContext: d\n    });\n  }, yn = (e, t) => {\n    t = j(t), V(e, {\n      isVoid: !0,\n      name: t,\n      argPackAdvance: 0,\n      fromWireType: () => {\n      },\n      toWireType: (r, n) => {\n      }\n    });\n  }, Gt = [], mn = (e, t, r, n) => (e = Gt[e], t = G.toValue(t), e(null, t, r, n)), gn = {}, wn = (e) => {\n    var t = gn[e];\n    return t === void 0 ? j(e) : t;\n  }, Ce = () => {\n    if (typeof globalThis == \"object\")\n      return globalThis;\n    function e(t) {\n      t.$$$embind_global$$$ = t;\n      var r = typeof $$$embind_global$$$ == \"object\" && t.$$$embind_global$$$ == t;\n      return r || delete t.$$$embind_global$$$, r;\n    }\n    if (typeof $$$embind_global$$$ == \"object\" || (typeof global == \"object\" && e(global) ? $$$embind_global$$$ = global : typeof self == \"object\" && e(self) && ($$$embind_global$$$ = self), typeof $$$embind_global$$$ == \"object\"))\n      return $$$embind_global$$$;\n    throw Error(\"unable to get global object.\");\n  }, $n = (e) => e === 0 ? G.toHandle(Ce()) : (e = wn(e), G.toHandle(Ce()[e])), bn = (e) => {\n    var t = Gt.length;\n    return Gt.push(e), t;\n  }, Te = (e, t) => {\n    var r = K[e];\n    return r === void 0 && C(`${t} has unknown type ${ve(e)}`), r;\n  }, Cn = (e, t) => {\n    for (var r = new Array(e), n = 0; n < e; ++n)\n      r[n] = Te(P[t + n * 4 >> 2], `parameter ${n}`);\n    return r;\n  }, Tn = (e, t, r) => {\n    var n = [], a = e.toWireType(n, r);\n    return n.length && (P[t >> 2] = G.toHandle(n)), a;\n  }, Pn = Reflect.construct, En = (e, t, r) => {\n    var n = Cn(e, t), a = n.shift();\n    e--;\n    var i = new Array(e), u = (l, d, h, p) => {\n      for (var w = 0, b = 0; b < e; ++b)\n        i[b] = n[b].readValueFromPointer(p + w), w += n[b].argPackAdvance;\n      var _ = r === 1 ? Pn(d, i) : d.apply(l, i);\n      return Tn(a, h, _);\n    }, s = `methodCaller<(${n.map((l) => l.name).join(\", \")}) => ${a.name}>`;\n    return bn(Ut(s, u));\n  }, _n = (e) => {\n    e > 9 && (z[e + 1] += 1);\n  }, On = (e) => {\n    var t = G.toValue(e);\n    Lt(t), zt(e);\n  }, xn = (e, t) => {\n    e = Te(e, \"_emval_take_value\");\n    var r = e.readValueFromPointer(t);\n    return G.toHandle(r);\n  }, An = (e, t, r, n) => {\n    var a = (/* @__PURE__ */ new Date()).getFullYear(), i = new Date(a, 0, 1), u = new Date(a, 6, 1), s = i.getTimezoneOffset(), l = u.getTimezoneOffset(), d = Math.max(s, l);\n    P[e >> 2] = d * 60, at[t >> 2] = +(s != l);\n    var h = (b) => {\n      var _ = b >= 0 ? \"-\" : \"+\", S = Math.abs(b), A = String(Math.floor(S / 60)).padStart(2, \"0\"), I = String(S % 60).padStart(2, \"0\");\n      return `UTC${_}${A}${I}`;\n    }, p = h(s), w = h(l);\n    l < s ? (it(p, r, 17), it(w, n, 17)) : (it(p, n, 17), it(w, r, 17));\n  }, Sn = () => 2147483648, In = (e, t) => Math.ceil(e / t) * t, Dn = (e) => {\n    var t = nt.buffer, r = (e - t.byteLength + 65535) / 65536 | 0;\n    try {\n      return nt.grow(r), ee(), 1;\n    } catch {\n    }\n  }, Mn = (e) => {\n    var t = B.length;\n    e >>>= 0;\n    var r = Sn();\n    if (e > r)\n      return !1;\n    for (var n = 1; n <= 4; n *= 2) {\n      var a = t * (1 + 0.2 / n);\n      a = Math.min(a, e + 100663296);\n      var i = Math.min(r, In(Math.max(e, a), 65536)), u = Dn(i);\n      if (u)\n        return !0;\n    }\n    return !1;\n  }, Xt = {}, Fn = () => M || \"./this.program\", ht = () => {\n    if (!ht.strings) {\n      var e = (typeof navigator == \"object\" && navigator.languages && navigator.languages[0] || \"C\").replace(\"-\", \"_\") + \".UTF-8\", t = {\n        USER: \"web_user\",\n        LOGNAME: \"web_user\",\n        PATH: \"/\",\n        PWD: \"/\",\n        HOME: \"/home/<USER>",\n        LANG: e,\n        _: Fn()\n      };\n      for (var r in Xt)\n        Xt[r] === void 0 ? delete t[r] : t[r] = Xt[r];\n      var n = [];\n      for (var r in t)\n        n.push(`${r}=${t[r]}`);\n      ht.strings = n;\n    }\n    return ht.strings;\n  }, jn = (e, t) => {\n    var r = 0, n = 0;\n    for (var a of ht()) {\n      var i = t + r;\n      P[e + n >> 2] = i, r += it(a, i, 1 / 0) + 1, n += 4;\n    }\n    return 0;\n  }, Rn = (e, t) => {\n    var r = ht();\n    P[e >> 2] = r.length;\n    var n = 0;\n    for (var a of r)\n      n += ge(a) + 1;\n    return P[t >> 2] = n, 0;\n  }, Ln = (e) => 52;\n  function Bn(e, t, r, n, a) {\n    return 70;\n  }\n  var Wn = [null, [], []], Un = (e, t) => {\n    var r = Wn[e];\n    t === 0 || t === 10 ? ((e === 1 ? st : k)($e(r)), r.length = 0) : r.push(t);\n  }, Vn = (e, t, r, n) => {\n    for (var a = 0, i = 0; i < r; i++) {\n      var u = P[t >> 2], s = P[t + 4 >> 2];\n      t += 8;\n      for (var l = 0; l < s; l++)\n        Un(e, B[u + l]);\n      a += s;\n    }\n    return P[n >> 2] = a, 0;\n  }, kn = (e) => e;\n  br(), _r(), Br(), Xr(), c.noExitRuntime && c.noExitRuntime, c.print && (st = c.print), c.printErr && (k = c.printErr), c.wasmBinary && (Z = c.wasmBinary), c.arguments && c.arguments, c.thisProgram && (M = c.thisProgram);\n  var Hn = {\n    s: ur,\n    w: cr,\n    a: lr,\n    j: fr,\n    m: dr,\n    N: hr,\n    p: pr,\n    da: vr,\n    d: yr,\n    _: mr,\n    sa: wr,\n    Z: $r,\n    na: Tr,\n    qa: kr,\n    pa: Nr,\n    F: zr,\n    la: qr,\n    R: Zr,\n    S: Qr,\n    y: Kr,\n    t: tn,\n    ra: rn,\n    ma: on,\n    O: hn,\n    K: pn,\n    ta: vn,\n    oa: yn,\n    V: mn,\n    ua: zt,\n    wa: $n,\n    $: En,\n    T: _n,\n    va: On,\n    ka: xn,\n    aa: An,\n    ea: Mn,\n    ba: jn,\n    ca: Rn,\n    fa: Ln,\n    X: Bn,\n    Q: Vn,\n    I: ba,\n    C: Ta,\n    U: oa,\n    P: Sa,\n    q: ma,\n    b: ea,\n    D: $a,\n    ia: Ea,\n    c: na,\n    ha: _a,\n    h: aa,\n    i: la,\n    r: da,\n    M: wa,\n    v: pa,\n    E: ya,\n    J: ga,\n    A: Pa,\n    H: Ia,\n    W: Fa,\n    k: sa,\n    f: ia,\n    e: ra,\n    g: ta,\n    L: Aa,\n    l: ca,\n    ja: Ca,\n    o: ha,\n    x: fa,\n    u: va,\n    ga: xa,\n    B: Oa,\n    n: ua,\n    G: Da,\n    Y: Ma,\n    z: kn\n  }, E = await or();\n  E.ya;\n  var Nn = E.za, et = c._free = E.Aa, Pe = c._malloc = E.Ca, zn = E.Da, v = E.Ea, Gn = E.Fa, Xn = E.Ga, qn = E.Ha, Yn = E.Ia, Zn = E.Ja, Qn = E.Ka;\n  c.dynCall_viijii = E.La;\n  var Jn = c.dynCall_vij = E.Ma;\n  c.dynCall_jiji = E.Na;\n  var Kn = c.dynCall_jiiii = E.Oa;\n  c.dynCall_iiiiij = E.Pa, c.dynCall_iiiiijj = E.Qa, c.dynCall_iiiiiijj = E.Ra;\n  function ta(e, t, r, n) {\n    var a = m();\n    try {\n      g(e)(t, r, n);\n    } catch (i) {\n      if (y(a), i !== i + 0) throw i;\n      v(1, 0);\n    }\n  }\n  function ea(e, t) {\n    var r = m();\n    try {\n      return g(e)(t);\n    } catch (n) {\n      if (y(r), n !== n + 0) throw n;\n      v(1, 0);\n    }\n  }\n  function ra(e, t, r) {\n    var n = m();\n    try {\n      g(e)(t, r);\n    } catch (a) {\n      if (y(n), a !== a + 0) throw a;\n      v(1, 0);\n    }\n  }\n  function na(e, t, r) {\n    var n = m();\n    try {\n      return g(e)(t, r);\n    } catch (a) {\n      if (y(n), a !== a + 0) throw a;\n      v(1, 0);\n    }\n  }\n  function aa(e, t, r, n) {\n    var a = m();\n    try {\n      return g(e)(t, r, n);\n    } catch (i) {\n      if (y(a), i !== i + 0) throw i;\n      v(1, 0);\n    }\n  }\n  function oa(e, t, r, n, a) {\n    var i = m();\n    try {\n      return g(e)(t, r, n, a);\n    } catch (u) {\n      if (y(i), u !== u + 0) throw u;\n      v(1, 0);\n    }\n  }\n  function ia(e, t) {\n    var r = m();\n    try {\n      g(e)(t);\n    } catch (n) {\n      if (y(r), n !== n + 0) throw n;\n      v(1, 0);\n    }\n  }\n  function sa(e) {\n    var t = m();\n    try {\n      g(e)();\n    } catch (r) {\n      if (y(t), r !== r + 0) throw r;\n      v(1, 0);\n    }\n  }\n  function ua(e, t, r, n, a, i, u, s, l, d, h) {\n    var p = m();\n    try {\n      g(e)(t, r, n, a, i, u, s, l, d, h);\n    } catch (w) {\n      if (y(p), w !== w + 0) throw w;\n      v(1, 0);\n    }\n  }\n  function ca(e, t, r, n, a) {\n    var i = m();\n    try {\n      g(e)(t, r, n, a);\n    } catch (u) {\n      if (y(i), u !== u + 0) throw u;\n      v(1, 0);\n    }\n  }\n  function la(e, t, r, n, a) {\n    var i = m();\n    try {\n      return g(e)(t, r, n, a);\n    } catch (u) {\n      if (y(i), u !== u + 0) throw u;\n      v(1, 0);\n    }\n  }\n  function fa(e, t, r, n, a, i, u) {\n    var s = m();\n    try {\n      g(e)(t, r, n, a, i, u);\n    } catch (l) {\n      if (y(s), l !== l + 0) throw l;\n      v(1, 0);\n    }\n  }\n  function da(e, t, r, n, a, i) {\n    var u = m();\n    try {\n      return g(e)(t, r, n, a, i);\n    } catch (s) {\n      if (y(u), s !== s + 0) throw s;\n      v(1, 0);\n    }\n  }\n  function ha(e, t, r, n, a, i) {\n    var u = m();\n    try {\n      g(e)(t, r, n, a, i);\n    } catch (s) {\n      if (y(u), s !== s + 0) throw s;\n      v(1, 0);\n    }\n  }\n  function pa(e, t, r, n, a, i, u) {\n    var s = m();\n    try {\n      return g(e)(t, r, n, a, i, u);\n    } catch (l) {\n      if (y(s), l !== l + 0) throw l;\n      v(1, 0);\n    }\n  }\n  function va(e, t, r, n, a, i, u, s) {\n    var l = m();\n    try {\n      g(e)(t, r, n, a, i, u, s);\n    } catch (d) {\n      if (y(l), d !== d + 0) throw d;\n      v(1, 0);\n    }\n  }\n  function ya(e, t, r, n, a, i, u, s) {\n    var l = m();\n    try {\n      return g(e)(t, r, n, a, i, u, s);\n    } catch (d) {\n      if (y(l), d !== d + 0) throw d;\n      v(1, 0);\n    }\n  }\n  function ma(e) {\n    var t = m();\n    try {\n      return g(e)();\n    } catch (r) {\n      if (y(t), r !== r + 0) throw r;\n      v(1, 0);\n    }\n  }\n  function ga(e, t, r, n, a, i, u, s, l) {\n    var d = m();\n    try {\n      return g(e)(t, r, n, a, i, u, s, l);\n    } catch (h) {\n      if (y(d), h !== h + 0) throw h;\n      v(1, 0);\n    }\n  }\n  function wa(e, t, r, n, a, i, u) {\n    var s = m();\n    try {\n      return g(e)(t, r, n, a, i, u);\n    } catch (l) {\n      if (y(s), l !== l + 0) throw l;\n      v(1, 0);\n    }\n  }\n  function $a(e, t, r, n) {\n    var a = m();\n    try {\n      return g(e)(t, r, n);\n    } catch (i) {\n      if (y(a), i !== i + 0) throw i;\n      v(1, 0);\n    }\n  }\n  function ba(e, t, r, n) {\n    var a = m();\n    try {\n      return g(e)(t, r, n);\n    } catch (i) {\n      if (y(a), i !== i + 0) throw i;\n      v(1, 0);\n    }\n  }\n  function Ca(e, t, r, n, a, i, u, s) {\n    var l = m();\n    try {\n      g(e)(t, r, n, a, i, u, s);\n    } catch (d) {\n      if (y(l), d !== d + 0) throw d;\n      v(1, 0);\n    }\n  }\n  function Ta(e, t, r, n, a, i) {\n    var u = m();\n    try {\n      return g(e)(t, r, n, a, i);\n    } catch (s) {\n      if (y(u), s !== s + 0) throw s;\n      v(1, 0);\n    }\n  }\n  function Pa(e, t, r, n, a, i, u, s, l, d) {\n    var h = m();\n    try {\n      return g(e)(t, r, n, a, i, u, s, l, d);\n    } catch (p) {\n      if (y(h), p !== p + 0) throw p;\n      v(1, 0);\n    }\n  }\n  function Ea(e, t, r) {\n    var n = m();\n    try {\n      return g(e)(t, r);\n    } catch (a) {\n      if (y(n), a !== a + 0) throw a;\n      v(1, 0);\n    }\n  }\n  function _a(e, t, r, n, a) {\n    var i = m();\n    try {\n      return g(e)(t, r, n, a);\n    } catch (u) {\n      if (y(i), u !== u + 0) throw u;\n      v(1, 0);\n    }\n  }\n  function Oa(e, t, r, n, a, i, u, s, l, d) {\n    var h = m();\n    try {\n      g(e)(t, r, n, a, i, u, s, l, d);\n    } catch (p) {\n      if (y(h), p !== p + 0) throw p;\n      v(1, 0);\n    }\n  }\n  function xa(e, t, r, n, a, i, u, s, l) {\n    var d = m();\n    try {\n      g(e)(t, r, n, a, i, u, s, l);\n    } catch (h) {\n      if (y(d), h !== h + 0) throw h;\n      v(1, 0);\n    }\n  }\n  function Aa(e, t, r, n, a, i, u) {\n    var s = m();\n    try {\n      g(e)(t, r, n, a, i, u);\n    } catch (l) {\n      if (y(s), l !== l + 0) throw l;\n      v(1, 0);\n    }\n  }\n  function Sa(e, t, r, n) {\n    var a = m();\n    try {\n      return g(e)(t, r, n);\n    } catch (i) {\n      if (y(a), i !== i + 0) throw i;\n      v(1, 0);\n    }\n  }\n  function Ia(e, t, r, n, a, i, u, s, l, d, h, p) {\n    var w = m();\n    try {\n      return g(e)(t, r, n, a, i, u, s, l, d, h, p);\n    } catch (b) {\n      if (y(w), b !== b + 0) throw b;\n      v(1, 0);\n    }\n  }\n  function Da(e, t, r, n, a, i, u, s, l, d, h, p, w, b, _, S) {\n    var A = m();\n    try {\n      g(e)(t, r, n, a, i, u, s, l, d, h, p, w, b, _, S);\n    } catch (I) {\n      if (y(A), I !== I + 0) throw I;\n      v(1, 0);\n    }\n  }\n  function Ma(e, t, r, n) {\n    var a = m();\n    try {\n      Jn(e, t, r, n);\n    } catch (i) {\n      if (y(a), i !== i + 0) throw i;\n      v(1, 0);\n    }\n  }\n  function Fa(e, t, r, n, a) {\n    var i = m();\n    try {\n      return Kn(e, t, r, n, a);\n    } catch (u) {\n      if (y(i), u !== u + 0) throw u;\n      v(1, 0);\n    }\n  }\n  function qt() {\n    if (J > 0) {\n      ct = qt;\n      return;\n    }\n    if (qe(), J > 0) {\n      ct = qt;\n      return;\n    }\n    function e() {\n      var t;\n      c.calledRun = !0, !Jt && (Ye(), T(c), (t = c.onRuntimeInitialized) === null || t === void 0 || t.call(c), Ze());\n    }\n    c.setStatus ? (c.setStatus(\"Running...\"), setTimeout(() => {\n      setTimeout(() => c.setStatus(\"\"), 1), e();\n    }, 1)) : e();\n  }\n  function ja() {\n    if (c.preInit)\n      for (typeof c.preInit == \"function\" && (c.preInit = [c.preInit]); c.preInit.length > 0; )\n        c.preInit.shift()();\n  }\n  return ja(), qt(), f = x, f;\n};\nfunction Be(o) {\n  return Le(Qt, o);\n}\nfunction Co() {\n  return Ka(Qt);\n}\nfunction To(o) {\n  Be({\n    overrides: o,\n    equalityFn: Object.is,\n    fireImmediately: !1\n  });\n}\nasync function eo(o, f) {\n  return to(Qt, o, f);\n}\nconst Po = \"3c07027dbfd6da47330ac18e74e6924019d9ac9a4c1e161b800666f9e3c925ca\", We = [\n  [\"aztec\", \"Aztec\"],\n  [\"code_128\", \"Code128\"],\n  [\"code_39\", \"Code39\"],\n  [\"code_93\", \"Code93\"],\n  [\"codabar\", \"Codabar\"],\n  [\"databar\", \"DataBar\"],\n  [\"databar_expanded\", \"DataBarExpanded\"],\n  [\"databar_limited\", \"DataBarLimited\"],\n  [\"data_matrix\", \"DataMatrix\"],\n  [\"dx_film_edge\", \"DXFilmEdge\"],\n  [\"ean_13\", \"EAN-13\"],\n  [\"ean_8\", \"EAN-8\"],\n  [\"itf\", \"ITF\"],\n  [\"maxi_code\", \"MaxiCode\"],\n  [\"micro_qr_code\", \"MicroQRCode\"],\n  [\"pdf417\", \"PDF417\"],\n  [\"qr_code\", \"QRCode\"],\n  [\"rm_qr_code\", \"rMQRCode\"],\n  [\"upc_a\", \"UPC-A\"],\n  [\"upc_e\", \"UPC-E\"],\n  [\"linear_codes\", \"Linear-Codes\"],\n  [\"matrix_codes\", \"Matrix-Codes\"],\n  [\"any\", \"Any\"]\n], ro = [...We, [\"unknown\"]].map((o) => o[0]), Zt = new Map(\n  We\n);\nfunction no(o) {\n  for (const [f, c] of Zt)\n    if (o === c)\n      return f;\n  return \"unknown\";\n}\nfunction ao(o) {\n  if (Ue(o))\n    return {\n      width: o.naturalWidth,\n      height: o.naturalHeight\n    };\n  if (Ve(o))\n    return {\n      width: o.width.baseVal.value,\n      height: o.height.baseVal.value\n    };\n  if (ke(o))\n    return {\n      width: o.videoWidth,\n      height: o.videoHeight\n    };\n  if (Ne(o))\n    return {\n      width: o.width,\n      height: o.height\n    };\n  if (Ge(o))\n    return {\n      width: o.displayWidth,\n      height: o.displayHeight\n    };\n  if (He(o))\n    return {\n      width: o.width,\n      height: o.height\n    };\n  if (ze(o))\n    return {\n      width: o.width,\n      height: o.height\n    };\n  throw new TypeError(\n    \"The provided value is not of type '(Blob or HTMLCanvasElement or HTMLImageElement or HTMLVideoElement or ImageBitmap or ImageData or OffscreenCanvas or SVGImageElement or VideoFrame)'.\"\n  );\n}\nfunction Ue(o) {\n  var f, c;\n  try {\n    return o instanceof ((c = (f = o == null ? void 0 : o.ownerDocument) == null ? void 0 : f.defaultView) == null ? void 0 : c.HTMLImageElement);\n  } catch {\n    return !1;\n  }\n}\nfunction Ve(o) {\n  var f, c;\n  try {\n    return o instanceof ((c = (f = o == null ? void 0 : o.ownerDocument) == null ? void 0 : f.defaultView) == null ? void 0 : c.SVGImageElement);\n  } catch {\n    return !1;\n  }\n}\nfunction ke(o) {\n  var f, c;\n  try {\n    return o instanceof ((c = (f = o == null ? void 0 : o.ownerDocument) == null ? void 0 : f.defaultView) == null ? void 0 : c.HTMLVideoElement);\n  } catch {\n    return !1;\n  }\n}\nfunction He(o) {\n  var f, c;\n  try {\n    return o instanceof ((c = (f = o == null ? void 0 : o.ownerDocument) == null ? void 0 : f.defaultView) == null ? void 0 : c.HTMLCanvasElement);\n  } catch {\n    return !1;\n  }\n}\nfunction Ne(o) {\n  try {\n    return o instanceof ImageBitmap || Object.prototype.toString.call(o) === \"[object ImageBitmap]\";\n  } catch {\n    return !1;\n  }\n}\nfunction ze(o) {\n  try {\n    return o instanceof OffscreenCanvas || Object.prototype.toString.call(o) === \"[object OffscreenCanvas]\";\n  } catch {\n    return !1;\n  }\n}\nfunction Ge(o) {\n  try {\n    return o instanceof VideoFrame || Object.prototype.toString.call(o) === \"[object VideoFrame]\";\n  } catch {\n    return !1;\n  }\n}\nfunction oo(o) {\n  try {\n    return o instanceof Blob || Object.prototype.toString.call(o) === \"[object Blob]\";\n  } catch {\n    return !1;\n  }\n}\nfunction io(o) {\n  try {\n    return o instanceof ImageData || Object.prototype.toString.call(o) === \"[object ImageData]\";\n  } catch {\n    return !1;\n  }\n}\nfunction so(o, f) {\n  try {\n    const c = new OffscreenCanvas(o, f);\n    if (c.getContext(\"2d\") instanceof OffscreenCanvasRenderingContext2D)\n      return c;\n    throw void 0;\n  } catch {\n    const c = document.createElement(\"canvas\");\n    return c.width = o, c.height = f, c;\n  }\n}\nasync function Xe(o) {\n  if (Ue(o) && !await fo(o))\n    throw new DOMException(\n      \"Failed to load or decode HTMLImageElement.\",\n      \"InvalidStateError\"\n    );\n  if (Ve(o) && !await ho(o))\n    throw new DOMException(\n      \"Failed to load or decode SVGImageElement.\",\n      \"InvalidStateError\"\n    );\n  if (Ge(o) && po(o))\n    throw new DOMException(\"VideoFrame is closed.\", \"InvalidStateError\");\n  if (ke(o) && (o.readyState === 0 || o.readyState === 1))\n    throw new DOMException(\"Invalid element or state.\", \"InvalidStateError\");\n  if (Ne(o) && yo(o))\n    throw new DOMException(\n      \"The image source is detached.\",\n      \"InvalidStateError\"\n    );\n  const { width: f, height: c } = ao(o);\n  if (f === 0 || c === 0)\n    return null;\n  const $ = so(f, c).getContext(\"2d\");\n  $.drawImage(o, 0, 0);\n  try {\n    return $.getImageData(0, 0, f, c);\n  } catch {\n    throw new DOMException(\"Source would taint origin.\", \"SecurityError\");\n  }\n}\nasync function uo(o) {\n  let f;\n  try {\n    f = await createImageBitmap(o);\n  } catch {\n    try {\n      if (globalThis.Image) {\n        f = new Image();\n        let $ = \"\";\n        try {\n          $ = URL.createObjectURL(o), f.src = $, await f.decode();\n        } finally {\n          URL.revokeObjectURL($);\n        }\n      } else\n        return o;\n    } catch {\n      throw new DOMException(\n        \"Failed to load or decode Blob.\",\n        \"InvalidStateError\"\n      );\n    }\n  }\n  return await Xe(f);\n}\nfunction co(o) {\n  const { width: f, height: c } = o;\n  if (f === 0 || c === 0)\n    return null;\n  const T = o.getContext(\"2d\");\n  try {\n    return T.getImageData(0, 0, f, c);\n  } catch {\n    throw new DOMException(\"Source would taint origin.\", \"SecurityError\");\n  }\n}\nasync function lo(o) {\n  if (oo(o))\n    return await uo(o);\n  if (io(o)) {\n    if (vo(o))\n      throw new DOMException(\n        \"The image data has been detached.\",\n        \"InvalidStateError\"\n      );\n    return o;\n  }\n  return He(o) || ze(o) ? co(o) : await Xe(o);\n}\nasync function fo(o) {\n  try {\n    return await o.decode(), !0;\n  } catch {\n    return !1;\n  }\n}\nasync function ho(o) {\n  var f;\n  try {\n    return await ((f = o.decode) == null ? void 0 : f.call(o)), !0;\n  } catch {\n    return !1;\n  }\n}\nfunction po(o) {\n  return o.format === null;\n}\nfunction vo(o) {\n  return o.data.buffer.byteLength === 0;\n}\nfunction yo(o) {\n  return o.width === 0 && o.height === 0;\n}\nfunction Re(o, f) {\n  return mo(o) ? new DOMException(`${f}: ${o.message}`, o.name) : go(o) ? new o.constructor(`${f}: ${o.message}`) : new Error(`${f}: ${o}`);\n}\nfunction mo(o) {\n  return o instanceof DOMException || Object.prototype.toString.call(o) === \"[object DOMException]\";\n}\nfunction go(o) {\n  return o instanceof Error || Object.prototype.toString.call(o) === \"[object Error]\";\n}\nvar pt;\nclass Eo {\n  constructor(f = {}) {\n    De(this, pt);\n    var c;\n    try {\n      const T = (c = f == null ? void 0 : f.formats) == null ? void 0 : c.filter(\n        ($) => $ !== \"unknown\"\n      );\n      if ((T == null ? void 0 : T.length) === 0)\n        throw new TypeError(\"Hint option provided, but is empty.\");\n      for (const $ of T != null ? T : [])\n        if (!Zt.has($))\n          throw new TypeError(\n            `Failed to read the 'formats' property from 'BarcodeDetectorOptions': The provided value '${$}' is not a valid enum value of type BarcodeFormat.`\n          );\n      Me(this, pt, T != null ? T : []), Be({ fireImmediately: !0 }).catch(() => {\n      });\n    } catch (T) {\n      throw Re(\n        T,\n        \"Failed to construct 'BarcodeDetector'\"\n      );\n    }\n  }\n  static async getSupportedFormats() {\n    return ro.filter((f) => f !== \"unknown\");\n  }\n  async detect(f) {\n    try {\n      const c = await lo(f);\n      if (c === null)\n        return [];\n      let T;\n      const $ = {\n        tryCode39ExtendedMode: !1,\n        textMode: \"Plain\",\n        formats: Ie(this, pt).map((x) => Zt.get(x))\n      };\n      try {\n        T = await eo(c, $);\n      } catch (x) {\n        throw console.error(x), new DOMException(\n          \"Barcode detection service unavailable.\",\n          \"NotSupportedError\"\n        );\n      }\n      return T.map((x) => {\n        const {\n          topLeft: { x: D, y: R },\n          topRight: { x: O, y: M },\n          bottomLeft: { x: F, y: q },\n          bottomRight: { x: rt, y: Y }\n        } = x.position, st = Math.min(D, O, F, rt), k = Math.min(R, M, q, Y), Z = Math.max(D, O, F, rt), nt = Math.max(R, M, q, Y);\n        return {\n          boundingBox: new DOMRectReadOnly(\n            st,\n            k,\n            Z - st,\n            nt - k\n          ),\n          rawValue: x.text,\n          format: no(x.format),\n          cornerPoints: [\n            {\n              x: D,\n              y: R\n            },\n            {\n              x: O,\n              y: M\n            },\n            {\n              x: rt,\n              y: Y\n            },\n            {\n              x: F,\n              y: q\n            }\n          ]\n        };\n      });\n    } catch (c) {\n      throw Re(\n        c,\n        \"Failed to execute 'detect' on 'BarcodeDetector'\"\n      );\n    }\n  }\n}\npt = new WeakMap();\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/barcode-detector/dist/es/ponyfill.js\n");

/***/ })

};
;