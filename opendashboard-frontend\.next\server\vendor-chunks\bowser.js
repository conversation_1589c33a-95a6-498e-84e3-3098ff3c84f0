"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bowser";
exports.ids = ["vendor-chunks/bowser"];
exports.modules = {

/***/ "(ssr)/./node_modules/bowser/src/bowser.js":
/*!*******************************************!*\
  !*** ./node_modules/bowser/src/bowser.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parser.js */ \"(ssr)/./node_modules/bowser/src/parser.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n/*!\n * Bowser - a browser detector\n * https://github.com/lancedikson/bowser\n * MIT License | (c) Dustin Diaz 2012-2015\n * MIT License | (c) Denis Demchenko 2015-2019\n */\n\n\n\n/**\n * Bowser class.\n * Keep it simple as much as it can be.\n * It's supposed to work with collections of {@link Parser} instances\n * rather then solve one-instance problems.\n * All the one-instance stuff is located in Parser class.\n *\n * @class\n * @classdesc Bowser is a static object, that provides an API to the Parsers\n * @hideconstructor\n */\nclass Bowser {\n  /**\n   * Creates a {@link Parser} instance\n   *\n   * @param {String} UA UserAgent string\n   * @param {Boolean} [skipParsing=false] Will make the Parser postpone parsing until you ask it\n   * explicitly. Same as `skipParsing` for {@link Parser}.\n   * @returns {Parser}\n   * @throws {Error} when UA is not a String\n   *\n   * @example\n   * const parser = Bowser.getParser(window.navigator.userAgent);\n   * const result = parser.getResult();\n   */\n  static getParser(UA, skipParsing = false) {\n    if (typeof UA !== 'string') {\n      throw new Error('UserAgent should be a string');\n    }\n    return new _parser_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](UA, skipParsing);\n  }\n\n  /**\n   * Creates a {@link Parser} instance and runs {@link Parser.getResult} immediately\n   *\n   * @param UA\n   * @return {ParsedResult}\n   *\n   * @example\n   * const result = Bowser.parse(window.navigator.userAgent);\n   */\n  static parse(UA) {\n    return (new _parser_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"](UA)).getResult();\n  }\n\n  static get BROWSER_MAP() {\n    return _constants_js__WEBPACK_IMPORTED_MODULE_1__.BROWSER_MAP;\n  }\n\n  static get ENGINE_MAP() {\n    return _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP;\n  }\n\n  static get OS_MAP() {\n    return _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP;\n  }\n\n  static get PLATFORMS_MAP() {\n    return _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP;\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Bowser);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/bowser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/constants.js":
/*!**********************************************!*\
  !*** ./node_modules/bowser/src/constants.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BROWSER_ALIASES_MAP: () => (/* binding */ BROWSER_ALIASES_MAP),\n/* harmony export */   BROWSER_MAP: () => (/* binding */ BROWSER_MAP),\n/* harmony export */   ENGINE_MAP: () => (/* binding */ ENGINE_MAP),\n/* harmony export */   OS_MAP: () => (/* binding */ OS_MAP),\n/* harmony export */   PLATFORMS_MAP: () => (/* binding */ PLATFORMS_MAP)\n/* harmony export */ });\n// NOTE: this list must be up-to-date with browsers listed in\n// test/acceptance/useragentstrings.yml\nconst BROWSER_ALIASES_MAP = {\n  'Amazon Silk': 'amazon_silk',\n  'Android Browser': 'android',\n  Bada: 'bada',\n  BlackBerry: 'blackberry',\n  Chrome: 'chrome',\n  Chromium: 'chromium',\n  Electron: 'electron',\n  Epiphany: 'epiphany',\n  Firefox: 'firefox',\n  Focus: 'focus',\n  Generic: 'generic',\n  'Google Search': 'google_search',\n  Googlebot: 'googlebot',\n  'Internet Explorer': 'ie',\n  'K-Meleon': 'k_meleon',\n  Maxthon: 'maxthon',\n  'Microsoft Edge': 'edge',\n  'MZ Browser': 'mz',\n  'NAVER Whale Browser': 'naver',\n  Opera: 'opera',\n  'Opera Coast': 'opera_coast',\n  PhantomJS: 'phantomjs',\n  Puffin: 'puffin',\n  QupZilla: 'qupzilla',\n  QQ: 'qq',\n  QQLite: 'qqlite',\n  Safari: 'safari',\n  Sailfish: 'sailfish',\n  'Samsung Internet for Android': 'samsung_internet',\n  SeaMonkey: 'seamonkey',\n  Sleipnir: 'sleipnir',\n  Swing: 'swing',\n  Tizen: 'tizen',\n  'UC Browser': 'uc',\n  Vivaldi: 'vivaldi',\n  'WebOS Browser': 'webos',\n  WeChat: 'wechat',\n  'Yandex Browser': 'yandex',\n  Roku: 'roku',\n};\n\nconst BROWSER_MAP = {\n  amazon_silk: 'Amazon Silk',\n  android: 'Android Browser',\n  bada: 'Bada',\n  blackberry: 'BlackBerry',\n  chrome: 'Chrome',\n  chromium: 'Chromium',\n  electron: 'Electron',\n  epiphany: 'Epiphany',\n  firefox: 'Firefox',\n  focus: 'Focus',\n  generic: 'Generic',\n  googlebot: 'Googlebot',\n  google_search: 'Google Search',\n  ie: 'Internet Explorer',\n  k_meleon: 'K-Meleon',\n  maxthon: 'Maxthon',\n  edge: 'Microsoft Edge',\n  mz: 'MZ Browser',\n  naver: 'NAVER Whale Browser',\n  opera: 'Opera',\n  opera_coast: 'Opera Coast',\n  phantomjs: 'PhantomJS',\n  puffin: 'Puffin',\n  qupzilla: 'QupZilla',\n  qq: 'QQ Browser',\n  qqlite: 'QQ Browser Lite',\n  safari: 'Safari',\n  sailfish: 'Sailfish',\n  samsung_internet: 'Samsung Internet for Android',\n  seamonkey: 'SeaMonkey',\n  sleipnir: 'Sleipnir',\n  swing: 'Swing',\n  tizen: 'Tizen',\n  uc: 'UC Browser',\n  vivaldi: 'Vivaldi',\n  webos: 'WebOS Browser',\n  wechat: 'WeChat',\n  yandex: 'Yandex Browser',\n};\n\nconst PLATFORMS_MAP = {\n  tablet: 'tablet',\n  mobile: 'mobile',\n  desktop: 'desktop',\n  tv: 'tv',\n};\n\nconst OS_MAP = {\n  WindowsPhone: 'Windows Phone',\n  Windows: 'Windows',\n  MacOS: 'macOS',\n  iOS: 'iOS',\n  Android: 'Android',\n  WebOS: 'WebOS',\n  BlackBerry: 'BlackBerry',\n  Bada: 'Bada',\n  Tizen: 'Tizen',\n  Linux: 'Linux',\n  ChromeOS: 'Chrome OS',\n  PlayStation4: 'PlayStation 4',\n  Roku: 'Roku',\n};\n\nconst ENGINE_MAP = {\n  EdgeHTML: 'EdgeHTML',\n  Blink: 'Blink',\n  Trident: 'Trident',\n  Presto: 'Presto',\n  Gecko: 'Gecko',\n  WebKit: 'WebKit',\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser-browsers.js":
/*!****************************************************!*\
  !*** ./node_modules/bowser/src/parser-browsers.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n/**\n * Browsers' descriptors\n *\n * The idea of descriptors is simple. You should know about them two simple things:\n * 1. Every descriptor has a method or property called `test` and a `describe` method.\n * 2. Order of descriptors is important.\n *\n * More details:\n * 1. Method or property `test` serves as a way to detect whether the UA string\n * matches some certain browser or not. The `describe` method helps to make a result\n * object with params that show some browser-specific things: name, version, etc.\n * 2. Order of descriptors is important because a Parser goes through them one by one\n * in course. For example, if you insert Chrome's descriptor as the first one,\n * more then a half of browsers will be described as Chrome, because they will pass\n * the Chrome descriptor's test.\n *\n * Descriptor's `test` could be a property with an array of RegExps, where every RegExp\n * will be applied to a UA string to test it whether it matches or not.\n * If a descriptor has two or more regexps in the `test` array it tests them one by one\n * with a logical sum operation. Parser stops if it has found any RegExp that matches the UA.\n *\n * Or `test` could be a method. In that case it gets a Parser instance and should\n * return true/false to get the Parser know if this browser descriptor matches the UA or not.\n */\n\n\n\nconst commonVersionIdentifier = /version\\/(\\d+(\\.?_?\\d+)+)/i;\n\nconst browsersList = [\n  /* Googlebot */\n  {\n    test: [/googlebot/i],\n    describe(ua) {\n      const browser = {\n        name: 'Googlebot',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/googlebot\\/(\\d+(\\.\\d+))/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Opera < 13.0 */\n  {\n    test: [/opera/i],\n    describe(ua) {\n      const browser = {\n        name: 'Opera',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:opera)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Opera > 13.0 */\n  {\n    test: [/opr\\/|opios/i],\n    describe(ua) {\n      const browser = {\n        name: 'Opera',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:opr|opios)[\\s/](\\S+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/SamsungBrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'Samsung Internet for Android',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:SamsungBrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/Whale/i],\n    describe(ua) {\n      const browser = {\n        name: 'NAVER Whale Browser',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:whale)[\\s/](\\d+(?:\\.\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/MZBrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'MZ Browser',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:MZBrowser)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/focus/i],\n    describe(ua) {\n      const browser = {\n        name: 'Focus',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:focus)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/swing/i],\n    describe(ua) {\n      const browser = {\n        name: 'Swing',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:swing)[\\s/](\\d+(?:\\.\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/coast/i],\n    describe(ua) {\n      const browser = {\n        name: 'Opera Coast',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:coast)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/opt\\/\\d+(?:.?_?\\d+)+/i],\n    describe(ua) {\n      const browser = {\n        name: 'Opera Touch',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:opt)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/yabrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'Yandex Browser',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:yabrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/ucbrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'UC Browser',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:ucbrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/Maxthon|mxios/i],\n    describe(ua) {\n      const browser = {\n        name: 'Maxthon',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:Maxthon|mxios)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/epiphany/i],\n    describe(ua) {\n      const browser = {\n        name: 'Epiphany',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:epiphany)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/puffin/i],\n    describe(ua) {\n      const browser = {\n        name: 'Puffin',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:puffin)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/sleipnir/i],\n    describe(ua) {\n      const browser = {\n        name: 'Sleipnir',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:sleipnir)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/k-meleon/i],\n    describe(ua) {\n      const browser = {\n        name: 'K-Meleon',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:k-meleon)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/micromessenger/i],\n    describe(ua) {\n      const browser = {\n        name: 'WeChat',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:micromessenger)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/qqbrowser/i],\n    describe(ua) {\n      const browser = {\n        name: (/qqbrowserlite/i).test(ua) ? 'QQ Browser Lite' : 'QQ Browser',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:qqbrowserlite|qqbrowser)[/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/msie|trident/i],\n    describe(ua) {\n      const browser = {\n        name: 'Internet Explorer',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:msie |rv:)(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/\\sedg\\//i],\n    describe(ua) {\n      const browser = {\n        name: 'Microsoft Edge',\n      };\n\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/\\sedg\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/edg([ea]|ios)/i],\n    describe(ua) {\n      const browser = {\n        name: 'Microsoft Edge',\n      };\n\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getSecondMatch(/edg([ea]|ios)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/vivaldi/i],\n    describe(ua) {\n      const browser = {\n        name: 'Vivaldi',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/vivaldi\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/seamonkey/i],\n    describe(ua) {\n      const browser = {\n        name: 'SeaMonkey',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/seamonkey\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/sailfish/i],\n    describe(ua) {\n      const browser = {\n        name: 'Sailfish',\n      };\n\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/sailfish\\s?browser\\/(\\d+(\\.\\d+)?)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/silk/i],\n    describe(ua) {\n      const browser = {\n        name: 'Amazon Silk',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/silk\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/phantom/i],\n    describe(ua) {\n      const browser = {\n        name: 'PhantomJS',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/phantomjs\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/slimerjs/i],\n    describe(ua) {\n      const browser = {\n        name: 'SlimerJS',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/slimerjs\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/blackberry|\\bbb\\d+/i, /rim\\stablet/i],\n    describe(ua) {\n      const browser = {\n        name: 'BlackBerry',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/blackberry[\\d]+\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/(web|hpw)[o0]s/i],\n    describe(ua) {\n      const browser = {\n        name: 'WebOS Browser',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/w(?:eb)?[o0]sbrowser\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/bada/i],\n    describe(ua) {\n      const browser = {\n        name: 'Bada',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/dolfin\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/tizen/i],\n    describe(ua) {\n      const browser = {\n        name: 'Tizen',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:tizen\\s?)?browser\\/(\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/qupzilla/i],\n    describe(ua) {\n      const browser = {\n        name: 'QupZilla',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:qupzilla)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/firefox|iceweasel|fxios/i],\n    describe(ua) {\n      const browser = {\n        name: 'Firefox',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:firefox|iceweasel|fxios)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/electron/i],\n    describe(ua) {\n      const browser = {\n        name: 'Electron',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:electron)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/MiuiBrowser/i],\n    describe(ua) {\n      const browser = {\n        name: 'Miui',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:MiuiBrowser)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/chromium/i],\n    describe(ua) {\n      const browser = {\n        name: 'Chromium',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:chromium)[\\s/](\\d+(\\.?_?\\d+)+)/i, ua) || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/chrome|crios|crmo/i],\n    describe(ua) {\n      const browser = {\n        name: 'Chrome',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:chrome|crios|crmo)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n  {\n    test: [/GSA/i],\n    describe(ua) {\n      const browser = {\n        name: 'Google Search',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:GSA)\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Android Browser */\n  {\n    test(parser) {\n      const notLikeAndroid = !parser.test(/like android/i);\n      const butAndroid = parser.test(/android/i);\n      return notLikeAndroid && butAndroid;\n    },\n    describe(ua) {\n      const browser = {\n        name: 'Android Browser',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* PlayStation 4 */\n  {\n    test: [/playstation 4/i],\n    describe(ua) {\n      const browser = {\n        name: 'PlayStation 4',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Safari */\n  {\n    test: [/safari|applewebkit/i],\n    describe(ua) {\n      const browser = {\n        name: 'Safari',\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(commonVersionIdentifier, ua);\n\n      if (version) {\n        browser.version = version;\n      }\n\n      return browser;\n    },\n  },\n\n  /* Something else */\n  {\n    test: [/.*/i],\n    describe(ua) {\n      /* Here we try to make sure that there are explicit details about the device\n       * in order to decide what regexp exactly we want to apply\n       * (as there is a specific decision based on that conclusion)\n       */\n      const regexpWithoutDeviceSpec = /^(.*)\\/(.*) /;\n      const regexpWithDeviceSpec = /^(.*)\\/(.*)[ \\t]\\((.*)/;\n      const hasDeviceSpec = ua.search('\\\\(') !== -1;\n      const regexp = hasDeviceSpec ? regexpWithDeviceSpec : regexpWithoutDeviceSpec;\n      return {\n        name: _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(regexp, ua),\n        version: _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getSecondMatch(regexp, ua),\n      };\n    },\n  },\n];\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (browsersList);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser-browsers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser-engines.js":
/*!***************************************************!*\
  !*** ./node_modules/bowser/src/parser-engines.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n\n\n\n/*\n * More specific goes first\n */\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n  /* EdgeHTML */\n  {\n    test(parser) {\n      return parser.getBrowserName(true) === 'microsoft edge';\n    },\n    describe(ua) {\n      const isBlinkBased = /\\sedg\\//i.test(ua);\n\n      // return blink if it's blink-based one\n      if (isBlinkBased) {\n        return {\n          name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Blink,\n        };\n      }\n\n      // otherwise match the version and return EdgeHTML\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/edge\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.EdgeHTML,\n        version,\n      };\n    },\n  },\n\n  /* Trident */\n  {\n    test: [/trident/i],\n    describe(ua) {\n      const engine = {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Trident,\n      };\n\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/trident\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        engine.version = version;\n      }\n\n      return engine;\n    },\n  },\n\n  /* Presto */\n  {\n    test(parser) {\n      return parser.test(/presto/i);\n    },\n    describe(ua) {\n      const engine = {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Presto,\n      };\n\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/presto\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        engine.version = version;\n      }\n\n      return engine;\n    },\n  },\n\n  /* Gecko */\n  {\n    test(parser) {\n      const isGecko = parser.test(/gecko/i);\n      const likeGecko = parser.test(/like gecko/i);\n      return isGecko && !likeGecko;\n    },\n    describe(ua) {\n      const engine = {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Gecko,\n      };\n\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/gecko\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        engine.version = version;\n      }\n\n      return engine;\n    },\n  },\n\n  /* Blink */\n  {\n    test: [/(apple)?webkit\\/537\\.36/i],\n    describe() {\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.Blink,\n      };\n    },\n  },\n\n  /* WebKit */\n  {\n    test: [/(apple)?webkit/i],\n    describe(ua) {\n      const engine = {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.ENGINE_MAP.WebKit,\n      };\n\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/webkit\\/(\\d+(\\.?_?\\d+)+)/i, ua);\n\n      if (version) {\n        engine.version = version;\n      }\n\n      return engine;\n    },\n  },\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser-engines.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser-os.js":
/*!**********************************************!*\
  !*** ./node_modules/bowser/src/parser-os.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n  /* Roku */\n  {\n    test: [/Roku\\/DVP/],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/Roku\\/DVP-(\\d+\\.\\d+)/i, ua);\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Roku,\n        version,\n      };\n    },\n  },\n\n  /* Windows Phone */\n  {\n    test: [/windows phone/i],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/windows phone (?:os)?\\s?(\\d+(\\.\\d+)*)/i, ua);\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.WindowsPhone,\n        version,\n      };\n    },\n  },\n\n  /* Windows */\n  {\n    test: [/windows /i],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/Windows ((NT|XP)( \\d\\d?.\\d)?)/i, ua);\n      const versionName = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getWindowsVersionName(version);\n\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Windows,\n        version,\n        versionName,\n      };\n    },\n  },\n\n  /* Firefox on iPad */\n  {\n    test: [/Macintosh(.*?) FxiOS(.*?)\\//],\n    describe(ua) {\n      const result = {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.iOS,\n      };\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getSecondMatch(/(Version\\/)(\\d[\\d.]+)/, ua);\n      if (version) {\n        result.version = version;\n      }\n      return result;\n    },\n  },\n\n  /* macOS */\n  {\n    test: [/macintosh/i],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/mac os x (\\d+(\\.?_?\\d+)+)/i, ua).replace(/[_\\s]/g, '.');\n      const versionName = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getMacOSVersionName(version);\n\n      const os = {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.MacOS,\n        version,\n      };\n      if (versionName) {\n        os.versionName = versionName;\n      }\n      return os;\n    },\n  },\n\n  /* iOS */\n  {\n    test: [/(ipod|iphone|ipad)/i],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/os (\\d+([_\\s]\\d+)*) like mac os x/i, ua).replace(/[_\\s]/g, '.');\n\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.iOS,\n        version,\n      };\n    },\n  },\n\n  /* Android */\n  {\n    test(parser) {\n      const notLikeAndroid = !parser.test(/like android/i);\n      const butAndroid = parser.test(/android/i);\n      return notLikeAndroid && butAndroid;\n    },\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/android[\\s/-](\\d+(\\.\\d+)*)/i, ua);\n      const versionName = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getAndroidVersionName(version);\n      const os = {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Android,\n        version,\n      };\n      if (versionName) {\n        os.versionName = versionName;\n      }\n      return os;\n    },\n  },\n\n  /* WebOS */\n  {\n    test: [/(web|hpw)[o0]s/i],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(?:web|hpw)[o0]s\\/(\\d+(\\.\\d+)*)/i, ua);\n      const os = {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.WebOS,\n      };\n\n      if (version && version.length) {\n        os.version = version;\n      }\n      return os;\n    },\n  },\n\n  /* BlackBerry */\n  {\n    test: [/blackberry|\\bbb\\d+/i, /rim\\stablet/i],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/rim\\stablet\\sos\\s(\\d+(\\.\\d+)*)/i, ua)\n        || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/blackberry\\d+\\/(\\d+([_\\s]\\d+)*)/i, ua)\n        || _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/\\bbb(\\d+)/i, ua);\n\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.BlackBerry,\n        version,\n      };\n    },\n  },\n\n  /* Bada */\n  {\n    test: [/bada/i],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/bada\\/(\\d+(\\.\\d+)*)/i, ua);\n\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Bada,\n        version,\n      };\n    },\n  },\n\n  /* Tizen */\n  {\n    test: [/tizen/i],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/tizen[/\\s](\\d+(\\.\\d+)*)/i, ua);\n\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Tizen,\n        version,\n      };\n    },\n  },\n\n  /* Linux */\n  {\n    test: [/linux/i],\n    describe() {\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.Linux,\n      };\n    },\n  },\n\n  /* Chrome OS */\n  {\n    test: [/CrOS/],\n    describe() {\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.ChromeOS,\n      };\n    },\n  },\n\n  /* Playstation 4 */\n  {\n    test: [/PlayStation 4/],\n    describe(ua) {\n      const version = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/PlayStation 4[/\\s](\\d+(\\.\\d+)*)/i, ua);\n      return {\n        name: _constants_js__WEBPACK_IMPORTED_MODULE_1__.OS_MAP.PlayStation4,\n        version,\n      };\n    },\n  },\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser-os.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser-platforms.js":
/*!*****************************************************!*\
  !*** ./node_modules/bowser/src/parser-platforms.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n\n\n\n/*\n * Tablets go first since usually they have more specific\n * signs to detect.\n */\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ([\n  /* Googlebot */\n  {\n    test: [/googlebot/i],\n    describe() {\n      return {\n        type: 'bot',\n        vendor: 'Google',\n      };\n    },\n  },\n\n  /* Huawei */\n  {\n    test: [/huawei/i],\n    describe(ua) {\n      const model = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(can-l01)/i, ua) && 'Nova';\n      const platform = {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n        vendor: 'Huawei',\n      };\n      if (model) {\n        platform.model = model;\n      }\n      return platform;\n    },\n  },\n\n  /* Nexus Tablet */\n  {\n    test: [/nexus\\s*(?:7|8|9|10).*/i],\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n        vendor: 'Nexus',\n      };\n    },\n  },\n\n  /* iPad */\n  {\n    test: [/ipad/i],\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n        vendor: 'Apple',\n        model: 'iPad',\n      };\n    },\n  },\n\n  /* Firefox on iPad */\n  {\n    test: [/Macintosh(.*?) FxiOS(.*?)\\//],\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n        vendor: 'Apple',\n        model: 'iPad',\n      };\n    },\n  },\n\n  /* Amazon Kindle Fire */\n  {\n    test: [/kftt build/i],\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n        vendor: 'Amazon',\n        model: 'Kindle Fire HD 7',\n      };\n    },\n  },\n\n  /* Another Amazon Tablet with Silk */\n  {\n    test: [/silk/i],\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n        vendor: 'Amazon',\n      };\n    },\n  },\n\n  /* Tablet */\n  {\n    test: [/tablet(?! pc)/i],\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n      };\n    },\n  },\n\n  /* iPod/iPhone */\n  {\n    test(parser) {\n      const iDevice = parser.test(/ipod|iphone/i);\n      const likeIDevice = parser.test(/like (ipod|iphone)/i);\n      return iDevice && !likeIDevice;\n    },\n    describe(ua) {\n      const model = _utils_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].getFirstMatch(/(ipod|iphone)/i, ua);\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n        vendor: 'Apple',\n        model,\n      };\n    },\n  },\n\n  /* Nexus Mobile */\n  {\n    test: [/nexus\\s*[0-6].*/i, /galaxy nexus/i],\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n        vendor: 'Nexus',\n      };\n    },\n  },\n\n  /* Mobile */\n  {\n    test: [/[^-]mobi/i],\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n      };\n    },\n  },\n\n  /* BlackBerry */\n  {\n    test(parser) {\n      return parser.getBrowserName(true) === 'blackberry';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n        vendor: 'BlackBerry',\n      };\n    },\n  },\n\n  /* Bada */\n  {\n    test(parser) {\n      return parser.getBrowserName(true) === 'bada';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n      };\n    },\n  },\n\n  /* Windows Phone */\n  {\n    test(parser) {\n      return parser.getBrowserName() === 'windows phone';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n        vendor: 'Microsoft',\n      };\n    },\n  },\n\n  /* Android Tablet */\n  {\n    test(parser) {\n      const osMajorVersion = Number(String(parser.getOSVersion()).split('.')[0]);\n      return parser.getOSName(true) === 'android' && (osMajorVersion >= 3);\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tablet,\n      };\n    },\n  },\n\n  /* Android Mobile */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'android';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.mobile,\n      };\n    },\n  },\n\n  /* desktop */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'macos';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.desktop,\n        vendor: 'Apple',\n      };\n    },\n  },\n\n  /* Windows */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'windows';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.desktop,\n      };\n    },\n  },\n\n  /* Linux */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'linux';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.desktop,\n      };\n    },\n  },\n\n  /* PlayStation 4 */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'playstation 4';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tv,\n      };\n    },\n  },\n\n  /* Roku */\n  {\n    test(parser) {\n      return parser.getOSName(true) === 'roku';\n    },\n    describe() {\n      return {\n        type: _constants_js__WEBPACK_IMPORTED_MODULE_1__.PLATFORMS_MAP.tv,\n      };\n    },\n  },\n]);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser-platforms.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/parser.js":
/*!*******************************************!*\
  !*** ./node_modules/bowser/src/parser.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _parser_browsers_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parser-browsers.js */ \"(ssr)/./node_modules/bowser/src/parser-browsers.js\");\n/* harmony import */ var _parser_os_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parser-os.js */ \"(ssr)/./node_modules/bowser/src/parser-os.js\");\n/* harmony import */ var _parser_platforms_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./parser-platforms.js */ \"(ssr)/./node_modules/bowser/src/parser-platforms.js\");\n/* harmony import */ var _parser_engines_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./parser-engines.js */ \"(ssr)/./node_modules/bowser/src/parser-engines.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/bowser/src/utils.js\");\n\n\n\n\n\n\n/**\n * The main class that arranges the whole parsing process.\n */\nclass Parser {\n  /**\n   * Create instance of Parser\n   *\n   * @param {String} UA User-Agent string\n   * @param {Boolean} [skipParsing=false] parser can skip parsing in purpose of performance\n   * improvements if you need to make a more particular parsing\n   * like {@link Parser#parseBrowser} or {@link Parser#parsePlatform}\n   *\n   * @throw {Error} in case of empty UA String\n   *\n   * @constructor\n   */\n  constructor(UA, skipParsing = false) {\n    if (UA === void (0) || UA === null || UA === '') {\n      throw new Error(\"UserAgent parameter can't be empty\");\n    }\n\n    this._ua = UA;\n\n    /**\n     * @typedef ParsedResult\n     * @property {Object} browser\n     * @property {String|undefined} [browser.name]\n     * Browser name, like `\"Chrome\"` or `\"Internet Explorer\"`\n     * @property {String|undefined} [browser.version] Browser version as a String `\"12.01.45334.10\"`\n     * @property {Object} os\n     * @property {String|undefined} [os.name] OS name, like `\"Windows\"` or `\"macOS\"`\n     * @property {String|undefined} [os.version] OS version, like `\"NT 5.1\"` or `\"10.11.1\"`\n     * @property {String|undefined} [os.versionName] OS name, like `\"XP\"` or `\"High Sierra\"`\n     * @property {Object} platform\n     * @property {String|undefined} [platform.type]\n     * platform type, can be either `\"desktop\"`, `\"tablet\"` or `\"mobile\"`\n     * @property {String|undefined} [platform.vendor] Vendor of the device,\n     * like `\"Apple\"` or `\"Samsung\"`\n     * @property {String|undefined} [platform.model] Device model,\n     * like `\"iPhone\"` or `\"Kindle Fire HD 7\"`\n     * @property {Object} engine\n     * @property {String|undefined} [engine.name]\n     * Can be any of this: `WebKit`, `Blink`, `Gecko`, `Trident`, `Presto`, `EdgeHTML`\n     * @property {String|undefined} [engine.version] String version of the engine\n     */\n    this.parsedResult = {};\n\n    if (skipParsing !== true) {\n      this.parse();\n    }\n  }\n\n  /**\n   * Get UserAgent string of current Parser instance\n   * @return {String} User-Agent String of the current <Parser> object\n   *\n   * @public\n   */\n  getUA() {\n    return this._ua;\n  }\n\n  /**\n   * Test a UA string for a regexp\n   * @param {RegExp} regex\n   * @return {Boolean}\n   */\n  test(regex) {\n    return regex.test(this._ua);\n  }\n\n  /**\n   * Get parsed browser object\n   * @return {Object}\n   */\n  parseBrowser() {\n    this.parsedResult.browser = {};\n\n    const browserDescriptor = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(_parser_browsers_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"], (_browser) => {\n      if (typeof _browser.test === 'function') {\n        return _browser.test(this);\n      }\n\n      if (_browser.test instanceof Array) {\n        return _browser.test.some(condition => this.test(condition));\n      }\n\n      throw new Error(\"Browser's test function is not valid\");\n    });\n\n    if (browserDescriptor) {\n      this.parsedResult.browser = browserDescriptor.describe(this.getUA());\n    }\n\n    return this.parsedResult.browser;\n  }\n\n  /**\n   * Get parsed browser object\n   * @return {Object}\n   *\n   * @public\n   */\n  getBrowser() {\n    if (this.parsedResult.browser) {\n      return this.parsedResult.browser;\n    }\n\n    return this.parseBrowser();\n  }\n\n  /**\n   * Get browser's name\n   * @return {String} Browser's name or an empty string\n   *\n   * @public\n   */\n  getBrowserName(toLowerCase) {\n    if (toLowerCase) {\n      return String(this.getBrowser().name).toLowerCase() || '';\n    }\n    return this.getBrowser().name || '';\n  }\n\n\n  /**\n   * Get browser's version\n   * @return {String} version of browser\n   *\n   * @public\n   */\n  getBrowserVersion() {\n    return this.getBrowser().version;\n  }\n\n  /**\n   * Get OS\n   * @return {Object}\n   *\n   * @example\n   * this.getOS();\n   * {\n   *   name: 'macOS',\n   *   version: '10.11.12'\n   * }\n   */\n  getOS() {\n    if (this.parsedResult.os) {\n      return this.parsedResult.os;\n    }\n\n    return this.parseOS();\n  }\n\n  /**\n   * Parse OS and save it to this.parsedResult.os\n   * @return {*|{}}\n   */\n  parseOS() {\n    this.parsedResult.os = {};\n\n    const os = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(_parser_os_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"], (_os) => {\n      if (typeof _os.test === 'function') {\n        return _os.test(this);\n      }\n\n      if (_os.test instanceof Array) {\n        return _os.test.some(condition => this.test(condition));\n      }\n\n      throw new Error(\"Browser's test function is not valid\");\n    });\n\n    if (os) {\n      this.parsedResult.os = os.describe(this.getUA());\n    }\n\n    return this.parsedResult.os;\n  }\n\n  /**\n   * Get OS name\n   * @param {Boolean} [toLowerCase] return lower-cased value\n   * @return {String} name of the OS — macOS, Windows, Linux, etc.\n   */\n  getOSName(toLowerCase) {\n    const { name } = this.getOS();\n\n    if (toLowerCase) {\n      return String(name).toLowerCase() || '';\n    }\n\n    return name || '';\n  }\n\n  /**\n   * Get OS version\n   * @return {String} full version with dots ('10.11.12', '5.6', etc)\n   */\n  getOSVersion() {\n    return this.getOS().version;\n  }\n\n  /**\n   * Get parsed platform\n   * @return {{}}\n   */\n  getPlatform() {\n    if (this.parsedResult.platform) {\n      return this.parsedResult.platform;\n    }\n\n    return this.parsePlatform();\n  }\n\n  /**\n   * Get platform name\n   * @param {Boolean} [toLowerCase=false]\n   * @return {*}\n   */\n  getPlatformType(toLowerCase = false) {\n    const { type } = this.getPlatform();\n\n    if (toLowerCase) {\n      return String(type).toLowerCase() || '';\n    }\n\n    return type || '';\n  }\n\n  /**\n   * Get parsed platform\n   * @return {{}}\n   */\n  parsePlatform() {\n    this.parsedResult.platform = {};\n\n    const platform = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(_parser_platforms_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"], (_platform) => {\n      if (typeof _platform.test === 'function') {\n        return _platform.test(this);\n      }\n\n      if (_platform.test instanceof Array) {\n        return _platform.test.some(condition => this.test(condition));\n      }\n\n      throw new Error(\"Browser's test function is not valid\");\n    });\n\n    if (platform) {\n      this.parsedResult.platform = platform.describe(this.getUA());\n    }\n\n    return this.parsedResult.platform;\n  }\n\n  /**\n   * Get parsed engine\n   * @return {{}}\n   */\n  getEngine() {\n    if (this.parsedResult.engine) {\n      return this.parsedResult.engine;\n    }\n\n    return this.parseEngine();\n  }\n\n  /**\n   * Get engines's name\n   * @return {String} Engines's name or an empty string\n   *\n   * @public\n   */\n  getEngineName(toLowerCase) {\n    if (toLowerCase) {\n      return String(this.getEngine().name).toLowerCase() || '';\n    }\n    return this.getEngine().name || '';\n  }\n\n  /**\n   * Get parsed platform\n   * @return {{}}\n   */\n  parseEngine() {\n    this.parsedResult.engine = {};\n\n    const engine = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(_parser_engines_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"], (_engine) => {\n      if (typeof _engine.test === 'function') {\n        return _engine.test(this);\n      }\n\n      if (_engine.test instanceof Array) {\n        return _engine.test.some(condition => this.test(condition));\n      }\n\n      throw new Error(\"Browser's test function is not valid\");\n    });\n\n    if (engine) {\n      this.parsedResult.engine = engine.describe(this.getUA());\n    }\n\n    return this.parsedResult.engine;\n  }\n\n  /**\n   * Parse full information about the browser\n   * @returns {Parser}\n   */\n  parse() {\n    this.parseBrowser();\n    this.parseOS();\n    this.parsePlatform();\n    this.parseEngine();\n\n    return this;\n  }\n\n  /**\n   * Get parsed result\n   * @return {ParsedResult}\n   */\n  getResult() {\n    return _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].assign({}, this.parsedResult);\n  }\n\n  /**\n   * Check if parsed browser matches certain conditions\n   *\n   * @param {Object} checkTree It's one or two layered object,\n   * which can include a platform or an OS on the first layer\n   * and should have browsers specs on the bottom-laying layer\n   *\n   * @returns {Boolean|undefined} Whether the browser satisfies the set conditions or not.\n   * Returns `undefined` when the browser is no described in the checkTree object.\n   *\n   * @example\n   * const browser = Bowser.getParser(window.navigator.userAgent);\n   * if (browser.satisfies({chrome: '>118.01.1322' }))\n   * // or with os\n   * if (browser.satisfies({windows: { chrome: '>118.01.1322' } }))\n   * // or with platforms\n   * if (browser.satisfies({desktop: { chrome: '>118.01.1322' } }))\n   */\n  satisfies(checkTree) {\n    const platformsAndOSes = {};\n    let platformsAndOSCounter = 0;\n    const browsers = {};\n    let browsersCounter = 0;\n\n    const allDefinitions = Object.keys(checkTree);\n\n    allDefinitions.forEach((key) => {\n      const currentDefinition = checkTree[key];\n      if (typeof currentDefinition === 'string') {\n        browsers[key] = currentDefinition;\n        browsersCounter += 1;\n      } else if (typeof currentDefinition === 'object') {\n        platformsAndOSes[key] = currentDefinition;\n        platformsAndOSCounter += 1;\n      }\n    });\n\n    if (platformsAndOSCounter > 0) {\n      const platformsAndOSNames = Object.keys(platformsAndOSes);\n      const OSMatchingDefinition = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(platformsAndOSNames, name => (this.isOS(name)));\n\n      if (OSMatchingDefinition) {\n        const osResult = this.satisfies(platformsAndOSes[OSMatchingDefinition]);\n\n        if (osResult !== void 0) {\n          return osResult;\n        }\n      }\n\n      const platformMatchingDefinition = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(\n        platformsAndOSNames,\n        name => (this.isPlatform(name)),\n      );\n      if (platformMatchingDefinition) {\n        const platformResult = this.satisfies(platformsAndOSes[platformMatchingDefinition]);\n\n        if (platformResult !== void 0) {\n          return platformResult;\n        }\n      }\n    }\n\n    if (browsersCounter > 0) {\n      const browserNames = Object.keys(browsers);\n      const matchingDefinition = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].find(browserNames, name => (this.isBrowser(name, true)));\n\n      if (matchingDefinition !== void 0) {\n        return this.compareVersion(browsers[matchingDefinition]);\n      }\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Check if the browser name equals the passed string\n   * @param browserName The string to compare with the browser name\n   * @param [includingAlias=false] The flag showing whether alias will be included into comparison\n   * @returns {boolean}\n   */\n  isBrowser(browserName, includingAlias = false) {\n    const defaultBrowserName = this.getBrowserName().toLowerCase();\n    let browserNameLower = browserName.toLowerCase();\n    const alias = _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getBrowserTypeByAlias(browserNameLower);\n\n    if (includingAlias && alias) {\n      browserNameLower = alias.toLowerCase();\n    }\n    return browserNameLower === defaultBrowserName;\n  }\n\n  compareVersion(version) {\n    let expectedResults = [0];\n    let comparableVersion = version;\n    let isLoose = false;\n\n    const currentBrowserVersion = this.getBrowserVersion();\n\n    if (typeof currentBrowserVersion !== 'string') {\n      return void 0;\n    }\n\n    if (version[0] === '>' || version[0] === '<') {\n      comparableVersion = version.substr(1);\n      if (version[1] === '=') {\n        isLoose = true;\n        comparableVersion = version.substr(2);\n      } else {\n        expectedResults = [];\n      }\n      if (version[0] === '>') {\n        expectedResults.push(1);\n      } else {\n        expectedResults.push(-1);\n      }\n    } else if (version[0] === '=') {\n      comparableVersion = version.substr(1);\n    } else if (version[0] === '~') {\n      isLoose = true;\n      comparableVersion = version.substr(1);\n    }\n\n    return expectedResults.indexOf(\n      _utils_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"].compareVersions(currentBrowserVersion, comparableVersion, isLoose),\n    ) > -1;\n  }\n\n  isOS(osName) {\n    return this.getOSName(true) === String(osName).toLowerCase();\n  }\n\n  isPlatform(platformType) {\n    return this.getPlatformType(true) === String(platformType).toLowerCase();\n  }\n\n  isEngine(engineName) {\n    return this.getEngineName(true) === String(engineName).toLowerCase();\n  }\n\n  /**\n   * Is anything? Check if the browser is called \"anything\",\n   * the OS called \"anything\" or the platform called \"anything\"\n   * @param {String} anything\n   * @param [includingAlias=false] The flag showing whether alias will be included into comparison\n   * @returns {Boolean}\n   */\n  is(anything, includingAlias = false) {\n    return this.isBrowser(anything, includingAlias) || this.isOS(anything)\n      || this.isPlatform(anything);\n  }\n\n  /**\n   * Check if any of the given values satisfies this.is(anything)\n   * @param {String[]} anythings\n   * @returns {Boolean}\n   */\n  some(anythings = []) {\n    return anythings.some(anything => this.is(anything));\n  }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Parser);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/bowser/src/utils.js":
/*!******************************************!*\
  !*** ./node_modules/bowser/src/utils.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Utils)\n/* harmony export */ });\n/* harmony import */ var _constants_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants.js */ \"(ssr)/./node_modules/bowser/src/constants.js\");\n\n\nclass Utils {\n  /**\n   * Get first matched item for a string\n   * @param {RegExp} regexp\n   * @param {String} ua\n   * @return {Array|{index: number, input: string}|*|boolean|string}\n   */\n  static getFirstMatch(regexp, ua) {\n    const match = ua.match(regexp);\n    return (match && match.length > 0 && match[1]) || '';\n  }\n\n  /**\n   * Get second matched item for a string\n   * @param regexp\n   * @param {String} ua\n   * @return {Array|{index: number, input: string}|*|boolean|string}\n   */\n  static getSecondMatch(regexp, ua) {\n    const match = ua.match(regexp);\n    return (match && match.length > 1 && match[2]) || '';\n  }\n\n  /**\n   * Match a regexp and return a constant or undefined\n   * @param {RegExp} regexp\n   * @param {String} ua\n   * @param {*} _const Any const that will be returned if regexp matches the string\n   * @return {*}\n   */\n  static matchAndReturnConst(regexp, ua, _const) {\n    if (regexp.test(ua)) {\n      return _const;\n    }\n    return void (0);\n  }\n\n  static getWindowsVersionName(version) {\n    switch (version) {\n      case 'NT': return 'NT';\n      case 'XP': return 'XP';\n      case 'NT 5.0': return '2000';\n      case 'NT 5.1': return 'XP';\n      case 'NT 5.2': return '2003';\n      case 'NT 6.0': return 'Vista';\n      case 'NT 6.1': return '7';\n      case 'NT 6.2': return '8';\n      case 'NT 6.3': return '8.1';\n      case 'NT 10.0': return '10';\n      default: return undefined;\n    }\n  }\n\n  /**\n   * Get macOS version name\n   *    10.5 - Leopard\n   *    10.6 - Snow Leopard\n   *    10.7 - Lion\n   *    10.8 - Mountain Lion\n   *    10.9 - Mavericks\n   *    10.10 - Yosemite\n   *    10.11 - El Capitan\n   *    10.12 - Sierra\n   *    10.13 - High Sierra\n   *    10.14 - Mojave\n   *    10.15 - Catalina\n   *\n   * @example\n   *   getMacOSVersionName(\"10.14\") // 'Mojave'\n   *\n   * @param  {string} version\n   * @return {string} versionName\n   */\n  static getMacOSVersionName(version) {\n    const v = version.split('.').splice(0, 2).map(s => parseInt(s, 10) || 0);\n    v.push(0);\n    if (v[0] !== 10) return undefined;\n    switch (v[1]) {\n      case 5: return 'Leopard';\n      case 6: return 'Snow Leopard';\n      case 7: return 'Lion';\n      case 8: return 'Mountain Lion';\n      case 9: return 'Mavericks';\n      case 10: return 'Yosemite';\n      case 11: return 'El Capitan';\n      case 12: return 'Sierra';\n      case 13: return 'High Sierra';\n      case 14: return 'Mojave';\n      case 15: return 'Catalina';\n      default: return undefined;\n    }\n  }\n\n  /**\n   * Get Android version name\n   *    1.5 - Cupcake\n   *    1.6 - Donut\n   *    2.0 - Eclair\n   *    2.1 - Eclair\n   *    2.2 - Froyo\n   *    2.x - Gingerbread\n   *    3.x - Honeycomb\n   *    4.0 - Ice Cream Sandwich\n   *    4.1 - Jelly Bean\n   *    4.4 - KitKat\n   *    5.x - Lollipop\n   *    6.x - Marshmallow\n   *    7.x - Nougat\n   *    8.x - Oreo\n   *    9.x - Pie\n   *\n   * @example\n   *   getAndroidVersionName(\"7.0\") // 'Nougat'\n   *\n   * @param  {string} version\n   * @return {string} versionName\n   */\n  static getAndroidVersionName(version) {\n    const v = version.split('.').splice(0, 2).map(s => parseInt(s, 10) || 0);\n    v.push(0);\n    if (v[0] === 1 && v[1] < 5) return undefined;\n    if (v[0] === 1 && v[1] < 6) return 'Cupcake';\n    if (v[0] === 1 && v[1] >= 6) return 'Donut';\n    if (v[0] === 2 && v[1] < 2) return 'Eclair';\n    if (v[0] === 2 && v[1] === 2) return 'Froyo';\n    if (v[0] === 2 && v[1] > 2) return 'Gingerbread';\n    if (v[0] === 3) return 'Honeycomb';\n    if (v[0] === 4 && v[1] < 1) return 'Ice Cream Sandwich';\n    if (v[0] === 4 && v[1] < 4) return 'Jelly Bean';\n    if (v[0] === 4 && v[1] >= 4) return 'KitKat';\n    if (v[0] === 5) return 'Lollipop';\n    if (v[0] === 6) return 'Marshmallow';\n    if (v[0] === 7) return 'Nougat';\n    if (v[0] === 8) return 'Oreo';\n    if (v[0] === 9) return 'Pie';\n    return undefined;\n  }\n\n  /**\n   * Get version precisions count\n   *\n   * @example\n   *   getVersionPrecision(\"1.10.3\") // 3\n   *\n   * @param  {string} version\n   * @return {number}\n   */\n  static getVersionPrecision(version) {\n    return version.split('.').length;\n  }\n\n  /**\n   * Calculate browser version weight\n   *\n   * @example\n   *   compareVersions('********',  '*******.90')    // 1\n   *   compareVersions('*********', '********.90');  // 1\n   *   compareVersions('********',  '********');     // 0\n   *   compareVersions('********',  '1.0800.2');     // -1\n   *   compareVersions('********',  '1.10',  true);  // 0\n   *\n   * @param {String} versionA versions versions to compare\n   * @param {String} versionB versions versions to compare\n   * @param {boolean} [isLoose] enable loose comparison\n   * @return {Number} comparison result: -1 when versionA is lower,\n   * 1 when versionA is bigger, 0 when both equal\n   */\n  /* eslint consistent-return: 1 */\n  static compareVersions(versionA, versionB, isLoose = false) {\n    // 1) get common precision for both versions, for example for \"10.0\" and \"9\" it should be 2\n    const versionAPrecision = Utils.getVersionPrecision(versionA);\n    const versionBPrecision = Utils.getVersionPrecision(versionB);\n\n    let precision = Math.max(versionAPrecision, versionBPrecision);\n    let lastPrecision = 0;\n\n    const chunks = Utils.map([versionA, versionB], (version) => {\n      const delta = precision - Utils.getVersionPrecision(version);\n\n      // 2) \"9\" -> \"9.0\" (for precision = 2)\n      const _version = version + new Array(delta + 1).join('.0');\n\n      // 3) \"9.0\" -> [\"000000000\"\", \"000000009\"]\n      return Utils.map(_version.split('.'), chunk => new Array(20 - chunk.length).join('0') + chunk).reverse();\n    });\n\n    // adjust precision for loose comparison\n    if (isLoose) {\n      lastPrecision = precision - Math.min(versionAPrecision, versionBPrecision);\n    }\n\n    // iterate in reverse order by reversed chunks array\n    precision -= 1;\n    while (precision >= lastPrecision) {\n      // 4) compare: \"000000009\" > \"000000010\" = false (but \"9\" > \"10\" = true)\n      if (chunks[0][precision] > chunks[1][precision]) {\n        return 1;\n      }\n\n      if (chunks[0][precision] === chunks[1][precision]) {\n        if (precision === lastPrecision) {\n          // all version chunks are same\n          return 0;\n        }\n\n        precision -= 1;\n      } else if (chunks[0][precision] < chunks[1][precision]) {\n        return -1;\n      }\n    }\n\n    return undefined;\n  }\n\n  /**\n   * Array::map polyfill\n   *\n   * @param  {Array} arr\n   * @param  {Function} iterator\n   * @return {Array}\n   */\n  static map(arr, iterator) {\n    const result = [];\n    let i;\n    if (Array.prototype.map) {\n      return Array.prototype.map.call(arr, iterator);\n    }\n    for (i = 0; i < arr.length; i += 1) {\n      result.push(iterator(arr[i]));\n    }\n    return result;\n  }\n\n  /**\n   * Array::find polyfill\n   *\n   * @param  {Array} arr\n   * @param  {Function} predicate\n   * @return {Array}\n   */\n  static find(arr, predicate) {\n    let i;\n    let l;\n    if (Array.prototype.find) {\n      return Array.prototype.find.call(arr, predicate);\n    }\n    for (i = 0, l = arr.length; i < l; i += 1) {\n      const value = arr[i];\n      if (predicate(value, i)) {\n        return value;\n      }\n    }\n    return undefined;\n  }\n\n  /**\n   * Object::assign polyfill\n   *\n   * @param  {Object} obj\n   * @param  {Object} ...objs\n   * @return {Object}\n   */\n  static assign(obj, ...assigners) {\n    const result = obj;\n    let i;\n    let l;\n    if (Object.assign) {\n      return Object.assign(obj, ...assigners);\n    }\n    for (i = 0, l = assigners.length; i < l; i += 1) {\n      const assigner = assigners[i];\n      if (typeof assigner === 'object' && assigner !== null) {\n        const keys = Object.keys(assigner);\n        keys.forEach((key) => {\n          result[key] = assigner[key];\n        });\n      }\n    }\n    return obj;\n  }\n\n  /**\n   * Get short version/alias for a browser name\n   *\n   * @example\n   *   getBrowserAlias('Microsoft Edge') // edge\n   *\n   * @param  {string} browserName\n   * @return {string}\n   */\n  static getBrowserAlias(browserName) {\n    return _constants_js__WEBPACK_IMPORTED_MODULE_0__.BROWSER_ALIASES_MAP[browserName];\n  }\n\n  /**\n   * Get short version/alias for a browser name\n   *\n   * @example\n   *   getBrowserAlias('edge') // Microsoft Edge\n   *\n   * @param  {string} browserAlias\n   * @return {string}\n   */\n  static getBrowserTypeByAlias(browserAlias) {\n    return _constants_js__WEBPACK_IMPORTED_MODULE_0__.BROWSER_MAP[browserAlias] || '';\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bowser/src/utils.js\n");

/***/ })

};
;