"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-format";
exports.ids = ["vendor-chunks/hast-util-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-format/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/hast-util-format/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   format: () => (/* binding */ format)\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/./node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_minify_whitespace__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-minify-whitespace */ \"(ssr)/./node_modules/hast-util-minify-whitespace/lib/index.js\");\n/* harmony import */ var hast_util_phrasing__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! hast-util-phrasing */ \"(ssr)/./node_modules/hast-util-phrasing/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var html_whitespace_sensitive_tag_names__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! html-whitespace-sensitive-tag-names */ \"(ssr)/./node_modules/html-whitespace-sensitive-tag-names/lib/index.js\");\n/* harmony import */ var unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit-parents */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {Nodes, RootContent, Root} from 'hast'\n * @import {BuildVisitor} from 'unist-util-visit-parents'\n * @import {Options, State} from './types.js'\n */\n\n\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\n\n/**\n * Format whitespace in HTML.\n *\n * @param {Root} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction format(tree, options) {\n  const settings = options || emptyOptions\n\n  /** @type {State} */\n  const state = {\n    blanks: settings.blanks || [],\n    head: false,\n    indentInitial: settings.indentInitial !== false,\n    indent:\n      typeof settings.indent === 'number'\n        ? ' '.repeat(settings.indent)\n        : typeof settings.indent === 'string'\n          ? settings.indent\n          : '  '\n  }\n\n  ;(0,hast_util_minify_whitespace__WEBPACK_IMPORTED_MODULE_0__.minifyWhitespace)(tree, {newlines: true})\n\n  ;(0,unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.visitParents)(tree, visitor)\n\n  /**\n   * @type {BuildVisitor<Root>}\n   */\n  function visitor(node, parents) {\n    if (!('children' in node)) {\n      return\n    }\n\n    if (node.type === 'element' && node.tagName === 'head') {\n      state.head = true\n    }\n\n    if (state.head && node.type === 'element' && node.tagName === 'body') {\n      state.head = false\n    }\n\n    if (\n      node.type === 'element' &&\n      html_whitespace_sensitive_tag_names__WEBPACK_IMPORTED_MODULE_2__.whitespaceSensitiveTagNames.includes(node.tagName)\n    ) {\n      return unist_util_visit_parents__WEBPACK_IMPORTED_MODULE_1__.SKIP\n    }\n\n    // Don’t indent content of whitespace-sensitive nodes / inlines.\n    if (node.children.length === 0 || !padding(state, node)) {\n      return\n    }\n\n    let level = parents.length\n\n    if (!state.indentInitial) {\n      level--\n    }\n\n    let eol = false\n\n    // Indent newlines in `text`.\n    for (const child of node.children) {\n      if (child.type === 'comment' || child.type === 'text') {\n        if (child.value.includes('\\n')) {\n          eol = true\n        }\n\n        child.value = child.value.replace(\n          / *\\n/g,\n          '$&' + state.indent.repeat(level)\n        )\n      }\n    }\n\n    /** @type {Array<RootContent>} */\n    const result = []\n    /** @type {RootContent | undefined} */\n    let previous\n\n    for (const child of node.children) {\n      if (padding(state, child) || (eol && !previous)) {\n        addBreak(result, level, child)\n        eol = true\n      }\n\n      previous = child\n      result.push(child)\n    }\n\n    if (previous && (eol || padding(state, previous))) {\n      // Ignore trailing whitespace (if that already existed), as we’ll add\n      // properly indented whitespace.\n      if ((0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(previous)) {\n        result.pop()\n        previous = result[result.length - 1]\n      }\n\n      addBreak(result, level - 1)\n    }\n\n    node.children = result\n  }\n\n  /**\n   * @param {Array<RootContent>} list\n   *   Nodes.\n   * @param {number} level\n   *   Indentation level.\n   * @param {RootContent | undefined} [next]\n   *   Next node.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function addBreak(list, level, next) {\n    const tail = list[list.length - 1]\n    const previous = tail && (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(tail) ? list[list.length - 2] : tail\n    const replace =\n      (blank(state, previous) && blank(state, next) ? '\\n\\n' : '\\n') +\n      state.indent.repeat(Math.max(level, 0))\n\n    if (tail && tail.type === 'text') {\n      tail.value = (0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(tail) ? replace : tail.value + replace\n    } else {\n      list.push({type: 'text', value: replace})\n    }\n  }\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes | undefined} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is a blank.\n */\nfunction blank(state, node) {\n  return Boolean(\n    node &&\n      node.type === 'element' &&\n      state.blanks.length > 0 &&\n      state.blanks.includes(node.tagName)\n  )\n}\n\n/**\n * @param {State} state\n *   Info passed around.\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` should be padded.\n */\nfunction padding(state, node) {\n  return (\n    node.type === 'root' ||\n    (node.type === 'element'\n      ? state.head ||\n        node.tagName === 'script' ||\n        (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_4__.embedded)(node) ||\n        !(0,hast_util_phrasing__WEBPACK_IMPORTED_MODULE_5__.phrasing)(node)\n      : false)\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-format/lib/index.js\n");

/***/ })

};
;