"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-dom";
exports.ids = ["vendor-chunks/hast-util-from-dom"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-dom/index.js":
/*!**************************************************!*\
  !*** ./node_modules/hast-util-from-dom/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromDom: () => (/* reexport safe */ _lib_index_js__WEBPACK_IMPORTED_MODULE_0__.fromDom)\n/* harmony export */ });\n/* harmony import */ var _lib_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/index.js */ \"(ssr)/./node_modules/hast-util-from-dom/lib/index.js\");\n/**\n * @typedef {import('./lib/index.js').AfterTransform} AfterTransform\n * @typedef {import('./lib/index.js').Options} Options\n */\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLWZyb20tZG9tL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHlDQUF5QztBQUN0RCxhQUFhLGtDQUFrQztBQUMvQzs7QUFFc0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1mcm9tLWRvbS9pbmRleC5qcz83ZDlkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHR5cGVkZWYge2ltcG9ydCgnLi9saWIvaW5kZXguanMnKS5BZnRlclRyYW5zZm9ybX0gQWZ0ZXJUcmFuc2Zvcm1cbiAqIEB0eXBlZGVmIHtpbXBvcnQoJy4vbGliL2luZGV4LmpzJykuT3B0aW9uc30gT3B0aW9uc1xuICovXG5cbmV4cG9ydCB7ZnJvbURvbX0gZnJvbSAnLi9saWIvaW5kZXguanMnXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-dom/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-from-dom/lib/index.js":
/*!******************************************************!*\
  !*** ./node_modules/hast-util-from-dom/lib/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromDom: () => (/* binding */ fromDom)\n/* harmony export */ });\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hastscript/lib/svg.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hastscript/lib/html.js\");\n/**\n * @typedef {import('hast').Root} HastRoot\n * @typedef {import('hast').DocType} HastDoctype\n * @typedef {import('hast').Element} HastElement\n * @typedef {import('hast').Text} HastText\n * @typedef {import('hast').Comment} HastComment\n * @typedef {import('hast').Content} HastContent\n */\n\n/**\n * @typedef {HastContent | HastRoot} HastNode\n *\n * @callback AfterTransform\n *   Callback called when each node is transformed.\n * @param {Node} domNode\n *   DOM node that was handled.\n * @param {HastNode} hastNode\n *   Corresponding hast node.\n * @returns {void}\n *   Nothing.\n *\n * @typedef Options\n *   Configuration.\n * @property {AfterTransform | null | undefined} [afterTransform]\n *   Callback called when each node is transformed.\n */\n\n\n\n\n/**\n * Transform a DOM tree to a hast tree.\n *\n * @param {Node} tree\n *   DOM tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {HastNode}\n *   Equivalent hast node.\n */\nfunction fromDom(tree, options) {\n  const result = tree ? transform(tree, options || {}) : undefined\n  return result || {type: 'root', children: []}\n}\n\n/**\n * @param {Node} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {HastNode | undefined}\n *   Equivalent hast node.\n *\n *   Note that certain legacy DOM nodes (i.e., Attr nodes (2),  CDATA, processing instructions)\n */\nfunction transform(node, options) {\n  const transformed = one(node, options)\n  if (transformed && options.afterTransform)\n    options.afterTransform(node, transformed)\n  return transformed\n}\n\n/**\n * @param {Node} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {HastNode | undefined}\n *   Equivalent hast node.\n */\nfunction one(node, options) {\n  switch (node.nodeType) {\n    case 1 /* Element */: {\n      // @ts-expect-error TypeScript is wrong.\n      return element(node, options)\n    }\n\n    // Ignore: Attr (2).\n\n    case 3 /* Text */: {\n      // @ts-expect-error TypeScript is wrong.\n      return text(node)\n    }\n\n    // Ignore: CDATA (4).\n    // Removed: Entity reference (5)\n    // Removed: Entity (6)\n    // Ignore: Processing instruction (7).\n\n    case 8 /* Comment */: {\n      // @ts-expect-error TypeScript is wrong.\n      return comment(node)\n    }\n\n    case 9 /* Document */: {\n      // @ts-expect-error TypeScript is wrong.\n      return root(node, options)\n    }\n\n    case 10 /* Document type */: {\n      return doctype()\n    }\n\n    case 11 /* Document fragment */: {\n      // @ts-expect-error TypeScript is wrong.\n      return root(node, options)\n    }\n\n    default: {\n      return undefined\n    }\n  }\n}\n\n/**\n * Transform a document.\n *\n * @param {Document | DocumentFragment} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {HastRoot}\n *   Equivalent hast node.\n */\nfunction root(node, options) {\n  return {type: 'root', children: all(node, options)}\n}\n\n/**\n * Transform a doctype.\n *\n * @returns {HastDoctype}\n *   Equivalent hast node.\n */\nfunction doctype() {\n  // @ts-expect-error hast types out of date.\n  return {type: 'doctype'}\n}\n\n/**\n * Transform a text.\n *\n * @param {Text} node\n *   DOM node to transform.\n * @returns {HastText}\n *   Equivalent hast node.\n */\nfunction text(node) {\n  return {type: 'text', value: node.nodeValue || ''}\n}\n\n/**\n * Transform a comment.\n *\n * @param {Comment} node\n *   DOM node to transform.\n * @returns {HastComment}\n *   Equivalent hast node.\n */\nfunction comment(node) {\n  return {type: 'comment', value: node.nodeValue || ''}\n}\n\n/**\n * Transform an element.\n *\n * @param {Element} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {HastElement}\n *   Equivalent hast node.\n */\nfunction element(node, options) {\n  const space = node.namespaceURI\n  const fn = space === web_namespaces__WEBPACK_IMPORTED_MODULE_0__.webNamespaces.svg ? hastscript__WEBPACK_IMPORTED_MODULE_1__.s : hastscript__WEBPACK_IMPORTED_MODULE_2__.h\n  const tagName =\n    space === web_namespaces__WEBPACK_IMPORTED_MODULE_0__.webNamespaces.html ? node.tagName.toLowerCase() : node.tagName\n  /** @type {DocumentFragment | Element} */\n  const content =\n    // @ts-expect-error Types are wrong.\n    space === web_namespaces__WEBPACK_IMPORTED_MODULE_0__.webNamespaces.html && tagName === 'template' ? node.content : node\n  const attributes = node.getAttributeNames()\n  /** @type {Record<string, string>} */\n  const props = {}\n  let index = -1\n\n  while (++index < attributes.length) {\n    props[attributes[index]] = node.getAttribute(attributes[index]) || ''\n  }\n\n  return fn(tagName, props, all(content, options))\n}\n\n/**\n * Transform child nodes in a parent.\n *\n * @param {Document | DocumentFragment | Element} node\n *   DOM node to transform.\n * @param {Options} options\n *   Configuration.\n * @returns {Array<HastContent>}\n *   Equivalent hast nodes.\n */\nfunction all(node, options) {\n  const nodes = node.childNodes\n  /** @type {Array<HastContent>} */\n  const children = []\n  let index = -1\n\n  while (++index < nodes.length) {\n    const child = transform(nodes[index], options)\n\n    if (child !== undefined) {\n      // @ts-expect-error Assume no document inside document.\n      children.push(child)\n    }\n  }\n\n  return children\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-dom/lib/index.js\n");

/***/ })

};
;