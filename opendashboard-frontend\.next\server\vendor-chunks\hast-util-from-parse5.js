"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-parse5";
exports.ids = ["vendor-chunks/hast-util-from-parse5"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-parse5/lib/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/lib/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromParse5: () => (/* binding */ fromParse5)\n/* harmony export */ });\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hastscript/lib/svg.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hastscript/lib/html.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var vfile_location__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile-location */ \"(ssr)/./node_modules/vfile-location/lib/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/**\n * @typedef {import('vfile').VFile} VFile\n * @typedef {import('property-information').Schema} Schema\n * @typedef {import('unist').Position} Position\n * @typedef {import('unist').Point} Point\n * @typedef {import('hast').Element} Element\n * @typedef {import('hast').Root} Root\n * @typedef {import('hast').Content} Content\n * @typedef {import('parse5').DefaultTreeAdapterMap} DefaultTreeAdapterMap\n * @typedef {import('parse5').Token.ElementLocation} P5ElementLocation\n * @typedef {import('parse5').Token.Location} P5Location\n */\n\n/**\n * @typedef {Content | Root} Node\n * @typedef {DefaultTreeAdapterMap['document']} P5Document\n * @typedef {DefaultTreeAdapterMap['documentFragment']} P5DocumentFragment\n * @typedef {DefaultTreeAdapterMap['documentType']} P5DocumentType\n * @typedef {DefaultTreeAdapterMap['commentNode']} P5Comment\n * @typedef {DefaultTreeAdapterMap['textNode']} P5Text\n * @typedef {DefaultTreeAdapterMap['element']} P5Element\n * @typedef {DefaultTreeAdapterMap['node']} P5Node\n * @typedef {DefaultTreeAdapterMap['template']} P5Template\n *\n * @typedef {'html' | 'svg'} Space\n *   Namespace.\n *\n * @typedef Options\n *   Configuration.\n * @property {Space | null | undefined} [space='html']\n *   Which space the document is in.\n *\n *   When an `<svg>` element is found in the HTML space, this package already\n *   automatically switches to and from the SVG space when entering and exiting\n *   it.\n * @property {VFile | null | undefined} [file]\n *   File used to add positional info to nodes.\n *\n *   If given, the file should represent the original HTML source.\n * @property {boolean} [verbose=false]\n *   Whether to add extra positional info about starting tags, closing tags,\n *   and attributes to elements.\n *\n *   > 👉 **Note**: only used when `file` is given.\n *\n * @typedef State\n *   Info passed around about the current state.\n * @property {Schema} schema\n *   Current schema.\n * @property {VFile | undefined} file\n *   Corresponding file.\n * @property {boolean | undefined} verbose\n *   Add extra positional info.\n * @property {boolean} location\n *   Whether location info was found.\n */\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n/** @type {unknown} */\n// type-coverage:ignore-next-line\nconst proto = Object.prototype\n\n/**\n * Transform a `parse5` AST to hast.\n *\n * @param {P5Node} tree\n *   `parse5` tree to transform.\n * @param {Options | VFile | null | undefined} [options]\n *   Configuration.\n * @returns {Node}\n *   hast tree.\n */\nfunction fromParse5(tree, options) {\n  const options_ = options || {}\n  /** @type {Options} */\n  let settings\n  /** @type {VFile | undefined} */\n  let file\n\n  if (isFile(options_)) {\n    file = options_\n    settings = {}\n  } else {\n    file = options_.file || undefined\n    settings = options_\n  }\n\n  return one(\n    {\n      schema: settings.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html,\n      file,\n      verbose: settings.verbose,\n      location: false\n    },\n    tree\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {P5Node} node\n *   p5 node.\n * @returns {Node}\n *   hast node.\n */\nfunction one(state, node) {\n  /** @type {Node} */\n  let result\n\n  switch (node.nodeName) {\n    case '#comment': {\n      const reference = /** @type {P5Comment} */ (node)\n      result = {type: 'comment', value: reference.data}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#document':\n    case '#document-fragment': {\n      const reference = /** @type {P5Document | P5DocumentFragment} */ (node)\n      const quirksMode =\n        'mode' in reference\n          ? reference.mode === 'quirks' || reference.mode === 'limited-quirks'\n          : false\n\n      result = {\n        type: 'root',\n        children: all(state, node.childNodes),\n        data: {quirksMode}\n      }\n\n      if (state.file && state.location) {\n        const doc = String(state.file)\n        const loc = (0,vfile_location__WEBPACK_IMPORTED_MODULE_1__.location)(doc)\n        const start = loc.toPoint(0)\n        const end = loc.toPoint(doc.length)\n        // @ts-expect-error: always defined as we give valid input.\n        result.position = {start, end}\n      }\n\n      return result\n    }\n\n    case '#documentType': {\n      const reference = /** @type {P5DocumentType} */ (node)\n      // @ts-expect-error Types are out of date.\n      result = {type: 'doctype'}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#text': {\n      const reference = /** @type {P5Text} */ (node)\n      result = {type: 'text', value: reference.value}\n      patch(state, reference, result)\n      return result\n    }\n\n    // Element.\n    default: {\n      const reference = /** @type {P5Element} */ (node)\n      result = element(state, reference)\n      return result\n    }\n  }\n}\n\n/**\n * Transform children.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Array<P5Node>} nodes\n *   Nodes.\n * @returns {Array<Content>}\n *   hast nodes.\n */\nfunction all(state, nodes) {\n  let index = -1\n  /** @type {Array<Content>} */\n  const result = []\n\n  while (++index < nodes.length) {\n    // @ts-expect-error Assume no roots in `nodes`.\n    result[index] = one(state, nodes[index])\n  }\n\n  return result\n}\n\n/**\n * Transform an element.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {P5Element} node\n *   `parse5` node to transform.\n * @returns {Element}\n *   hast node.\n */\nfunction element(state, node) {\n  const schema = state.schema\n\n  state.schema = node.namespaceURI === web_namespaces__WEBPACK_IMPORTED_MODULE_2__.webNamespaces.svg ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html\n\n  // Props.\n  let index = -1\n  /** @type {Record<string, string>} */\n  const props = {}\n\n  while (++index < node.attrs.length) {\n    const attribute = node.attrs[index]\n    const name =\n      (attribute.prefix ? attribute.prefix + ':' : '') + attribute.name\n    if (!own.call(proto, name)) {\n      props[name] = attribute.value\n    }\n  }\n\n  // Build.\n  const fn = state.schema.space === 'svg' ? hastscript__WEBPACK_IMPORTED_MODULE_3__.s : hastscript__WEBPACK_IMPORTED_MODULE_4__.h\n  const result = fn(node.tagName, props, all(state, node.childNodes))\n  patch(state, node, result)\n\n  // Switch content.\n  if (result.tagName === 'template') {\n    const reference = /** @type {P5Template} */ (node)\n    const pos = reference.sourceCodeLocation\n    const startTag = pos && pos.startTag && position(pos.startTag)\n    const endTag = pos && pos.endTag && position(pos.endTag)\n\n    /** @type {Root} */\n    // @ts-expect-error Types are wrong.\n    const content = one(state, reference.content)\n\n    if (startTag && endTag && state.file) {\n      content.position = {start: startTag.end, end: endTag.start}\n    }\n\n    result.content = content\n  }\n\n  state.schema = schema\n\n  return result\n}\n\n/**\n * Patch positional info from `from` onto `to`.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {P5Node} from\n *   p5 node.\n * @param {Node} to\n *   hast node.\n * @returns {void}\n *   Nothing.\n */\nfunction patch(state, from, to) {\n  if ('sourceCodeLocation' in from && from.sourceCodeLocation && state.file) {\n    const position = createLocation(state, to, from.sourceCodeLocation)\n\n    if (position) {\n      state.location = true\n      to.position = position\n    }\n  }\n}\n\n/**\n * Create clean positional information.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Node} node\n *   hast node.\n * @param {P5ElementLocation} location\n *   p5 location info.\n * @returns {Position | undefined}\n *   Position, or nothing.\n */\nfunction createLocation(state, node, location) {\n  const result = position(location)\n\n  if (node.type === 'element') {\n    const tail = node.children[node.children.length - 1]\n\n    // Bug for unclosed with children.\n    // See: <https://github.com/inikulin/parse5/issues/109>.\n    if (\n      result &&\n      !location.endTag &&\n      tail &&\n      tail.position &&\n      tail.position.end\n    ) {\n      result.end = Object.assign({}, tail.position.end)\n    }\n\n    if (state.verbose) {\n      /** @type {Record<string, Position | undefined>} */\n      const props = {}\n      /** @type {string} */\n      let key\n\n      if (location.attrs) {\n        for (key in location.attrs) {\n          if (own.call(location.attrs, key)) {\n            props[(0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, key).property] = position(\n              location.attrs[key]\n            )\n          }\n        }\n      }\n\n      node.data = {\n        position: {\n          // @ts-expect-error: assume not `undefined`.\n          opening: position(location.startTag),\n          closing: location.endTag ? position(location.endTag) : null,\n          properties: props\n        }\n      }\n    }\n  }\n\n  return result\n}\n\n/**\n * Turn a p5 location into a position.\n *\n * @param {P5Location} loc\n *   Location.\n * @returns {Position | undefined}\n *   Position or nothing.\n */\nfunction position(loc) {\n  const start = point({\n    line: loc.startLine,\n    column: loc.startCol,\n    offset: loc.startOffset\n  })\n  const end = point({\n    line: loc.endLine,\n    column: loc.endCol,\n    offset: loc.endOffset\n  })\n  // @ts-expect-error `undefined` is fine.\n  return start || end ? {start, end} : undefined\n}\n\n/**\n * Filter out invalid points.\n *\n * @param {Point} point\n *   Point with potentially `undefined` values.\n * @returns {Point | undefined}\n *   Point or nothing.\n */\nfunction point(point) {\n  return point.line && point.column ? point : undefined\n}\n\n/**\n * Check if something is a file.\n *\n * @param {VFile | Options} value\n *   File or options.\n * @returns {value is VFile}\n *   Whether `value` is a file.\n */\nfunction isFile(value) {\n  return 'messages' in value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\n");

/***/ })

};
;