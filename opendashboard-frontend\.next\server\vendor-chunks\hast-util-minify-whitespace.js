"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-minify-whitespace";
exports.ids = ["vendor-chunks/hast-util-minify-whitespace"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-minify-whitespace/lib/block.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/block.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blocks: () => (/* binding */ blocks)\n/* harmony export */ });\n// See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\nconst blocks = [\n  'address', // Flow content.\n  'article', // Sections and headings.\n  'aside', // Sections and headings.\n  'blockquote', // Flow content.\n  'body', // Page.\n  'br', // Contribute whitespace intrinsically.\n  'caption', // Similar to block.\n  'center', // Flow content, legacy.\n  'col', // Similar to block.\n  'colgroup', // Similar to block.\n  'dd', // Lists.\n  'dialog', // Flow content.\n  'dir', // Lists, legacy.\n  'div', // Flow content.\n  'dl', // Lists.\n  'dt', // Lists.\n  'figcaption', // Flow content.\n  'figure', // Flow content.\n  'footer', // Flow content.\n  'form', // Flow content.\n  'h1', // Sections and headings.\n  'h2', // Sections and headings.\n  'h3', // Sections and headings.\n  'h4', // Sections and headings.\n  'h5', // Sections and headings.\n  'h6', // Sections and headings.\n  'head', // Page.\n  'header', // Flow content.\n  'hgroup', // Sections and headings.\n  'hr', // Flow content.\n  'html', // Page.\n  'legend', // Flow content.\n  'li', // Block-like.\n  'li', // Similar to block.\n  'listing', // Flow content, legacy\n  'main', // Flow content.\n  'menu', // Lists.\n  'nav', // Sections and headings.\n  'ol', // Lists.\n  'optgroup', // Similar to block.\n  'option', // Similar to block.\n  'p', // Flow content.\n  'plaintext', // Flow content, legacy\n  'pre', // Flow content.\n  'section', // Sections and headings.\n  'summary', // Similar to block.\n  'table', // Similar to block.\n  'tbody', // Similar to block.\n  'td', // Block-like.\n  'td', // Similar to block.\n  'tfoot', // Similar to block.\n  'th', // Block-like.\n  'th', // Similar to block.\n  'thead', // Similar to block.\n  'tr', // Similar to block.\n  'ul', // Lists.\n  'wbr', // Contribute whitespace intrinsically.\n  'xmp' // Flow content, legacy\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-minify-whitespace/lib/block.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-minify-whitespace/lib/content.js":
/*!*****************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/content.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   content: () => (/* binding */ content)\n/* harmony export */ });\nconst content = [\n  // Form.\n  'button',\n  'input',\n  'select',\n  'textarea'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLW1pbmlmeS13aGl0ZXNwYWNlL2xpYi9jb250ZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLW1pbmlmeS13aGl0ZXNwYWNlL2xpYi9jb250ZW50LmpzPzg2MjQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGNvbnRlbnQgPSBbXG4gIC8vIEZvcm0uXG4gICdidXR0b24nLFxuICAnaW5wdXQnLFxuICAnc2VsZWN0JyxcbiAgJ3RleHRhcmVhJ1xuXVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-minify-whitespace/lib/content.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-minify-whitespace/lib/index.js":
/*!***************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/index.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   minifyWhitespace: () => (/* binding */ minifyWhitespace)\n/* harmony export */ });\n/* harmony import */ var hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-embedded */ \"(ssr)/./node_modules/hast-util-embedded/lib/index.js\");\n/* harmony import */ var hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-is-element */ \"(ssr)/./node_modules/hast-util-is-element/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/./node_modules/unist-util-is/lib/index.js\");\n/* harmony import */ var _block_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./block.js */ \"(ssr)/./node_modules/hast-util-minify-whitespace/lib/block.js\");\n/* harmony import */ var _content_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./content.js */ \"(ssr)/./node_modules/hast-util-minify-whitespace/lib/content.js\");\n/* harmony import */ var _skippable_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./skippable.js */ \"(ssr)/./node_modules/hast-util-minify-whitespace/lib/skippable.js\");\n/**\n * @import {Nodes, Parents, Text} from 'hast'\n */\n\n/**\n * @callback Collapse\n *   Collapse a string.\n * @param {string} value\n *   Value to collapse.\n * @returns {string}\n *   Collapsed value.\n *\n * @typedef Options\n *   Configuration.\n * @property {boolean | null | undefined} [newlines=false]\n *   Collapse whitespace containing newlines to `'\\n'` instead of `' '`\n *   (default: `false`); the default is to collapse to a single space.\n *\n * @typedef Result\n *   Result.\n * @property {boolean} remove\n *   Whether to remove.\n * @property {boolean} ignore\n *   Whether to ignore.\n * @property {boolean} stripAtStart\n *   Whether to strip at the start.\n *\n * @typedef State\n *   Info passed around.\n * @property {Collapse} collapse\n *   Collapse.\n * @property {Whitespace} whitespace\n *   Current whitespace.\n * @property {boolean | undefined} [before]\n *   Whether there is a break before (default: `false`).\n * @property {boolean | undefined} [after]\n *   Whether there is a break after (default: `false`).\n *\n * @typedef {'normal' | 'nowrap' | 'pre' | 'pre-wrap'} Whitespace\n *   Whitespace setting.\n */\n\n\n\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\nconst ignorableNode = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(['comment', 'doctype'])\n\n/**\n * Minify whitespace.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction minifyWhitespace(tree, options) {\n  const settings = options || emptyOptions\n\n  minify(tree, {\n    collapse: collapseFactory(\n      settings.newlines ? replaceNewlines : replaceWhitespace\n    ),\n    whitespace: 'normal'\n  })\n}\n\n/**\n * @param {Nodes} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minify(node, state) {\n  if ('children' in node) {\n    const settings = {...state}\n\n    if (node.type === 'root' || blocklike(node)) {\n      settings.before = true\n      settings.after = true\n    }\n\n    settings.whitespace = inferWhiteSpace(node, state)\n\n    return all(node, settings)\n  }\n\n  if (node.type === 'text') {\n    if (state.whitespace === 'normal') {\n      return minifyText(node, state)\n    }\n\n    // Naïve collapse, but no trimming:\n    if (state.whitespace === 'nowrap') {\n      node.value = state.collapse(node.value)\n    }\n\n    // The `pre-wrap` or `pre` whitespace settings are neither collapsed nor\n    // trimmed.\n  }\n\n  return {ignore: ignorableNode(node), stripAtStart: false, remove: false}\n}\n\n/**\n * @param {Text} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction minifyText(node, state) {\n  const value = state.collapse(node.value)\n  const result = {ignore: false, stripAtStart: false, remove: false}\n  let start = 0\n  let end = value.length\n\n  if (state.before && removable(value.charAt(0))) {\n    start++\n  }\n\n  if (start !== end && removable(value.charAt(end - 1))) {\n    if (state.after) {\n      end--\n    } else {\n      result.stripAtStart = true\n    }\n  }\n\n  if (start === end) {\n    result.remove = true\n  } else {\n    node.value = value.slice(start, end)\n  }\n\n  return result\n}\n\n/**\n * @param {Parents} parent\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Result}\n *   Result.\n */\nfunction all(parent, state) {\n  let before = state.before\n  const after = state.after\n  const children = parent.children\n  let length = children.length\n  let index = -1\n\n  while (++index < length) {\n    const result = minify(children[index], {\n      ...state,\n      after: collapsableAfter(children, index, after),\n      before\n    })\n\n    if (result.remove) {\n      children.splice(index, 1)\n      index--\n      length--\n    } else if (!result.ignore) {\n      before = result.stripAtStart\n    }\n\n    // If this element, such as a `<select>` or `<img>`, contributes content\n    // somehow, allow whitespace again.\n    if (content(children[index])) {\n      before = false\n    }\n  }\n\n  return {ignore: false, stripAtStart: Boolean(before || after), remove: false}\n}\n\n/**\n * @param {Array<Nodes>} nodes\n *   Nodes.\n * @param {number} index\n *   Index.\n * @param {boolean | undefined} [after]\n *   Whether there is a break after `nodes` (default: `false`).\n * @returns {boolean | undefined}\n *   Whether there is a break after the node at `index`.\n */\nfunction collapsableAfter(nodes, index, after) {\n  while (++index < nodes.length) {\n    const node = nodes[index]\n    let result = inferBoundary(node)\n\n    if (result === undefined && 'children' in node && !skippable(node)) {\n      result = collapsableAfter(node.children, -1)\n    }\n\n    if (typeof result === 'boolean') {\n      return result\n    }\n  }\n\n  return after\n}\n\n/**\n * Infer two types of boundaries:\n *\n * 1. `true` — boundary for which whitespace around it does not contribute\n *    anything\n * 2. `false` — boundary for which whitespace around it *does* contribute\n *\n * No result (`undefined`) is returned if it is unknown.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean | undefined}\n *   Boundary.\n */\nfunction inferBoundary(node) {\n  if (node.type === 'element') {\n    if (content(node)) {\n      return false\n    }\n\n    if (blocklike(node)) {\n      return true\n    }\n\n    // Unknown: either depends on siblings if embedded or metadata, or on\n    // children.\n  } else if (node.type === 'text') {\n    if (!(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_1__.whitespace)(node)) {\n      return false\n    }\n  } else if (!ignorableNode(node)) {\n    return false\n  }\n}\n\n/**\n * Infer whether a node is skippable.\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction content(node) {\n  return (0,hast_util_embedded__WEBPACK_IMPORTED_MODULE_2__.embedded)(node) || (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _content_js__WEBPACK_IMPORTED_MODULE_4__.content)\n}\n\n/**\n * See: <https://html.spec.whatwg.org/#the-css-user-agent-style-sheet-and-presentational-hints>\n *\n * @param {Nodes} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is block-like.\n */\nfunction blocklike(node) {\n  return (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _block_js__WEBPACK_IMPORTED_MODULE_5__.blocks)\n}\n\n/**\n * @param {Parents} node\n *   Node.\n * @returns {boolean}\n *   Whether `node` is skippable.\n */\nfunction skippable(node) {\n  return (\n    Boolean(node.type === 'element' && node.properties.hidden) ||\n    ignorableNode(node) ||\n    (0,hast_util_is_element__WEBPACK_IMPORTED_MODULE_3__.isElement)(node, _skippable_js__WEBPACK_IMPORTED_MODULE_6__.skippable)\n  )\n}\n\n/**\n * @param {string} character\n *   Character.\n * @returns {boolean}\n *   Whether `character` is removable.\n */\nfunction removable(character) {\n  return character === ' ' || character === '\\n'\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceNewlines(value) {\n  const match = /\\r?\\n|\\r/.exec(value)\n  return match ? match[0] : ' '\n}\n\n/**\n * @type {Collapse}\n */\nfunction replaceWhitespace() {\n  return ' '\n}\n\n/**\n * @param {Collapse} replace\n * @returns {Collapse}\n *   Collapse.\n */\nfunction collapseFactory(replace) {\n  return collapse\n\n  /**\n   * @type {Collapse}\n   */\n  function collapse(value) {\n    return String(value).replace(/[\\t\\n\\v\\f\\r ]+/g, replace)\n  }\n}\n\n/**\n * We don’t need to support void elements here (so `nobr wbr` -> `normal` is\n * ignored).\n *\n * @param {Parents} node\n *   Node.\n * @param {State} state\n *   Info passed around.\n * @returns {Whitespace}\n *   Whitespace.\n */\nfunction inferWhiteSpace(node, state) {\n  if ('tagName' in node && node.properties) {\n    switch (node.tagName) {\n      // Whitespace in script/style, while not displayed by CSS as significant,\n      // could have some meaning in JS/CSS, so we can’t touch them.\n      case 'listing':\n      case 'plaintext':\n      case 'script':\n      case 'style':\n      case 'xmp': {\n        return 'pre'\n      }\n\n      case 'nobr': {\n        return 'nowrap'\n      }\n\n      case 'pre': {\n        return node.properties.wrap ? 'pre-wrap' : 'pre'\n      }\n\n      case 'td':\n      case 'th': {\n        return node.properties.noWrap ? 'nowrap' : state.whitespace\n      }\n\n      case 'textarea': {\n        return 'pre-wrap'\n      }\n\n      default:\n    }\n  }\n\n  return state.whitespace\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-minify-whitespace/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-minify-whitespace/lib/skippable.js":
/*!*******************************************************************!*\
  !*** ./node_modules/hast-util-minify-whitespace/lib/skippable.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   skippable: () => (/* binding */ skippable)\n/* harmony export */ });\nconst skippable = [\n  'area',\n  'base',\n  'basefont',\n  'dialog',\n  'datalist',\n  'head',\n  'link',\n  'meta',\n  'noembed',\n  'noframes',\n  'param',\n  'rp',\n  'script',\n  'source',\n  'style',\n  'template',\n  'track',\n  'title'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLW1pbmlmeS13aGl0ZXNwYWNlL2xpYi9za2lwcGFibGUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1taW5pZnktd2hpdGVzcGFjZS9saWIvc2tpcHBhYmxlLmpzPzQzNTAiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IHNraXBwYWJsZSA9IFtcbiAgJ2FyZWEnLFxuICAnYmFzZScsXG4gICdiYXNlZm9udCcsXG4gICdkaWFsb2cnLFxuICAnZGF0YWxpc3QnLFxuICAnaGVhZCcsXG4gICdsaW5rJyxcbiAgJ21ldGEnLFxuICAnbm9lbWJlZCcsXG4gICdub2ZyYW1lcycsXG4gICdwYXJhbScsXG4gICdycCcsXG4gICdzY3JpcHQnLFxuICAnc291cmNlJyxcbiAgJ3N0eWxlJyxcbiAgJ3RlbXBsYXRlJyxcbiAgJ3RyYWNrJyxcbiAgJ3RpdGxlJ1xuXVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-minify-whitespace/lib/skippable.js\n");

/***/ })

};
;