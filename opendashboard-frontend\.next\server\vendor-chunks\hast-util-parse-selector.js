"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-parse-selector";
exports.ids = ["vendor-chunks/hast-util-parse-selector"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-parse-selector/lib/index.js":
/*!************************************************************!*\
  !*** ./node_modules/hast-util-parse-selector/lib/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseSelector: () => (/* binding */ parseSelector)\n/* harmony export */ });\n/**\n * @typedef {import('hast').Properties} Properties\n * @typedef {import('hast').Element} Element\n */\n\nconst search = /[#.]/g\n\n/**\n * Create a hast element from a simple CSS selector.\n *\n * @template {string} Selector\n *   Type of selector.\n * @template {string} [DefaultTagName='div']\n *   Type of default tag name.\n * @param {Selector | null | undefined} [selector]\n *   Simple CSS selector.\n *\n *   Can contain a tag name (`foo`), classes (`.bar`), and an ID (`#baz`).\n *   Multiple classes are allowed.\n *   Uses the last ID if multiple IDs are found.\n * @param {DefaultTagName | null | undefined} [defaultTagName='div']\n *   Tag name to use if `selector` does not specify one (default: `'div'`).\n * @returns {Element & {tagName: import('./extract.js').ExtractTagName<Selector, DefaultTagName>}}\n *   Built element.\n */\nfunction parseSelector(selector, defaultTagName) {\n  const value = selector || ''\n  /** @type {Properties} */\n  const props = {}\n  let start = 0\n  /** @type {string | undefined} */\n  let previous\n  /** @type {string | undefined} */\n  let tagName\n\n  while (start < value.length) {\n    search.lastIndex = start\n    const match = search.exec(value)\n    const subvalue = value.slice(start, match ? match.index : value.length)\n\n    if (subvalue) {\n      if (!previous) {\n        tagName = subvalue\n      } else if (previous === '#') {\n        props.id = subvalue\n      } else if (Array.isArray(props.className)) {\n        props.className.push(subvalue)\n      } else {\n        props.className = [subvalue]\n      }\n\n      start += subvalue.length\n    }\n\n    if (match) {\n      previous = match[0]\n      start++\n    }\n  }\n\n  return {\n    type: 'element',\n    // @ts-expect-error: fine.\n    tagName: tagName || defaultTagName || 'div',\n    properties: props,\n    children: []\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXBhcnNlLXNlbGVjdG9yL2xpYi9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxhQUFhLDJCQUEyQjtBQUN4QyxhQUFhLHdCQUF3QjtBQUNyQzs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLFFBQVE7QUFDdEI7QUFDQSxjQUFjLFFBQVE7QUFDdEI7QUFDQSxXQUFXLDZCQUE2QjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxtQ0FBbUM7QUFDOUM7QUFDQSxhQUFhLFdBQVc7QUFDeEI7QUFDQTtBQUNPO0FBQ1A7QUFDQSxhQUFhLFlBQVk7QUFDekI7QUFDQTtBQUNBLGFBQWEsb0JBQW9CO0FBQ2pDO0FBQ0EsYUFBYSxvQkFBb0I7QUFDakM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2hhc3QtdXRpbC1wYXJzZS1zZWxlY3Rvci9saWIvaW5kZXguanM/YThiOCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ2hhc3QnKS5Qcm9wZXJ0aWVzfSBQcm9wZXJ0aWVzXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdoYXN0JykuRWxlbWVudH0gRWxlbWVudFxuICovXG5cbmNvbnN0IHNlYXJjaCA9IC9bIy5dL2dcblxuLyoqXG4gKiBDcmVhdGUgYSBoYXN0IGVsZW1lbnQgZnJvbSBhIHNpbXBsZSBDU1Mgc2VsZWN0b3IuXG4gKlxuICogQHRlbXBsYXRlIHtzdHJpbmd9IFNlbGVjdG9yXG4gKiAgIFR5cGUgb2Ygc2VsZWN0b3IuXG4gKiBAdGVtcGxhdGUge3N0cmluZ30gW0RlZmF1bHRUYWdOYW1lPSdkaXYnXVxuICogICBUeXBlIG9mIGRlZmF1bHQgdGFnIG5hbWUuXG4gKiBAcGFyYW0ge1NlbGVjdG9yIHwgbnVsbCB8IHVuZGVmaW5lZH0gW3NlbGVjdG9yXVxuICogICBTaW1wbGUgQ1NTIHNlbGVjdG9yLlxuICpcbiAqICAgQ2FuIGNvbnRhaW4gYSB0YWcgbmFtZSAoYGZvb2ApLCBjbGFzc2VzIChgLmJhcmApLCBhbmQgYW4gSUQgKGAjYmF6YCkuXG4gKiAgIE11bHRpcGxlIGNsYXNzZXMgYXJlIGFsbG93ZWQuXG4gKiAgIFVzZXMgdGhlIGxhc3QgSUQgaWYgbXVsdGlwbGUgSURzIGFyZSBmb3VuZC5cbiAqIEBwYXJhbSB7RGVmYXVsdFRhZ05hbWUgfCBudWxsIHwgdW5kZWZpbmVkfSBbZGVmYXVsdFRhZ05hbWU9J2RpdiddXG4gKiAgIFRhZyBuYW1lIHRvIHVzZSBpZiBgc2VsZWN0b3JgIGRvZXMgbm90IHNwZWNpZnkgb25lIChkZWZhdWx0OiBgJ2RpdidgKS5cbiAqIEByZXR1cm5zIHtFbGVtZW50ICYge3RhZ05hbWU6IGltcG9ydCgnLi9leHRyYWN0LmpzJykuRXh0cmFjdFRhZ05hbWU8U2VsZWN0b3IsIERlZmF1bHRUYWdOYW1lPn19XG4gKiAgIEJ1aWx0IGVsZW1lbnQuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZVNlbGVjdG9yKHNlbGVjdG9yLCBkZWZhdWx0VGFnTmFtZSkge1xuICBjb25zdCB2YWx1ZSA9IHNlbGVjdG9yIHx8ICcnXG4gIC8qKiBAdHlwZSB7UHJvcGVydGllc30gKi9cbiAgY29uc3QgcHJvcHMgPSB7fVxuICBsZXQgc3RhcnQgPSAwXG4gIC8qKiBAdHlwZSB7c3RyaW5nIHwgdW5kZWZpbmVkfSAqL1xuICBsZXQgcHJldmlvdXNcbiAgLyoqIEB0eXBlIHtzdHJpbmcgfCB1bmRlZmluZWR9ICovXG4gIGxldCB0YWdOYW1lXG5cbiAgd2hpbGUgKHN0YXJ0IDwgdmFsdWUubGVuZ3RoKSB7XG4gICAgc2VhcmNoLmxhc3RJbmRleCA9IHN0YXJ0XG4gICAgY29uc3QgbWF0Y2ggPSBzZWFyY2guZXhlYyh2YWx1ZSlcbiAgICBjb25zdCBzdWJ2YWx1ZSA9IHZhbHVlLnNsaWNlKHN0YXJ0LCBtYXRjaCA/IG1hdGNoLmluZGV4IDogdmFsdWUubGVuZ3RoKVxuXG4gICAgaWYgKHN1YnZhbHVlKSB7XG4gICAgICBpZiAoIXByZXZpb3VzKSB7XG4gICAgICAgIHRhZ05hbWUgPSBzdWJ2YWx1ZVxuICAgICAgfSBlbHNlIGlmIChwcmV2aW91cyA9PT0gJyMnKSB7XG4gICAgICAgIHByb3BzLmlkID0gc3VidmFsdWVcbiAgICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShwcm9wcy5jbGFzc05hbWUpKSB7XG4gICAgICAgIHByb3BzLmNsYXNzTmFtZS5wdXNoKHN1YnZhbHVlKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcHJvcHMuY2xhc3NOYW1lID0gW3N1YnZhbHVlXVxuICAgICAgfVxuXG4gICAgICBzdGFydCArPSBzdWJ2YWx1ZS5sZW5ndGhcbiAgICB9XG5cbiAgICBpZiAobWF0Y2gpIHtcbiAgICAgIHByZXZpb3VzID0gbWF0Y2hbMF1cbiAgICAgIHN0YXJ0KytcbiAgICB9XG4gIH1cblxuICByZXR1cm4ge1xuICAgIHR5cGU6ICdlbGVtZW50JyxcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBmaW5lLlxuICAgIHRhZ05hbWU6IHRhZ05hbWUgfHwgZGVmYXVsdFRhZ05hbWUgfHwgJ2RpdicsXG4gICAgcHJvcGVydGllczogcHJvcHMsXG4gICAgY2hpbGRyZW46IFtdXG4gIH1cbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-parse-selector/lib/index.js\n");

/***/ })

};
;