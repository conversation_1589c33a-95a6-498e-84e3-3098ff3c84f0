/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/import-in-the-middle";
exports.ids = ["vendor-chunks/import-in-the-middle"];
exports.modules = {

/***/ "(ssr)/./node_modules/import-in-the-middle/index.js":
/*!****************************************************!*\
  !*** ./node_modules/import-in-the-middle/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst parse = __webpack_require__(/*! module-details-from-path */ \"(ssr)/./node_modules/module-details-from-path/index.js\")\nconst { fileURLToPath } = __webpack_require__(/*! url */ \"url\")\nconst { MessageChannel } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\n\nconst {\n  importHooks,\n  specifiers,\n  toHook\n} = __webpack_require__(/*! ./lib/register */ \"(ssr)/./node_modules/import-in-the-middle/lib/register.js\")\n\nfunction addHook (hook) {\n  importHooks.push(hook)\n  toHook.forEach(([name, namespace]) => hook(name, namespace))\n}\n\nfunction removeHook (hook) {\n  const index = importHooks.indexOf(hook)\n  if (index > -1) {\n    importHooks.splice(index, 1)\n  }\n}\n\nfunction callHookFn (hookFn, namespace, name, baseDir) {\n  const newDefault = hookFn(namespace, name, baseDir)\n  if (newDefault && newDefault !== namespace) {\n    namespace.default = newDefault\n  }\n}\n\nlet sendModulesToLoader\n\n/**\n * EXPERIMENTAL\n * This feature is experimental and may change in minor versions.\n * **NOTE** This feature is incompatible with the {internals: true} Hook option.\n *\n * Creates a message channel with a port that can be used to add hooks to the\n * list of exclusively included modules.\n *\n * This can be used to only wrap modules that are Hook'ed, however modules need\n * to be hooked before they are imported.\n *\n * ```ts\n * import { register } from 'module'\n * import { Hook, createAddHookMessageChannel } from 'import-in-the-middle'\n *\n * const { registerOptions, waitForAllMessagesAcknowledged } = createAddHookMessageChannel()\n *\n * register('import-in-the-middle/hook.mjs', import.meta.url, registerOptions)\n *\n * Hook(['fs'], (exported, name, baseDir) => {\n *   // Instrument the fs module\n * })\n *\n * // Ensure that the loader has acknowledged all the modules\n * // before we allow execution to continue\n * await waitForAllMessagesAcknowledged()\n * ```\n */\nfunction createAddHookMessageChannel () {\n  const { port1, port2 } = new MessageChannel()\n  let pendingAckCount = 0\n  let resolveFn\n\n  sendModulesToLoader = (modules) => {\n    pendingAckCount++\n    port1.postMessage(modules)\n  }\n\n  port1.on('message', () => {\n    pendingAckCount--\n\n    if (resolveFn && pendingAckCount <= 0) {\n      resolveFn()\n    }\n  }).unref()\n\n  function waitForAllMessagesAcknowledged () {\n    // This timer is to prevent the process from exiting with code 13:\n    // 13: Unsettled Top-Level Await.\n    const timer = setInterval(() => { }, 1000)\n    const promise = new Promise((resolve) => {\n      resolveFn = resolve\n    }).then(() => { clearInterval(timer) })\n\n    if (pendingAckCount === 0) {\n      resolveFn()\n    }\n\n    return promise\n  }\n\n  const addHookMessagePort = port2\n  const registerOptions = { data: { addHookMessagePort, include: [] }, transferList: [addHookMessagePort] }\n\n  return { registerOptions, addHookMessagePort, waitForAllMessagesAcknowledged }\n}\n\nfunction Hook (modules, options, hookFn) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, hookFn)\n  if (typeof modules === 'function') {\n    hookFn = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    hookFn = options\n    options = null\n  }\n  const internals = options ? options.internals === true : false\n\n  if (sendModulesToLoader && Array.isArray(modules)) {\n    sendModulesToLoader(modules)\n  }\n\n  this._iitmHook = (name, namespace) => {\n    const filename = name\n    const isBuiltin = name.startsWith('node:')\n    let baseDir\n\n    if (isBuiltin) {\n      name = name.replace(/^node:/, '')\n    } else {\n      if (name.startsWith('file://')) {\n        try {\n          name = fileURLToPath(name)\n        } catch (e) {}\n      }\n      const details = parse(name)\n      if (details) {\n        name = details.name\n        baseDir = details.basedir\n      }\n    }\n\n    if (modules) {\n      for (const moduleName of modules) {\n        if (moduleName === name) {\n          if (baseDir) {\n            if (internals) {\n              name = name + path.sep + path.relative(baseDir, fileURLToPath(filename))\n            } else {\n              if (!baseDir.endsWith(specifiers.get(filename))) continue\n            }\n          }\n          callHookFn(hookFn, namespace, name, baseDir)\n        }\n      }\n    } else {\n      callHookFn(hookFn, namespace, name, baseDir)\n    }\n  }\n\n  addHook(this._iitmHook)\n}\n\nHook.prototype.unhook = function () {\n  removeHook(this._iitmHook)\n}\n\nmodule.exports = Hook\nmodule.exports.Hook = Hook\nmodule.exports.addHook = addHook\nmodule.exports.removeHook = removeHook\nmodule.exports.createAddHookMessageChannel = createAddHookMessageChannel\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/import-in-the-middle/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/import-in-the-middle/lib/register.js":
/*!***********************************************************!*\
  !*** ./node_modules/import-in-the-middle/lib/register.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst importHooks = [] // TODO should this be a Set?\nconst setters = new WeakMap()\nconst getters = new WeakMap()\nconst specifiers = new Map()\nconst toHook = []\n\nconst proxyHandler = {\n  set (target, name, value) {\n    return setters.get(target)[name](value)\n  },\n\n  get (target, name) {\n    if (name === Symbol.toStringTag) {\n      return 'Module'\n    }\n\n    const getter = getters.get(target)[name]\n\n    if (typeof getter === 'function') {\n      return getter()\n    }\n  },\n\n  defineProperty (target, property, descriptor) {\n    if ((!('value' in descriptor))) {\n      throw new Error('Getters/setters are not supported for exports property descriptors.')\n    }\n\n    return setters.get(target)[property](descriptor.value)\n  }\n}\n\nfunction register (name, namespace, set, get, specifier) {\n  specifiers.set(name, specifier)\n  setters.set(namespace, set)\n  getters.set(namespace, get)\n  const proxy = new Proxy(namespace, proxyHandler)\n  importHooks.forEach(hook => hook(name, proxy))\n  toHook.push([name, proxy])\n}\n\nexports.register = register\nexports.importHooks = importHooks\nexports.specifiers = specifiers\nexports.toHook = toHook\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/import-in-the-middle/lib/register.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/import-in-the-middle/index.js":
/*!****************************************************!*\
  !*** ./node_modules/import-in-the-middle/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst path = __webpack_require__(/*! path */ \"path\")\nconst parse = __webpack_require__(/*! module-details-from-path */ \"(rsc)/./node_modules/module-details-from-path/index.js\")\nconst { fileURLToPath } = __webpack_require__(/*! url */ \"url\")\nconst { MessageChannel } = __webpack_require__(/*! worker_threads */ \"worker_threads\")\n\nconst {\n  importHooks,\n  specifiers,\n  toHook\n} = __webpack_require__(/*! ./lib/register */ \"(rsc)/./node_modules/import-in-the-middle/lib/register.js\")\n\nfunction addHook (hook) {\n  importHooks.push(hook)\n  toHook.forEach(([name, namespace]) => hook(name, namespace))\n}\n\nfunction removeHook (hook) {\n  const index = importHooks.indexOf(hook)\n  if (index > -1) {\n    importHooks.splice(index, 1)\n  }\n}\n\nfunction callHookFn (hookFn, namespace, name, baseDir) {\n  const newDefault = hookFn(namespace, name, baseDir)\n  if (newDefault && newDefault !== namespace) {\n    namespace.default = newDefault\n  }\n}\n\nlet sendModulesToLoader\n\n/**\n * EXPERIMENTAL\n * This feature is experimental and may change in minor versions.\n * **NOTE** This feature is incompatible with the {internals: true} Hook option.\n *\n * Creates a message channel with a port that can be used to add hooks to the\n * list of exclusively included modules.\n *\n * This can be used to only wrap modules that are Hook'ed, however modules need\n * to be hooked before they are imported.\n *\n * ```ts\n * import { register } from 'module'\n * import { Hook, createAddHookMessageChannel } from 'import-in-the-middle'\n *\n * const { registerOptions, waitForAllMessagesAcknowledged } = createAddHookMessageChannel()\n *\n * register('import-in-the-middle/hook.mjs', import.meta.url, registerOptions)\n *\n * Hook(['fs'], (exported, name, baseDir) => {\n *   // Instrument the fs module\n * })\n *\n * // Ensure that the loader has acknowledged all the modules\n * // before we allow execution to continue\n * await waitForAllMessagesAcknowledged()\n * ```\n */\nfunction createAddHookMessageChannel () {\n  const { port1, port2 } = new MessageChannel()\n  let pendingAckCount = 0\n  let resolveFn\n\n  sendModulesToLoader = (modules) => {\n    pendingAckCount++\n    port1.postMessage(modules)\n  }\n\n  port1.on('message', () => {\n    pendingAckCount--\n\n    if (resolveFn && pendingAckCount <= 0) {\n      resolveFn()\n    }\n  }).unref()\n\n  function waitForAllMessagesAcknowledged () {\n    // This timer is to prevent the process from exiting with code 13:\n    // 13: Unsettled Top-Level Await.\n    const timer = setInterval(() => { }, 1000)\n    const promise = new Promise((resolve) => {\n      resolveFn = resolve\n    }).then(() => { clearInterval(timer) })\n\n    if (pendingAckCount === 0) {\n      resolveFn()\n    }\n\n    return promise\n  }\n\n  const addHookMessagePort = port2\n  const registerOptions = { data: { addHookMessagePort, include: [] }, transferList: [addHookMessagePort] }\n\n  return { registerOptions, addHookMessagePort, waitForAllMessagesAcknowledged }\n}\n\nfunction Hook (modules, options, hookFn) {\n  if ((this instanceof Hook) === false) return new Hook(modules, options, hookFn)\n  if (typeof modules === 'function') {\n    hookFn = modules\n    modules = null\n    options = null\n  } else if (typeof options === 'function') {\n    hookFn = options\n    options = null\n  }\n  const internals = options ? options.internals === true : false\n\n  if (sendModulesToLoader && Array.isArray(modules)) {\n    sendModulesToLoader(modules)\n  }\n\n  this._iitmHook = (name, namespace) => {\n    const filename = name\n    const isBuiltin = name.startsWith('node:')\n    let baseDir\n\n    if (isBuiltin) {\n      name = name.replace(/^node:/, '')\n    } else {\n      if (name.startsWith('file://')) {\n        try {\n          name = fileURLToPath(name)\n        } catch (e) {}\n      }\n      const details = parse(name)\n      if (details) {\n        name = details.name\n        baseDir = details.basedir\n      }\n    }\n\n    if (modules) {\n      for (const moduleName of modules) {\n        if (moduleName === name) {\n          if (baseDir) {\n            if (internals) {\n              name = name + path.sep + path.relative(baseDir, fileURLToPath(filename))\n            } else {\n              if (!baseDir.endsWith(specifiers.get(filename))) continue\n            }\n          }\n          callHookFn(hookFn, namespace, name, baseDir)\n        }\n      }\n    } else {\n      callHookFn(hookFn, namespace, name, baseDir)\n    }\n  }\n\n  addHook(this._iitmHook)\n}\n\nHook.prototype.unhook = function () {\n  removeHook(this._iitmHook)\n}\n\nmodule.exports = Hook\nmodule.exports.Hook = Hook\nmodule.exports.addHook = addHook\nmodule.exports.removeHook = removeHook\nmodule.exports.createAddHookMessageChannel = createAddHookMessageChannel\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/import-in-the-middle/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/import-in-the-middle/lib/register.js":
/*!***********************************************************!*\
  !*** ./node_modules/import-in-the-middle/lib/register.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("// Unless explicitly stated otherwise all files in this repository are licensed under the Apache 2.0 License.\n//\n// This product includes software developed at Datadog (https://www.datadoghq.com/). Copyright 2021 Datadog, Inc.\n\nconst importHooks = [] // TODO should this be a Set?\nconst setters = new WeakMap()\nconst getters = new WeakMap()\nconst specifiers = new Map()\nconst toHook = []\n\nconst proxyHandler = {\n  set (target, name, value) {\n    return setters.get(target)[name](value)\n  },\n\n  get (target, name) {\n    if (name === Symbol.toStringTag) {\n      return 'Module'\n    }\n\n    const getter = getters.get(target)[name]\n\n    if (typeof getter === 'function') {\n      return getter()\n    }\n  },\n\n  defineProperty (target, property, descriptor) {\n    if ((!('value' in descriptor))) {\n      throw new Error('Getters/setters are not supported for exports property descriptors.')\n    }\n\n    return setters.get(target)[property](descriptor.value)\n  }\n}\n\nfunction register (name, namespace, set, get, specifier) {\n  specifiers.set(name, specifier)\n  setters.set(namespace, set)\n  getters.set(namespace, get)\n  const proxy = new Proxy(namespace, proxyHandler)\n  importHooks.forEach(hook => hook(name, proxy))\n  toHook.push([name, proxy])\n}\n\nexports.register = register\nexports.importHooks = importHooks\nexports.specifiers = specifiers\nexports.toHook = toHook\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/import-in-the-middle/lib/register.js\n");

/***/ })

};
;