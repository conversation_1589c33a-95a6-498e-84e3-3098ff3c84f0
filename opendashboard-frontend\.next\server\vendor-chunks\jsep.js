"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jsep";
exports.ids = ["vendor-chunks/jsep"];
exports.modules = {

/***/ "(ssr)/./node_modules/jsep/dist/cjs/jsep.cjs.js":
/*!************************************************!*\
  !*** ./node_modules/jsep/dist/cjs/jsep.cjs.js ***!
  \************************************************/
/***/ ((module) => {

eval("\n\n/**\n * @implements {IHooks}\n */\nclass Hooks {\n\t/**\n\t * @callback HookCallback\n\t * @this {*|Jsep} this\n\t * @param {Jsep} env\n\t * @returns: void\n\t */\n\t/**\n\t * Adds the given callback to the list of callbacks for the given hook.\n\t *\n\t * The callback will be invoked when the hook it is registered for is run.\n\t *\n\t * One callback function can be registered to multiple hooks and the same hook multiple times.\n\t *\n\t * @param {string|object} name The name of the hook, or an object of callbacks keyed by name\n\t * @param {HookCallback|boolean} callback The callback function which is given environment variables.\n\t * @param {?boolean} [first=false] Will add the hook to the top of the list (defaults to the bottom)\n\t * @public\n\t */\n\tadd(name, callback, first) {\n\t\tif (typeof arguments[0] != 'string') {\n\t\t\t// Multiple hook callbacks, keyed by name\n\t\t\tfor (let name in arguments[0]) {\n\t\t\t\tthis.add(name, arguments[0][name], arguments[1]);\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\t(Array.isArray(name) ? name : [name]).forEach(function (name) {\n\t\t\t\tthis[name] = this[name] || [];\n\n\t\t\t\tif (callback) {\n\t\t\t\t\tthis[name][first ? 'unshift' : 'push'](callback);\n\t\t\t\t}\n\t\t\t}, this);\n\t\t}\n\t}\n\n\t/**\n\t * Runs a hook invoking all registered callbacks with the given environment variables.\n\t *\n\t * Callbacks will be invoked synchronously and in the order in which they were registered.\n\t *\n\t * @param {string} name The name of the hook.\n\t * @param {Object<string, any>} env The environment variables of the hook passed to all callbacks registered.\n\t * @public\n\t */\n\trun(name, env) {\n\t\tthis[name] = this[name] || [];\n\t\tthis[name].forEach(function (callback) {\n\t\t\tcallback.call(env && env.context ? env.context : env, env);\n\t\t});\n\t}\n}\n\n/**\n * @implements {IPlugins}\n */\nclass Plugins {\n\tconstructor(jsep) {\n\t\tthis.jsep = jsep;\n\t\tthis.registered = {};\n\t}\n\n\t/**\n\t * @callback PluginSetup\n\t * @this {Jsep} jsep\n\t * @returns: void\n\t */\n\t/**\n\t * Adds the given plugin(s) to the registry\n\t *\n\t * @param {object} plugins\n\t * @param {string} plugins.name The name of the plugin\n\t * @param {PluginSetup} plugins.init The init function\n\t * @public\n\t */\n\tregister(...plugins) {\n\t\tplugins.forEach((plugin) => {\n\t\t\tif (typeof plugin !== 'object' || !plugin.name || !plugin.init) {\n\t\t\t\tthrow new Error('Invalid JSEP plugin format');\n\t\t\t}\n\t\t\tif (this.registered[plugin.name]) {\n\t\t\t\t// already registered. Ignore.\n\t\t\t\treturn;\n\t\t\t}\n\t\t\tplugin.init(this.jsep);\n\t\t\tthis.registered[plugin.name] = plugin;\n\t\t});\n\t}\n}\n\n//     JavaScript Expression Parser (JSEP) 1.4.0\n\nclass Jsep {\n\t/**\n\t * @returns {string}\n\t */\n\tstatic get version() {\n\t\t// To be filled in by the template\n\t\treturn '1.4.0';\n\t}\n\n\t/**\n\t * @returns {string}\n\t */\n\tstatic toString() {\n\t\treturn 'JavaScript Expression Parser (JSEP) v' + Jsep.version;\n\t};\n\n\t// ==================== CONFIG ================================\n\t/**\n\t * @method addUnaryOp\n\t * @param {string} op_name The name of the unary op to add\n\t * @returns {Jsep}\n\t */\n\tstatic addUnaryOp(op_name) {\n\t\tJsep.max_unop_len = Math.max(op_name.length, Jsep.max_unop_len);\n\t\tJsep.unary_ops[op_name] = 1;\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method jsep.addBinaryOp\n\t * @param {string} op_name The name of the binary op to add\n\t * @param {number} precedence The precedence of the binary op (can be a float). Higher number = higher precedence\n\t * @param {boolean} [isRightAssociative=false] whether operator is right-associative\n\t * @returns {Jsep}\n\t */\n\tstatic addBinaryOp(op_name, precedence, isRightAssociative) {\n\t\tJsep.max_binop_len = Math.max(op_name.length, Jsep.max_binop_len);\n\t\tJsep.binary_ops[op_name] = precedence;\n\t\tif (isRightAssociative) {\n\t\t\tJsep.right_associative.add(op_name);\n\t\t}\n\t\telse {\n\t\t\tJsep.right_associative.delete(op_name);\n\t\t}\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method addIdentifierChar\n\t * @param {string} char The additional character to treat as a valid part of an identifier\n\t * @returns {Jsep}\n\t */\n\tstatic addIdentifierChar(char) {\n\t\tJsep.additional_identifier_chars.add(char);\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method addLiteral\n\t * @param {string} literal_name The name of the literal to add\n\t * @param {*} literal_value The value of the literal\n\t * @returns {Jsep}\n\t */\n\tstatic addLiteral(literal_name, literal_value) {\n\t\tJsep.literals[literal_name] = literal_value;\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeUnaryOp\n\t * @param {string} op_name The name of the unary op to remove\n\t * @returns {Jsep}\n\t */\n\tstatic removeUnaryOp(op_name) {\n\t\tdelete Jsep.unary_ops[op_name];\n\t\tif (op_name.length === Jsep.max_unop_len) {\n\t\t\tJsep.max_unop_len = Jsep.getMaxKeyLen(Jsep.unary_ops);\n\t\t}\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeAllUnaryOps\n\t * @returns {Jsep}\n\t */\n\tstatic removeAllUnaryOps() {\n\t\tJsep.unary_ops = {};\n\t\tJsep.max_unop_len = 0;\n\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeIdentifierChar\n\t * @param {string} char The additional character to stop treating as a valid part of an identifier\n\t * @returns {Jsep}\n\t */\n\tstatic removeIdentifierChar(char) {\n\t\tJsep.additional_identifier_chars.delete(char);\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeBinaryOp\n\t * @param {string} op_name The name of the binary op to remove\n\t * @returns {Jsep}\n\t */\n\tstatic removeBinaryOp(op_name) {\n\t\tdelete Jsep.binary_ops[op_name];\n\n\t\tif (op_name.length === Jsep.max_binop_len) {\n\t\t\tJsep.max_binop_len = Jsep.getMaxKeyLen(Jsep.binary_ops);\n\t\t}\n\t\tJsep.right_associative.delete(op_name);\n\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeAllBinaryOps\n\t * @returns {Jsep}\n\t */\n\tstatic removeAllBinaryOps() {\n\t\tJsep.binary_ops = {};\n\t\tJsep.max_binop_len = 0;\n\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeLiteral\n\t * @param {string} literal_name The name of the literal to remove\n\t * @returns {Jsep}\n\t */\n\tstatic removeLiteral(literal_name) {\n\t\tdelete Jsep.literals[literal_name];\n\t\treturn Jsep;\n\t}\n\n\t/**\n\t * @method removeAllLiterals\n\t * @returns {Jsep}\n\t */\n\tstatic removeAllLiterals() {\n\t\tJsep.literals = {};\n\n\t\treturn Jsep;\n\t}\n\t// ==================== END CONFIG ============================\n\n\n\t/**\n\t * @returns {string}\n\t */\n\tget char() {\n\t\treturn this.expr.charAt(this.index);\n\t}\n\n\t/**\n\t * @returns {number}\n\t */\n\tget code() {\n\t\treturn this.expr.charCodeAt(this.index);\n\t};\n\n\n\t/**\n\t * @param {string} expr a string with the passed in express\n\t * @returns Jsep\n\t */\n\tconstructor(expr) {\n\t\t// `index` stores the character number we are currently at\n\t\t// All of the gobbles below will modify `index` as we move along\n\t\tthis.expr = expr;\n\t\tthis.index = 0;\n\t}\n\n\t/**\n\t * static top-level parser\n\t * @returns {jsep.Expression}\n\t */\n\tstatic parse(expr) {\n\t\treturn (new Jsep(expr)).parse();\n\t}\n\n\t/**\n\t * Get the longest key length of any object\n\t * @param {object} obj\n\t * @returns {number}\n\t */\n\tstatic getMaxKeyLen(obj) {\n\t\treturn Math.max(0, ...Object.keys(obj).map(k => k.length));\n\t}\n\n\t/**\n\t * `ch` is a character code in the next three functions\n\t * @param {number} ch\n\t * @returns {boolean}\n\t */\n\tstatic isDecimalDigit(ch) {\n\t\treturn (ch >= 48 && ch <= 57); // 0...9\n\t}\n\n\t/**\n\t * Returns the precedence of a binary operator or `0` if it isn't a binary operator. Can be float.\n\t * @param {string} op_val\n\t * @returns {number}\n\t */\n\tstatic binaryPrecedence(op_val) {\n\t\treturn Jsep.binary_ops[op_val] || 0;\n\t}\n\n\t/**\n\t * Looks for start of identifier\n\t * @param {number} ch\n\t * @returns {boolean}\n\t */\n\tstatic isIdentifierStart(ch) {\n\t\treturn  (ch >= 65 && ch <= 90) || // A...Z\n\t\t\t(ch >= 97 && ch <= 122) || // a...z\n\t\t\t(ch >= 128 && !Jsep.binary_ops[String.fromCharCode(ch)]) || // any non-ASCII that is not an operator\n\t\t\t(Jsep.additional_identifier_chars.has(String.fromCharCode(ch))); // additional characters\n\t}\n\n\t/**\n\t * @param {number} ch\n\t * @returns {boolean}\n\t */\n\tstatic isIdentifierPart(ch) {\n\t\treturn Jsep.isIdentifierStart(ch) || Jsep.isDecimalDigit(ch);\n\t}\n\n\t/**\n\t * throw error at index of the expression\n\t * @param {string} message\n\t * @throws\n\t */\n\tthrowError(message) {\n\t\tconst error = new Error(message + ' at character ' + this.index);\n\t\terror.index = this.index;\n\t\terror.description = message;\n\t\tthrow error;\n\t}\n\n\t/**\n\t * Run a given hook\n\t * @param {string} name\n\t * @param {jsep.Expression|false} [node]\n\t * @returns {?jsep.Expression}\n\t */\n\trunHook(name, node) {\n\t\tif (Jsep.hooks[name]) {\n\t\t\tconst env = { context: this, node };\n\t\t\tJsep.hooks.run(name, env);\n\t\t\treturn env.node;\n\t\t}\n\t\treturn node;\n\t}\n\n\t/**\n\t * Runs a given hook until one returns a node\n\t * @param {string} name\n\t * @returns {?jsep.Expression}\n\t */\n\tsearchHook(name) {\n\t\tif (Jsep.hooks[name]) {\n\t\t\tconst env = { context: this };\n\t\t\tJsep.hooks[name].find(function (callback) {\n\t\t\t\tcallback.call(env.context, env);\n\t\t\t\treturn env.node;\n\t\t\t});\n\t\t\treturn env.node;\n\t\t}\n\t}\n\n\t/**\n\t * Push `index` up to the next non-space character\n\t */\n\tgobbleSpaces() {\n\t\tlet ch = this.code;\n\t\t// Whitespace\n\t\twhile (ch === Jsep.SPACE_CODE\n\t\t|| ch === Jsep.TAB_CODE\n\t\t|| ch === Jsep.LF_CODE\n\t\t|| ch === Jsep.CR_CODE) {\n\t\t\tch = this.expr.charCodeAt(++this.index);\n\t\t}\n\t\tthis.runHook('gobble-spaces');\n\t}\n\n\t/**\n\t * Top-level method to parse all expressions and returns compound or single node\n\t * @returns {jsep.Expression}\n\t */\n\tparse() {\n\t\tthis.runHook('before-all');\n\t\tconst nodes = this.gobbleExpressions();\n\n\t\t// If there's only one expression just try returning the expression\n\t\tconst node = nodes.length === 1\n\t\t  ? nodes[0]\n\t\t\t: {\n\t\t\t\ttype: Jsep.COMPOUND,\n\t\t\t\tbody: nodes\n\t\t\t};\n\t\treturn this.runHook('after-all', node);\n\t}\n\n\t/**\n\t * top-level parser (but can be reused within as well)\n\t * @param {number} [untilICode]\n\t * @returns {jsep.Expression[]}\n\t */\n\tgobbleExpressions(untilICode) {\n\t\tlet nodes = [], ch_i, node;\n\n\t\twhile (this.index < this.expr.length) {\n\t\t\tch_i = this.code;\n\n\t\t\t// Expressions can be separated by semicolons, commas, or just inferred without any\n\t\t\t// separators\n\t\t\tif (ch_i === Jsep.SEMCOL_CODE || ch_i === Jsep.COMMA_CODE) {\n\t\t\t\tthis.index++; // ignore separators\n\t\t\t}\n\t\t\telse {\n\t\t\t\t// Try to gobble each expression individually\n\t\t\t\tif (node = this.gobbleExpression()) {\n\t\t\t\t\tnodes.push(node);\n\t\t\t\t\t// If we weren't able to find a binary expression and are out of room, then\n\t\t\t\t\t// the expression passed in probably has too much\n\t\t\t\t}\n\t\t\t\telse if (this.index < this.expr.length) {\n\t\t\t\t\tif (ch_i === untilICode) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tthis.throwError('Unexpected \"' + this.char + '\"');\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn nodes;\n\t}\n\n\t/**\n\t * The main parsing function.\n\t * @returns {?jsep.Expression}\n\t */\n\tgobbleExpression() {\n\t\tconst node = this.searchHook('gobble-expression') || this.gobbleBinaryExpression();\n\t\tthis.gobbleSpaces();\n\n\t\treturn this.runHook('after-expression', node);\n\t}\n\n\t/**\n\t * Search for the operation portion of the string (e.g. `+`, `===`)\n\t * Start by taking the longest possible binary operations (3 characters: `===`, `!==`, `>>>`)\n\t * and move down from 3 to 2 to 1 character until a matching binary operation is found\n\t * then, return that binary operation\n\t * @returns {string|boolean}\n\t */\n\tgobbleBinaryOp() {\n\t\tthis.gobbleSpaces();\n\t\tlet to_check = this.expr.substr(this.index, Jsep.max_binop_len);\n\t\tlet tc_len = to_check.length;\n\n\t\twhile (tc_len > 0) {\n\t\t\t// Don't accept a binary op when it is an identifier.\n\t\t\t// Binary ops that start with a identifier-valid character must be followed\n\t\t\t// by a non identifier-part valid character\n\t\t\tif (Jsep.binary_ops.hasOwnProperty(to_check) && (\n\t\t\t\t!Jsep.isIdentifierStart(this.code) ||\n\t\t\t\t(this.index + to_check.length < this.expr.length && !Jsep.isIdentifierPart(this.expr.charCodeAt(this.index + to_check.length)))\n\t\t\t)) {\n\t\t\t\tthis.index += tc_len;\n\t\t\t\treturn to_check;\n\t\t\t}\n\t\t\tto_check = to_check.substr(0, --tc_len);\n\t\t}\n\t\treturn false;\n\t}\n\n\t/**\n\t * This function is responsible for gobbling an individual expression,\n\t * e.g. `1`, `1+2`, `a+(b*2)-Math.sqrt(2)`\n\t * @returns {?jsep.BinaryExpression}\n\t */\n\tgobbleBinaryExpression() {\n\t\tlet node, biop, prec, stack, biop_info, left, right, i, cur_biop;\n\n\t\t// First, try to get the leftmost thing\n\t\t// Then, check to see if there's a binary operator operating on that leftmost thing\n\t\t// Don't gobbleBinaryOp without a left-hand-side\n\t\tleft = this.gobbleToken();\n\t\tif (!left) {\n\t\t\treturn left;\n\t\t}\n\t\tbiop = this.gobbleBinaryOp();\n\n\t\t// If there wasn't a binary operator, just return the leftmost node\n\t\tif (!biop) {\n\t\t\treturn left;\n\t\t}\n\n\t\t// Otherwise, we need to start a stack to properly place the binary operations in their\n\t\t// precedence structure\n\t\tbiop_info = { value: biop, prec: Jsep.binaryPrecedence(biop), right_a: Jsep.right_associative.has(biop) };\n\n\t\tright = this.gobbleToken();\n\n\t\tif (!right) {\n\t\t\tthis.throwError(\"Expected expression after \" + biop);\n\t\t}\n\n\t\tstack = [left, biop_info, right];\n\n\t\t// Properly deal with precedence using [recursive descent](http://www.engr.mun.ca/~theo/Misc/exp_parsing.htm)\n\t\twhile ((biop = this.gobbleBinaryOp())) {\n\t\t\tprec = Jsep.binaryPrecedence(biop);\n\n\t\t\tif (prec === 0) {\n\t\t\t\tthis.index -= biop.length;\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tbiop_info = { value: biop, prec, right_a: Jsep.right_associative.has(biop) };\n\n\t\t\tcur_biop = biop;\n\n\t\t\t// Reduce: make a binary expression from the three topmost entries.\n\t\t\tconst comparePrev = prev => biop_info.right_a && prev.right_a\n\t\t\t\t? prec > prev.prec\n\t\t\t\t: prec <= prev.prec;\n\t\t\twhile ((stack.length > 2) && comparePrev(stack[stack.length - 2])) {\n\t\t\t\tright = stack.pop();\n\t\t\t\tbiop = stack.pop().value;\n\t\t\t\tleft = stack.pop();\n\t\t\t\tnode = {\n\t\t\t\t\ttype: Jsep.BINARY_EXP,\n\t\t\t\t\toperator: biop,\n\t\t\t\t\tleft,\n\t\t\t\t\tright\n\t\t\t\t};\n\t\t\t\tstack.push(node);\n\t\t\t}\n\n\t\t\tnode = this.gobbleToken();\n\n\t\t\tif (!node) {\n\t\t\t\tthis.throwError(\"Expected expression after \" + cur_biop);\n\t\t\t}\n\n\t\t\tstack.push(biop_info, node);\n\t\t}\n\n\t\ti = stack.length - 1;\n\t\tnode = stack[i];\n\n\t\twhile (i > 1) {\n\t\t\tnode = {\n\t\t\t\ttype: Jsep.BINARY_EXP,\n\t\t\t\toperator: stack[i - 1].value,\n\t\t\t\tleft: stack[i - 2],\n\t\t\t\tright: node\n\t\t\t};\n\t\t\ti -= 2;\n\t\t}\n\n\t\treturn node;\n\t}\n\n\t/**\n\t * An individual part of a binary expression:\n\t * e.g. `foo.bar(baz)`, `1`, `\"abc\"`, `(a % 2)` (because it's in parenthesis)\n\t * @returns {boolean|jsep.Expression}\n\t */\n\tgobbleToken() {\n\t\tlet ch, to_check, tc_len, node;\n\n\t\tthis.gobbleSpaces();\n\t\tnode = this.searchHook('gobble-token');\n\t\tif (node) {\n\t\t\treturn this.runHook('after-token', node);\n\t\t}\n\n\t\tch = this.code;\n\n\t\tif (Jsep.isDecimalDigit(ch) || ch === Jsep.PERIOD_CODE) {\n\t\t\t// Char code 46 is a dot `.` which can start off a numeric literal\n\t\t\treturn this.gobbleNumericLiteral();\n\t\t}\n\n\t\tif (ch === Jsep.SQUOTE_CODE || ch === Jsep.DQUOTE_CODE) {\n\t\t\t// Single or double quotes\n\t\t\tnode = this.gobbleStringLiteral();\n\t\t}\n\t\telse if (ch === Jsep.OBRACK_CODE) {\n\t\t\tnode = this.gobbleArray();\n\t\t}\n\t\telse {\n\t\t\tto_check = this.expr.substr(this.index, Jsep.max_unop_len);\n\t\t\ttc_len = to_check.length;\n\n\t\t\twhile (tc_len > 0) {\n\t\t\t\t// Don't accept an unary op when it is an identifier.\n\t\t\t\t// Unary ops that start with a identifier-valid character must be followed\n\t\t\t\t// by a non identifier-part valid character\n\t\t\t\tif (Jsep.unary_ops.hasOwnProperty(to_check) && (\n\t\t\t\t\t!Jsep.isIdentifierStart(this.code) ||\n\t\t\t\t\t(this.index + to_check.length < this.expr.length && !Jsep.isIdentifierPart(this.expr.charCodeAt(this.index + to_check.length)))\n\t\t\t\t)) {\n\t\t\t\t\tthis.index += tc_len;\n\t\t\t\t\tconst argument = this.gobbleToken();\n\t\t\t\t\tif (!argument) {\n\t\t\t\t\t\tthis.throwError('missing unaryOp argument');\n\t\t\t\t\t}\n\t\t\t\t\treturn this.runHook('after-token', {\n\t\t\t\t\t\ttype: Jsep.UNARY_EXP,\n\t\t\t\t\t\toperator: to_check,\n\t\t\t\t\t\targument,\n\t\t\t\t\t\tprefix: true\n\t\t\t\t\t});\n\t\t\t\t}\n\n\t\t\t\tto_check = to_check.substr(0, --tc_len);\n\t\t\t}\n\n\t\t\tif (Jsep.isIdentifierStart(ch)) {\n\t\t\t\tnode = this.gobbleIdentifier();\n\t\t\t\tif (Jsep.literals.hasOwnProperty(node.name)) {\n\t\t\t\t\tnode = {\n\t\t\t\t\t\ttype: Jsep.LITERAL,\n\t\t\t\t\t\tvalue: Jsep.literals[node.name],\n\t\t\t\t\t\traw: node.name,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\telse if (node.name === Jsep.this_str) {\n\t\t\t\t\tnode = { type: Jsep.THIS_EXP };\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (ch === Jsep.OPAREN_CODE) { // open parenthesis\n\t\t\t\tnode = this.gobbleGroup();\n\t\t\t}\n\t\t}\n\n\t\tif (!node) {\n\t\t\treturn this.runHook('after-token', false);\n\t\t}\n\n\t\tnode = this.gobbleTokenProperty(node);\n\t\treturn this.runHook('after-token', node);\n\t}\n\n\t/**\n\t * Gobble properties of of identifiers/strings/arrays/groups.\n\t * e.g. `foo`, `bar.baz`, `foo['bar'].baz`\n\t * It also gobbles function calls:\n\t * e.g. `Math.acos(obj.angle)`\n\t * @param {jsep.Expression} node\n\t * @returns {jsep.Expression}\n\t */\n\tgobbleTokenProperty(node) {\n\t\tthis.gobbleSpaces();\n\n\t\tlet ch = this.code;\n\t\twhile (ch === Jsep.PERIOD_CODE || ch === Jsep.OBRACK_CODE || ch === Jsep.OPAREN_CODE || ch === Jsep.QUMARK_CODE) {\n\t\t\tlet optional;\n\t\t\tif (ch === Jsep.QUMARK_CODE) {\n\t\t\t\tif (this.expr.charCodeAt(this.index + 1) !== Jsep.PERIOD_CODE) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\toptional = true;\n\t\t\t\tthis.index += 2;\n\t\t\t\tthis.gobbleSpaces();\n\t\t\t\tch = this.code;\n\t\t\t}\n\t\t\tthis.index++;\n\n\t\t\tif (ch === Jsep.OBRACK_CODE) {\n\t\t\t\tnode = {\n\t\t\t\t\ttype: Jsep.MEMBER_EXP,\n\t\t\t\t\tcomputed: true,\n\t\t\t\t\tobject: node,\n\t\t\t\t\tproperty: this.gobbleExpression()\n\t\t\t\t};\n\t\t\t\tif (!node.property) {\n\t\t\t\t\tthis.throwError('Unexpected \"' + this.char + '\"');\n\t\t\t\t}\n\t\t\t\tthis.gobbleSpaces();\n\t\t\t\tch = this.code;\n\t\t\t\tif (ch !== Jsep.CBRACK_CODE) {\n\t\t\t\t\tthis.throwError('Unclosed [');\n\t\t\t\t}\n\t\t\t\tthis.index++;\n\t\t\t}\n\t\t\telse if (ch === Jsep.OPAREN_CODE) {\n\t\t\t\t// A function call is being made; gobble all the arguments\n\t\t\t\tnode = {\n\t\t\t\t\ttype: Jsep.CALL_EXP,\n\t\t\t\t\t'arguments': this.gobbleArguments(Jsep.CPAREN_CODE),\n\t\t\t\t\tcallee: node\n\t\t\t\t};\n\t\t\t}\n\t\t\telse if (ch === Jsep.PERIOD_CODE || optional) {\n\t\t\t\tif (optional) {\n\t\t\t\t\tthis.index--;\n\t\t\t\t}\n\t\t\t\tthis.gobbleSpaces();\n\t\t\t\tnode = {\n\t\t\t\t\ttype: Jsep.MEMBER_EXP,\n\t\t\t\t\tcomputed: false,\n\t\t\t\t\tobject: node,\n\t\t\t\t\tproperty: this.gobbleIdentifier(),\n\t\t\t\t};\n\t\t\t}\n\n\t\t\tif (optional) {\n\t\t\t\tnode.optional = true;\n\t\t\t} // else leave undefined for compatibility with esprima\n\n\t\t\tthis.gobbleSpaces();\n\t\t\tch = this.code;\n\t\t}\n\n\t\treturn node;\n\t}\n\n\t/**\n\t * Parse simple numeric literals: `12`, `3.4`, `.5`. Do this by using a string to\n\t * keep track of everything in the numeric literal and then calling `parseFloat` on that string\n\t * @returns {jsep.Literal}\n\t */\n\tgobbleNumericLiteral() {\n\t\tlet number = '', ch, chCode;\n\n\t\twhile (Jsep.isDecimalDigit(this.code)) {\n\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t}\n\n\t\tif (this.code === Jsep.PERIOD_CODE) { // can start with a decimal marker\n\t\t\tnumber += this.expr.charAt(this.index++);\n\n\t\t\twhile (Jsep.isDecimalDigit(this.code)) {\n\t\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t\t}\n\t\t}\n\n\t\tch = this.char;\n\n\t\tif (ch === 'e' || ch === 'E') { // exponent marker\n\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t\tch = this.char;\n\n\t\t\tif (ch === '+' || ch === '-') { // exponent sign\n\t\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t\t}\n\n\t\t\twhile (Jsep.isDecimalDigit(this.code)) { // exponent itself\n\t\t\t\tnumber += this.expr.charAt(this.index++);\n\t\t\t}\n\n\t\t\tif (!Jsep.isDecimalDigit(this.expr.charCodeAt(this.index - 1)) ) {\n\t\t\t\tthis.throwError('Expected exponent (' + number + this.char + ')');\n\t\t\t}\n\t\t}\n\n\t\tchCode = this.code;\n\n\t\t// Check to make sure this isn't a variable name that start with a number (123abc)\n\t\tif (Jsep.isIdentifierStart(chCode)) {\n\t\t\tthis.throwError('Variable names cannot start with a number (' +\n\t\t\t\tnumber + this.char + ')');\n\t\t}\n\t\telse if (chCode === Jsep.PERIOD_CODE || (number.length === 1 && number.charCodeAt(0) === Jsep.PERIOD_CODE)) {\n\t\t\tthis.throwError('Unexpected period');\n\t\t}\n\n\t\treturn {\n\t\t\ttype: Jsep.LITERAL,\n\t\t\tvalue: parseFloat(number),\n\t\t\traw: number\n\t\t};\n\t}\n\n\t/**\n\t * Parses a string literal, staring with single or double quotes with basic support for escape codes\n\t * e.g. `\"hello world\"`, `'this is\\nJSEP'`\n\t * @returns {jsep.Literal}\n\t */\n\tgobbleStringLiteral() {\n\t\tlet str = '';\n\t\tconst startIndex = this.index;\n\t\tconst quote = this.expr.charAt(this.index++);\n\t\tlet closed = false;\n\n\t\twhile (this.index < this.expr.length) {\n\t\t\tlet ch = this.expr.charAt(this.index++);\n\n\t\t\tif (ch === quote) {\n\t\t\t\tclosed = true;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\telse if (ch === '\\\\') {\n\t\t\t\t// Check for all of the common escape codes\n\t\t\t\tch = this.expr.charAt(this.index++);\n\n\t\t\t\tswitch (ch) {\n\t\t\t\t\tcase 'n': str += '\\n'; break;\n\t\t\t\t\tcase 'r': str += '\\r'; break;\n\t\t\t\t\tcase 't': str += '\\t'; break;\n\t\t\t\t\tcase 'b': str += '\\b'; break;\n\t\t\t\t\tcase 'f': str += '\\f'; break;\n\t\t\t\t\tcase 'v': str += '\\x0B'; break;\n\t\t\t\t\tdefault : str += ch;\n\t\t\t\t}\n\t\t\t}\n\t\t\telse {\n\t\t\t\tstr += ch;\n\t\t\t}\n\t\t}\n\n\t\tif (!closed) {\n\t\t\tthis.throwError('Unclosed quote after \"' + str + '\"');\n\t\t}\n\n\t\treturn {\n\t\t\ttype: Jsep.LITERAL,\n\t\t\tvalue: str,\n\t\t\traw: this.expr.substring(startIndex, this.index),\n\t\t};\n\t}\n\n\t/**\n\t * Gobbles only identifiers\n\t * e.g.: `foo`, `_value`, `$x1`\n\t * Also, this function checks if that identifier is a literal:\n\t * (e.g. `true`, `false`, `null`) or `this`\n\t * @returns {jsep.Identifier}\n\t */\n\tgobbleIdentifier() {\n\t\tlet ch = this.code, start = this.index;\n\n\t\tif (Jsep.isIdentifierStart(ch)) {\n\t\t\tthis.index++;\n\t\t}\n\t\telse {\n\t\t\tthis.throwError('Unexpected ' + this.char);\n\t\t}\n\n\t\twhile (this.index < this.expr.length) {\n\t\t\tch = this.code;\n\n\t\t\tif (Jsep.isIdentifierPart(ch)) {\n\t\t\t\tthis.index++;\n\t\t\t}\n\t\t\telse {\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t\treturn {\n\t\t\ttype: Jsep.IDENTIFIER,\n\t\t\tname: this.expr.slice(start, this.index),\n\t\t};\n\t}\n\n\t/**\n\t * Gobbles a list of arguments within the context of a function call\n\t * or array literal. This function also assumes that the opening character\n\t * `(` or `[` has already been gobbled, and gobbles expressions and commas\n\t * until the terminator character `)` or `]` is encountered.\n\t * e.g. `foo(bar, baz)`, `my_func()`, or `[bar, baz]`\n\t * @param {number} termination\n\t * @returns {jsep.Expression[]}\n\t */\n\tgobbleArguments(termination) {\n\t\tconst args = [];\n\t\tlet closed = false;\n\t\tlet separator_count = 0;\n\n\t\twhile (this.index < this.expr.length) {\n\t\t\tthis.gobbleSpaces();\n\t\t\tlet ch_i = this.code;\n\n\t\t\tif (ch_i === termination) { // done parsing\n\t\t\t\tclosed = true;\n\t\t\t\tthis.index++;\n\n\t\t\t\tif (termination === Jsep.CPAREN_CODE && separator_count && separator_count >= args.length){\n\t\t\t\t\tthis.throwError('Unexpected token ' + String.fromCharCode(termination));\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\t}\n\t\t\telse if (ch_i === Jsep.COMMA_CODE) { // between expressions\n\t\t\t\tthis.index++;\n\t\t\t\tseparator_count++;\n\n\t\t\t\tif (separator_count !== args.length) { // missing argument\n\t\t\t\t\tif (termination === Jsep.CPAREN_CODE) {\n\t\t\t\t\t\tthis.throwError('Unexpected token ,');\n\t\t\t\t\t}\n\t\t\t\t\telse if (termination === Jsep.CBRACK_CODE) {\n\t\t\t\t\t\tfor (let arg = args.length; arg < separator_count; arg++) {\n\t\t\t\t\t\t\targs.push(null);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\telse if (args.length !== separator_count && separator_count !== 0) {\n\t\t\t\t// NOTE: `&& separator_count !== 0` allows for either all commas, or all spaces as arguments\n\t\t\t\tthis.throwError('Expected comma');\n\t\t\t}\n\t\t\telse {\n\t\t\t\tconst node = this.gobbleExpression();\n\n\t\t\t\tif (!node || node.type === Jsep.COMPOUND) {\n\t\t\t\t\tthis.throwError('Expected comma');\n\t\t\t\t}\n\n\t\t\t\targs.push(node);\n\t\t\t}\n\t\t}\n\n\t\tif (!closed) {\n\t\t\tthis.throwError('Expected ' + String.fromCharCode(termination));\n\t\t}\n\n\t\treturn args;\n\t}\n\n\t/**\n\t * Responsible for parsing a group of things within parentheses `()`\n\t * that have no identifier in front (so not a function call)\n\t * This function assumes that it needs to gobble the opening parenthesis\n\t * and then tries to gobble everything within that parenthesis, assuming\n\t * that the next thing it should see is the close parenthesis. If not,\n\t * then the expression probably doesn't have a `)`\n\t * @returns {boolean|jsep.Expression}\n\t */\n\tgobbleGroup() {\n\t\tthis.index++;\n\t\tlet nodes = this.gobbleExpressions(Jsep.CPAREN_CODE);\n\t\tif (this.code === Jsep.CPAREN_CODE) {\n\t\t\tthis.index++;\n\t\t\tif (nodes.length === 1) {\n\t\t\t\treturn nodes[0];\n\t\t\t}\n\t\t\telse if (!nodes.length) {\n\t\t\t\treturn false;\n\t\t\t}\n\t\t\telse {\n\t\t\t\treturn {\n\t\t\t\t\ttype: Jsep.SEQUENCE_EXP,\n\t\t\t\t\texpressions: nodes,\n\t\t\t\t};\n\t\t\t}\n\t\t}\n\t\telse {\n\t\t\tthis.throwError('Unclosed (');\n\t\t}\n\t}\n\n\t/**\n\t * Responsible for parsing Array literals `[1, 2, 3]`\n\t * This function assumes that it needs to gobble the opening bracket\n\t * and then tries to gobble the expressions as arguments.\n\t * @returns {jsep.ArrayExpression}\n\t */\n\tgobbleArray() {\n\t\tthis.index++;\n\n\t\treturn {\n\t\t\ttype: Jsep.ARRAY_EXP,\n\t\t\telements: this.gobbleArguments(Jsep.CBRACK_CODE)\n\t\t};\n\t}\n}\n\n// Static fields:\nconst hooks = new Hooks();\nObject.assign(Jsep, {\n\thooks,\n\tplugins: new Plugins(Jsep),\n\n\t// Node Types\n\t// ----------\n\t// This is the full set of types that any JSEP node can be.\n\t// Store them here to save space when minified\n\tCOMPOUND:        'Compound',\n\tSEQUENCE_EXP:    'SequenceExpression',\n\tIDENTIFIER:      'Identifier',\n\tMEMBER_EXP:      'MemberExpression',\n\tLITERAL:         'Literal',\n\tTHIS_EXP:        'ThisExpression',\n\tCALL_EXP:        'CallExpression',\n\tUNARY_EXP:       'UnaryExpression',\n\tBINARY_EXP:      'BinaryExpression',\n\tARRAY_EXP:       'ArrayExpression',\n\n\tTAB_CODE:    9,\n\tLF_CODE:     10,\n\tCR_CODE:     13,\n\tSPACE_CODE:  32,\n\tPERIOD_CODE: 46, // '.'\n\tCOMMA_CODE:  44, // ','\n\tSQUOTE_CODE: 39, // single quote\n\tDQUOTE_CODE: 34, // double quotes\n\tOPAREN_CODE: 40, // (\n\tCPAREN_CODE: 41, // )\n\tOBRACK_CODE: 91, // [\n\tCBRACK_CODE: 93, // ]\n\tQUMARK_CODE: 63, // ?\n\tSEMCOL_CODE: 59, // ;\n\tCOLON_CODE:  58, // :\n\n\n\t// Operations\n\t// ----------\n\t// Use a quickly-accessible map to store all of the unary operators\n\t// Values are set to `1` (it really doesn't matter)\n\tunary_ops: {\n\t\t'-': 1,\n\t\t'!': 1,\n\t\t'~': 1,\n\t\t'+': 1\n\t},\n\n\t// Also use a map for the binary operations but set their values to their\n\t// binary precedence for quick reference (higher number = higher precedence)\n\t// see [Order of operations](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/Operator_Precedence)\n\tbinary_ops: {\n\t\t'||': 1, '??': 1,\n\t\t'&&': 2, '|': 3, '^': 4, '&': 5,\n\t\t'==': 6, '!=': 6, '===': 6, '!==': 6,\n\t\t'<': 7, '>': 7, '<=': 7, '>=': 7,\n\t\t'<<': 8, '>>': 8, '>>>': 8,\n\t\t'+': 9, '-': 9,\n\t\t'*': 10, '/': 10, '%': 10,\n\t\t'**': 11,\n\t},\n\n\t// sets specific binary_ops as right-associative\n\tright_associative: new Set(['**']),\n\n\t// Additional valid identifier chars, apart from a-z, A-Z and 0-9 (except on the starting char)\n\tadditional_identifier_chars: new Set(['$', '_']),\n\n\t// Literals\n\t// ----------\n\t// Store the values to return for the various literals we may encounter\n\tliterals: {\n\t\t'true': true,\n\t\t'false': false,\n\t\t'null': null\n\t},\n\n\t// Except for `this`, which is special. This could be changed to something like `'self'` as well\n\tthis_str: 'this',\n});\nJsep.max_unop_len = Jsep.getMaxKeyLen(Jsep.unary_ops);\nJsep.max_binop_len = Jsep.getMaxKeyLen(Jsep.binary_ops);\n\n// Backward Compatibility:\nconst jsep = expr => (new Jsep(expr)).parse();\nconst stdClassProps = Object.getOwnPropertyNames(class Test{});\nObject.getOwnPropertyNames(Jsep)\n\t.filter(prop => !stdClassProps.includes(prop) && jsep[prop] === undefined)\n\t.forEach((m) => {\n\t\tjsep[m] = Jsep[m];\n\t});\njsep.Jsep = Jsep; // allows for const { Jsep } = require('jsep');\n\nconst CONDITIONAL_EXP = 'ConditionalExpression';\n\nvar ternary = {\n\tname: 'ternary',\n\n\tinit(jsep) {\n\t\t// Ternary expression: test ? consequent : alternate\n\t\tjsep.hooks.add('after-expression', function gobbleTernary(env) {\n\t\t\tif (env.node && this.code === jsep.QUMARK_CODE) {\n\t\t\t\tthis.index++;\n\t\t\t\tconst test = env.node;\n\t\t\t\tconst consequent = this.gobbleExpression();\n\n\t\t\t\tif (!consequent) {\n\t\t\t\t\tthis.throwError('Expected expression');\n\t\t\t\t}\n\n\t\t\t\tthis.gobbleSpaces();\n\n\t\t\t\tif (this.code === jsep.COLON_CODE) {\n\t\t\t\t\tthis.index++;\n\t\t\t\t\tconst alternate = this.gobbleExpression();\n\n\t\t\t\t\tif (!alternate) {\n\t\t\t\t\t\tthis.throwError('Expected expression');\n\t\t\t\t\t}\n\t\t\t\t\tenv.node = {\n\t\t\t\t\t\ttype: CONDITIONAL_EXP,\n\t\t\t\t\t\ttest,\n\t\t\t\t\t\tconsequent,\n\t\t\t\t\t\talternate,\n\t\t\t\t\t};\n\n\t\t\t\t\t// check for operators of higher priority than ternary (i.e. assignment)\n\t\t\t\t\t// jsep sets || at 1, and assignment at 0.9, and conditional should be between them\n\t\t\t\t\tif (test.operator && jsep.binary_ops[test.operator] <= 0.9) {\n\t\t\t\t\t\tlet newTest = test;\n\t\t\t\t\t\twhile (newTest.right.operator && jsep.binary_ops[newTest.right.operator] <= 0.9) {\n\t\t\t\t\t\t\tnewTest = newTest.right;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tenv.node.test = newTest.right;\n\t\t\t\t\t\tnewTest.right = env.node;\n\t\t\t\t\t\tenv.node = test;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\telse {\n\t\t\t\t\tthis.throwError('Expected :');\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t},\n};\n\n// Add default plugins:\n\njsep.plugins.register(ternary);\n\nmodule.exports = jsep;\n//# sourceMappingURL=jsep.cjs.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jsep/dist/cjs/jsep.cjs.js\n");

/***/ })

};
;