"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/lib0";
exports.ids = ["vendor-chunks/lib0"];
exports.modules = {

/***/ "(ssr)/./node_modules/lib0/array.js":
/*!************************************!*\
  !*** ./node_modules/lib0/array.js ***!
  \************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appendTo: () => (/* binding */ appendTo),\n/* harmony export */   bubblesortItem: () => (/* binding */ bubblesortItem),\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   equalFlat: () => (/* binding */ equalFlat),\n/* harmony export */   every: () => (/* binding */ every),\n/* harmony export */   flatten: () => (/* binding */ flatten),\n/* harmony export */   fold: () => (/* binding */ fold),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   last: () => (/* binding */ last),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   some: () => (/* binding */ some),\n/* harmony export */   unfold: () => (/* binding */ unfold),\n/* harmony export */   unique: () => (/* binding */ unique),\n/* harmony export */   uniqueBy: () => (/* binding */ uniqueBy)\n/* harmony export */ });\n/* harmony import */ var _set_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./set.js */ \"(ssr)/./node_modules/lib0/set.js\");\n/**\n * Utility module to work with Arrays.\n *\n * @module array\n */\n\n\n\n/**\n * Return the last element of an array. The element must exist\n *\n * @template L\n * @param {ArrayLike<L>} arr\n * @return {L}\n */\nconst last = arr => arr[arr.length - 1]\n\n/**\n * @template C\n * @return {Array<C>}\n */\nconst create = () => /** @type {Array<C>} */ ([])\n\n/**\n * @template D\n * @param {Array<D>} a\n * @return {Array<D>}\n */\nconst copy = a => /** @type {Array<D>} */ (a.slice())\n\n/**\n * Append elements from src to dest\n *\n * @template M\n * @param {Array<M>} dest\n * @param {Array<M>} src\n */\nconst appendTo = (dest, src) => {\n  for (let i = 0; i < src.length; i++) {\n    dest.push(src[i])\n  }\n}\n\n/**\n * Transforms something array-like to an actual Array.\n *\n * @function\n * @template T\n * @param {ArrayLike<T>|Iterable<T>} arraylike\n * @return {T}\n */\nconst from = Array.from\n\n/**\n * True iff condition holds on every element in the Array.\n *\n * @function\n * @template ITEM\n * @template {ArrayLike<ITEM>} ARR\n *\n * @param {ARR} arr\n * @param {function(ITEM, number, ARR):boolean} f\n * @return {boolean}\n */\nconst every = (arr, f) => {\n  for (let i = 0; i < arr.length; i++) {\n    if (!f(arr[i], i, arr)) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * True iff condition holds on some element in the Array.\n *\n * @function\n * @template S\n * @template {ArrayLike<S>} ARR\n * @param {ARR} arr\n * @param {function(S, number, ARR):boolean} f\n * @return {boolean}\n */\nconst some = (arr, f) => {\n  for (let i = 0; i < arr.length; i++) {\n    if (f(arr[i], i, arr)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * @template ELEM\n *\n * @param {ArrayLike<ELEM>} a\n * @param {ArrayLike<ELEM>} b\n * @return {boolean}\n */\nconst equalFlat = (a, b) => a.length === b.length && every(a, (item, index) => item === b[index])\n\n/**\n * @template ELEM\n * @param {Array<Array<ELEM>>} arr\n * @return {Array<ELEM>}\n */\nconst flatten = arr => fold(arr, /** @type {Array<ELEM>} */ ([]), (acc, val) => acc.concat(val))\n\n/**\n * @template T\n * @param {number} len\n * @param {function(number, Array<T>):T} f\n * @return {Array<T>}\n */\nconst unfold = (len, f) => {\n  const array = new Array(len)\n  for (let i = 0; i < len; i++) {\n    array[i] = f(i, array)\n  }\n  return array\n}\n\n/**\n * @template T\n * @template RESULT\n * @param {Array<T>} arr\n * @param {RESULT} seed\n * @param {function(RESULT, T, number):RESULT} folder\n */\nconst fold = (arr, seed, folder) => arr.reduce(folder, seed)\n\nconst isArray = Array.isArray\n\n/**\n * @template T\n * @param {Array<T>} arr\n * @return {Array<T>}\n */\nconst unique = arr => from(_set_js__WEBPACK_IMPORTED_MODULE_0__.from(arr))\n\n/**\n * @template T\n * @template M\n * @param {ArrayLike<T>} arr\n * @param {function(T):M} mapper\n * @return {Array<T>}\n */\nconst uniqueBy = (arr, mapper) => {\n  /**\n   * @type {Set<M>}\n   */\n  const happened = _set_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  /**\n   * @type {Array<T>}\n   */\n  const result = []\n  for (let i = 0; i < arr.length; i++) {\n    const el = arr[i]\n    const mapped = mapper(el)\n    if (!happened.has(mapped)) {\n      happened.add(mapped)\n      result.push(el)\n    }\n  }\n  return result\n}\n\n/**\n * @template {ArrayLike<any>} ARR\n * @template {function(ARR extends ArrayLike<infer T> ? T : never, number, ARR):any} MAPPER\n * @param {ARR} arr\n * @param {MAPPER} mapper\n * @return {Array<MAPPER extends function(...any): infer M ? M : never>}\n */\nconst map = (arr, mapper) => {\n  /**\n   * @type {Array<any>}\n   */\n  const res = Array(arr.length)\n  for (let i = 0; i < arr.length; i++) {\n    res[i] = mapper(/** @type {any} */ (arr[i]), i, /** @type {any} */ (arr))\n  }\n  return /** @type {any} */ (res)\n}\n\n/**\n * This function bubble-sorts a single item to the correct position. The sort happens in-place and\n * might be useful to ensure that a single item is at the correct position in an otherwise sorted\n * array.\n *\n * @example\n *  const arr = [3, 2, 5]\n *  arr.sort((a, b) => a - b)\n *  arr // => [2, 3, 5]\n *  arr.splice(1, 0, 7)\n *  array.bubbleSortItem(arr, 1, (a, b) => a - b)\n *  arr // => [2, 3, 5, 7]\n *\n * @template T\n * @param {Array<T>} arr\n * @param {number} i\n * @param {(a:T,b:T) => number} compareFn\n */\nconst bubblesortItem = (arr, i, compareFn) => {\n  const n = arr[i]\n  let j = i\n  // try to sort to the right\n  while (j + 1 < arr.length && compareFn(n, arr[j + 1]) > 0) {\n    arr[j] = arr[j + 1]\n    arr[++j] = n\n  }\n  if (i === j && j > 0) { // no change yet\n    // sort to the left\n    while (j > 0 && compareFn(arr[j - 1], n) > 0) {\n      arr[j] = arr[j - 1]\n      arr[--j] = n\n    }\n  }\n  return j\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/binary.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/binary.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BIT1: () => (/* binding */ BIT1),\n/* harmony export */   BIT10: () => (/* binding */ BIT10),\n/* harmony export */   BIT11: () => (/* binding */ BIT11),\n/* harmony export */   BIT12: () => (/* binding */ BIT12),\n/* harmony export */   BIT13: () => (/* binding */ BIT13),\n/* harmony export */   BIT14: () => (/* binding */ BIT14),\n/* harmony export */   BIT15: () => (/* binding */ BIT15),\n/* harmony export */   BIT16: () => (/* binding */ BIT16),\n/* harmony export */   BIT17: () => (/* binding */ BIT17),\n/* harmony export */   BIT18: () => (/* binding */ BIT18),\n/* harmony export */   BIT19: () => (/* binding */ BIT19),\n/* harmony export */   BIT2: () => (/* binding */ BIT2),\n/* harmony export */   BIT20: () => (/* binding */ BIT20),\n/* harmony export */   BIT21: () => (/* binding */ BIT21),\n/* harmony export */   BIT22: () => (/* binding */ BIT22),\n/* harmony export */   BIT23: () => (/* binding */ BIT23),\n/* harmony export */   BIT24: () => (/* binding */ BIT24),\n/* harmony export */   BIT25: () => (/* binding */ BIT25),\n/* harmony export */   BIT26: () => (/* binding */ BIT26),\n/* harmony export */   BIT27: () => (/* binding */ BIT27),\n/* harmony export */   BIT28: () => (/* binding */ BIT28),\n/* harmony export */   BIT29: () => (/* binding */ BIT29),\n/* harmony export */   BIT3: () => (/* binding */ BIT3),\n/* harmony export */   BIT30: () => (/* binding */ BIT30),\n/* harmony export */   BIT31: () => (/* binding */ BIT31),\n/* harmony export */   BIT32: () => (/* binding */ BIT32),\n/* harmony export */   BIT4: () => (/* binding */ BIT4),\n/* harmony export */   BIT5: () => (/* binding */ BIT5),\n/* harmony export */   BIT6: () => (/* binding */ BIT6),\n/* harmony export */   BIT7: () => (/* binding */ BIT7),\n/* harmony export */   BIT8: () => (/* binding */ BIT8),\n/* harmony export */   BIT9: () => (/* binding */ BIT9),\n/* harmony export */   BITS0: () => (/* binding */ BITS0),\n/* harmony export */   BITS1: () => (/* binding */ BITS1),\n/* harmony export */   BITS10: () => (/* binding */ BITS10),\n/* harmony export */   BITS11: () => (/* binding */ BITS11),\n/* harmony export */   BITS12: () => (/* binding */ BITS12),\n/* harmony export */   BITS13: () => (/* binding */ BITS13),\n/* harmony export */   BITS14: () => (/* binding */ BITS14),\n/* harmony export */   BITS15: () => (/* binding */ BITS15),\n/* harmony export */   BITS16: () => (/* binding */ BITS16),\n/* harmony export */   BITS17: () => (/* binding */ BITS17),\n/* harmony export */   BITS18: () => (/* binding */ BITS18),\n/* harmony export */   BITS19: () => (/* binding */ BITS19),\n/* harmony export */   BITS2: () => (/* binding */ BITS2),\n/* harmony export */   BITS20: () => (/* binding */ BITS20),\n/* harmony export */   BITS21: () => (/* binding */ BITS21),\n/* harmony export */   BITS22: () => (/* binding */ BITS22),\n/* harmony export */   BITS23: () => (/* binding */ BITS23),\n/* harmony export */   BITS24: () => (/* binding */ BITS24),\n/* harmony export */   BITS25: () => (/* binding */ BITS25),\n/* harmony export */   BITS26: () => (/* binding */ BITS26),\n/* harmony export */   BITS27: () => (/* binding */ BITS27),\n/* harmony export */   BITS28: () => (/* binding */ BITS28),\n/* harmony export */   BITS29: () => (/* binding */ BITS29),\n/* harmony export */   BITS3: () => (/* binding */ BITS3),\n/* harmony export */   BITS30: () => (/* binding */ BITS30),\n/* harmony export */   BITS31: () => (/* binding */ BITS31),\n/* harmony export */   BITS32: () => (/* binding */ BITS32),\n/* harmony export */   BITS4: () => (/* binding */ BITS4),\n/* harmony export */   BITS5: () => (/* binding */ BITS5),\n/* harmony export */   BITS6: () => (/* binding */ BITS6),\n/* harmony export */   BITS7: () => (/* binding */ BITS7),\n/* harmony export */   BITS8: () => (/* binding */ BITS8),\n/* harmony export */   BITS9: () => (/* binding */ BITS9)\n/* harmony export */ });\n/* eslint-env browser */\n\n/**\n * Binary data constants.\n *\n * @module binary\n */\n\n/**\n * n-th bit activated.\n *\n * @type {number}\n */\nconst BIT1 = 1\nconst BIT2 = 2\nconst BIT3 = 4\nconst BIT4 = 8\nconst BIT5 = 16\nconst BIT6 = 32\nconst BIT7 = 64\nconst BIT8 = 128\nconst BIT9 = 256\nconst BIT10 = 512\nconst BIT11 = 1024\nconst BIT12 = 2048\nconst BIT13 = 4096\nconst BIT14 = 8192\nconst BIT15 = 16384\nconst BIT16 = 32768\nconst BIT17 = 65536\nconst BIT18 = 1 << 17\nconst BIT19 = 1 << 18\nconst BIT20 = 1 << 19\nconst BIT21 = 1 << 20\nconst BIT22 = 1 << 21\nconst BIT23 = 1 << 22\nconst BIT24 = 1 << 23\nconst BIT25 = 1 << 24\nconst BIT26 = 1 << 25\nconst BIT27 = 1 << 26\nconst BIT28 = 1 << 27\nconst BIT29 = 1 << 28\nconst BIT30 = 1 << 29\nconst BIT31 = 1 << 30\nconst BIT32 = 1 << 31\n\n/**\n * First n bits activated.\n *\n * @type {number}\n */\nconst BITS0 = 0\nconst BITS1 = 1\nconst BITS2 = 3\nconst BITS3 = 7\nconst BITS4 = 15\nconst BITS5 = 31\nconst BITS6 = 63\nconst BITS7 = 127\nconst BITS8 = 255\nconst BITS9 = 511\nconst BITS10 = 1023\nconst BITS11 = 2047\nconst BITS12 = 4095\nconst BITS13 = 8191\nconst BITS14 = 16383\nconst BITS15 = 32767\nconst BITS16 = 65535\nconst BITS17 = BIT18 - 1\nconst BITS18 = BIT19 - 1\nconst BITS19 = BIT20 - 1\nconst BITS20 = BIT21 - 1\nconst BITS21 = BIT22 - 1\nconst BITS22 = BIT23 - 1\nconst BITS23 = BIT24 - 1\nconst BITS24 = BIT25 - 1\nconst BITS25 = BIT26 - 1\nconst BITS26 = BIT27 - 1\nconst BITS27 = BIT28 - 1\nconst BITS28 = BIT29 - 1\nconst BITS29 = BIT30 - 1\nconst BITS30 = BIT31 - 1\n/**\n * @type {number}\n */\nconst BITS31 = 0x7FFFFFFF\n/**\n * @type {number}\n */\nconst BITS32 = 0xFFFFFFFF\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9iaW5hcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRVA7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ087QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDUDtBQUNBLFVBQVU7QUFDVjtBQUNPO0FBQ1A7QUFDQSxVQUFVO0FBQ1Y7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbGliMC9iaW5hcnkuanM/ZWRhYSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiBlc2xpbnQtZW52IGJyb3dzZXIgKi9cblxuLyoqXG4gKiBCaW5hcnkgZGF0YSBjb25zdGFudHMuXG4gKlxuICogQG1vZHVsZSBiaW5hcnlcbiAqL1xuXG4vKipcbiAqIG4tdGggYml0IGFjdGl2YXRlZC5cbiAqXG4gKiBAdHlwZSB7bnVtYmVyfVxuICovXG5leHBvcnQgY29uc3QgQklUMSA9IDFcbmV4cG9ydCBjb25zdCBCSVQyID0gMlxuZXhwb3J0IGNvbnN0IEJJVDMgPSA0XG5leHBvcnQgY29uc3QgQklUNCA9IDhcbmV4cG9ydCBjb25zdCBCSVQ1ID0gMTZcbmV4cG9ydCBjb25zdCBCSVQ2ID0gMzJcbmV4cG9ydCBjb25zdCBCSVQ3ID0gNjRcbmV4cG9ydCBjb25zdCBCSVQ4ID0gMTI4XG5leHBvcnQgY29uc3QgQklUOSA9IDI1NlxuZXhwb3J0IGNvbnN0IEJJVDEwID0gNTEyXG5leHBvcnQgY29uc3QgQklUMTEgPSAxMDI0XG5leHBvcnQgY29uc3QgQklUMTIgPSAyMDQ4XG5leHBvcnQgY29uc3QgQklUMTMgPSA0MDk2XG5leHBvcnQgY29uc3QgQklUMTQgPSA4MTkyXG5leHBvcnQgY29uc3QgQklUMTUgPSAxNjM4NFxuZXhwb3J0IGNvbnN0IEJJVDE2ID0gMzI3NjhcbmV4cG9ydCBjb25zdCBCSVQxNyA9IDY1NTM2XG5leHBvcnQgY29uc3QgQklUMTggPSAxIDw8IDE3XG5leHBvcnQgY29uc3QgQklUMTkgPSAxIDw8IDE4XG5leHBvcnQgY29uc3QgQklUMjAgPSAxIDw8IDE5XG5leHBvcnQgY29uc3QgQklUMjEgPSAxIDw8IDIwXG5leHBvcnQgY29uc3QgQklUMjIgPSAxIDw8IDIxXG5leHBvcnQgY29uc3QgQklUMjMgPSAxIDw8IDIyXG5leHBvcnQgY29uc3QgQklUMjQgPSAxIDw8IDIzXG5leHBvcnQgY29uc3QgQklUMjUgPSAxIDw8IDI0XG5leHBvcnQgY29uc3QgQklUMjYgPSAxIDw8IDI1XG5leHBvcnQgY29uc3QgQklUMjcgPSAxIDw8IDI2XG5leHBvcnQgY29uc3QgQklUMjggPSAxIDw8IDI3XG5leHBvcnQgY29uc3QgQklUMjkgPSAxIDw8IDI4XG5leHBvcnQgY29uc3QgQklUMzAgPSAxIDw8IDI5XG5leHBvcnQgY29uc3QgQklUMzEgPSAxIDw8IDMwXG5leHBvcnQgY29uc3QgQklUMzIgPSAxIDw8IDMxXG5cbi8qKlxuICogRmlyc3QgbiBiaXRzIGFjdGl2YXRlZC5cbiAqXG4gKiBAdHlwZSB7bnVtYmVyfVxuICovXG5leHBvcnQgY29uc3QgQklUUzAgPSAwXG5leHBvcnQgY29uc3QgQklUUzEgPSAxXG5leHBvcnQgY29uc3QgQklUUzIgPSAzXG5leHBvcnQgY29uc3QgQklUUzMgPSA3XG5leHBvcnQgY29uc3QgQklUUzQgPSAxNVxuZXhwb3J0IGNvbnN0IEJJVFM1ID0gMzFcbmV4cG9ydCBjb25zdCBCSVRTNiA9IDYzXG5leHBvcnQgY29uc3QgQklUUzcgPSAxMjdcbmV4cG9ydCBjb25zdCBCSVRTOCA9IDI1NVxuZXhwb3J0IGNvbnN0IEJJVFM5ID0gNTExXG5leHBvcnQgY29uc3QgQklUUzEwID0gMTAyM1xuZXhwb3J0IGNvbnN0IEJJVFMxMSA9IDIwNDdcbmV4cG9ydCBjb25zdCBCSVRTMTIgPSA0MDk1XG5leHBvcnQgY29uc3QgQklUUzEzID0gODE5MVxuZXhwb3J0IGNvbnN0IEJJVFMxNCA9IDE2MzgzXG5leHBvcnQgY29uc3QgQklUUzE1ID0gMzI3NjdcbmV4cG9ydCBjb25zdCBCSVRTMTYgPSA2NTUzNVxuZXhwb3J0IGNvbnN0IEJJVFMxNyA9IEJJVDE4IC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMxOCA9IEJJVDE5IC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMxOSA9IEJJVDIwIC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyMCA9IEJJVDIxIC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyMSA9IEJJVDIyIC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyMiA9IEJJVDIzIC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyMyA9IEJJVDI0IC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyNCA9IEJJVDI1IC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyNSA9IEJJVDI2IC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyNiA9IEJJVDI3IC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyNyA9IEJJVDI4IC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyOCA9IEJJVDI5IC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMyOSA9IEJJVDMwIC0gMVxuZXhwb3J0IGNvbnN0IEJJVFMzMCA9IEJJVDMxIC0gMVxuLyoqXG4gKiBAdHlwZSB7bnVtYmVyfVxuICovXG5leHBvcnQgY29uc3QgQklUUzMxID0gMHg3RkZGRkZGRlxuLyoqXG4gKiBAdHlwZSB7bnVtYmVyfVxuICovXG5leHBvcnQgY29uc3QgQklUUzMyID0gMHhGRkZGRkZGRlxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/binary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/broadcastchannel.js":
/*!***********************************************!*\
  !*** ./node_modules/lib0/broadcastchannel.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   publish: () => (/* binding */ publish),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   unsubscribe: () => (/* binding */ unsubscribe)\n/* harmony export */ });\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/lib0/map.js\");\n/* harmony import */ var _set_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./set.js */ \"(ssr)/./node_modules/lib0/set.js\");\n/* harmony import */ var _buffer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./buffer.js */ \"(ssr)/./node_modules/lib0/buffer.js\");\n/* harmony import */ var _storage_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./storage.js */ \"(ssr)/./node_modules/lib0/storage.js\");\n/* eslint-env browser */\n\n/**\n * Helpers for cross-tab communication using broadcastchannel with LocalStorage fallback.\n *\n * ```js\n * // In browser window A:\n * broadcastchannel.subscribe('my events', data => console.log(data))\n * broadcastchannel.publish('my events', 'Hello world!') // => A: 'Hello world!' fires synchronously in same tab\n *\n * // In browser window B:\n * broadcastchannel.publish('my events', 'hello from tab B') // => A: 'hello from tab B'\n * ```\n *\n * @module broadcastchannel\n */\n\n// @todo before next major: use Uint8Array instead as buffer object\n\n\n\n\n\n\n/**\n * @typedef {Object} Channel\n * @property {Set<function(any, any):any>} Channel.subs\n * @property {any} Channel.bc\n */\n\n/**\n * @type {Map<string, Channel>}\n */\nconst channels = new Map()\n\n/* c8 ignore start */\nclass LocalStoragePolyfill {\n  /**\n   * @param {string} room\n   */\n  constructor (room) {\n    this.room = room\n    /**\n     * @type {null|function({data:ArrayBuffer}):void}\n     */\n    this.onmessage = null\n    /**\n     * @param {any} e\n     */\n    this._onChange = e => e.key === room && this.onmessage !== null && this.onmessage({ data: _buffer_js__WEBPACK_IMPORTED_MODULE_0__.fromBase64(e.newValue || '') })\n    _storage_js__WEBPACK_IMPORTED_MODULE_1__.onChange(this._onChange)\n  }\n\n  /**\n   * @param {ArrayBuffer} buf\n   */\n  postMessage (buf) {\n    _storage_js__WEBPACK_IMPORTED_MODULE_1__.varStorage.setItem(this.room, _buffer_js__WEBPACK_IMPORTED_MODULE_0__.toBase64(_buffer_js__WEBPACK_IMPORTED_MODULE_0__.createUint8ArrayFromArrayBuffer(buf)))\n  }\n\n  close () {\n    _storage_js__WEBPACK_IMPORTED_MODULE_1__.offChange(this._onChange)\n  }\n}\n/* c8 ignore stop */\n\n// Use BroadcastChannel or Polyfill\n/* c8 ignore next */\nconst BC = typeof BroadcastChannel === 'undefined' ? LocalStoragePolyfill : BroadcastChannel\n\n/**\n * @param {string} room\n * @return {Channel}\n */\nconst getChannel = room =>\n  _map_js__WEBPACK_IMPORTED_MODULE_2__.setIfUndefined(channels, room, () => {\n    const subs = _set_js__WEBPACK_IMPORTED_MODULE_3__.create()\n    const bc = new BC(room)\n    /**\n     * @param {{data:ArrayBuffer}} e\n     */\n    /* c8 ignore next */\n    bc.onmessage = e => subs.forEach(sub => sub(e.data, 'broadcastchannel'))\n    return {\n      bc, subs\n    }\n  })\n\n/**\n * Subscribe to global `publish` events.\n *\n * @function\n * @param {string} room\n * @param {function(any, any):any} f\n */\nconst subscribe = (room, f) => {\n  getChannel(room).subs.add(f)\n  return f\n}\n\n/**\n * Unsubscribe from `publish` global events.\n *\n * @function\n * @param {string} room\n * @param {function(any, any):any} f\n */\nconst unsubscribe = (room, f) => {\n  const channel = getChannel(room)\n  const unsubscribed = channel.subs.delete(f)\n  if (unsubscribed && channel.subs.size === 0) {\n    channel.bc.close()\n    channels.delete(room)\n  }\n  return unsubscribed\n}\n\n/**\n * Publish data to all subscribers (including subscribers on this tab)\n *\n * @function\n * @param {string} room\n * @param {any} data\n * @param {any} [origin]\n */\nconst publish = (room, data, origin = null) => {\n  const c = getChannel(room)\n  c.bc.postMessage(data)\n  c.subs.forEach(sub => sub(data, origin))\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/broadcastchannel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/buffer.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/buffer.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   copyUint8Array: () => (/* binding */ copyUint8Array),\n/* harmony export */   createUint8ArrayFromArrayBuffer: () => (/* binding */ createUint8ArrayFromArrayBuffer),\n/* harmony export */   createUint8ArrayFromLen: () => (/* binding */ createUint8ArrayFromLen),\n/* harmony export */   createUint8ArrayViewFromArrayBuffer: () => (/* binding */ createUint8ArrayViewFromArrayBuffer),\n/* harmony export */   decodeAny: () => (/* binding */ decodeAny),\n/* harmony export */   encodeAny: () => (/* binding */ encodeAny),\n/* harmony export */   fromBase64: () => (/* binding */ fromBase64),\n/* harmony export */   fromBase64UrlEncoded: () => (/* binding */ fromBase64UrlEncoded),\n/* harmony export */   fromHexString: () => (/* binding */ fromHexString),\n/* harmony export */   shiftNBitsLeft: () => (/* binding */ shiftNBitsLeft),\n/* harmony export */   toBase64: () => (/* binding */ toBase64),\n/* harmony export */   toBase64UrlEncoded: () => (/* binding */ toBase64UrlEncoded),\n/* harmony export */   toHexString: () => (/* binding */ toHexString)\n/* harmony export */ });\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./string.js */ \"(ssr)/./node_modules/lib0/string.js\");\n/* harmony import */ var _environment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./environment.js */ \"(ssr)/./node_modules/lib0/environment.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/lib0/array.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var _encoding_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./encoding.js */ \"(ssr)/./node_modules/lib0/encoding.js\");\n/* harmony import */ var _decoding_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./decoding.js */ \"(ssr)/./node_modules/lib0/decoding.js\");\n/**\n * Utility functions to work with buffers (Uint8Array).\n *\n * @module buffer\n */\n\n\n\n\n\n\n\n\n/**\n * @param {number} len\n */\nconst createUint8ArrayFromLen = len => new Uint8Array(len)\n\n/**\n * Create Uint8Array with initial content from buffer\n *\n * @param {ArrayBuffer} buffer\n * @param {number} byteOffset\n * @param {number} length\n */\nconst createUint8ArrayViewFromArrayBuffer = (buffer, byteOffset, length) => new Uint8Array(buffer, byteOffset, length)\n\n/**\n * Create Uint8Array with initial content from buffer\n *\n * @param {ArrayBuffer} buffer\n */\nconst createUint8ArrayFromArrayBuffer = buffer => new Uint8Array(buffer)\n\n/* c8 ignore start */\n/**\n * @param {Uint8Array} bytes\n * @return {string}\n */\nconst toBase64Browser = bytes => {\n  let s = ''\n  for (let i = 0; i < bytes.byteLength; i++) {\n    s += _string_js__WEBPACK_IMPORTED_MODULE_0__.fromCharCode(bytes[i])\n  }\n  // eslint-disable-next-line no-undef\n  return btoa(s)\n}\n/* c8 ignore stop */\n\n/**\n * @param {Uint8Array} bytes\n * @return {string}\n */\nconst toBase64Node = bytes => Buffer.from(bytes.buffer, bytes.byteOffset, bytes.byteLength).toString('base64')\n\n/* c8 ignore start */\n/**\n * @param {string} s\n * @return {Uint8Array}\n */\nconst fromBase64Browser = s => {\n  // eslint-disable-next-line no-undef\n  const a = atob(s)\n  const bytes = createUint8ArrayFromLen(a.length)\n  for (let i = 0; i < a.length; i++) {\n    bytes[i] = a.charCodeAt(i)\n  }\n  return bytes\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} s\n */\nconst fromBase64Node = s => {\n  const buf = Buffer.from(s, 'base64')\n  return createUint8ArrayViewFromArrayBuffer(buf.buffer, buf.byteOffset, buf.byteLength)\n}\n\n/* c8 ignore next */\nconst toBase64 = _environment_js__WEBPACK_IMPORTED_MODULE_1__.isBrowser ? toBase64Browser : toBase64Node\n\n/* c8 ignore next */\nconst fromBase64 = _environment_js__WEBPACK_IMPORTED_MODULE_1__.isBrowser ? fromBase64Browser : fromBase64Node\n\n/**\n * Implements base64url - see https://datatracker.ietf.org/doc/html/rfc4648#section-5\n * @param {Uint8Array} buf\n */\nconst toBase64UrlEncoded = buf => toBase64(buf).replaceAll('+', '-').replaceAll('/', '_').replaceAll('=', '')\n\n/**\n * @param {string} base64\n */\nconst fromBase64UrlEncoded = base64 => fromBase64(base64.replaceAll('-', '+').replaceAll('_', '/'))\n\n/**\n * Base64 is always a more efficient choice. This exists for utility purposes only.\n *\n * @param {Uint8Array} buf\n */\nconst toHexString = buf => _array_js__WEBPACK_IMPORTED_MODULE_2__.map(buf, b => b.toString(16).padStart(2, '0')).join('')\n\n/**\n * Note: This function expects that the hex doesn't start with 0x..\n *\n * @param {string} hex\n */\nconst fromHexString = hex => {\n  const hlen = hex.length\n  const buf = new Uint8Array(_math_js__WEBPACK_IMPORTED_MODULE_3__.ceil(hlen / 2))\n  for (let i = 0; i < hlen; i += 2) {\n    buf[buf.length - i / 2 - 1] = Number.parseInt(hex.slice(hlen - i - 2, hlen - i), 16)\n  }\n  return buf\n}\n\n/**\n * Copy the content of an Uint8Array view to a new ArrayBuffer.\n *\n * @param {Uint8Array} uint8Array\n * @return {Uint8Array}\n */\nconst copyUint8Array = uint8Array => {\n  const newBuf = createUint8ArrayFromLen(uint8Array.byteLength)\n  newBuf.set(uint8Array)\n  return newBuf\n}\n\n/**\n * Encode anything as a UInt8Array. It's a pun on typescripts's `any` type.\n * See encoding.writeAny for more information.\n *\n * @param {any} data\n * @return {Uint8Array}\n */\nconst encodeAny = data =>\n  _encoding_js__WEBPACK_IMPORTED_MODULE_4__.encode(encoder => _encoding_js__WEBPACK_IMPORTED_MODULE_4__.writeAny(encoder, data))\n\n/**\n * Decode an any-encoded value.\n *\n * @param {Uint8Array} buf\n * @return {any}\n */\nconst decodeAny = buf => _decoding_js__WEBPACK_IMPORTED_MODULE_5__.readAny(_decoding_js__WEBPACK_IMPORTED_MODULE_5__.createDecoder(buf))\n\n/**\n * Shift Byte Array {N} bits to the left. Does not expand byte array.\n *\n * @param {Uint8Array} bs\n * @param {number} N should be in the range of [0-7]\n */\nconst shiftNBitsLeft = (bs, N) => {\n  if (N === 0) return bs\n  bs = new Uint8Array(bs)\n  bs[0] <<= N\n  for (let i = 1; i < bs.length; i++) {\n    bs[i - 1] |= bs[i] >>> (8 - N)\n    bs[i] <<= N\n  }\n  return bs\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/buffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/conditions.js":
/*!*****************************************!*\
  !*** ./node_modules/lib0/conditions.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   undefinedToNull: () => (/* binding */ undefinedToNull)\n/* harmony export */ });\n/**\n * Often used conditions.\n *\n * @module conditions\n */\n\n/**\n * @template T\n * @param {T|null|undefined} v\n * @return {T|null}\n */\n/* c8 ignore next */\nconst undefinedToNull = v => v === undefined ? null : v\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9jb25kaXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxXQUFXLGtCQUFrQjtBQUM3QixZQUFZO0FBQ1o7QUFDQTtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9saWIwL2NvbmRpdGlvbnMuanM/M2ZkZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE9mdGVuIHVzZWQgY29uZGl0aW9ucy5cbiAqXG4gKiBAbW9kdWxlIGNvbmRpdGlvbnNcbiAqL1xuXG4vKipcbiAqIEB0ZW1wbGF0ZSBUXG4gKiBAcGFyYW0ge1R8bnVsbHx1bmRlZmluZWR9IHZcbiAqIEByZXR1cm4ge1R8bnVsbH1cbiAqL1xuLyogYzggaWdub3JlIG5leHQgKi9cbmV4cG9ydCBjb25zdCB1bmRlZmluZWRUb051bGwgPSB2ID0+IHYgPT09IHVuZGVmaW5lZCA/IG51bGwgOiB2XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/conditions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/decoding.js":
/*!***************************************!*\
  !*** ./node_modules/lib0/decoding.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Decoder: () => (/* binding */ Decoder),\n/* harmony export */   IncUintOptRleDecoder: () => (/* binding */ IncUintOptRleDecoder),\n/* harmony export */   IntDiffDecoder: () => (/* binding */ IntDiffDecoder),\n/* harmony export */   IntDiffOptRleDecoder: () => (/* binding */ IntDiffOptRleDecoder),\n/* harmony export */   RleDecoder: () => (/* binding */ RleDecoder),\n/* harmony export */   RleIntDiffDecoder: () => (/* binding */ RleIntDiffDecoder),\n/* harmony export */   StringDecoder: () => (/* binding */ StringDecoder),\n/* harmony export */   UintOptRleDecoder: () => (/* binding */ UintOptRleDecoder),\n/* harmony export */   _readVarStringNative: () => (/* binding */ _readVarStringNative),\n/* harmony export */   _readVarStringPolyfill: () => (/* binding */ _readVarStringPolyfill),\n/* harmony export */   clone: () => (/* binding */ clone),\n/* harmony export */   createDecoder: () => (/* binding */ createDecoder),\n/* harmony export */   hasContent: () => (/* binding */ hasContent),\n/* harmony export */   peekUint16: () => (/* binding */ peekUint16),\n/* harmony export */   peekUint32: () => (/* binding */ peekUint32),\n/* harmony export */   peekUint8: () => (/* binding */ peekUint8),\n/* harmony export */   peekVarInt: () => (/* binding */ peekVarInt),\n/* harmony export */   peekVarString: () => (/* binding */ peekVarString),\n/* harmony export */   peekVarUint: () => (/* binding */ peekVarUint),\n/* harmony export */   readAny: () => (/* binding */ readAny),\n/* harmony export */   readBigInt64: () => (/* binding */ readBigInt64),\n/* harmony export */   readBigUint64: () => (/* binding */ readBigUint64),\n/* harmony export */   readFloat32: () => (/* binding */ readFloat32),\n/* harmony export */   readFloat64: () => (/* binding */ readFloat64),\n/* harmony export */   readFromDataView: () => (/* binding */ readFromDataView),\n/* harmony export */   readTailAsUint8Array: () => (/* binding */ readTailAsUint8Array),\n/* harmony export */   readTerminatedString: () => (/* binding */ readTerminatedString),\n/* harmony export */   readTerminatedUint8Array: () => (/* binding */ readTerminatedUint8Array),\n/* harmony export */   readUint16: () => (/* binding */ readUint16),\n/* harmony export */   readUint32: () => (/* binding */ readUint32),\n/* harmony export */   readUint32BigEndian: () => (/* binding */ readUint32BigEndian),\n/* harmony export */   readUint8: () => (/* binding */ readUint8),\n/* harmony export */   readUint8Array: () => (/* binding */ readUint8Array),\n/* harmony export */   readVarInt: () => (/* binding */ readVarInt),\n/* harmony export */   readVarString: () => (/* binding */ readVarString),\n/* harmony export */   readVarUint: () => (/* binding */ readVarUint),\n/* harmony export */   readVarUint8Array: () => (/* binding */ readVarUint8Array),\n/* harmony export */   skip8: () => (/* binding */ skip8)\n/* harmony export */ });\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/./node_modules/lib0/binary.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/lib0/number.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./string.js */ \"(ssr)/./node_modules/lib0/string.js\");\n/* harmony import */ var _error_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error.js */ \"(ssr)/./node_modules/lib0/error.js\");\n/* harmony import */ var _encoding_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./encoding.js */ \"(ssr)/./node_modules/lib0/encoding.js\");\n/**\n * Efficient schema-less binary decoding with support for variable length encoding.\n *\n * Use [lib0/decoding] with [lib0/encoding]. Every encoding function has a corresponding decoding function.\n *\n * Encodes numbers in little-endian order (least to most significant byte order)\n * and is compatible with Golang's binary encoding (https://golang.org/pkg/encoding/binary/)\n * which is also used in Protocol Buffers.\n *\n * ```js\n * // encoding step\n * const encoder = encoding.createEncoder()\n * encoding.writeVarUint(encoder, 256)\n * encoding.writeVarString(encoder, 'Hello world!')\n * const buf = encoding.toUint8Array(encoder)\n * ```\n *\n * ```js\n * // decoding step\n * const decoder = decoding.createDecoder(buf)\n * decoding.readVarUint(decoder) // => 256\n * decoding.readVarString(decoder) // => 'Hello world!'\n * decoding.hasContent(decoder) // => false - all data is read\n * ```\n *\n * @module decoding\n */\n\n\n\n\n\n\n\n\nconst errorUnexpectedEndOfArray = _error_js__WEBPACK_IMPORTED_MODULE_0__.create('Unexpected end of array')\nconst errorIntegerOutOfRange = _error_js__WEBPACK_IMPORTED_MODULE_0__.create('Integer out of Range')\n\n/**\n * A Decoder handles the decoding of an Uint8Array.\n */\nclass Decoder {\n  /**\n   * @param {Uint8Array} uint8Array Binary data to decode\n   */\n  constructor (uint8Array) {\n    /**\n     * Decoding target.\n     *\n     * @type {Uint8Array}\n     */\n    this.arr = uint8Array\n    /**\n     * Current decoding position.\n     *\n     * @type {number}\n     */\n    this.pos = 0\n  }\n}\n\n/**\n * @function\n * @param {Uint8Array} uint8Array\n * @return {Decoder}\n */\nconst createDecoder = uint8Array => new Decoder(uint8Array)\n\n/**\n * @function\n * @param {Decoder} decoder\n * @return {boolean}\n */\nconst hasContent = decoder => decoder.pos !== decoder.arr.length\n\n/**\n * Clone a decoder instance.\n * Optionally set a new position parameter.\n *\n * @function\n * @param {Decoder} decoder The decoder instance\n * @param {number} [newPos] Defaults to current position\n * @return {Decoder} A clone of `decoder`\n */\nconst clone = (decoder, newPos = decoder.pos) => {\n  const _decoder = createDecoder(decoder.arr)\n  _decoder.pos = newPos\n  return _decoder\n}\n\n/**\n * Create an Uint8Array view of the next `len` bytes and advance the position by `len`.\n *\n * Important: The Uint8Array still points to the underlying ArrayBuffer. Make sure to discard the result as soon as possible to prevent any memory leaks.\n *            Use `buffer.copyUint8Array` to copy the result into a new Uint8Array.\n *\n * @function\n * @param {Decoder} decoder The decoder instance\n * @param {number} len The length of bytes to read\n * @return {Uint8Array}\n */\nconst readUint8Array = (decoder, len) => {\n  const view = new Uint8Array(decoder.arr.buffer, decoder.pos + decoder.arr.byteOffset, len)\n  decoder.pos += len\n  return view\n}\n\n/**\n * Read variable length Uint8Array.\n *\n * Important: The Uint8Array still points to the underlying ArrayBuffer. Make sure to discard the result as soon as possible to prevent any memory leaks.\n *            Use `buffer.copyUint8Array` to copy the result into a new Uint8Array.\n *\n * @function\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nconst readVarUint8Array = decoder => readUint8Array(decoder, readVarUint(decoder))\n\n/**\n * Read the rest of the content as an ArrayBuffer\n * @function\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nconst readTailAsUint8Array = decoder => readUint8Array(decoder, decoder.arr.length - decoder.pos)\n\n/**\n * Skip one byte, jump to the next position.\n * @function\n * @param {Decoder} decoder The decoder instance\n * @return {number} The next position\n */\nconst skip8 = decoder => decoder.pos++\n\n/**\n * Read one byte as unsigned integer.\n * @function\n * @param {Decoder} decoder The decoder instance\n * @return {number} Unsigned 8-bit integer\n */\nconst readUint8 = decoder => decoder.arr[decoder.pos++]\n\n/**\n * Read 2 bytes as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst readUint16 = decoder => {\n  const uint =\n    decoder.arr[decoder.pos] +\n    (decoder.arr[decoder.pos + 1] << 8)\n  decoder.pos += 2\n  return uint\n}\n\n/**\n * Read 4 bytes as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst readUint32 = decoder => {\n  const uint =\n    (decoder.arr[decoder.pos] +\n    (decoder.arr[decoder.pos + 1] << 8) +\n    (decoder.arr[decoder.pos + 2] << 16) +\n    (decoder.arr[decoder.pos + 3] << 24)) >>> 0\n  decoder.pos += 4\n  return uint\n}\n\n/**\n * Read 4 bytes as unsigned integer in big endian order.\n * (most significant byte first)\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst readUint32BigEndian = decoder => {\n  const uint =\n    (decoder.arr[decoder.pos + 3] +\n    (decoder.arr[decoder.pos + 2] << 8) +\n    (decoder.arr[decoder.pos + 1] << 16) +\n    (decoder.arr[decoder.pos] << 24)) >>> 0\n  decoder.pos += 4\n  return uint\n}\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst peekUint8 = decoder => decoder.arr[decoder.pos]\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst peekUint16 = decoder =>\n  decoder.arr[decoder.pos] +\n  (decoder.arr[decoder.pos + 1] << 8)\n\n/**\n * Look ahead without incrementing the position\n * to the next byte and read it as unsigned integer.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.\n */\nconst peekUint32 = decoder => (\n  decoder.arr[decoder.pos] +\n  (decoder.arr[decoder.pos + 1] << 8) +\n  (decoder.arr[decoder.pos + 2] << 16) +\n  (decoder.arr[decoder.pos + 3] << 24)\n) >>> 0\n\n/**\n * Read unsigned integer (32bit) with variable length.\n * 1/8th of the storage is used as encoding overhead.\n *  * numbers < 2^7 is stored in one bytlength\n *  * numbers < 2^14 is stored in two bylength\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.length\n */\nconst readVarUint = decoder => {\n  let num = 0\n  let mult = 1\n  const len = decoder.arr.length\n  while (decoder.pos < len) {\n    const r = decoder.arr[decoder.pos++]\n    // num = num | ((r & binary.BITS7) << len)\n    num = num + (r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7) * mult // shift $r << (7*#iterations) and add it to num\n    mult *= 128 // next iteration, shift 7 \"more\" to the left\n    if (r < _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8) {\n      return num\n    }\n    /* c8 ignore start */\n    if (num > _number_js__WEBPACK_IMPORTED_MODULE_2__.MAX_SAFE_INTEGER) {\n      throw errorIntegerOutOfRange\n    }\n    /* c8 ignore stop */\n  }\n  throw errorUnexpectedEndOfArray\n}\n\n/**\n * Read signed integer (32bit) with variable length.\n * 1/8th of the storage is used as encoding overhead.\n *  * numbers < 2^7 is stored in one bytlength\n *  * numbers < 2^14 is stored in two bylength\n * @todo This should probably create the inverse ~num if number is negative - but this would be a breaking change.\n *\n * @function\n * @param {Decoder} decoder\n * @return {number} An unsigned integer.length\n */\nconst readVarInt = decoder => {\n  let r = decoder.arr[decoder.pos++]\n  let num = r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS6\n  let mult = 64\n  const sign = (r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT7) > 0 ? -1 : 1\n  if ((r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8) === 0) {\n    // don't continue reading\n    return sign * num\n  }\n  const len = decoder.arr.length\n  while (decoder.pos < len) {\n    r = decoder.arr[decoder.pos++]\n    // num = num | ((r & binary.BITS7) << len)\n    num = num + (r & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7) * mult\n    mult *= 128\n    if (r < _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8) {\n      return sign * num\n    }\n    /* c8 ignore start */\n    if (num > _number_js__WEBPACK_IMPORTED_MODULE_2__.MAX_SAFE_INTEGER) {\n      throw errorIntegerOutOfRange\n    }\n    /* c8 ignore stop */\n  }\n  throw errorUnexpectedEndOfArray\n}\n\n/**\n * Look ahead and read varUint without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {number}\n */\nconst peekVarUint = decoder => {\n  const pos = decoder.pos\n  const s = readVarUint(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * Look ahead and read varUint without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {number}\n */\nconst peekVarInt = decoder => {\n  const pos = decoder.pos\n  const s = readVarInt(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * We don't test this function anymore as we use native decoding/encoding by default now.\n * Better not modify this anymore..\n *\n * Transforming utf8 to a string is pretty expensive. The code performs 10x better\n * when String.fromCodePoint is fed with all characters as arguments.\n * But most environments have a maximum number of arguments per functions.\n * For effiency reasons we apply a maximum of 10000 characters at once.\n *\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String.\n */\n/* c8 ignore start */\nconst _readVarStringPolyfill = decoder => {\n  let remainingLen = readVarUint(decoder)\n  if (remainingLen === 0) {\n    return ''\n  } else {\n    let encodedString = String.fromCodePoint(readUint8(decoder)) // remember to decrease remainingLen\n    if (--remainingLen < 100) { // do not create a Uint8Array for small strings\n      while (remainingLen--) {\n        encodedString += String.fromCodePoint(readUint8(decoder))\n      }\n    } else {\n      while (remainingLen > 0) {\n        const nextLen = remainingLen < 10000 ? remainingLen : 10000\n        // this is dangerous, we create a fresh array view from the existing buffer\n        const bytes = decoder.arr.subarray(decoder.pos, decoder.pos + nextLen)\n        decoder.pos += nextLen\n        // Starting with ES5.1 we can supply a generic array-like object as arguments\n        encodedString += String.fromCodePoint.apply(null, /** @type {any} */ (bytes))\n        remainingLen -= nextLen\n      }\n    }\n    return decodeURIComponent(escape(encodedString))\n  }\n}\n/* c8 ignore stop */\n\n/**\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String\n */\nconst _readVarStringNative = decoder =>\n  /** @type any */ (_string_js__WEBPACK_IMPORTED_MODULE_3__.utf8TextDecoder).decode(readVarUint8Array(decoder))\n\n/**\n * Read string of variable length\n * * varUint is used to store the length of the string\n *\n * @function\n * @param {Decoder} decoder\n * @return {String} The read String\n *\n */\n/* c8 ignore next */\nconst readVarString = _string_js__WEBPACK_IMPORTED_MODULE_3__.utf8TextDecoder ? _readVarStringNative : _readVarStringPolyfill\n\n/**\n * @param {Decoder} decoder\n * @return {Uint8Array}\n */\nconst readTerminatedUint8Array = decoder => {\n  const encoder = _encoding_js__WEBPACK_IMPORTED_MODULE_4__.createEncoder()\n  let b\n  while (true) {\n    b = readUint8(decoder)\n    if (b === 0) {\n      return _encoding_js__WEBPACK_IMPORTED_MODULE_4__.toUint8Array(encoder)\n    }\n    if (b === 1) {\n      b = readUint8(decoder)\n    }\n    _encoding_js__WEBPACK_IMPORTED_MODULE_4__.write(encoder, b)\n  }\n}\n\n/**\n * @param {Decoder} decoder\n * @return {string}\n */\nconst readTerminatedString = decoder => _string_js__WEBPACK_IMPORTED_MODULE_3__.decodeUtf8(readTerminatedUint8Array(decoder))\n\n/**\n * Look ahead and read varString without incrementing position\n *\n * @function\n * @param {Decoder} decoder\n * @return {string}\n */\nconst peekVarString = decoder => {\n  const pos = decoder.pos\n  const s = readVarString(decoder)\n  decoder.pos = pos\n  return s\n}\n\n/**\n * @param {Decoder} decoder\n * @param {number} len\n * @return {DataView}\n */\nconst readFromDataView = (decoder, len) => {\n  const dv = new DataView(decoder.arr.buffer, decoder.arr.byteOffset + decoder.pos, len)\n  decoder.pos += len\n  return dv\n}\n\n/**\n * @param {Decoder} decoder\n */\nconst readFloat32 = decoder => readFromDataView(decoder, 4).getFloat32(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nconst readFloat64 = decoder => readFromDataView(decoder, 8).getFloat64(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nconst readBigInt64 = decoder => /** @type {any} */ (readFromDataView(decoder, 8)).getBigInt64(0, false)\n\n/**\n * @param {Decoder} decoder\n */\nconst readBigUint64 = decoder => /** @type {any} */ (readFromDataView(decoder, 8)).getBigUint64(0, false)\n\n/**\n * @type {Array<function(Decoder):any>}\n */\nconst readAnyLookupTable = [\n  decoder => undefined, // CASE 127: undefined\n  decoder => null, // CASE 126: null\n  readVarInt, // CASE 125: integer\n  readFloat32, // CASE 124: float32\n  readFloat64, // CASE 123: float64\n  readBigInt64, // CASE 122: bigint\n  decoder => false, // CASE 121: boolean (false)\n  decoder => true, // CASE 120: boolean (true)\n  readVarString, // CASE 119: string\n  decoder => { // CASE 118: object<string,any>\n    const len = readVarUint(decoder)\n    /**\n     * @type {Object<string,any>}\n     */\n    const obj = {}\n    for (let i = 0; i < len; i++) {\n      const key = readVarString(decoder)\n      obj[key] = readAny(decoder)\n    }\n    return obj\n  },\n  decoder => { // CASE 117: array<any>\n    const len = readVarUint(decoder)\n    const arr = []\n    for (let i = 0; i < len; i++) {\n      arr.push(readAny(decoder))\n    }\n    return arr\n  },\n  readVarUint8Array // CASE 116: Uint8Array\n]\n\n/**\n * @param {Decoder} decoder\n */\nconst readAny = decoder => readAnyLookupTable[127 - readUint8(decoder)](decoder)\n\n/**\n * T must not be null.\n *\n * @template T\n */\nclass RleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {function(Decoder):T} reader\n   */\n  constructor (uint8Array, reader) {\n    super(uint8Array)\n    /**\n     * The reader\n     */\n    this.reader = reader\n    /**\n     * Current state\n     * @type {T|null}\n     */\n    this.s = null\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = this.reader(this)\n      if (hasContent(this)) {\n        this.count = readVarUint(this) + 1 // see encoder implementation for the reason why this is incremented\n      } else {\n        this.count = -1 // read the current value forever\n      }\n    }\n    this.count--\n    return /** @type {T} */ (this.s)\n  }\n}\n\nclass IntDiffDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {number} start\n   */\n  constructor (uint8Array, start) {\n    super(uint8Array)\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    this.s += readVarInt(this)\n    return this.s\n  }\n}\n\nclass RleIntDiffDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   * @param {number} start\n   */\n  constructor (uint8Array, start) {\n    super(uint8Array)\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n    this.count = 0\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    if (this.count === 0) {\n      this.s += readVarInt(this)\n      if (hasContent(this)) {\n        this.count = readVarUint(this) + 1 // see encoder implementation for the reason why this is incremented\n      } else {\n        this.count = -1 // read the current value forever\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s)\n  }\n}\n\nclass UintOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = readVarInt(this)\n      // if the sign is negative, we read the count too, otherwise count is 1\n      const isNegative = _math_js__WEBPACK_IMPORTED_MODULE_5__.isNegativeZero(this.s)\n      this.count = 1\n      if (isNegative) {\n        this.s = -this.s\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s)\n  }\n}\n\nclass IncUintOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  read () {\n    if (this.count === 0) {\n      this.s = readVarInt(this)\n      // if the sign is negative, we read the count too, otherwise count is 1\n      const isNegative = _math_js__WEBPACK_IMPORTED_MODULE_5__.isNegativeZero(this.s)\n      this.count = 1\n      if (isNegative) {\n        this.s = -this.s\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.count--\n    return /** @type {number} */ (this.s++)\n  }\n}\n\nclass IntDiffOptRleDecoder extends Decoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    super(uint8Array)\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n    this.diff = 0\n  }\n\n  /**\n   * @return {number}\n   */\n  read () {\n    if (this.count === 0) {\n      const diff = readVarInt(this)\n      // if the first bit is set, we read more data\n      const hasCount = diff & 1\n      this.diff = _math_js__WEBPACK_IMPORTED_MODULE_5__.floor(diff / 2) // shift >> 1\n      this.count = 1\n      if (hasCount) {\n        this.count = readVarUint(this) + 2\n      }\n    }\n    this.s += this.diff\n    this.count--\n    return this.s\n  }\n}\n\nclass StringDecoder {\n  /**\n   * @param {Uint8Array} uint8Array\n   */\n  constructor (uint8Array) {\n    this.decoder = new UintOptRleDecoder(uint8Array)\n    this.str = readVarString(this.decoder)\n    /**\n     * @type {number}\n     */\n    this.spos = 0\n  }\n\n  /**\n   * @return {string}\n   */\n  read () {\n    const end = this.spos + this.decoder.read()\n    const res = this.str.slice(this.spos, end)\n    this.spos = end\n    return res\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9kZWNvZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFcUM7QUFDSjtBQUNJO0FBQ0E7QUFDRjtBQUNNOztBQUV6QyxrQ0FBa0MsNkNBQVk7QUFDOUMsK0JBQStCLDZDQUFZOztBQUUzQztBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsYUFBYSxZQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxZQUFZO0FBQ3ZCLFlBQVk7QUFDWjtBQUNPOztBQUVQO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWTtBQUNaO0FBQ087O0FBRVA7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkIsWUFBWSxTQUFTO0FBQ3JCO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZO0FBQ1o7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWTtBQUNaO0FBQ087O0FBRVA7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFlBQVksUUFBUTtBQUNwQjtBQUNPOztBQUVQO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZLFFBQVE7QUFDcEI7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZLFFBQVE7QUFDcEI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZLFFBQVE7QUFDcEI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZLFFBQVE7QUFDcEI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZLFFBQVE7QUFDcEI7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFlBQVksUUFBUTtBQUNwQjtBQUNPO0FBQ1A7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFlBQVksUUFBUTtBQUNwQjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZLFFBQVE7QUFDcEI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQiw2Q0FBWTtBQUNqQztBQUNBLFlBQVksNENBQVc7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsY0FBYyx3REFBdUI7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWSxRQUFRO0FBQ3BCO0FBQ087QUFDUDtBQUNBLGdCQUFnQiw2Q0FBWTtBQUM1QjtBQUNBLG9CQUFvQiw0Q0FBVztBQUMvQixXQUFXLDRDQUFXO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDZDQUFZO0FBQ2pDO0FBQ0EsWUFBWSw0Q0FBVztBQUN2QjtBQUNBO0FBQ0E7QUFDQSxjQUFjLHdEQUF1QjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWTtBQUNaO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFlBQVk7QUFDWjtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZLFFBQVE7QUFDcEI7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0EsZ0NBQWdDO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxRUFBcUUsS0FBSztBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFlBQVksUUFBUTtBQUNwQjtBQUNPO0FBQ1Asb0JBQW9CLHVEQUFzQjs7QUFFMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixZQUFZLFFBQVE7QUFDcEI7QUFDQTtBQUNBO0FBQ08sc0JBQXNCLHVEQUFzQjs7QUFFbkQ7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWTtBQUNaO0FBQ087QUFDUCxrQkFBa0IsdURBQXNCO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxzREFBcUI7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLCtDQUFjO0FBQ2xCO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWTtBQUNaO0FBQ08sd0NBQXdDLGtEQUFpQjs7QUFFaEU7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWTtBQUNaO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCO0FBQ087O0FBRVA7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDTzs7QUFFUDtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNPLDJDQUEyQyxLQUFLOztBQUV2RDtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNPLDRDQUE0QyxLQUFLOztBQUV4RDtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBLG9CQUFvQixTQUFTO0FBQzdCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILGVBQWU7QUFDZjtBQUNBO0FBQ0Esb0JBQW9CLFNBQVM7QUFDN0I7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLGFBQWEsWUFBWTtBQUN6QixhQUFhLHFCQUFxQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixHQUFHO0FBQ3pCO0FBQ0E7O0FBRU87QUFDUDtBQUNBLGFBQWEsWUFBWTtBQUN6QixhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVPO0FBQ1A7QUFDQSxhQUFhLFlBQVk7QUFDekIsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLFFBQVE7QUFDOUI7QUFDQTs7QUFFTztBQUNQO0FBQ0EsYUFBYSxZQUFZO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLG9EQUFtQjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixRQUFRO0FBQzlCO0FBQ0E7O0FBRU87QUFDUDtBQUNBLGFBQWEsWUFBWTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixvREFBbUI7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsUUFBUTtBQUM5QjtBQUNBOztBQUVPO0FBQ1A7QUFDQSxhQUFhLFlBQVk7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsMkNBQVU7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87QUFDUDtBQUNBLGFBQWEsWUFBWTtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9saWIwL2RlY29kaW5nLmpzP2EyNTAiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFZmZpY2llbnQgc2NoZW1hLWxlc3MgYmluYXJ5IGRlY29kaW5nIHdpdGggc3VwcG9ydCBmb3IgdmFyaWFibGUgbGVuZ3RoIGVuY29kaW5nLlxuICpcbiAqIFVzZSBbbGliMC9kZWNvZGluZ10gd2l0aCBbbGliMC9lbmNvZGluZ10uIEV2ZXJ5IGVuY29kaW5nIGZ1bmN0aW9uIGhhcyBhIGNvcnJlc3BvbmRpbmcgZGVjb2RpbmcgZnVuY3Rpb24uXG4gKlxuICogRW5jb2RlcyBudW1iZXJzIGluIGxpdHRsZS1lbmRpYW4gb3JkZXIgKGxlYXN0IHRvIG1vc3Qgc2lnbmlmaWNhbnQgYnl0ZSBvcmRlcilcbiAqIGFuZCBpcyBjb21wYXRpYmxlIHdpdGggR29sYW5nJ3MgYmluYXJ5IGVuY29kaW5nIChodHRwczovL2dvbGFuZy5vcmcvcGtnL2VuY29kaW5nL2JpbmFyeS8pXG4gKiB3aGljaCBpcyBhbHNvIHVzZWQgaW4gUHJvdG9jb2wgQnVmZmVycy5cbiAqXG4gKiBgYGBqc1xuICogLy8gZW5jb2Rpbmcgc3RlcFxuICogY29uc3QgZW5jb2RlciA9IGVuY29kaW5nLmNyZWF0ZUVuY29kZXIoKVxuICogZW5jb2Rpbmcud3JpdGVWYXJVaW50KGVuY29kZXIsIDI1NilcbiAqIGVuY29kaW5nLndyaXRlVmFyU3RyaW5nKGVuY29kZXIsICdIZWxsbyB3b3JsZCEnKVxuICogY29uc3QgYnVmID0gZW5jb2RpbmcudG9VaW50OEFycmF5KGVuY29kZXIpXG4gKiBgYGBcbiAqXG4gKiBgYGBqc1xuICogLy8gZGVjb2Rpbmcgc3RlcFxuICogY29uc3QgZGVjb2RlciA9IGRlY29kaW5nLmNyZWF0ZURlY29kZXIoYnVmKVxuICogZGVjb2RpbmcucmVhZFZhclVpbnQoZGVjb2RlcikgLy8gPT4gMjU2XG4gKiBkZWNvZGluZy5yZWFkVmFyU3RyaW5nKGRlY29kZXIpIC8vID0+ICdIZWxsbyB3b3JsZCEnXG4gKiBkZWNvZGluZy5oYXNDb250ZW50KGRlY29kZXIpIC8vID0+IGZhbHNlIC0gYWxsIGRhdGEgaXMgcmVhZFxuICogYGBgXG4gKlxuICogQG1vZHVsZSBkZWNvZGluZ1xuICovXG5cbmltcG9ydCAqIGFzIGJpbmFyeSBmcm9tICcuL2JpbmFyeS5qcydcbmltcG9ydCAqIGFzIG1hdGggZnJvbSAnLi9tYXRoLmpzJ1xuaW1wb3J0ICogYXMgbnVtYmVyIGZyb20gJy4vbnVtYmVyLmpzJ1xuaW1wb3J0ICogYXMgc3RyaW5nIGZyb20gJy4vc3RyaW5nLmpzJ1xuaW1wb3J0ICogYXMgZXJyb3IgZnJvbSAnLi9lcnJvci5qcydcbmltcG9ydCAqIGFzIGVuY29kaW5nIGZyb20gJy4vZW5jb2RpbmcuanMnXG5cbmNvbnN0IGVycm9yVW5leHBlY3RlZEVuZE9mQXJyYXkgPSBlcnJvci5jcmVhdGUoJ1VuZXhwZWN0ZWQgZW5kIG9mIGFycmF5JylcbmNvbnN0IGVycm9ySW50ZWdlck91dE9mUmFuZ2UgPSBlcnJvci5jcmVhdGUoJ0ludGVnZXIgb3V0IG9mIFJhbmdlJylcblxuLyoqXG4gKiBBIERlY29kZXIgaGFuZGxlcyB0aGUgZGVjb2Rpbmcgb2YgYW4gVWludDhBcnJheS5cbiAqL1xuZXhwb3J0IGNsYXNzIERlY29kZXIge1xuICAvKipcbiAgICogQHBhcmFtIHtVaW50OEFycmF5fSB1aW50OEFycmF5IEJpbmFyeSBkYXRhIHRvIGRlY29kZVxuICAgKi9cbiAgY29uc3RydWN0b3IgKHVpbnQ4QXJyYXkpIHtcbiAgICAvKipcbiAgICAgKiBEZWNvZGluZyB0YXJnZXQuXG4gICAgICpcbiAgICAgKiBAdHlwZSB7VWludDhBcnJheX1cbiAgICAgKi9cbiAgICB0aGlzLmFyciA9IHVpbnQ4QXJyYXlcbiAgICAvKipcbiAgICAgKiBDdXJyZW50IGRlY29kaW5nIHBvc2l0aW9uLlxuICAgICAqXG4gICAgICogQHR5cGUge251bWJlcn1cbiAgICAgKi9cbiAgICB0aGlzLnBvcyA9IDBcbiAgfVxufVxuXG4vKipcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtVaW50OEFycmF5fSB1aW50OEFycmF5XG4gKiBAcmV0dXJuIHtEZWNvZGVyfVxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlRGVjb2RlciA9IHVpbnQ4QXJyYXkgPT4gbmV3IERlY29kZXIodWludDhBcnJheSlcblxuLyoqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICogQHJldHVybiB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGNvbnN0IGhhc0NvbnRlbnQgPSBkZWNvZGVyID0+IGRlY29kZXIucG9zICE9PSBkZWNvZGVyLmFyci5sZW5ndGhcblxuLyoqXG4gKiBDbG9uZSBhIGRlY29kZXIgaW5zdGFuY2UuXG4gKiBPcHRpb25hbGx5IHNldCBhIG5ldyBwb3NpdGlvbiBwYXJhbWV0ZXIuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0RlY29kZXJ9IGRlY29kZXIgVGhlIGRlY29kZXIgaW5zdGFuY2VcbiAqIEBwYXJhbSB7bnVtYmVyfSBbbmV3UG9zXSBEZWZhdWx0cyB0byBjdXJyZW50IHBvc2l0aW9uXG4gKiBAcmV0dXJuIHtEZWNvZGVyfSBBIGNsb25lIG9mIGBkZWNvZGVyYFxuICovXG5leHBvcnQgY29uc3QgY2xvbmUgPSAoZGVjb2RlciwgbmV3UG9zID0gZGVjb2Rlci5wb3MpID0+IHtcbiAgY29uc3QgX2RlY29kZXIgPSBjcmVhdGVEZWNvZGVyKGRlY29kZXIuYXJyKVxuICBfZGVjb2Rlci5wb3MgPSBuZXdQb3NcbiAgcmV0dXJuIF9kZWNvZGVyXG59XG5cbi8qKlxuICogQ3JlYXRlIGFuIFVpbnQ4QXJyYXkgdmlldyBvZiB0aGUgbmV4dCBgbGVuYCBieXRlcyBhbmQgYWR2YW5jZSB0aGUgcG9zaXRpb24gYnkgYGxlbmAuXG4gKlxuICogSW1wb3J0YW50OiBUaGUgVWludDhBcnJheSBzdGlsbCBwb2ludHMgdG8gdGhlIHVuZGVybHlpbmcgQXJyYXlCdWZmZXIuIE1ha2Ugc3VyZSB0byBkaXNjYXJkIHRoZSByZXN1bHQgYXMgc29vbiBhcyBwb3NzaWJsZSB0byBwcmV2ZW50IGFueSBtZW1vcnkgbGVha3MuXG4gKiAgICAgICAgICAgIFVzZSBgYnVmZmVyLmNvcHlVaW50OEFycmF5YCB0byBjb3B5IHRoZSByZXN1bHQgaW50byBhIG5ldyBVaW50OEFycmF5LlxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyIFRoZSBkZWNvZGVyIGluc3RhbmNlXG4gKiBAcGFyYW0ge251bWJlcn0gbGVuIFRoZSBsZW5ndGggb2YgYnl0ZXMgdG8gcmVhZFxuICogQHJldHVybiB7VWludDhBcnJheX1cbiAqL1xuZXhwb3J0IGNvbnN0IHJlYWRVaW50OEFycmF5ID0gKGRlY29kZXIsIGxlbikgPT4ge1xuICBjb25zdCB2aWV3ID0gbmV3IFVpbnQ4QXJyYXkoZGVjb2Rlci5hcnIuYnVmZmVyLCBkZWNvZGVyLnBvcyArIGRlY29kZXIuYXJyLmJ5dGVPZmZzZXQsIGxlbilcbiAgZGVjb2Rlci5wb3MgKz0gbGVuXG4gIHJldHVybiB2aWV3XG59XG5cbi8qKlxuICogUmVhZCB2YXJpYWJsZSBsZW5ndGggVWludDhBcnJheS5cbiAqXG4gKiBJbXBvcnRhbnQ6IFRoZSBVaW50OEFycmF5IHN0aWxsIHBvaW50cyB0byB0aGUgdW5kZXJseWluZyBBcnJheUJ1ZmZlci4gTWFrZSBzdXJlIHRvIGRpc2NhcmQgdGhlIHJlc3VsdCBhcyBzb29uIGFzIHBvc3NpYmxlIHRvIHByZXZlbnQgYW55IG1lbW9yeSBsZWFrcy5cbiAqICAgICAgICAgICAgVXNlIGBidWZmZXIuY29weVVpbnQ4QXJyYXlgIHRvIGNvcHkgdGhlIHJlc3VsdCBpbnRvIGEgbmV3IFVpbnQ4QXJyYXkuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0RlY29kZXJ9IGRlY29kZXJcbiAqIEByZXR1cm4ge1VpbnQ4QXJyYXl9XG4gKi9cbmV4cG9ydCBjb25zdCByZWFkVmFyVWludDhBcnJheSA9IGRlY29kZXIgPT4gcmVhZFVpbnQ4QXJyYXkoZGVjb2RlciwgcmVhZFZhclVpbnQoZGVjb2RlcikpXG5cbi8qKlxuICogUmVhZCB0aGUgcmVzdCBvZiB0aGUgY29udGVudCBhcyBhbiBBcnJheUJ1ZmZlclxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0RlY29kZXJ9IGRlY29kZXJcbiAqIEByZXR1cm4ge1VpbnQ4QXJyYXl9XG4gKi9cbmV4cG9ydCBjb25zdCByZWFkVGFpbEFzVWludDhBcnJheSA9IGRlY29kZXIgPT4gcmVhZFVpbnQ4QXJyYXkoZGVjb2RlciwgZGVjb2Rlci5hcnIubGVuZ3RoIC0gZGVjb2Rlci5wb3MpXG5cbi8qKlxuICogU2tpcCBvbmUgYnl0ZSwganVtcCB0byB0aGUgbmV4dCBwb3NpdGlvbi5cbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyIFRoZSBkZWNvZGVyIGluc3RhbmNlXG4gKiBAcmV0dXJuIHtudW1iZXJ9IFRoZSBuZXh0IHBvc2l0aW9uXG4gKi9cbmV4cG9ydCBjb25zdCBza2lwOCA9IGRlY29kZXIgPT4gZGVjb2Rlci5wb3MrK1xuXG4vKipcbiAqIFJlYWQgb25lIGJ5dGUgYXMgdW5zaWduZWQgaW50ZWdlci5cbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyIFRoZSBkZWNvZGVyIGluc3RhbmNlXG4gKiBAcmV0dXJuIHtudW1iZXJ9IFVuc2lnbmVkIDgtYml0IGludGVnZXJcbiAqL1xuZXhwb3J0IGNvbnN0IHJlYWRVaW50OCA9IGRlY29kZXIgPT4gZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3MrK11cblxuLyoqXG4gKiBSZWFkIDIgYnl0ZXMgYXMgdW5zaWduZWQgaW50ZWdlci5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICogQHJldHVybiB7bnVtYmVyfSBBbiB1bnNpZ25lZCBpbnRlZ2VyLlxuICovXG5leHBvcnQgY29uc3QgcmVhZFVpbnQxNiA9IGRlY29kZXIgPT4ge1xuICBjb25zdCB1aW50ID1cbiAgICBkZWNvZGVyLmFycltkZWNvZGVyLnBvc10gK1xuICAgIChkZWNvZGVyLmFycltkZWNvZGVyLnBvcyArIDFdIDw8IDgpXG4gIGRlY29kZXIucG9zICs9IDJcbiAgcmV0dXJuIHVpbnRcbn1cblxuLyoqXG4gKiBSZWFkIDQgYnl0ZXMgYXMgdW5zaWduZWQgaW50ZWdlci5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICogQHJldHVybiB7bnVtYmVyfSBBbiB1bnNpZ25lZCBpbnRlZ2VyLlxuICovXG5leHBvcnQgY29uc3QgcmVhZFVpbnQzMiA9IGRlY29kZXIgPT4ge1xuICBjb25zdCB1aW50ID1cbiAgICAoZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3NdICtcbiAgICAoZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3MgKyAxXSA8PCA4KSArXG4gICAgKGRlY29kZXIuYXJyW2RlY29kZXIucG9zICsgMl0gPDwgMTYpICtcbiAgICAoZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3MgKyAzXSA8PCAyNCkpID4+PiAwXG4gIGRlY29kZXIucG9zICs9IDRcbiAgcmV0dXJuIHVpbnRcbn1cblxuLyoqXG4gKiBSZWFkIDQgYnl0ZXMgYXMgdW5zaWduZWQgaW50ZWdlciBpbiBiaWcgZW5kaWFuIG9yZGVyLlxuICogKG1vc3Qgc2lnbmlmaWNhbnQgYnl0ZSBmaXJzdClcbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICogQHJldHVybiB7bnVtYmVyfSBBbiB1bnNpZ25lZCBpbnRlZ2VyLlxuICovXG5leHBvcnQgY29uc3QgcmVhZFVpbnQzMkJpZ0VuZGlhbiA9IGRlY29kZXIgPT4ge1xuICBjb25zdCB1aW50ID1cbiAgICAoZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3MgKyAzXSArXG4gICAgKGRlY29kZXIuYXJyW2RlY29kZXIucG9zICsgMl0gPDwgOCkgK1xuICAgIChkZWNvZGVyLmFycltkZWNvZGVyLnBvcyArIDFdIDw8IDE2KSArXG4gICAgKGRlY29kZXIuYXJyW2RlY29kZXIucG9zXSA8PCAyNCkpID4+PiAwXG4gIGRlY29kZXIucG9zICs9IDRcbiAgcmV0dXJuIHVpbnRcbn1cblxuLyoqXG4gKiBMb29rIGFoZWFkIHdpdGhvdXQgaW5jcmVtZW50aW5nIHRoZSBwb3NpdGlvblxuICogdG8gdGhlIG5leHQgYnl0ZSBhbmQgcmVhZCBpdCBhcyB1bnNpZ25lZCBpbnRlZ2VyLlxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyXG4gKiBAcmV0dXJuIHtudW1iZXJ9IEFuIHVuc2lnbmVkIGludGVnZXIuXG4gKi9cbmV4cG9ydCBjb25zdCBwZWVrVWludDggPSBkZWNvZGVyID0+IGRlY29kZXIuYXJyW2RlY29kZXIucG9zXVxuXG4vKipcbiAqIExvb2sgYWhlYWQgd2l0aG91dCBpbmNyZW1lbnRpbmcgdGhlIHBvc2l0aW9uXG4gKiB0byB0aGUgbmV4dCBieXRlIGFuZCByZWFkIGl0IGFzIHVuc2lnbmVkIGludGVnZXIuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0RlY29kZXJ9IGRlY29kZXJcbiAqIEByZXR1cm4ge251bWJlcn0gQW4gdW5zaWduZWQgaW50ZWdlci5cbiAqL1xuZXhwb3J0IGNvbnN0IHBlZWtVaW50MTYgPSBkZWNvZGVyID0+XG4gIGRlY29kZXIuYXJyW2RlY29kZXIucG9zXSArXG4gIChkZWNvZGVyLmFycltkZWNvZGVyLnBvcyArIDFdIDw8IDgpXG5cbi8qKlxuICogTG9vayBhaGVhZCB3aXRob3V0IGluY3JlbWVudGluZyB0aGUgcG9zaXRpb25cbiAqIHRvIHRoZSBuZXh0IGJ5dGUgYW5kIHJlYWQgaXQgYXMgdW5zaWduZWQgaW50ZWdlci5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICogQHJldHVybiB7bnVtYmVyfSBBbiB1bnNpZ25lZCBpbnRlZ2VyLlxuICovXG5leHBvcnQgY29uc3QgcGVla1VpbnQzMiA9IGRlY29kZXIgPT4gKFxuICBkZWNvZGVyLmFycltkZWNvZGVyLnBvc10gK1xuICAoZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3MgKyAxXSA8PCA4KSArXG4gIChkZWNvZGVyLmFycltkZWNvZGVyLnBvcyArIDJdIDw8IDE2KSArXG4gIChkZWNvZGVyLmFycltkZWNvZGVyLnBvcyArIDNdIDw8IDI0KVxuKSA+Pj4gMFxuXG4vKipcbiAqIFJlYWQgdW5zaWduZWQgaW50ZWdlciAoMzJiaXQpIHdpdGggdmFyaWFibGUgbGVuZ3RoLlxuICogMS84dGggb2YgdGhlIHN0b3JhZ2UgaXMgdXNlZCBhcyBlbmNvZGluZyBvdmVyaGVhZC5cbiAqICAqIG51bWJlcnMgPCAyXjcgaXMgc3RvcmVkIGluIG9uZSBieXRsZW5ndGhcbiAqICAqIG51bWJlcnMgPCAyXjE0IGlzIHN0b3JlZCBpbiB0d28gYnlsZW5ndGhcbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICogQHJldHVybiB7bnVtYmVyfSBBbiB1bnNpZ25lZCBpbnRlZ2VyLmxlbmd0aFxuICovXG5leHBvcnQgY29uc3QgcmVhZFZhclVpbnQgPSBkZWNvZGVyID0+IHtcbiAgbGV0IG51bSA9IDBcbiAgbGV0IG11bHQgPSAxXG4gIGNvbnN0IGxlbiA9IGRlY29kZXIuYXJyLmxlbmd0aFxuICB3aGlsZSAoZGVjb2Rlci5wb3MgPCBsZW4pIHtcbiAgICBjb25zdCByID0gZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3MrK11cbiAgICAvLyBudW0gPSBudW0gfCAoKHIgJiBiaW5hcnkuQklUUzcpIDw8IGxlbilcbiAgICBudW0gPSBudW0gKyAociAmIGJpbmFyeS5CSVRTNykgKiBtdWx0IC8vIHNoaWZ0ICRyIDw8ICg3KiNpdGVyYXRpb25zKSBhbmQgYWRkIGl0IHRvIG51bVxuICAgIG11bHQgKj0gMTI4IC8vIG5leHQgaXRlcmF0aW9uLCBzaGlmdCA3IFwibW9yZVwiIHRvIHRoZSBsZWZ0XG4gICAgaWYgKHIgPCBiaW5hcnkuQklUOCkge1xuICAgICAgcmV0dXJuIG51bVxuICAgIH1cbiAgICAvKiBjOCBpZ25vcmUgc3RhcnQgKi9cbiAgICBpZiAobnVtID4gbnVtYmVyLk1BWF9TQUZFX0lOVEVHRVIpIHtcbiAgICAgIHRocm93IGVycm9ySW50ZWdlck91dE9mUmFuZ2VcbiAgICB9XG4gICAgLyogYzggaWdub3JlIHN0b3AgKi9cbiAgfVxuICB0aHJvdyBlcnJvclVuZXhwZWN0ZWRFbmRPZkFycmF5XG59XG5cbi8qKlxuICogUmVhZCBzaWduZWQgaW50ZWdlciAoMzJiaXQpIHdpdGggdmFyaWFibGUgbGVuZ3RoLlxuICogMS84dGggb2YgdGhlIHN0b3JhZ2UgaXMgdXNlZCBhcyBlbmNvZGluZyBvdmVyaGVhZC5cbiAqICAqIG51bWJlcnMgPCAyXjcgaXMgc3RvcmVkIGluIG9uZSBieXRsZW5ndGhcbiAqICAqIG51bWJlcnMgPCAyXjE0IGlzIHN0b3JlZCBpbiB0d28gYnlsZW5ndGhcbiAqIEB0b2RvIFRoaXMgc2hvdWxkIHByb2JhYmx5IGNyZWF0ZSB0aGUgaW52ZXJzZSB+bnVtIGlmIG51bWJlciBpcyBuZWdhdGl2ZSAtIGJ1dCB0aGlzIHdvdWxkIGJlIGEgYnJlYWtpbmcgY2hhbmdlLlxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyXG4gKiBAcmV0dXJuIHtudW1iZXJ9IEFuIHVuc2lnbmVkIGludGVnZXIubGVuZ3RoXG4gKi9cbmV4cG9ydCBjb25zdCByZWFkVmFySW50ID0gZGVjb2RlciA9PiB7XG4gIGxldCByID0gZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3MrK11cbiAgbGV0IG51bSA9IHIgJiBiaW5hcnkuQklUUzZcbiAgbGV0IG11bHQgPSA2NFxuICBjb25zdCBzaWduID0gKHIgJiBiaW5hcnkuQklUNykgPiAwID8gLTEgOiAxXG4gIGlmICgociAmIGJpbmFyeS5CSVQ4KSA9PT0gMCkge1xuICAgIC8vIGRvbid0IGNvbnRpbnVlIHJlYWRpbmdcbiAgICByZXR1cm4gc2lnbiAqIG51bVxuICB9XG4gIGNvbnN0IGxlbiA9IGRlY29kZXIuYXJyLmxlbmd0aFxuICB3aGlsZSAoZGVjb2Rlci5wb3MgPCBsZW4pIHtcbiAgICByID0gZGVjb2Rlci5hcnJbZGVjb2Rlci5wb3MrK11cbiAgICAvLyBudW0gPSBudW0gfCAoKHIgJiBiaW5hcnkuQklUUzcpIDw8IGxlbilcbiAgICBudW0gPSBudW0gKyAociAmIGJpbmFyeS5CSVRTNykgKiBtdWx0XG4gICAgbXVsdCAqPSAxMjhcbiAgICBpZiAociA8IGJpbmFyeS5CSVQ4KSB7XG4gICAgICByZXR1cm4gc2lnbiAqIG51bVxuICAgIH1cbiAgICAvKiBjOCBpZ25vcmUgc3RhcnQgKi9cbiAgICBpZiAobnVtID4gbnVtYmVyLk1BWF9TQUZFX0lOVEVHRVIpIHtcbiAgICAgIHRocm93IGVycm9ySW50ZWdlck91dE9mUmFuZ2VcbiAgICB9XG4gICAgLyogYzggaWdub3JlIHN0b3AgKi9cbiAgfVxuICB0aHJvdyBlcnJvclVuZXhwZWN0ZWRFbmRPZkFycmF5XG59XG5cbi8qKlxuICogTG9vayBhaGVhZCBhbmQgcmVhZCB2YXJVaW50IHdpdGhvdXQgaW5jcmVtZW50aW5nIHBvc2l0aW9uXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0RlY29kZXJ9IGRlY29kZXJcbiAqIEByZXR1cm4ge251bWJlcn1cbiAqL1xuZXhwb3J0IGNvbnN0IHBlZWtWYXJVaW50ID0gZGVjb2RlciA9PiB7XG4gIGNvbnN0IHBvcyA9IGRlY29kZXIucG9zXG4gIGNvbnN0IHMgPSByZWFkVmFyVWludChkZWNvZGVyKVxuICBkZWNvZGVyLnBvcyA9IHBvc1xuICByZXR1cm4gc1xufVxuXG4vKipcbiAqIExvb2sgYWhlYWQgYW5kIHJlYWQgdmFyVWludCB3aXRob3V0IGluY3JlbWVudGluZyBwb3NpdGlvblxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyXG4gKiBAcmV0dXJuIHtudW1iZXJ9XG4gKi9cbmV4cG9ydCBjb25zdCBwZWVrVmFySW50ID0gZGVjb2RlciA9PiB7XG4gIGNvbnN0IHBvcyA9IGRlY29kZXIucG9zXG4gIGNvbnN0IHMgPSByZWFkVmFySW50KGRlY29kZXIpXG4gIGRlY29kZXIucG9zID0gcG9zXG4gIHJldHVybiBzXG59XG5cbi8qKlxuICogV2UgZG9uJ3QgdGVzdCB0aGlzIGZ1bmN0aW9uIGFueW1vcmUgYXMgd2UgdXNlIG5hdGl2ZSBkZWNvZGluZy9lbmNvZGluZyBieSBkZWZhdWx0IG5vdy5cbiAqIEJldHRlciBub3QgbW9kaWZ5IHRoaXMgYW55bW9yZS4uXG4gKlxuICogVHJhbnNmb3JtaW5nIHV0ZjggdG8gYSBzdHJpbmcgaXMgcHJldHR5IGV4cGVuc2l2ZS4gVGhlIGNvZGUgcGVyZm9ybXMgMTB4IGJldHRlclxuICogd2hlbiBTdHJpbmcuZnJvbUNvZGVQb2ludCBpcyBmZWQgd2l0aCBhbGwgY2hhcmFjdGVycyBhcyBhcmd1bWVudHMuXG4gKiBCdXQgbW9zdCBlbnZpcm9ubWVudHMgaGF2ZSBhIG1heGltdW0gbnVtYmVyIG9mIGFyZ3VtZW50cyBwZXIgZnVuY3Rpb25zLlxuICogRm9yIGVmZmllbmN5IHJlYXNvbnMgd2UgYXBwbHkgYSBtYXhpbXVtIG9mIDEwMDAwIGNoYXJhY3RlcnMgYXQgb25jZS5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICogQHJldHVybiB7U3RyaW5nfSBUaGUgcmVhZCBTdHJpbmcuXG4gKi9cbi8qIGM4IGlnbm9yZSBzdGFydCAqL1xuZXhwb3J0IGNvbnN0IF9yZWFkVmFyU3RyaW5nUG9seWZpbGwgPSBkZWNvZGVyID0+IHtcbiAgbGV0IHJlbWFpbmluZ0xlbiA9IHJlYWRWYXJVaW50KGRlY29kZXIpXG4gIGlmIChyZW1haW5pbmdMZW4gPT09IDApIHtcbiAgICByZXR1cm4gJydcbiAgfSBlbHNlIHtcbiAgICBsZXQgZW5jb2RlZFN0cmluZyA9IFN0cmluZy5mcm9tQ29kZVBvaW50KHJlYWRVaW50OChkZWNvZGVyKSkgLy8gcmVtZW1iZXIgdG8gZGVjcmVhc2UgcmVtYWluaW5nTGVuXG4gICAgaWYgKC0tcmVtYWluaW5nTGVuIDwgMTAwKSB7IC8vIGRvIG5vdCBjcmVhdGUgYSBVaW50OEFycmF5IGZvciBzbWFsbCBzdHJpbmdzXG4gICAgICB3aGlsZSAocmVtYWluaW5nTGVuLS0pIHtcbiAgICAgICAgZW5jb2RlZFN0cmluZyArPSBTdHJpbmcuZnJvbUNvZGVQb2ludChyZWFkVWludDgoZGVjb2RlcikpXG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHdoaWxlIChyZW1haW5pbmdMZW4gPiAwKSB7XG4gICAgICAgIGNvbnN0IG5leHRMZW4gPSByZW1haW5pbmdMZW4gPCAxMDAwMCA/IHJlbWFpbmluZ0xlbiA6IDEwMDAwXG4gICAgICAgIC8vIHRoaXMgaXMgZGFuZ2Vyb3VzLCB3ZSBjcmVhdGUgYSBmcmVzaCBhcnJheSB2aWV3IGZyb20gdGhlIGV4aXN0aW5nIGJ1ZmZlclxuICAgICAgICBjb25zdCBieXRlcyA9IGRlY29kZXIuYXJyLnN1YmFycmF5KGRlY29kZXIucG9zLCBkZWNvZGVyLnBvcyArIG5leHRMZW4pXG4gICAgICAgIGRlY29kZXIucG9zICs9IG5leHRMZW5cbiAgICAgICAgLy8gU3RhcnRpbmcgd2l0aCBFUzUuMSB3ZSBjYW4gc3VwcGx5IGEgZ2VuZXJpYyBhcnJheS1saWtlIG9iamVjdCBhcyBhcmd1bWVudHNcbiAgICAgICAgZW5jb2RlZFN0cmluZyArPSBTdHJpbmcuZnJvbUNvZGVQb2ludC5hcHBseShudWxsLCAvKiogQHR5cGUge2FueX0gKi8gKGJ5dGVzKSlcbiAgICAgICAgcmVtYWluaW5nTGVuIC09IG5leHRMZW5cbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGRlY29kZVVSSUNvbXBvbmVudChlc2NhcGUoZW5jb2RlZFN0cmluZykpXG4gIH1cbn1cbi8qIGM4IGlnbm9yZSBzdG9wICovXG5cbi8qKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0RlY29kZXJ9IGRlY29kZXJcbiAqIEByZXR1cm4ge1N0cmluZ30gVGhlIHJlYWQgU3RyaW5nXG4gKi9cbmV4cG9ydCBjb25zdCBfcmVhZFZhclN0cmluZ05hdGl2ZSA9IGRlY29kZXIgPT5cbiAgLyoqIEB0eXBlIGFueSAqLyAoc3RyaW5nLnV0ZjhUZXh0RGVjb2RlcikuZGVjb2RlKHJlYWRWYXJVaW50OEFycmF5KGRlY29kZXIpKVxuXG4vKipcbiAqIFJlYWQgc3RyaW5nIG9mIHZhcmlhYmxlIGxlbmd0aFxuICogKiB2YXJVaW50IGlzIHVzZWQgdG8gc3RvcmUgdGhlIGxlbmd0aCBvZiB0aGUgc3RyaW5nXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0RlY29kZXJ9IGRlY29kZXJcbiAqIEByZXR1cm4ge1N0cmluZ30gVGhlIHJlYWQgU3RyaW5nXG4gKlxuICovXG4vKiBjOCBpZ25vcmUgbmV4dCAqL1xuZXhwb3J0IGNvbnN0IHJlYWRWYXJTdHJpbmcgPSBzdHJpbmcudXRmOFRleHREZWNvZGVyID8gX3JlYWRWYXJTdHJpbmdOYXRpdmUgOiBfcmVhZFZhclN0cmluZ1BvbHlmaWxsXG5cbi8qKlxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyXG4gKiBAcmV0dXJuIHtVaW50OEFycmF5fVxuICovXG5leHBvcnQgY29uc3QgcmVhZFRlcm1pbmF0ZWRVaW50OEFycmF5ID0gZGVjb2RlciA9PiB7XG4gIGNvbnN0IGVuY29kZXIgPSBlbmNvZGluZy5jcmVhdGVFbmNvZGVyKClcbiAgbGV0IGJcbiAgd2hpbGUgKHRydWUpIHtcbiAgICBiID0gcmVhZFVpbnQ4KGRlY29kZXIpXG4gICAgaWYgKGIgPT09IDApIHtcbiAgICAgIHJldHVybiBlbmNvZGluZy50b1VpbnQ4QXJyYXkoZW5jb2RlcilcbiAgICB9XG4gICAgaWYgKGIgPT09IDEpIHtcbiAgICAgIGIgPSByZWFkVWludDgoZGVjb2RlcilcbiAgICB9XG4gICAgZW5jb2Rpbmcud3JpdGUoZW5jb2RlciwgYilcbiAgfVxufVxuXG4vKipcbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICogQHJldHVybiB7c3RyaW5nfVxuICovXG5leHBvcnQgY29uc3QgcmVhZFRlcm1pbmF0ZWRTdHJpbmcgPSBkZWNvZGVyID0+IHN0cmluZy5kZWNvZGVVdGY4KHJlYWRUZXJtaW5hdGVkVWludDhBcnJheShkZWNvZGVyKSlcblxuLyoqXG4gKiBMb29rIGFoZWFkIGFuZCByZWFkIHZhclN0cmluZyB3aXRob3V0IGluY3JlbWVudGluZyBwb3NpdGlvblxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyXG4gKiBAcmV0dXJuIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBjb25zdCBwZWVrVmFyU3RyaW5nID0gZGVjb2RlciA9PiB7XG4gIGNvbnN0IHBvcyA9IGRlY29kZXIucG9zXG4gIGNvbnN0IHMgPSByZWFkVmFyU3RyaW5nKGRlY29kZXIpXG4gIGRlY29kZXIucG9zID0gcG9zXG4gIHJldHVybiBzXG59XG5cbi8qKlxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyXG4gKiBAcGFyYW0ge251bWJlcn0gbGVuXG4gKiBAcmV0dXJuIHtEYXRhVmlld31cbiAqL1xuZXhwb3J0IGNvbnN0IHJlYWRGcm9tRGF0YVZpZXcgPSAoZGVjb2RlciwgbGVuKSA9PiB7XG4gIGNvbnN0IGR2ID0gbmV3IERhdGFWaWV3KGRlY29kZXIuYXJyLmJ1ZmZlciwgZGVjb2Rlci5hcnIuYnl0ZU9mZnNldCArIGRlY29kZXIucG9zLCBsZW4pXG4gIGRlY29kZXIucG9zICs9IGxlblxuICByZXR1cm4gZHZcbn1cblxuLyoqXG4gKiBAcGFyYW0ge0RlY29kZXJ9IGRlY29kZXJcbiAqL1xuZXhwb3J0IGNvbnN0IHJlYWRGbG9hdDMyID0gZGVjb2RlciA9PiByZWFkRnJvbURhdGFWaWV3KGRlY29kZXIsIDQpLmdldEZsb2F0MzIoMCwgZmFsc2UpXG5cbi8qKlxuICogQHBhcmFtIHtEZWNvZGVyfSBkZWNvZGVyXG4gKi9cbmV4cG9ydCBjb25zdCByZWFkRmxvYXQ2NCA9IGRlY29kZXIgPT4gcmVhZEZyb21EYXRhVmlldyhkZWNvZGVyLCA4KS5nZXRGbG9hdDY0KDAsIGZhbHNlKVxuXG4vKipcbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICovXG5leHBvcnQgY29uc3QgcmVhZEJpZ0ludDY0ID0gZGVjb2RlciA9PiAvKiogQHR5cGUge2FueX0gKi8gKHJlYWRGcm9tRGF0YVZpZXcoZGVjb2RlciwgOCkpLmdldEJpZ0ludDY0KDAsIGZhbHNlKVxuXG4vKipcbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICovXG5leHBvcnQgY29uc3QgcmVhZEJpZ1VpbnQ2NCA9IGRlY29kZXIgPT4gLyoqIEB0eXBlIHthbnl9ICovIChyZWFkRnJvbURhdGFWaWV3KGRlY29kZXIsIDgpKS5nZXRCaWdVaW50NjQoMCwgZmFsc2UpXG5cbi8qKlxuICogQHR5cGUge0FycmF5PGZ1bmN0aW9uKERlY29kZXIpOmFueT59XG4gKi9cbmNvbnN0IHJlYWRBbnlMb29rdXBUYWJsZSA9IFtcbiAgZGVjb2RlciA9PiB1bmRlZmluZWQsIC8vIENBU0UgMTI3OiB1bmRlZmluZWRcbiAgZGVjb2RlciA9PiBudWxsLCAvLyBDQVNFIDEyNjogbnVsbFxuICByZWFkVmFySW50LCAvLyBDQVNFIDEyNTogaW50ZWdlclxuICByZWFkRmxvYXQzMiwgLy8gQ0FTRSAxMjQ6IGZsb2F0MzJcbiAgcmVhZEZsb2F0NjQsIC8vIENBU0UgMTIzOiBmbG9hdDY0XG4gIHJlYWRCaWdJbnQ2NCwgLy8gQ0FTRSAxMjI6IGJpZ2ludFxuICBkZWNvZGVyID0+IGZhbHNlLCAvLyBDQVNFIDEyMTogYm9vbGVhbiAoZmFsc2UpXG4gIGRlY29kZXIgPT4gdHJ1ZSwgLy8gQ0FTRSAxMjA6IGJvb2xlYW4gKHRydWUpXG4gIHJlYWRWYXJTdHJpbmcsIC8vIENBU0UgMTE5OiBzdHJpbmdcbiAgZGVjb2RlciA9PiB7IC8vIENBU0UgMTE4OiBvYmplY3Q8c3RyaW5nLGFueT5cbiAgICBjb25zdCBsZW4gPSByZWFkVmFyVWludChkZWNvZGVyKVxuICAgIC8qKlxuICAgICAqIEB0eXBlIHtPYmplY3Q8c3RyaW5nLGFueT59XG4gICAgICovXG4gICAgY29uc3Qgb2JqID0ge31cbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IGxlbjsgaSsrKSB7XG4gICAgICBjb25zdCBrZXkgPSByZWFkVmFyU3RyaW5nKGRlY29kZXIpXG4gICAgICBvYmpba2V5XSA9IHJlYWRBbnkoZGVjb2RlcilcbiAgICB9XG4gICAgcmV0dXJuIG9ialxuICB9LFxuICBkZWNvZGVyID0+IHsgLy8gQ0FTRSAxMTc6IGFycmF5PGFueT5cbiAgICBjb25zdCBsZW4gPSByZWFkVmFyVWludChkZWNvZGVyKVxuICAgIGNvbnN0IGFyciA9IFtdXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW47IGkrKykge1xuICAgICAgYXJyLnB1c2gocmVhZEFueShkZWNvZGVyKSlcbiAgICB9XG4gICAgcmV0dXJuIGFyclxuICB9LFxuICByZWFkVmFyVWludDhBcnJheSAvLyBDQVNFIDExNjogVWludDhBcnJheVxuXVxuXG4vKipcbiAqIEBwYXJhbSB7RGVjb2Rlcn0gZGVjb2RlclxuICovXG5leHBvcnQgY29uc3QgcmVhZEFueSA9IGRlY29kZXIgPT4gcmVhZEFueUxvb2t1cFRhYmxlWzEyNyAtIHJlYWRVaW50OChkZWNvZGVyKV0oZGVjb2RlcilcblxuLyoqXG4gKiBUIG11c3Qgbm90IGJlIG51bGwuXG4gKlxuICogQHRlbXBsYXRlIFRcbiAqL1xuZXhwb3J0IGNsYXNzIFJsZURlY29kZXIgZXh0ZW5kcyBEZWNvZGVyIHtcbiAgLyoqXG4gICAqIEBwYXJhbSB7VWludDhBcnJheX0gdWludDhBcnJheVxuICAgKiBAcGFyYW0ge2Z1bmN0aW9uKERlY29kZXIpOlR9IHJlYWRlclxuICAgKi9cbiAgY29uc3RydWN0b3IgKHVpbnQ4QXJyYXksIHJlYWRlcikge1xuICAgIHN1cGVyKHVpbnQ4QXJyYXkpXG4gICAgLyoqXG4gICAgICogVGhlIHJlYWRlclxuICAgICAqL1xuICAgIHRoaXMucmVhZGVyID0gcmVhZGVyXG4gICAgLyoqXG4gICAgICogQ3VycmVudCBzdGF0ZVxuICAgICAqIEB0eXBlIHtUfG51bGx9XG4gICAgICovXG4gICAgdGhpcy5zID0gbnVsbFxuICAgIHRoaXMuY291bnQgPSAwXG4gIH1cblxuICByZWFkICgpIHtcbiAgICBpZiAodGhpcy5jb3VudCA9PT0gMCkge1xuICAgICAgdGhpcy5zID0gdGhpcy5yZWFkZXIodGhpcylcbiAgICAgIGlmIChoYXNDb250ZW50KHRoaXMpKSB7XG4gICAgICAgIHRoaXMuY291bnQgPSByZWFkVmFyVWludCh0aGlzKSArIDEgLy8gc2VlIGVuY29kZXIgaW1wbGVtZW50YXRpb24gZm9yIHRoZSByZWFzb24gd2h5IHRoaXMgaXMgaW5jcmVtZW50ZWRcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRoaXMuY291bnQgPSAtMSAvLyByZWFkIHRoZSBjdXJyZW50IHZhbHVlIGZvcmV2ZXJcbiAgICAgIH1cbiAgICB9XG4gICAgdGhpcy5jb3VudC0tXG4gICAgcmV0dXJuIC8qKiBAdHlwZSB7VH0gKi8gKHRoaXMucylcbiAgfVxufVxuXG5leHBvcnQgY2xhc3MgSW50RGlmZkRlY29kZXIgZXh0ZW5kcyBEZWNvZGVyIHtcbiAgLyoqXG4gICAqIEBwYXJhbSB7VWludDhBcnJheX0gdWludDhBcnJheVxuICAgKiBAcGFyYW0ge251bWJlcn0gc3RhcnRcbiAgICovXG4gIGNvbnN0cnVjdG9yICh1aW50OEFycmF5LCBzdGFydCkge1xuICAgIHN1cGVyKHVpbnQ4QXJyYXkpXG4gICAgLyoqXG4gICAgICogQ3VycmVudCBzdGF0ZVxuICAgICAqIEB0eXBlIHtudW1iZXJ9XG4gICAgICovXG4gICAgdGhpcy5zID0gc3RhcnRcbiAgfVxuXG4gIC8qKlxuICAgKiBAcmV0dXJuIHtudW1iZXJ9XG4gICAqL1xuICByZWFkICgpIHtcbiAgICB0aGlzLnMgKz0gcmVhZFZhckludCh0aGlzKVxuICAgIHJldHVybiB0aGlzLnNcbiAgfVxufVxuXG5leHBvcnQgY2xhc3MgUmxlSW50RGlmZkRlY29kZXIgZXh0ZW5kcyBEZWNvZGVyIHtcbiAgLyoqXG4gICAqIEBwYXJhbSB7VWludDhBcnJheX0gdWludDhBcnJheVxuICAgKiBAcGFyYW0ge251bWJlcn0gc3RhcnRcbiAgICovXG4gIGNvbnN0cnVjdG9yICh1aW50OEFycmF5LCBzdGFydCkge1xuICAgIHN1cGVyKHVpbnQ4QXJyYXkpXG4gICAgLyoqXG4gICAgICogQ3VycmVudCBzdGF0ZVxuICAgICAqIEB0eXBlIHtudW1iZXJ9XG4gICAgICovXG4gICAgdGhpcy5zID0gc3RhcnRcbiAgICB0aGlzLmNvdW50ID0gMFxuICB9XG5cbiAgLyoqXG4gICAqIEByZXR1cm4ge251bWJlcn1cbiAgICovXG4gIHJlYWQgKCkge1xuICAgIGlmICh0aGlzLmNvdW50ID09PSAwKSB7XG4gICAgICB0aGlzLnMgKz0gcmVhZFZhckludCh0aGlzKVxuICAgICAgaWYgKGhhc0NvbnRlbnQodGhpcykpIHtcbiAgICAgICAgdGhpcy5jb3VudCA9IHJlYWRWYXJVaW50KHRoaXMpICsgMSAvLyBzZWUgZW5jb2RlciBpbXBsZW1lbnRhdGlvbiBmb3IgdGhlIHJlYXNvbiB3aHkgdGhpcyBpcyBpbmNyZW1lbnRlZFxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhpcy5jb3VudCA9IC0xIC8vIHJlYWQgdGhlIGN1cnJlbnQgdmFsdWUgZm9yZXZlclxuICAgICAgfVxuICAgIH1cbiAgICB0aGlzLmNvdW50LS1cbiAgICByZXR1cm4gLyoqIEB0eXBlIHtudW1iZXJ9ICovICh0aGlzLnMpXG4gIH1cbn1cblxuZXhwb3J0IGNsYXNzIFVpbnRPcHRSbGVEZWNvZGVyIGV4dGVuZHMgRGVjb2RlciB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge1VpbnQ4QXJyYXl9IHVpbnQ4QXJyYXlcbiAgICovXG4gIGNvbnN0cnVjdG9yICh1aW50OEFycmF5KSB7XG4gICAgc3VwZXIodWludDhBcnJheSlcbiAgICAvKipcbiAgICAgKiBAdHlwZSB7bnVtYmVyfVxuICAgICAqL1xuICAgIHRoaXMucyA9IDBcbiAgICB0aGlzLmNvdW50ID0gMFxuICB9XG5cbiAgcmVhZCAoKSB7XG4gICAgaWYgKHRoaXMuY291bnQgPT09IDApIHtcbiAgICAgIHRoaXMucyA9IHJlYWRWYXJJbnQodGhpcylcbiAgICAgIC8vIGlmIHRoZSBzaWduIGlzIG5lZ2F0aXZlLCB3ZSByZWFkIHRoZSBjb3VudCB0b28sIG90aGVyd2lzZSBjb3VudCBpcyAxXG4gICAgICBjb25zdCBpc05lZ2F0aXZlID0gbWF0aC5pc05lZ2F0aXZlWmVybyh0aGlzLnMpXG4gICAgICB0aGlzLmNvdW50ID0gMVxuICAgICAgaWYgKGlzTmVnYXRpdmUpIHtcbiAgICAgICAgdGhpcy5zID0gLXRoaXMuc1xuICAgICAgICB0aGlzLmNvdW50ID0gcmVhZFZhclVpbnQodGhpcykgKyAyXG4gICAgICB9XG4gICAgfVxuICAgIHRoaXMuY291bnQtLVxuICAgIHJldHVybiAvKiogQHR5cGUge251bWJlcn0gKi8gKHRoaXMucylcbiAgfVxufVxuXG5leHBvcnQgY2xhc3MgSW5jVWludE9wdFJsZURlY29kZXIgZXh0ZW5kcyBEZWNvZGVyIHtcbiAgLyoqXG4gICAqIEBwYXJhbSB7VWludDhBcnJheX0gdWludDhBcnJheVxuICAgKi9cbiAgY29uc3RydWN0b3IgKHVpbnQ4QXJyYXkpIHtcbiAgICBzdXBlcih1aW50OEFycmF5KVxuICAgIC8qKlxuICAgICAqIEB0eXBlIHtudW1iZXJ9XG4gICAgICovXG4gICAgdGhpcy5zID0gMFxuICAgIHRoaXMuY291bnQgPSAwXG4gIH1cblxuICByZWFkICgpIHtcbiAgICBpZiAodGhpcy5jb3VudCA9PT0gMCkge1xuICAgICAgdGhpcy5zID0gcmVhZFZhckludCh0aGlzKVxuICAgICAgLy8gaWYgdGhlIHNpZ24gaXMgbmVnYXRpdmUsIHdlIHJlYWQgdGhlIGNvdW50IHRvbywgb3RoZXJ3aXNlIGNvdW50IGlzIDFcbiAgICAgIGNvbnN0IGlzTmVnYXRpdmUgPSBtYXRoLmlzTmVnYXRpdmVaZXJvKHRoaXMucylcbiAgICAgIHRoaXMuY291bnQgPSAxXG4gICAgICBpZiAoaXNOZWdhdGl2ZSkge1xuICAgICAgICB0aGlzLnMgPSAtdGhpcy5zXG4gICAgICAgIHRoaXMuY291bnQgPSByZWFkVmFyVWludCh0aGlzKSArIDJcbiAgICAgIH1cbiAgICB9XG4gICAgdGhpcy5jb3VudC0tXG4gICAgcmV0dXJuIC8qKiBAdHlwZSB7bnVtYmVyfSAqLyAodGhpcy5zKyspXG4gIH1cbn1cblxuZXhwb3J0IGNsYXNzIEludERpZmZPcHRSbGVEZWNvZGVyIGV4dGVuZHMgRGVjb2RlciB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge1VpbnQ4QXJyYXl9IHVpbnQ4QXJyYXlcbiAgICovXG4gIGNvbnN0cnVjdG9yICh1aW50OEFycmF5KSB7XG4gICAgc3VwZXIodWludDhBcnJheSlcbiAgICAvKipcbiAgICAgKiBAdHlwZSB7bnVtYmVyfVxuICAgICAqL1xuICAgIHRoaXMucyA9IDBcbiAgICB0aGlzLmNvdW50ID0gMFxuICAgIHRoaXMuZGlmZiA9IDBcbiAgfVxuXG4gIC8qKlxuICAgKiBAcmV0dXJuIHtudW1iZXJ9XG4gICAqL1xuICByZWFkICgpIHtcbiAgICBpZiAodGhpcy5jb3VudCA9PT0gMCkge1xuICAgICAgY29uc3QgZGlmZiA9IHJlYWRWYXJJbnQodGhpcylcbiAgICAgIC8vIGlmIHRoZSBmaXJzdCBiaXQgaXMgc2V0LCB3ZSByZWFkIG1vcmUgZGF0YVxuICAgICAgY29uc3QgaGFzQ291bnQgPSBkaWZmICYgMVxuICAgICAgdGhpcy5kaWZmID0gbWF0aC5mbG9vcihkaWZmIC8gMikgLy8gc2hpZnQgPj4gMVxuICAgICAgdGhpcy5jb3VudCA9IDFcbiAgICAgIGlmIChoYXNDb3VudCkge1xuICAgICAgICB0aGlzLmNvdW50ID0gcmVhZFZhclVpbnQodGhpcykgKyAyXG4gICAgICB9XG4gICAgfVxuICAgIHRoaXMucyArPSB0aGlzLmRpZmZcbiAgICB0aGlzLmNvdW50LS1cbiAgICByZXR1cm4gdGhpcy5zXG4gIH1cbn1cblxuZXhwb3J0IGNsYXNzIFN0cmluZ0RlY29kZXIge1xuICAvKipcbiAgICogQHBhcmFtIHtVaW50OEFycmF5fSB1aW50OEFycmF5XG4gICAqL1xuICBjb25zdHJ1Y3RvciAodWludDhBcnJheSkge1xuICAgIHRoaXMuZGVjb2RlciA9IG5ldyBVaW50T3B0UmxlRGVjb2Rlcih1aW50OEFycmF5KVxuICAgIHRoaXMuc3RyID0gcmVhZFZhclN0cmluZyh0aGlzLmRlY29kZXIpXG4gICAgLyoqXG4gICAgICogQHR5cGUge251bWJlcn1cbiAgICAgKi9cbiAgICB0aGlzLnNwb3MgPSAwXG4gIH1cblxuICAvKipcbiAgICogQHJldHVybiB7c3RyaW5nfVxuICAgKi9cbiAgcmVhZCAoKSB7XG4gICAgY29uc3QgZW5kID0gdGhpcy5zcG9zICsgdGhpcy5kZWNvZGVyLnJlYWQoKVxuICAgIGNvbnN0IHJlcyA9IHRoaXMuc3RyLnNsaWNlKHRoaXMuc3BvcywgZW5kKVxuICAgIHRoaXMuc3BvcyA9IGVuZFxuICAgIHJldHVybiByZXNcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/decoding.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/diff.js":
/*!***********************************!*\
  !*** ./node_modules/lib0/diff.js ***!
  \***********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   simpleDiff: () => (/* binding */ simpleDiff),\n/* harmony export */   simpleDiffArray: () => (/* binding */ simpleDiffArray),\n/* harmony export */   simpleDiffString: () => (/* binding */ simpleDiffString),\n/* harmony export */   simpleDiffStringWithCursor: () => (/* binding */ simpleDiffStringWithCursor)\n/* harmony export */ });\n/* harmony import */ var _function_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./function.js */ \"(ssr)/./node_modules/lib0/function.js\");\n/**\n * Efficient diffs.\n *\n * @module diff\n */\n\n\n\n/**\n * A SimpleDiff describes a change on a String.\n *\n * ```js\n * console.log(a) // the old value\n * console.log(b) // the updated value\n * // Apply changes of diff (pseudocode)\n * a.remove(diff.index, diff.remove) // Remove `diff.remove` characters\n * a.insert(diff.index, diff.insert) // Insert `diff.insert`\n * a === b // values match\n * ```\n *\n * @typedef {Object} SimpleDiff\n * @property {Number} index The index where changes were applied\n * @property {Number} remove The number of characters to delete starting\n *                                  at `index`.\n * @property {T} insert The new text to insert at `index` after applying\n *                           `delete`\n *\n * @template T\n */\n\nconst highSurrogateRegex = /[\\uD800-\\uDBFF]/\nconst lowSurrogateRegex = /[\\uDC00-\\uDFFF]/\n\n/**\n * Create a diff between two strings. This diff implementation is highly\n * efficient, but not very sophisticated.\n *\n * @function\n *\n * @param {string} a The old version of the string\n * @param {string} b The updated version of the string\n * @return {SimpleDiff<string>} The diff description.\n */\nconst simpleDiffString = (a, b) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  while (left < a.length && left < b.length && a[left] === b[left]) {\n    left++\n  }\n  // If the last same character is a high surrogate, we need to rollback to the previous character\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  while (right + left < a.length && right + left < b.length && a[a.length - right - 1] === b[b.length - right - 1]) {\n    right++\n  }\n  // If the last same character is a low surrogate, we need to rollback to the previous character\n  if (right > 0 && lowSurrogateRegex.test(a[a.length - right])) right--\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n\n/**\n * @todo Remove in favor of simpleDiffString\n * @deprecated\n */\nconst simpleDiff = simpleDiffString\n\n/**\n * Create a diff between two arrays. This diff implementation is highly\n * efficient, but not very sophisticated.\n *\n * Note: This is basically the same function as above. Another function was created so that the runtime\n * can better optimize these function calls.\n *\n * @function\n * @template T\n *\n * @param {Array<T>} a The old version of the array\n * @param {Array<T>} b The updated version of the array\n * @param {function(T, T):boolean} [compare]\n * @return {SimpleDiff<Array<T>>} The diff description.\n */\nconst simpleDiffArray = (a, b, compare = _function_js__WEBPACK_IMPORTED_MODULE_0__.equalityStrict) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  while (left < a.length && left < b.length && compare(a[left], b[left])) {\n    left++\n  }\n  while (right + left < a.length && right + left < b.length && compare(a[a.length - right - 1], b[b.length - right - 1])) {\n    right++\n  }\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n\n/**\n * Diff text and try to diff at the current cursor position.\n *\n * @param {string} a\n * @param {string} b\n * @param {number} cursor This should refer to the current left cursor-range position\n */\nconst simpleDiffStringWithCursor = (a, b, cursor) => {\n  let left = 0 // number of same characters counting from left\n  let right = 0 // number of same characters counting from right\n  // Iterate left to the right until we find a changed character\n  // First iteration considers the current cursor position\n  while (\n    left < a.length &&\n    left < b.length &&\n    a[left] === b[left] &&\n    left < cursor\n  ) {\n    left++\n  }\n  // If the last same character is a high surrogate, we need to rollback to the previous character\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  // Iterate right to the left until we find a changed character\n  while (\n    right + left < a.length &&\n    right + left < b.length &&\n    a[a.length - right - 1] === b[b.length - right - 1]\n  ) {\n    right++\n  }\n  // If the last same character is a low surrogate, we need to rollback to the previous character\n  if (right > 0 && lowSurrogateRegex.test(a[a.length - right])) right--\n  // Try to iterate left further to the right without caring about the current cursor position\n  while (\n    right + left < a.length &&\n    right + left < b.length &&\n    a[left] === b[left]\n  ) {\n    left++\n  }\n  if (left > 0 && highSurrogateRegex.test(a[left - 1])) left--\n  return {\n    index: left,\n    remove: a.length - left - right,\n    insert: b.slice(left, b.length - right)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/diff.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/dom.js":
/*!**********************************!*\
  !*** ./node_modules/lib0/dom.js ***!
  \**********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA_SECTION_NODE: () => (/* binding */ CDATA_SECTION_NODE),\n/* harmony export */   COMMENT_NODE: () => (/* binding */ COMMENT_NODE),\n/* harmony export */   DOCUMENT_FRAGMENT_NODE: () => (/* binding */ DOCUMENT_FRAGMENT_NODE),\n/* harmony export */   DOCUMENT_NODE: () => (/* binding */ DOCUMENT_NODE),\n/* harmony export */   DOCUMENT_TYPE_NODE: () => (/* binding */ DOCUMENT_TYPE_NODE),\n/* harmony export */   ELEMENT_NODE: () => (/* binding */ ELEMENT_NODE),\n/* harmony export */   TEXT_NODE: () => (/* binding */ TEXT_NODE),\n/* harmony export */   addEventListener: () => (/* binding */ addEventListener),\n/* harmony export */   addEventListeners: () => (/* binding */ addEventListeners),\n/* harmony export */   append: () => (/* binding */ append),\n/* harmony export */   appendChild: () => (/* binding */ appendChild),\n/* harmony export */   canvas: () => (/* binding */ canvas),\n/* harmony export */   checkNodeType: () => (/* binding */ checkNodeType),\n/* harmony export */   createDocumentFragment: () => (/* binding */ createDocumentFragment),\n/* harmony export */   createElement: () => (/* binding */ createElement),\n/* harmony export */   createTextNode: () => (/* binding */ createTextNode),\n/* harmony export */   doc: () => (/* binding */ doc),\n/* harmony export */   domParser: () => (/* binding */ domParser),\n/* harmony export */   element: () => (/* binding */ element),\n/* harmony export */   emitCustomEvent: () => (/* binding */ emitCustomEvent),\n/* harmony export */   fragment: () => (/* binding */ fragment),\n/* harmony export */   getElementById: () => (/* binding */ getElementById),\n/* harmony export */   insertBefore: () => (/* binding */ insertBefore),\n/* harmony export */   isParentOf: () => (/* binding */ isParentOf),\n/* harmony export */   mapToStyleString: () => (/* binding */ mapToStyleString),\n/* harmony export */   pairToStyleString: () => (/* binding */ pairToStyleString),\n/* harmony export */   pairsToStyleString: () => (/* binding */ pairsToStyleString),\n/* harmony export */   parseElement: () => (/* binding */ parseElement),\n/* harmony export */   parseFragment: () => (/* binding */ parseFragment),\n/* harmony export */   querySelector: () => (/* binding */ querySelector),\n/* harmony export */   querySelectorAll: () => (/* binding */ querySelectorAll),\n/* harmony export */   remove: () => (/* binding */ remove),\n/* harmony export */   removeEventListener: () => (/* binding */ removeEventListener),\n/* harmony export */   removeEventListeners: () => (/* binding */ removeEventListeners),\n/* harmony export */   replaceWith: () => (/* binding */ replaceWith),\n/* harmony export */   setAttributes: () => (/* binding */ setAttributes),\n/* harmony export */   setAttributesMap: () => (/* binding */ setAttributesMap),\n/* harmony export */   text: () => (/* binding */ text)\n/* harmony export */ });\n/* harmony import */ var _pair_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pair.js */ \"(ssr)/./node_modules/lib0/pair.js\");\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/lib0/map.js\");\n/* eslint-env browser */\n\n/**\n * Utility module to work with the DOM.\n *\n * @module dom\n */\n\n\n\n\n/* c8 ignore start */\n/**\n * @type {Document}\n */\nconst doc = /** @type {Document} */ (typeof document !== 'undefined' ? document : {})\n\n/**\n * @param {string} name\n * @return {HTMLElement}\n */\nconst createElement = name => doc.createElement(name)\n\n/**\n * @return {DocumentFragment}\n */\nconst createDocumentFragment = () => doc.createDocumentFragment()\n\n/**\n * @param {string} text\n * @return {Text}\n */\nconst createTextNode = text => doc.createTextNode(text)\n\nconst domParser = /** @type {DOMParser} */ (typeof DOMParser !== 'undefined' ? new DOMParser() : null)\n\n/**\n * @param {HTMLElement} el\n * @param {string} name\n * @param {Object} opts\n */\nconst emitCustomEvent = (el, name, opts) => el.dispatchEvent(new CustomEvent(name, opts))\n\n/**\n * @param {Element} el\n * @param {Array<pair.Pair<string,string|boolean>>} attrs Array of key-value pairs\n * @return {Element}\n */\nconst setAttributes = (el, attrs) => {\n  _pair_js__WEBPACK_IMPORTED_MODULE_0__.forEach(attrs, (key, value) => {\n    if (value === false) {\n      el.removeAttribute(key)\n    } else if (value === true) {\n      el.setAttribute(key, '')\n    } else {\n      // @ts-ignore\n      el.setAttribute(key, value)\n    }\n  })\n  return el\n}\n\n/**\n * @param {Element} el\n * @param {Map<string, string>} attrs Array of key-value pairs\n * @return {Element}\n */\nconst setAttributesMap = (el, attrs) => {\n  attrs.forEach((value, key) => { el.setAttribute(key, value) })\n  return el\n}\n\n/**\n * @param {Array<Node>|HTMLCollection} children\n * @return {DocumentFragment}\n */\nconst fragment = children => {\n  const fragment = createDocumentFragment()\n  for (let i = 0; i < children.length; i++) {\n    appendChild(fragment, children[i])\n  }\n  return fragment\n}\n\n/**\n * @param {Element} parent\n * @param {Array<Node>} nodes\n * @return {Element}\n */\nconst append = (parent, nodes) => {\n  appendChild(parent, fragment(nodes))\n  return parent\n}\n\n/**\n * @param {HTMLElement} el\n */\nconst remove = el => el.remove()\n\n/**\n * @param {EventTarget} el\n * @param {string} name\n * @param {EventListener} f\n */\nconst addEventListener = (el, name, f) => el.addEventListener(name, f)\n\n/**\n * @param {EventTarget} el\n * @param {string} name\n * @param {EventListener} f\n */\nconst removeEventListener = (el, name, f) => el.removeEventListener(name, f)\n\n/**\n * @param {Node} node\n * @param {Array<pair.Pair<string,EventListener>>} listeners\n * @return {Node}\n */\nconst addEventListeners = (node, listeners) => {\n  _pair_js__WEBPACK_IMPORTED_MODULE_0__.forEach(listeners, (name, f) => addEventListener(node, name, f))\n  return node\n}\n\n/**\n * @param {Node} node\n * @param {Array<pair.Pair<string,EventListener>>} listeners\n * @return {Node}\n */\nconst removeEventListeners = (node, listeners) => {\n  _pair_js__WEBPACK_IMPORTED_MODULE_0__.forEach(listeners, (name, f) => removeEventListener(node, name, f))\n  return node\n}\n\n/**\n * @param {string} name\n * @param {Array<pair.Pair<string,string>|pair.Pair<string,boolean>>} attrs Array of key-value pairs\n * @param {Array<Node>} children\n * @return {Element}\n */\nconst element = (name, attrs = [], children = []) =>\n  append(setAttributes(createElement(name), attrs), children)\n\n/**\n * @param {number} width\n * @param {number} height\n */\nconst canvas = (width, height) => {\n  const c = /** @type {HTMLCanvasElement} */ (createElement('canvas'))\n  c.height = height\n  c.width = width\n  return c\n}\n\n/**\n * @param {string} t\n * @return {Text}\n */\nconst text = createTextNode\n\n/**\n * @param {pair.Pair<string,string>} pair\n */\nconst pairToStyleString = pair => `${pair.left}:${pair.right};`\n\n/**\n * @param {Array<pair.Pair<string,string>>} pairs\n * @return {string}\n */\nconst pairsToStyleString = pairs => pairs.map(pairToStyleString).join('')\n\n/**\n * @param {Map<string,string>} m\n * @return {string}\n */\nconst mapToStyleString = m => _map_js__WEBPACK_IMPORTED_MODULE_1__.map(m, (value, key) => `${key}:${value};`).join('')\n\n/**\n * @todo should always query on a dom element\n *\n * @param {HTMLElement|ShadowRoot} el\n * @param {string} query\n * @return {HTMLElement | null}\n */\nconst querySelector = (el, query) => el.querySelector(query)\n\n/**\n * @param {HTMLElement|ShadowRoot} el\n * @param {string} query\n * @return {NodeListOf<HTMLElement>}\n */\nconst querySelectorAll = (el, query) => el.querySelectorAll(query)\n\n/**\n * @param {string} id\n * @return {HTMLElement}\n */\nconst getElementById = id => /** @type {HTMLElement} */ (doc.getElementById(id))\n\n/**\n * @param {string} html\n * @return {HTMLElement}\n */\nconst _parse = html => domParser.parseFromString(`<html><body>${html}</body></html>`, 'text/html').body\n\n/**\n * @param {string} html\n * @return {DocumentFragment}\n */\nconst parseFragment = html => fragment(/** @type {any} */ (_parse(html).childNodes))\n\n/**\n * @param {string} html\n * @return {HTMLElement}\n */\nconst parseElement = html => /** @type HTMLElement */ (_parse(html).firstElementChild)\n\n/**\n * @param {HTMLElement} oldEl\n * @param {HTMLElement|DocumentFragment} newEl\n */\nconst replaceWith = (oldEl, newEl) => oldEl.replaceWith(newEl)\n\n/**\n * @param {HTMLElement} parent\n * @param {HTMLElement} el\n * @param {Node|null} ref\n * @return {HTMLElement}\n */\nconst insertBefore = (parent, el, ref) => parent.insertBefore(el, ref)\n\n/**\n * @param {Node} parent\n * @param {Node} child\n * @return {Node}\n */\nconst appendChild = (parent, child) => parent.appendChild(child)\n\nconst ELEMENT_NODE = doc.ELEMENT_NODE\nconst TEXT_NODE = doc.TEXT_NODE\nconst CDATA_SECTION_NODE = doc.CDATA_SECTION_NODE\nconst COMMENT_NODE = doc.COMMENT_NODE\nconst DOCUMENT_NODE = doc.DOCUMENT_NODE\nconst DOCUMENT_TYPE_NODE = doc.DOCUMENT_TYPE_NODE\nconst DOCUMENT_FRAGMENT_NODE = doc.DOCUMENT_FRAGMENT_NODE\n\n/**\n * @param {any} node\n * @param {number} type\n */\nconst checkNodeType = (node, type) => node.nodeType === type\n\n/**\n * @param {Node} parent\n * @param {HTMLElement} child\n */\nconst isParentOf = (parent, child) => {\n  let p = child.parentNode\n  while (p && p !== parent) {\n    p = p.parentNode\n  }\n  return p === parent\n}\n/* c8 ignore stop */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/dom.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/encoding.js":
/*!***************************************!*\
  !*** ./node_modules/lib0/encoding.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Encoder: () => (/* binding */ Encoder),\n/* harmony export */   IncUintOptRleEncoder: () => (/* binding */ IncUintOptRleEncoder),\n/* harmony export */   IntDiffEncoder: () => (/* binding */ IntDiffEncoder),\n/* harmony export */   IntDiffOptRleEncoder: () => (/* binding */ IntDiffOptRleEncoder),\n/* harmony export */   RleEncoder: () => (/* binding */ RleEncoder),\n/* harmony export */   RleIntDiffEncoder: () => (/* binding */ RleIntDiffEncoder),\n/* harmony export */   StringEncoder: () => (/* binding */ StringEncoder),\n/* harmony export */   UintOptRleEncoder: () => (/* binding */ UintOptRleEncoder),\n/* harmony export */   _writeVarStringNative: () => (/* binding */ _writeVarStringNative),\n/* harmony export */   _writeVarStringPolyfill: () => (/* binding */ _writeVarStringPolyfill),\n/* harmony export */   createEncoder: () => (/* binding */ createEncoder),\n/* harmony export */   encode: () => (/* binding */ encode),\n/* harmony export */   hasContent: () => (/* binding */ hasContent),\n/* harmony export */   length: () => (/* binding */ length),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   setUint16: () => (/* binding */ setUint16),\n/* harmony export */   setUint32: () => (/* binding */ setUint32),\n/* harmony export */   setUint8: () => (/* binding */ setUint8),\n/* harmony export */   toUint8Array: () => (/* binding */ toUint8Array),\n/* harmony export */   verifyLen: () => (/* binding */ verifyLen),\n/* harmony export */   write: () => (/* binding */ write),\n/* harmony export */   writeAny: () => (/* binding */ writeAny),\n/* harmony export */   writeBigInt64: () => (/* binding */ writeBigInt64),\n/* harmony export */   writeBigUint64: () => (/* binding */ writeBigUint64),\n/* harmony export */   writeBinaryEncoder: () => (/* binding */ writeBinaryEncoder),\n/* harmony export */   writeFloat32: () => (/* binding */ writeFloat32),\n/* harmony export */   writeFloat64: () => (/* binding */ writeFloat64),\n/* harmony export */   writeOnDataView: () => (/* binding */ writeOnDataView),\n/* harmony export */   writeTerminatedString: () => (/* binding */ writeTerminatedString),\n/* harmony export */   writeTerminatedUint8Array: () => (/* binding */ writeTerminatedUint8Array),\n/* harmony export */   writeUint16: () => (/* binding */ writeUint16),\n/* harmony export */   writeUint32: () => (/* binding */ writeUint32),\n/* harmony export */   writeUint32BigEndian: () => (/* binding */ writeUint32BigEndian),\n/* harmony export */   writeUint8: () => (/* binding */ writeUint8),\n/* harmony export */   writeUint8Array: () => (/* binding */ writeUint8Array),\n/* harmony export */   writeVarInt: () => (/* binding */ writeVarInt),\n/* harmony export */   writeVarString: () => (/* binding */ writeVarString),\n/* harmony export */   writeVarUint: () => (/* binding */ writeVarUint),\n/* harmony export */   writeVarUint8Array: () => (/* binding */ writeVarUint8Array)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var _number_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./number.js */ \"(ssr)/./node_modules/lib0/number.js\");\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/./node_modules/lib0/binary.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./string.js */ \"(ssr)/./node_modules/lib0/string.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/lib0/array.js\");\n/**\n * Efficient schema-less binary encoding with support for variable length encoding.\n *\n * Use [lib0/encoding] with [lib0/decoding]. Every encoding function has a corresponding decoding function.\n *\n * Encodes numbers in little-endian order (least to most significant byte order)\n * and is compatible with Golang's binary encoding (https://golang.org/pkg/encoding/binary/)\n * which is also used in Protocol Buffers.\n *\n * ```js\n * // encoding step\n * const encoder = encoding.createEncoder()\n * encoding.writeVarUint(encoder, 256)\n * encoding.writeVarString(encoder, 'Hello world!')\n * const buf = encoding.toUint8Array(encoder)\n * ```\n *\n * ```js\n * // decoding step\n * const decoder = decoding.createDecoder(buf)\n * decoding.readVarUint(decoder) // => 256\n * decoding.readVarString(decoder) // => 'Hello world!'\n * decoding.hasContent(decoder) // => false - all data is read\n * ```\n *\n * @module encoding\n */\n\n\n\n\n\n\n\n/**\n * A BinaryEncoder handles the encoding to an Uint8Array.\n */\nclass Encoder {\n  constructor () {\n    this.cpos = 0\n    this.cbuf = new Uint8Array(100)\n    /**\n     * @type {Array<Uint8Array>}\n     */\n    this.bufs = []\n  }\n}\n\n/**\n * @function\n * @return {Encoder}\n */\nconst createEncoder = () => new Encoder()\n\n/**\n * @param {function(Encoder):void} f\n */\nconst encode = (f) => {\n  const encoder = createEncoder()\n  f(encoder)\n  return toUint8Array(encoder)\n}\n\n/**\n * The current length of the encoded data.\n *\n * @function\n * @param {Encoder} encoder\n * @return {number}\n */\nconst length = encoder => {\n  let len = encoder.cpos\n  for (let i = 0; i < encoder.bufs.length; i++) {\n    len += encoder.bufs[i].length\n  }\n  return len\n}\n\n/**\n * Check whether encoder is empty.\n *\n * @function\n * @param {Encoder} encoder\n * @return {boolean}\n */\nconst hasContent = encoder => encoder.cpos > 0 || encoder.bufs.length > 0\n\n/**\n * Transform to Uint8Array.\n *\n * @function\n * @param {Encoder} encoder\n * @return {Uint8Array} The created ArrayBuffer.\n */\nconst toUint8Array = encoder => {\n  const uint8arr = new Uint8Array(length(encoder))\n  let curPos = 0\n  for (let i = 0; i < encoder.bufs.length; i++) {\n    const d = encoder.bufs[i]\n    uint8arr.set(d, curPos)\n    curPos += d.length\n  }\n  uint8arr.set(new Uint8Array(encoder.cbuf.buffer, 0, encoder.cpos), curPos)\n  return uint8arr\n}\n\n/**\n * Verify that it is possible to write `len` bytes wtihout checking. If\n * necessary, a new Buffer with the required length is attached.\n *\n * @param {Encoder} encoder\n * @param {number} len\n */\nconst verifyLen = (encoder, len) => {\n  const bufferLen = encoder.cbuf.length\n  if (bufferLen - encoder.cpos < len) {\n    encoder.bufs.push(new Uint8Array(encoder.cbuf.buffer, 0, encoder.cpos))\n    encoder.cbuf = new Uint8Array(_math_js__WEBPACK_IMPORTED_MODULE_0__.max(bufferLen, len) * 2)\n    encoder.cpos = 0\n  }\n}\n\n/**\n * Write one byte to the encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The byte that is to be encoded.\n */\nconst write = (encoder, num) => {\n  const bufferLen = encoder.cbuf.length\n  if (encoder.cpos === bufferLen) {\n    encoder.bufs.push(encoder.cbuf)\n    encoder.cbuf = new Uint8Array(bufferLen * 2)\n    encoder.cpos = 0\n  }\n  encoder.cbuf[encoder.cpos++] = num\n}\n\n/**\n * Write one byte at a specific position.\n * Position must already be written (i.e. encoder.length > pos)\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos Position to which to write data\n * @param {number} num Unsigned 8-bit integer\n */\nconst set = (encoder, pos, num) => {\n  let buffer = null\n  // iterate all buffers and adjust position\n  for (let i = 0; i < encoder.bufs.length && buffer === null; i++) {\n    const b = encoder.bufs[i]\n    if (pos < b.length) {\n      buffer = b // found buffer\n    } else {\n      pos -= b.length\n    }\n  }\n  if (buffer === null) {\n    // use current buffer\n    buffer = encoder.cbuf\n  }\n  buffer[pos] = num\n}\n\n/**\n * Write one byte as an unsigned integer.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeUint8 = write\n\n/**\n * Write one byte as an unsigned Integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nconst setUint8 = set\n\n/**\n * Write two bytes as an unsigned integer.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeUint16 = (encoder, num) => {\n  write(encoder, num & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n  write(encoder, (num >>> 8) & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n}\n/**\n * Write two bytes as an unsigned integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nconst setUint16 = (encoder, pos, num) => {\n  set(encoder, pos, num & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n  set(encoder, pos + 1, (num >>> 8) & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n}\n\n/**\n * Write two bytes as an unsigned integer\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeUint32 = (encoder, num) => {\n  for (let i = 0; i < 4; i++) {\n    write(encoder, num & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n    num >>>= 8\n  }\n}\n\n/**\n * Write two bytes as an unsigned integer in big endian order.\n * (most significant byte first)\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeUint32BigEndian = (encoder, num) => {\n  for (let i = 3; i >= 0; i--) {\n    write(encoder, (num >>> (8 * i)) & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n  }\n}\n\n/**\n * Write two bytes as an unsigned integer at a specific location.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} pos The location where the data will be written.\n * @param {number} num The number that is to be encoded.\n */\nconst setUint32 = (encoder, pos, num) => {\n  for (let i = 0; i < 4; i++) {\n    set(encoder, pos + i, num & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS8)\n    num >>>= 8\n  }\n}\n\n/**\n * Write a variable length unsigned integer. Max encodable integer is 2^53.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeVarUint = (encoder, num) => {\n  while (num > _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7) {\n    write(encoder, _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8 | (_binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7 & num))\n    num = _math_js__WEBPACK_IMPORTED_MODULE_0__.floor(num / 128) // shift >>> 7\n  }\n  write(encoder, _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7 & num)\n}\n\n/**\n * Write a variable length integer.\n *\n * We use the 7th bit instead for signaling that this is a negative number.\n *\n * @function\n * @param {Encoder} encoder\n * @param {number} num The number that is to be encoded.\n */\nconst writeVarInt = (encoder, num) => {\n  const isNegative = _math_js__WEBPACK_IMPORTED_MODULE_0__.isNegativeZero(num)\n  if (isNegative) {\n    num = -num\n  }\n  //             |- whether to continue reading         |- whether is negative     |- number\n  write(encoder, (num > _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS6 ? _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8 : 0) | (isNegative ? _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT7 : 0) | (_binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS6 & num))\n  num = _math_js__WEBPACK_IMPORTED_MODULE_0__.floor(num / 64) // shift >>> 6\n  // We don't need to consider the case of num === 0 so we can use a different\n  // pattern here than above.\n  while (num > 0) {\n    write(encoder, (num > _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7 ? _binary_js__WEBPACK_IMPORTED_MODULE_1__.BIT8 : 0) | (_binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS7 & num))\n    num = _math_js__WEBPACK_IMPORTED_MODULE_0__.floor(num / 128) // shift >>> 7\n  }\n}\n\n/**\n * A cache to store strings temporarily\n */\nconst _strBuffer = new Uint8Array(30000)\nconst _maxStrBSize = _strBuffer.length / 3\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nconst _writeVarStringNative = (encoder, str) => {\n  if (str.length < _maxStrBSize) {\n    // We can encode the string into the existing buffer\n    /* c8 ignore next */\n    const written = _string_js__WEBPACK_IMPORTED_MODULE_2__.utf8TextEncoder.encodeInto(str, _strBuffer).written || 0\n    writeVarUint(encoder, written)\n    for (let i = 0; i < written; i++) {\n      write(encoder, _strBuffer[i])\n    }\n  } else {\n    writeVarUint8Array(encoder, _string_js__WEBPACK_IMPORTED_MODULE_2__.encodeUtf8(str))\n  }\n}\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nconst _writeVarStringPolyfill = (encoder, str) => {\n  const encodedString = unescape(encodeURIComponent(str))\n  const len = encodedString.length\n  writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    write(encoder, /** @type {number} */ (encodedString.codePointAt(i)))\n  }\n}\n\n/**\n * Write a variable length string.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\n/* c8 ignore next */\nconst writeVarString = (_string_js__WEBPACK_IMPORTED_MODULE_2__.utf8TextEncoder && /** @type {any} */ (_string_js__WEBPACK_IMPORTED_MODULE_2__.utf8TextEncoder).encodeInto) ? _writeVarStringNative : _writeVarStringPolyfill\n\n/**\n * Write a string terminated by a special byte sequence. This is not very performant and is\n * generally discouraged. However, the resulting byte arrays are lexiographically ordered which\n * makes this a nice feature for databases.\n *\n * The string will be encoded using utf8 and then terminated and escaped using writeTerminatingUint8Array.\n *\n * @function\n * @param {Encoder} encoder\n * @param {String} str The string that is to be encoded.\n */\nconst writeTerminatedString = (encoder, str) =>\n  writeTerminatedUint8Array(encoder, _string_js__WEBPACK_IMPORTED_MODULE_2__.encodeUtf8(str))\n\n/**\n * Write a terminating Uint8Array. Note that this is not performant and is generally\n * discouraged. There are few situations when this is needed.\n *\n * We use 0x0 as a terminating character. 0x1 serves as an escape character for 0x0 and 0x1.\n *\n * Example: [0,1,2] is encoded to [1,0,1,1,2,0]. 0x0, and 0x1 needed to be escaped using 0x1. Then\n * the result is terminated using the 0x0 character.\n *\n * This is basically how many systems implement null terminated strings. However, we use an escape\n * character 0x1 to avoid issues and potenial attacks on our database (if this is used as a key\n * encoder for NoSql databases).\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} buf The string that is to be encoded.\n */\nconst writeTerminatedUint8Array = (encoder, buf) => {\n  for (let i = 0; i < buf.length; i++) {\n    const b = buf[i]\n    if (b === 0 || b === 1) {\n      write(encoder, 1)\n    }\n    write(encoder, buf[i])\n  }\n  write(encoder, 0)\n}\n\n/**\n * Write the content of another Encoder.\n *\n * @TODO: can be improved!\n *        - Note: Should consider that when appending a lot of small Encoders, we should rather clone than referencing the old structure.\n *                Encoders start with a rather big initial buffer.\n *\n * @function\n * @param {Encoder} encoder The enUint8Arr\n * @param {Encoder} append The BinaryEncoder to be written.\n */\nconst writeBinaryEncoder = (encoder, append) => writeUint8Array(encoder, toUint8Array(append))\n\n/**\n * Append fixed-length Uint8Array to the encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} uint8Array\n */\nconst writeUint8Array = (encoder, uint8Array) => {\n  const bufferLen = encoder.cbuf.length\n  const cpos = encoder.cpos\n  const leftCopyLen = _math_js__WEBPACK_IMPORTED_MODULE_0__.min(bufferLen - cpos, uint8Array.length)\n  const rightCopyLen = uint8Array.length - leftCopyLen\n  encoder.cbuf.set(uint8Array.subarray(0, leftCopyLen), cpos)\n  encoder.cpos += leftCopyLen\n  if (rightCopyLen > 0) {\n    // Still something to write, write right half..\n    // Append new buffer\n    encoder.bufs.push(encoder.cbuf)\n    // must have at least size of remaining buffer\n    encoder.cbuf = new Uint8Array(_math_js__WEBPACK_IMPORTED_MODULE_0__.max(bufferLen * 2, rightCopyLen))\n    // copy array\n    encoder.cbuf.set(uint8Array.subarray(leftCopyLen))\n    encoder.cpos = rightCopyLen\n  }\n}\n\n/**\n * Append an Uint8Array to Encoder.\n *\n * @function\n * @param {Encoder} encoder\n * @param {Uint8Array} uint8Array\n */\nconst writeVarUint8Array = (encoder, uint8Array) => {\n  writeVarUint(encoder, uint8Array.byteLength)\n  writeUint8Array(encoder, uint8Array)\n}\n\n/**\n * Create an DataView of the next `len` bytes. Use it to write data after\n * calling this function.\n *\n * ```js\n * // write float32 using DataView\n * const dv = writeOnDataView(encoder, 4)\n * dv.setFloat32(0, 1.1)\n * // read float32 using DataView\n * const dv = readFromDataView(encoder, 4)\n * dv.getFloat32(0) // => 1.100000023841858 (leaving it to the reader to find out why this is the correct result)\n * ```\n *\n * @param {Encoder} encoder\n * @param {number} len\n * @return {DataView}\n */\nconst writeOnDataView = (encoder, len) => {\n  verifyLen(encoder, len)\n  const dview = new DataView(encoder.cbuf.buffer, encoder.cpos, len)\n  encoder.cpos += len\n  return dview\n}\n\n/**\n * @param {Encoder} encoder\n * @param {number} num\n */\nconst writeFloat32 = (encoder, num) => writeOnDataView(encoder, 4).setFloat32(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {number} num\n */\nconst writeFloat64 = (encoder, num) => writeOnDataView(encoder, 8).setFloat64(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {bigint} num\n */\nconst writeBigInt64 = (encoder, num) => /** @type {any} */ (writeOnDataView(encoder, 8)).setBigInt64(0, num, false)\n\n/**\n * @param {Encoder} encoder\n * @param {bigint} num\n */\nconst writeBigUint64 = (encoder, num) => /** @type {any} */ (writeOnDataView(encoder, 8)).setBigUint64(0, num, false)\n\nconst floatTestBed = new DataView(new ArrayBuffer(4))\n/**\n * Check if a number can be encoded as a 32 bit float.\n *\n * @param {number} num\n * @return {boolean}\n */\nconst isFloat32 = num => {\n  floatTestBed.setFloat32(0, num)\n  return floatTestBed.getFloat32(0) === num\n}\n\n/**\n * Encode data with efficient binary format.\n *\n * Differences to JSON:\n * • Transforms data to a binary format (not to a string)\n * • Encodes undefined, NaN, and ArrayBuffer (these can't be represented in JSON)\n * • Numbers are efficiently encoded either as a variable length integer, as a\n *   32 bit float, as a 64 bit float, or as a 64 bit bigint.\n *\n * Encoding table:\n *\n * | Data Type           | Prefix   | Encoding Method    | Comment |\n * | ------------------- | -------- | ------------------ | ------- |\n * | undefined           | 127      |                    | Functions, symbol, and everything that cannot be identified is encoded as undefined |\n * | null                | 126      |                    | |\n * | integer             | 125      | writeVarInt        | Only encodes 32 bit signed integers |\n * | float32             | 124      | writeFloat32       | |\n * | float64             | 123      | writeFloat64       | |\n * | bigint              | 122      | writeBigInt64      | |\n * | boolean (false)     | 121      |                    | True and false are different data types so we save the following byte |\n * | boolean (true)      | 120      |                    | - 0b01111000 so the last bit determines whether true or false |\n * | string              | 119      | writeVarString     | |\n * | object<string,any>  | 118      | custom             | Writes {length} then {length} key-value pairs |\n * | array<any>          | 117      | custom             | Writes {length} then {length} json values |\n * | Uint8Array          | 116      | writeVarUint8Array | We use Uint8Array for any kind of binary data |\n *\n * Reasons for the decreasing prefix:\n * We need the first bit for extendability (later we may want to encode the\n * prefix with writeVarUint). The remaining 7 bits are divided as follows:\n * [0-30]   the beginning of the data range is used for custom purposes\n *          (defined by the function that uses this library)\n * [31-127] the end of the data range is used for data encoding by\n *          lib0/encoding.js\n *\n * @param {Encoder} encoder\n * @param {undefined|null|number|bigint|boolean|string|Object<string,any>|Array<any>|Uint8Array} data\n */\nconst writeAny = (encoder, data) => {\n  switch (typeof data) {\n    case 'string':\n      // TYPE 119: STRING\n      write(encoder, 119)\n      writeVarString(encoder, data)\n      break\n    case 'number':\n      if (_number_js__WEBPACK_IMPORTED_MODULE_3__.isInteger(data) && _math_js__WEBPACK_IMPORTED_MODULE_0__.abs(data) <= _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS31) {\n        // TYPE 125: INTEGER\n        write(encoder, 125)\n        writeVarInt(encoder, data)\n      } else if (isFloat32(data)) {\n        // TYPE 124: FLOAT32\n        write(encoder, 124)\n        writeFloat32(encoder, data)\n      } else {\n        // TYPE 123: FLOAT64\n        write(encoder, 123)\n        writeFloat64(encoder, data)\n      }\n      break\n    case 'bigint':\n      // TYPE 122: BigInt\n      write(encoder, 122)\n      writeBigInt64(encoder, data)\n      break\n    case 'object':\n      if (data === null) {\n        // TYPE 126: null\n        write(encoder, 126)\n      } else if (_array_js__WEBPACK_IMPORTED_MODULE_4__.isArray(data)) {\n        // TYPE 117: Array\n        write(encoder, 117)\n        writeVarUint(encoder, data.length)\n        for (let i = 0; i < data.length; i++) {\n          writeAny(encoder, data[i])\n        }\n      } else if (data instanceof Uint8Array) {\n        // TYPE 116: ArrayBuffer\n        write(encoder, 116)\n        writeVarUint8Array(encoder, data)\n      } else {\n        // TYPE 118: Object\n        write(encoder, 118)\n        const keys = Object.keys(data)\n        writeVarUint(encoder, keys.length)\n        for (let i = 0; i < keys.length; i++) {\n          const key = keys[i]\n          writeVarString(encoder, key)\n          writeAny(encoder, data[key])\n        }\n      }\n      break\n    case 'boolean':\n      // TYPE 120/121: boolean (true/false)\n      write(encoder, data ? 120 : 121)\n      break\n    default:\n      // TYPE 127: undefined\n      write(encoder, 127)\n  }\n}\n\n/**\n * Now come a few stateful encoder that have their own classes.\n */\n\n/**\n * Basic Run Length Encoder - a basic compression implementation.\n *\n * Encodes [1,1,1,7] to [1,3,7,1] (3 times 1, 1 time 7). This encoder might do more harm than good if there are a lot of values that are not repeated.\n *\n * It was originally used for image compression. Cool .. article http://csbruce.com/cbm/transactor/pdfs/trans_v7_i06.pdf\n *\n * @note T must not be null!\n *\n * @template T\n */\nclass RleEncoder extends Encoder {\n  /**\n   * @param {function(Encoder, T):void} writer\n   */\n  constructor (writer) {\n    super()\n    /**\n     * The writer\n     */\n    this.w = writer\n    /**\n     * Current state\n     * @type {T|null}\n     */\n    this.s = null\n    this.count = 0\n  }\n\n  /**\n   * @param {T} v\n   */\n  write (v) {\n    if (this.s === v) {\n      this.count++\n    } else {\n      if (this.count > 0) {\n        // flush counter, unless this is the first value (count = 0)\n        writeVarUint(this, this.count - 1) // since count is always > 0, we can decrement by one. non-standard encoding ftw\n      }\n      this.count = 1\n      // write first value\n      this.w(this, v)\n      this.s = v\n    }\n  }\n}\n\n/**\n * Basic diff decoder using variable length encoding.\n *\n * Encodes the values [3, 1100, 1101, 1050, 0] to [3, 1097, 1, -51, -1050] using writeVarInt.\n */\nclass IntDiffEncoder extends Encoder {\n  /**\n   * @param {number} start\n   */\n  constructor (start) {\n    super()\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    writeVarInt(this, v - this.s)\n    this.s = v\n  }\n}\n\n/**\n * A combination of IntDiffEncoder and RleEncoder.\n *\n * Basically first writes the IntDiffEncoder and then counts duplicate diffs using RleEncoding.\n *\n * Encodes the values [1,1,1,2,3,4,5,6] as [1,1,0,2,1,5] (RLE([1,0,0,1,1,1,1,1]) ⇒ RleIntDiff[1,1,0,2,1,5])\n */\nclass RleIntDiffEncoder extends Encoder {\n  /**\n   * @param {number} start\n   */\n  constructor (start) {\n    super()\n    /**\n     * Current state\n     * @type {number}\n     */\n    this.s = start\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s === v && this.count > 0) {\n      this.count++\n    } else {\n      if (this.count > 0) {\n        // flush counter, unless this is the first value (count = 0)\n        writeVarUint(this, this.count - 1) // since count is always > 0, we can decrement by one. non-standard encoding ftw\n      }\n      this.count = 1\n      // write first value\n      writeVarInt(this, v - this.s)\n      this.s = v\n    }\n  }\n}\n\n/**\n * @param {UintOptRleEncoder} encoder\n */\nconst flushUintOptRleEncoder = encoder => {\n  if (encoder.count > 0) {\n    // flush counter, unless this is the first value (count = 0)\n    // case 1: just a single value. set sign to positive\n    // case 2: write several values. set sign to negative to indicate that there is a length coming\n    writeVarInt(encoder.encoder, encoder.count === 1 ? encoder.s : -encoder.s)\n    if (encoder.count > 1) {\n      writeVarUint(encoder.encoder, encoder.count - 2) // since count is always > 1, we can decrement by one. non-standard encoding ftw\n    }\n  }\n}\n\n/**\n * Optimized Rle encoder that does not suffer from the mentioned problem of the basic Rle encoder.\n *\n * Internally uses VarInt encoder to write unsigned integers. If the input occurs multiple times, we write\n * write it as a negative number. The UintOptRleDecoder then understands that it needs to read a count.\n *\n * Encodes [1,2,3,3,3] as [1,2,-3,3] (once 1, once 2, three times 3)\n */\nclass UintOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s === v) {\n      this.count++\n    } else {\n      flushUintOptRleEncoder(this)\n      this.count = 1\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushUintOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * Increasing Uint Optimized RLE Encoder\n *\n * The RLE encoder counts the number of same occurences of the same value.\n * The IncUintOptRle encoder counts if the value increases.\n * I.e. 7, 8, 9, 10 will be encoded as [-7, 4]. 1, 3, 5 will be encoded\n * as [1, 3, 5].\n */\nclass IncUintOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.s + this.count === v) {\n      this.count++\n    } else {\n      flushUintOptRleEncoder(this)\n      this.count = 1\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushUintOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * @param {IntDiffOptRleEncoder} encoder\n */\nconst flushIntDiffOptRleEncoder = encoder => {\n  if (encoder.count > 0) {\n    //          31 bit making up the diff | wether to write the counter\n    // const encodedDiff = encoder.diff << 1 | (encoder.count === 1 ? 0 : 1)\n    const encodedDiff = encoder.diff * 2 + (encoder.count === 1 ? 0 : 1)\n    // flush counter, unless this is the first value (count = 0)\n    // case 1: just a single value. set first bit to positive\n    // case 2: write several values. set first bit to negative to indicate that there is a length coming\n    writeVarInt(encoder.encoder, encodedDiff)\n    if (encoder.count > 1) {\n      writeVarUint(encoder.encoder, encoder.count - 2) // since count is always > 1, we can decrement by one. non-standard encoding ftw\n    }\n  }\n}\n\n/**\n * A combination of the IntDiffEncoder and the UintOptRleEncoder.\n *\n * The count approach is similar to the UintDiffOptRleEncoder, but instead of using the negative bitflag, it encodes\n * in the LSB whether a count is to be read. Therefore this Encoder only supports 31 bit integers!\n *\n * Encodes [1, 2, 3, 2] as [3, 1, 6, -1] (more specifically [(1 << 1) | 1, (3 << 0) | 0, -1])\n *\n * Internally uses variable length encoding. Contrary to normal UintVar encoding, the first byte contains:\n * * 1 bit that denotes whether the next value is a count (LSB)\n * * 1 bit that denotes whether this value is negative (MSB - 1)\n * * 1 bit that denotes whether to continue reading the variable length integer (MSB)\n *\n * Therefore, only five bits remain to encode diff ranges.\n *\n * Use this Encoder only when appropriate. In most cases, this is probably a bad idea.\n */\nclass IntDiffOptRleEncoder {\n  constructor () {\n    this.encoder = new Encoder()\n    /**\n     * @type {number}\n     */\n    this.s = 0\n    this.count = 0\n    this.diff = 0\n  }\n\n  /**\n   * @param {number} v\n   */\n  write (v) {\n    if (this.diff === v - this.s) {\n      this.s = v\n      this.count++\n    } else {\n      flushIntDiffOptRleEncoder(this)\n      this.count = 1\n      this.diff = v - this.s\n      this.s = v\n    }\n  }\n\n  /**\n   * Flush the encoded state and transform this to a Uint8Array.\n   *\n   * Note that this should only be called once.\n   */\n  toUint8Array () {\n    flushIntDiffOptRleEncoder(this)\n    return toUint8Array(this.encoder)\n  }\n}\n\n/**\n * Optimized String Encoder.\n *\n * Encoding many small strings in a simple Encoder is not very efficient. The function call to decode a string takes some time and creates references that must be eventually deleted.\n * In practice, when decoding several million small strings, the GC will kick in more and more often to collect orphaned string objects (or maybe there is another reason?).\n *\n * This string encoder solves the above problem. All strings are concatenated and written as a single string using a single encoding call.\n *\n * The lengths are encoded using a UintOptRleEncoder.\n */\nclass StringEncoder {\n  constructor () {\n    /**\n     * @type {Array<string>}\n     */\n    this.sarr = []\n    this.s = ''\n    this.lensE = new UintOptRleEncoder()\n  }\n\n  /**\n   * @param {string} string\n   */\n  write (string) {\n    this.s += string\n    if (this.s.length > 19) {\n      this.sarr.push(this.s)\n      this.s = ''\n    }\n    this.lensE.write(string.length)\n  }\n\n  toUint8Array () {\n    const encoder = new Encoder()\n    this.sarr.push(this.s)\n    this.s = ''\n    writeVarString(encoder, this.sarr.join(''))\n    writeUint8Array(encoder, this.lensE.toUint8Array())\n    return toUint8Array(encoder)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9lbmNvZGluZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFaUM7QUFDSTtBQUNBO0FBQ0E7QUFDRjs7QUFFbkM7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ087O0FBRVA7QUFDQSxXQUFXLHdCQUF3QjtBQUNuQztBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWTtBQUNaO0FBQ087QUFDUDtBQUNBLGtCQUFrQix5QkFBeUI7QUFDM0M7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWTtBQUNaO0FBQ087O0FBRVA7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsWUFBWSxZQUFZO0FBQ3hCO0FBQ087QUFDUDtBQUNBO0FBQ0Esa0JBQWtCLHlCQUF5QjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyx5Q0FBUTtBQUMxQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQjtBQUNPO0FBQ1A7QUFDQTtBQUNBLGtCQUFrQiw0Q0FBNEM7QUFDOUQ7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkI7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CO0FBQ087O0FBRVA7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CO0FBQ087QUFDUCx1QkFBdUIsNkNBQVk7QUFDbkMsK0JBQStCLDZDQUFZO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQjtBQUNPO0FBQ1AsMEJBQTBCLDZDQUFZO0FBQ3RDLHNDQUFzQyw2Q0FBWTtBQUNsRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQLGtCQUFrQixPQUFPO0FBQ3pCLHlCQUF5Qiw2Q0FBWTtBQUNyQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQLGtCQUFrQixRQUFRO0FBQzFCLHVDQUF1Qyw2Q0FBWTtBQUNuRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsUUFBUTtBQUNuQixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQLGtCQUFrQixPQUFPO0FBQ3pCLGdDQUFnQyw2Q0FBWTtBQUM1QztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CO0FBQ087QUFDUCxlQUFlLDZDQUFZO0FBQzNCLG1CQUFtQiw0Q0FBVyxJQUFJLDZDQUFZO0FBQzlDLFVBQVUsMkNBQVU7QUFDcEI7QUFDQSxpQkFBaUIsNkNBQVk7QUFDN0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsUUFBUTtBQUNuQjtBQUNPO0FBQ1AscUJBQXFCLG9EQUFtQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw2Q0FBWSxHQUFHLDRDQUFXLHNCQUFzQiw0Q0FBVyxTQUFTLDZDQUFZO0FBQ3hHLFFBQVEsMkNBQVU7QUFDbEI7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLDZDQUFZLEdBQUcsNENBQVcsU0FBUyw2Q0FBWTtBQUN6RSxVQUFVLDJDQUFVO0FBQ3BCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQix1REFBc0I7QUFDMUM7QUFDQSxvQkFBb0IsYUFBYTtBQUNqQztBQUNBO0FBQ0EsSUFBSTtBQUNKLGdDQUFnQyxrREFBaUI7QUFDakQ7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixTQUFTO0FBQzNCLDhCQUE4QixRQUFRO0FBQ3RDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CO0FBQ0E7QUFDTyx3QkFBd0IsdURBQXNCLGVBQWUsS0FBSyxJQUFJLHVEQUFzQjs7QUFFbkc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFFBQVE7QUFDbkI7QUFDTztBQUNQLHFDQUFxQyxrREFBaUI7O0FBRXREO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxZQUFZO0FBQ3ZCO0FBQ087QUFDUCxrQkFBa0IsZ0JBQWdCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFNBQVM7QUFDcEI7QUFDTzs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsU0FBUztBQUNwQixXQUFXLFlBQVk7QUFDdkI7QUFDTztBQUNQO0FBQ0E7QUFDQSxzQkFBc0IseUNBQVE7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyx5Q0FBUTtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsWUFBWTtBQUN2QjtBQUNPO0FBQ1A7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CO0FBQ087O0FBRVA7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CO0FBQ087O0FBRVA7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxRQUFRO0FBQ25CO0FBQ08sbURBQW1ELEtBQUs7O0FBRS9EO0FBQ0EsV0FBVyxTQUFTO0FBQ3BCLFdBQVcsUUFBUTtBQUNuQjtBQUNPLG9EQUFvRCxLQUFLOztBQUVoRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1FQUFtRSxRQUFRLE1BQU0sUUFBUTtBQUN6RixtRUFBbUUsUUFBUSxNQUFNLFFBQVE7QUFDekY7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFNBQVM7QUFDcEIsV0FBVyxzRkFBc0Y7QUFDakc7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxpREFBZ0IsVUFBVSx5Q0FBUSxVQUFVLDhDQUFhO0FBQ25FO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsU0FBUyw4Q0FBYTtBQUM5QjtBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsaUJBQWlCO0FBQ3pDO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGlCQUFpQjtBQUN6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsYUFBYSwyQkFBMkI7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsYUFBYSxHQUFHO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxXQUFXLHNCQUFzQjtBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9saWIwL2VuY29kaW5nLmpzP2JhNjkiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBFZmZpY2llbnQgc2NoZW1hLWxlc3MgYmluYXJ5IGVuY29kaW5nIHdpdGggc3VwcG9ydCBmb3IgdmFyaWFibGUgbGVuZ3RoIGVuY29kaW5nLlxuICpcbiAqIFVzZSBbbGliMC9lbmNvZGluZ10gd2l0aCBbbGliMC9kZWNvZGluZ10uIEV2ZXJ5IGVuY29kaW5nIGZ1bmN0aW9uIGhhcyBhIGNvcnJlc3BvbmRpbmcgZGVjb2RpbmcgZnVuY3Rpb24uXG4gKlxuICogRW5jb2RlcyBudW1iZXJzIGluIGxpdHRsZS1lbmRpYW4gb3JkZXIgKGxlYXN0IHRvIG1vc3Qgc2lnbmlmaWNhbnQgYnl0ZSBvcmRlcilcbiAqIGFuZCBpcyBjb21wYXRpYmxlIHdpdGggR29sYW5nJ3MgYmluYXJ5IGVuY29kaW5nIChodHRwczovL2dvbGFuZy5vcmcvcGtnL2VuY29kaW5nL2JpbmFyeS8pXG4gKiB3aGljaCBpcyBhbHNvIHVzZWQgaW4gUHJvdG9jb2wgQnVmZmVycy5cbiAqXG4gKiBgYGBqc1xuICogLy8gZW5jb2Rpbmcgc3RlcFxuICogY29uc3QgZW5jb2RlciA9IGVuY29kaW5nLmNyZWF0ZUVuY29kZXIoKVxuICogZW5jb2Rpbmcud3JpdGVWYXJVaW50KGVuY29kZXIsIDI1NilcbiAqIGVuY29kaW5nLndyaXRlVmFyU3RyaW5nKGVuY29kZXIsICdIZWxsbyB3b3JsZCEnKVxuICogY29uc3QgYnVmID0gZW5jb2RpbmcudG9VaW50OEFycmF5KGVuY29kZXIpXG4gKiBgYGBcbiAqXG4gKiBgYGBqc1xuICogLy8gZGVjb2Rpbmcgc3RlcFxuICogY29uc3QgZGVjb2RlciA9IGRlY29kaW5nLmNyZWF0ZURlY29kZXIoYnVmKVxuICogZGVjb2RpbmcucmVhZFZhclVpbnQoZGVjb2RlcikgLy8gPT4gMjU2XG4gKiBkZWNvZGluZy5yZWFkVmFyU3RyaW5nKGRlY29kZXIpIC8vID0+ICdIZWxsbyB3b3JsZCEnXG4gKiBkZWNvZGluZy5oYXNDb250ZW50KGRlY29kZXIpIC8vID0+IGZhbHNlIC0gYWxsIGRhdGEgaXMgcmVhZFxuICogYGBgXG4gKlxuICogQG1vZHVsZSBlbmNvZGluZ1xuICovXG5cbmltcG9ydCAqIGFzIG1hdGggZnJvbSAnLi9tYXRoLmpzJ1xuaW1wb3J0ICogYXMgbnVtYmVyIGZyb20gJy4vbnVtYmVyLmpzJ1xuaW1wb3J0ICogYXMgYmluYXJ5IGZyb20gJy4vYmluYXJ5LmpzJ1xuaW1wb3J0ICogYXMgc3RyaW5nIGZyb20gJy4vc3RyaW5nLmpzJ1xuaW1wb3J0ICogYXMgYXJyYXkgZnJvbSAnLi9hcnJheS5qcydcblxuLyoqXG4gKiBBIEJpbmFyeUVuY29kZXIgaGFuZGxlcyB0aGUgZW5jb2RpbmcgdG8gYW4gVWludDhBcnJheS5cbiAqL1xuZXhwb3J0IGNsYXNzIEVuY29kZXIge1xuICBjb25zdHJ1Y3RvciAoKSB7XG4gICAgdGhpcy5jcG9zID0gMFxuICAgIHRoaXMuY2J1ZiA9IG5ldyBVaW50OEFycmF5KDEwMClcbiAgICAvKipcbiAgICAgKiBAdHlwZSB7QXJyYXk8VWludDhBcnJheT59XG4gICAgICovXG4gICAgdGhpcy5idWZzID0gW11cbiAgfVxufVxuXG4vKipcbiAqIEBmdW5jdGlvblxuICogQHJldHVybiB7RW5jb2Rlcn1cbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUVuY29kZXIgPSAoKSA9PiBuZXcgRW5jb2RlcigpXG5cbi8qKlxuICogQHBhcmFtIHtmdW5jdGlvbihFbmNvZGVyKTp2b2lkfSBmXG4gKi9cbmV4cG9ydCBjb25zdCBlbmNvZGUgPSAoZikgPT4ge1xuICBjb25zdCBlbmNvZGVyID0gY3JlYXRlRW5jb2RlcigpXG4gIGYoZW5jb2RlcilcbiAgcmV0dXJuIHRvVWludDhBcnJheShlbmNvZGVyKVxufVxuXG4vKipcbiAqIFRoZSBjdXJyZW50IGxlbmd0aCBvZiB0aGUgZW5jb2RlZCBkYXRhLlxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcmV0dXJuIHtudW1iZXJ9XG4gKi9cbmV4cG9ydCBjb25zdCBsZW5ndGggPSBlbmNvZGVyID0+IHtcbiAgbGV0IGxlbiA9IGVuY29kZXIuY3Bvc1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGVuY29kZXIuYnVmcy5sZW5ndGg7IGkrKykge1xuICAgIGxlbiArPSBlbmNvZGVyLmJ1ZnNbaV0ubGVuZ3RoXG4gIH1cbiAgcmV0dXJuIGxlblxufVxuXG4vKipcbiAqIENoZWNrIHdoZXRoZXIgZW5jb2RlciBpcyBlbXB0eS5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHJldHVybiB7Ym9vbGVhbn1cbiAqL1xuZXhwb3J0IGNvbnN0IGhhc0NvbnRlbnQgPSBlbmNvZGVyID0+IGVuY29kZXIuY3BvcyA+IDAgfHwgZW5jb2Rlci5idWZzLmxlbmd0aCA+IDBcblxuLyoqXG4gKiBUcmFuc2Zvcm0gdG8gVWludDhBcnJheS5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHJldHVybiB7VWludDhBcnJheX0gVGhlIGNyZWF0ZWQgQXJyYXlCdWZmZXIuXG4gKi9cbmV4cG9ydCBjb25zdCB0b1VpbnQ4QXJyYXkgPSBlbmNvZGVyID0+IHtcbiAgY29uc3QgdWludDhhcnIgPSBuZXcgVWludDhBcnJheShsZW5ndGgoZW5jb2RlcikpXG4gIGxldCBjdXJQb3MgPSAwXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgZW5jb2Rlci5idWZzLmxlbmd0aDsgaSsrKSB7XG4gICAgY29uc3QgZCA9IGVuY29kZXIuYnVmc1tpXVxuICAgIHVpbnQ4YXJyLnNldChkLCBjdXJQb3MpXG4gICAgY3VyUG9zICs9IGQubGVuZ3RoXG4gIH1cbiAgdWludDhhcnIuc2V0KG5ldyBVaW50OEFycmF5KGVuY29kZXIuY2J1Zi5idWZmZXIsIDAsIGVuY29kZXIuY3BvcyksIGN1clBvcylcbiAgcmV0dXJuIHVpbnQ4YXJyXG59XG5cbi8qKlxuICogVmVyaWZ5IHRoYXQgaXQgaXMgcG9zc2libGUgdG8gd3JpdGUgYGxlbmAgYnl0ZXMgd3RpaG91dCBjaGVja2luZy4gSWZcbiAqIG5lY2Vzc2FyeSwgYSBuZXcgQnVmZmVyIHdpdGggdGhlIHJlcXVpcmVkIGxlbmd0aCBpcyBhdHRhY2hlZC5cbiAqXG4gKiBAcGFyYW0ge0VuY29kZXJ9IGVuY29kZXJcbiAqIEBwYXJhbSB7bnVtYmVyfSBsZW5cbiAqL1xuZXhwb3J0IGNvbnN0IHZlcmlmeUxlbiA9IChlbmNvZGVyLCBsZW4pID0+IHtcbiAgY29uc3QgYnVmZmVyTGVuID0gZW5jb2Rlci5jYnVmLmxlbmd0aFxuICBpZiAoYnVmZmVyTGVuIC0gZW5jb2Rlci5jcG9zIDwgbGVuKSB7XG4gICAgZW5jb2Rlci5idWZzLnB1c2gobmV3IFVpbnQ4QXJyYXkoZW5jb2Rlci5jYnVmLmJ1ZmZlciwgMCwgZW5jb2Rlci5jcG9zKSlcbiAgICBlbmNvZGVyLmNidWYgPSBuZXcgVWludDhBcnJheShtYXRoLm1heChidWZmZXJMZW4sIGxlbikgKiAyKVxuICAgIGVuY29kZXIuY3BvcyA9IDBcbiAgfVxufVxuXG4vKipcbiAqIFdyaXRlIG9uZSBieXRlIHRvIHRoZSBlbmNvZGVyLlxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge251bWJlcn0gbnVtIFRoZSBieXRlIHRoYXQgaXMgdG8gYmUgZW5jb2RlZC5cbiAqL1xuZXhwb3J0IGNvbnN0IHdyaXRlID0gKGVuY29kZXIsIG51bSkgPT4ge1xuICBjb25zdCBidWZmZXJMZW4gPSBlbmNvZGVyLmNidWYubGVuZ3RoXG4gIGlmIChlbmNvZGVyLmNwb3MgPT09IGJ1ZmZlckxlbikge1xuICAgIGVuY29kZXIuYnVmcy5wdXNoKGVuY29kZXIuY2J1ZilcbiAgICBlbmNvZGVyLmNidWYgPSBuZXcgVWludDhBcnJheShidWZmZXJMZW4gKiAyKVxuICAgIGVuY29kZXIuY3BvcyA9IDBcbiAgfVxuICBlbmNvZGVyLmNidWZbZW5jb2Rlci5jcG9zKytdID0gbnVtXG59XG5cbi8qKlxuICogV3JpdGUgb25lIGJ5dGUgYXQgYSBzcGVjaWZpYyBwb3NpdGlvbi5cbiAqIFBvc2l0aW9uIG11c3QgYWxyZWFkeSBiZSB3cml0dGVuIChpLmUuIGVuY29kZXIubGVuZ3RoID4gcG9zKVxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge251bWJlcn0gcG9zIFBvc2l0aW9uIHRvIHdoaWNoIHRvIHdyaXRlIGRhdGFcbiAqIEBwYXJhbSB7bnVtYmVyfSBudW0gVW5zaWduZWQgOC1iaXQgaW50ZWdlclxuICovXG5leHBvcnQgY29uc3Qgc2V0ID0gKGVuY29kZXIsIHBvcywgbnVtKSA9PiB7XG4gIGxldCBidWZmZXIgPSBudWxsXG4gIC8vIGl0ZXJhdGUgYWxsIGJ1ZmZlcnMgYW5kIGFkanVzdCBwb3NpdGlvblxuICBmb3IgKGxldCBpID0gMDsgaSA8IGVuY29kZXIuYnVmcy5sZW5ndGggJiYgYnVmZmVyID09PSBudWxsOyBpKyspIHtcbiAgICBjb25zdCBiID0gZW5jb2Rlci5idWZzW2ldXG4gICAgaWYgKHBvcyA8IGIubGVuZ3RoKSB7XG4gICAgICBidWZmZXIgPSBiIC8vIGZvdW5kIGJ1ZmZlclxuICAgIH0gZWxzZSB7XG4gICAgICBwb3MgLT0gYi5sZW5ndGhcbiAgICB9XG4gIH1cbiAgaWYgKGJ1ZmZlciA9PT0gbnVsbCkge1xuICAgIC8vIHVzZSBjdXJyZW50IGJ1ZmZlclxuICAgIGJ1ZmZlciA9IGVuY29kZXIuY2J1ZlxuICB9XG4gIGJ1ZmZlcltwb3NdID0gbnVtXG59XG5cbi8qKlxuICogV3JpdGUgb25lIGJ5dGUgYXMgYW4gdW5zaWduZWQgaW50ZWdlci5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtudW1iZXJ9IG51bSBUaGUgbnVtYmVyIHRoYXQgaXMgdG8gYmUgZW5jb2RlZC5cbiAqL1xuZXhwb3J0IGNvbnN0IHdyaXRlVWludDggPSB3cml0ZVxuXG4vKipcbiAqIFdyaXRlIG9uZSBieXRlIGFzIGFuIHVuc2lnbmVkIEludGVnZXIgYXQgYSBzcGVjaWZpYyBsb2NhdGlvbi5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtudW1iZXJ9IHBvcyBUaGUgbG9jYXRpb24gd2hlcmUgdGhlIGRhdGEgd2lsbCBiZSB3cml0dGVuLlxuICogQHBhcmFtIHtudW1iZXJ9IG51bSBUaGUgbnVtYmVyIHRoYXQgaXMgdG8gYmUgZW5jb2RlZC5cbiAqL1xuZXhwb3J0IGNvbnN0IHNldFVpbnQ4ID0gc2V0XG5cbi8qKlxuICogV3JpdGUgdHdvIGJ5dGVzIGFzIGFuIHVuc2lnbmVkIGludGVnZXIuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0VuY29kZXJ9IGVuY29kZXJcbiAqIEBwYXJhbSB7bnVtYmVyfSBudW0gVGhlIG51bWJlciB0aGF0IGlzIHRvIGJlIGVuY29kZWQuXG4gKi9cbmV4cG9ydCBjb25zdCB3cml0ZVVpbnQxNiA9IChlbmNvZGVyLCBudW0pID0+IHtcbiAgd3JpdGUoZW5jb2RlciwgbnVtICYgYmluYXJ5LkJJVFM4KVxuICB3cml0ZShlbmNvZGVyLCAobnVtID4+PiA4KSAmIGJpbmFyeS5CSVRTOClcbn1cbi8qKlxuICogV3JpdGUgdHdvIGJ5dGVzIGFzIGFuIHVuc2lnbmVkIGludGVnZXIgYXQgYSBzcGVjaWZpYyBsb2NhdGlvbi5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtudW1iZXJ9IHBvcyBUaGUgbG9jYXRpb24gd2hlcmUgdGhlIGRhdGEgd2lsbCBiZSB3cml0dGVuLlxuICogQHBhcmFtIHtudW1iZXJ9IG51bSBUaGUgbnVtYmVyIHRoYXQgaXMgdG8gYmUgZW5jb2RlZC5cbiAqL1xuZXhwb3J0IGNvbnN0IHNldFVpbnQxNiA9IChlbmNvZGVyLCBwb3MsIG51bSkgPT4ge1xuICBzZXQoZW5jb2RlciwgcG9zLCBudW0gJiBiaW5hcnkuQklUUzgpXG4gIHNldChlbmNvZGVyLCBwb3MgKyAxLCAobnVtID4+PiA4KSAmIGJpbmFyeS5CSVRTOClcbn1cblxuLyoqXG4gKiBXcml0ZSB0d28gYnl0ZXMgYXMgYW4gdW5zaWduZWQgaW50ZWdlclxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge251bWJlcn0gbnVtIFRoZSBudW1iZXIgdGhhdCBpcyB0byBiZSBlbmNvZGVkLlxuICovXG5leHBvcnQgY29uc3Qgd3JpdGVVaW50MzIgPSAoZW5jb2RlciwgbnVtKSA9PiB7XG4gIGZvciAobGV0IGkgPSAwOyBpIDwgNDsgaSsrKSB7XG4gICAgd3JpdGUoZW5jb2RlciwgbnVtICYgYmluYXJ5LkJJVFM4KVxuICAgIG51bSA+Pj49IDhcbiAgfVxufVxuXG4vKipcbiAqIFdyaXRlIHR3byBieXRlcyBhcyBhbiB1bnNpZ25lZCBpbnRlZ2VyIGluIGJpZyBlbmRpYW4gb3JkZXIuXG4gKiAobW9zdCBzaWduaWZpY2FudCBieXRlIGZpcnN0KVxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge251bWJlcn0gbnVtIFRoZSBudW1iZXIgdGhhdCBpcyB0byBiZSBlbmNvZGVkLlxuICovXG5leHBvcnQgY29uc3Qgd3JpdGVVaW50MzJCaWdFbmRpYW4gPSAoZW5jb2RlciwgbnVtKSA9PiB7XG4gIGZvciAobGV0IGkgPSAzOyBpID49IDA7IGktLSkge1xuICAgIHdyaXRlKGVuY29kZXIsIChudW0gPj4+ICg4ICogaSkpICYgYmluYXJ5LkJJVFM4KVxuICB9XG59XG5cbi8qKlxuICogV3JpdGUgdHdvIGJ5dGVzIGFzIGFuIHVuc2lnbmVkIGludGVnZXIgYXQgYSBzcGVjaWZpYyBsb2NhdGlvbi5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtudW1iZXJ9IHBvcyBUaGUgbG9jYXRpb24gd2hlcmUgdGhlIGRhdGEgd2lsbCBiZSB3cml0dGVuLlxuICogQHBhcmFtIHtudW1iZXJ9IG51bSBUaGUgbnVtYmVyIHRoYXQgaXMgdG8gYmUgZW5jb2RlZC5cbiAqL1xuZXhwb3J0IGNvbnN0IHNldFVpbnQzMiA9IChlbmNvZGVyLCBwb3MsIG51bSkgPT4ge1xuICBmb3IgKGxldCBpID0gMDsgaSA8IDQ7IGkrKykge1xuICAgIHNldChlbmNvZGVyLCBwb3MgKyBpLCBudW0gJiBiaW5hcnkuQklUUzgpXG4gICAgbnVtID4+Pj0gOFxuICB9XG59XG5cbi8qKlxuICogV3JpdGUgYSB2YXJpYWJsZSBsZW5ndGggdW5zaWduZWQgaW50ZWdlci4gTWF4IGVuY29kYWJsZSBpbnRlZ2VyIGlzIDJeNTMuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0VuY29kZXJ9IGVuY29kZXJcbiAqIEBwYXJhbSB7bnVtYmVyfSBudW0gVGhlIG51bWJlciB0aGF0IGlzIHRvIGJlIGVuY29kZWQuXG4gKi9cbmV4cG9ydCBjb25zdCB3cml0ZVZhclVpbnQgPSAoZW5jb2RlciwgbnVtKSA9PiB7XG4gIHdoaWxlIChudW0gPiBiaW5hcnkuQklUUzcpIHtcbiAgICB3cml0ZShlbmNvZGVyLCBiaW5hcnkuQklUOCB8IChiaW5hcnkuQklUUzcgJiBudW0pKVxuICAgIG51bSA9IG1hdGguZmxvb3IobnVtIC8gMTI4KSAvLyBzaGlmdCA+Pj4gN1xuICB9XG4gIHdyaXRlKGVuY29kZXIsIGJpbmFyeS5CSVRTNyAmIG51bSlcbn1cblxuLyoqXG4gKiBXcml0ZSBhIHZhcmlhYmxlIGxlbmd0aCBpbnRlZ2VyLlxuICpcbiAqIFdlIHVzZSB0aGUgN3RoIGJpdCBpbnN0ZWFkIGZvciBzaWduYWxpbmcgdGhhdCB0aGlzIGlzIGEgbmVnYXRpdmUgbnVtYmVyLlxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge251bWJlcn0gbnVtIFRoZSBudW1iZXIgdGhhdCBpcyB0byBiZSBlbmNvZGVkLlxuICovXG5leHBvcnQgY29uc3Qgd3JpdGVWYXJJbnQgPSAoZW5jb2RlciwgbnVtKSA9PiB7XG4gIGNvbnN0IGlzTmVnYXRpdmUgPSBtYXRoLmlzTmVnYXRpdmVaZXJvKG51bSlcbiAgaWYgKGlzTmVnYXRpdmUpIHtcbiAgICBudW0gPSAtbnVtXG4gIH1cbiAgLy8gICAgICAgICAgICAgfC0gd2hldGhlciB0byBjb250aW51ZSByZWFkaW5nICAgICAgICAgfC0gd2hldGhlciBpcyBuZWdhdGl2ZSAgICAgfC0gbnVtYmVyXG4gIHdyaXRlKGVuY29kZXIsIChudW0gPiBiaW5hcnkuQklUUzYgPyBiaW5hcnkuQklUOCA6IDApIHwgKGlzTmVnYXRpdmUgPyBiaW5hcnkuQklUNyA6IDApIHwgKGJpbmFyeS5CSVRTNiAmIG51bSkpXG4gIG51bSA9IG1hdGguZmxvb3IobnVtIC8gNjQpIC8vIHNoaWZ0ID4+PiA2XG4gIC8vIFdlIGRvbid0IG5lZWQgdG8gY29uc2lkZXIgdGhlIGNhc2Ugb2YgbnVtID09PSAwIHNvIHdlIGNhbiB1c2UgYSBkaWZmZXJlbnRcbiAgLy8gcGF0dGVybiBoZXJlIHRoYW4gYWJvdmUuXG4gIHdoaWxlIChudW0gPiAwKSB7XG4gICAgd3JpdGUoZW5jb2RlciwgKG51bSA+IGJpbmFyeS5CSVRTNyA/IGJpbmFyeS5CSVQ4IDogMCkgfCAoYmluYXJ5LkJJVFM3ICYgbnVtKSlcbiAgICBudW0gPSBtYXRoLmZsb29yKG51bSAvIDEyOCkgLy8gc2hpZnQgPj4+IDdcbiAgfVxufVxuXG4vKipcbiAqIEEgY2FjaGUgdG8gc3RvcmUgc3RyaW5ncyB0ZW1wb3JhcmlseVxuICovXG5jb25zdCBfc3RyQnVmZmVyID0gbmV3IFVpbnQ4QXJyYXkoMzAwMDApXG5jb25zdCBfbWF4U3RyQlNpemUgPSBfc3RyQnVmZmVyLmxlbmd0aCAvIDNcblxuLyoqXG4gKiBXcml0ZSBhIHZhcmlhYmxlIGxlbmd0aCBzdHJpbmcuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0VuY29kZXJ9IGVuY29kZXJcbiAqIEBwYXJhbSB7U3RyaW5nfSBzdHIgVGhlIHN0cmluZyB0aGF0IGlzIHRvIGJlIGVuY29kZWQuXG4gKi9cbmV4cG9ydCBjb25zdCBfd3JpdGVWYXJTdHJpbmdOYXRpdmUgPSAoZW5jb2Rlciwgc3RyKSA9PiB7XG4gIGlmIChzdHIubGVuZ3RoIDwgX21heFN0ckJTaXplKSB7XG4gICAgLy8gV2UgY2FuIGVuY29kZSB0aGUgc3RyaW5nIGludG8gdGhlIGV4aXN0aW5nIGJ1ZmZlclxuICAgIC8qIGM4IGlnbm9yZSBuZXh0ICovXG4gICAgY29uc3Qgd3JpdHRlbiA9IHN0cmluZy51dGY4VGV4dEVuY29kZXIuZW5jb2RlSW50byhzdHIsIF9zdHJCdWZmZXIpLndyaXR0ZW4gfHwgMFxuICAgIHdyaXRlVmFyVWludChlbmNvZGVyLCB3cml0dGVuKVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgd3JpdHRlbjsgaSsrKSB7XG4gICAgICB3cml0ZShlbmNvZGVyLCBfc3RyQnVmZmVyW2ldKVxuICAgIH1cbiAgfSBlbHNlIHtcbiAgICB3cml0ZVZhclVpbnQ4QXJyYXkoZW5jb2Rlciwgc3RyaW5nLmVuY29kZVV0Zjgoc3RyKSlcbiAgfVxufVxuXG4vKipcbiAqIFdyaXRlIGEgdmFyaWFibGUgbGVuZ3RoIHN0cmluZy5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtTdHJpbmd9IHN0ciBUaGUgc3RyaW5nIHRoYXQgaXMgdG8gYmUgZW5jb2RlZC5cbiAqL1xuZXhwb3J0IGNvbnN0IF93cml0ZVZhclN0cmluZ1BvbHlmaWxsID0gKGVuY29kZXIsIHN0cikgPT4ge1xuICBjb25zdCBlbmNvZGVkU3RyaW5nID0gdW5lc2NhcGUoZW5jb2RlVVJJQ29tcG9uZW50KHN0cikpXG4gIGNvbnN0IGxlbiA9IGVuY29kZWRTdHJpbmcubGVuZ3RoXG4gIHdyaXRlVmFyVWludChlbmNvZGVyLCBsZW4pXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgbGVuOyBpKyspIHtcbiAgICB3cml0ZShlbmNvZGVyLCAvKiogQHR5cGUge251bWJlcn0gKi8gKGVuY29kZWRTdHJpbmcuY29kZVBvaW50QXQoaSkpKVxuICB9XG59XG5cbi8qKlxuICogV3JpdGUgYSB2YXJpYWJsZSBsZW5ndGggc3RyaW5nLlxuICpcbiAqIEBmdW5jdGlvblxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge1N0cmluZ30gc3RyIFRoZSBzdHJpbmcgdGhhdCBpcyB0byBiZSBlbmNvZGVkLlxuICovXG4vKiBjOCBpZ25vcmUgbmV4dCAqL1xuZXhwb3J0IGNvbnN0IHdyaXRlVmFyU3RyaW5nID0gKHN0cmluZy51dGY4VGV4dEVuY29kZXIgJiYgLyoqIEB0eXBlIHthbnl9ICovIChzdHJpbmcudXRmOFRleHRFbmNvZGVyKS5lbmNvZGVJbnRvKSA/IF93cml0ZVZhclN0cmluZ05hdGl2ZSA6IF93cml0ZVZhclN0cmluZ1BvbHlmaWxsXG5cbi8qKlxuICogV3JpdGUgYSBzdHJpbmcgdGVybWluYXRlZCBieSBhIHNwZWNpYWwgYnl0ZSBzZXF1ZW5jZS4gVGhpcyBpcyBub3QgdmVyeSBwZXJmb3JtYW50IGFuZCBpc1xuICogZ2VuZXJhbGx5IGRpc2NvdXJhZ2VkLiBIb3dldmVyLCB0aGUgcmVzdWx0aW5nIGJ5dGUgYXJyYXlzIGFyZSBsZXhpb2dyYXBoaWNhbGx5IG9yZGVyZWQgd2hpY2hcbiAqIG1ha2VzIHRoaXMgYSBuaWNlIGZlYXR1cmUgZm9yIGRhdGFiYXNlcy5cbiAqXG4gKiBUaGUgc3RyaW5nIHdpbGwgYmUgZW5jb2RlZCB1c2luZyB1dGY4IGFuZCB0aGVuIHRlcm1pbmF0ZWQgYW5kIGVzY2FwZWQgdXNpbmcgd3JpdGVUZXJtaW5hdGluZ1VpbnQ4QXJyYXkuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0VuY29kZXJ9IGVuY29kZXJcbiAqIEBwYXJhbSB7U3RyaW5nfSBzdHIgVGhlIHN0cmluZyB0aGF0IGlzIHRvIGJlIGVuY29kZWQuXG4gKi9cbmV4cG9ydCBjb25zdCB3cml0ZVRlcm1pbmF0ZWRTdHJpbmcgPSAoZW5jb2Rlciwgc3RyKSA9PlxuICB3cml0ZVRlcm1pbmF0ZWRVaW50OEFycmF5KGVuY29kZXIsIHN0cmluZy5lbmNvZGVVdGY4KHN0cikpXG5cbi8qKlxuICogV3JpdGUgYSB0ZXJtaW5hdGluZyBVaW50OEFycmF5LiBOb3RlIHRoYXQgdGhpcyBpcyBub3QgcGVyZm9ybWFudCBhbmQgaXMgZ2VuZXJhbGx5XG4gKiBkaXNjb3VyYWdlZC4gVGhlcmUgYXJlIGZldyBzaXR1YXRpb25zIHdoZW4gdGhpcyBpcyBuZWVkZWQuXG4gKlxuICogV2UgdXNlIDB4MCBhcyBhIHRlcm1pbmF0aW5nIGNoYXJhY3Rlci4gMHgxIHNlcnZlcyBhcyBhbiBlc2NhcGUgY2hhcmFjdGVyIGZvciAweDAgYW5kIDB4MS5cbiAqXG4gKiBFeGFtcGxlOiBbMCwxLDJdIGlzIGVuY29kZWQgdG8gWzEsMCwxLDEsMiwwXS4gMHgwLCBhbmQgMHgxIG5lZWRlZCB0byBiZSBlc2NhcGVkIHVzaW5nIDB4MS4gVGhlblxuICogdGhlIHJlc3VsdCBpcyB0ZXJtaW5hdGVkIHVzaW5nIHRoZSAweDAgY2hhcmFjdGVyLlxuICpcbiAqIFRoaXMgaXMgYmFzaWNhbGx5IGhvdyBtYW55IHN5c3RlbXMgaW1wbGVtZW50IG51bGwgdGVybWluYXRlZCBzdHJpbmdzLiBIb3dldmVyLCB3ZSB1c2UgYW4gZXNjYXBlXG4gKiBjaGFyYWN0ZXIgMHgxIHRvIGF2b2lkIGlzc3VlcyBhbmQgcG90ZW5pYWwgYXR0YWNrcyBvbiBvdXIgZGF0YWJhc2UgKGlmIHRoaXMgaXMgdXNlZCBhcyBhIGtleVxuICogZW5jb2RlciBmb3IgTm9TcWwgZGF0YWJhc2VzKS5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtVaW50OEFycmF5fSBidWYgVGhlIHN0cmluZyB0aGF0IGlzIHRvIGJlIGVuY29kZWQuXG4gKi9cbmV4cG9ydCBjb25zdCB3cml0ZVRlcm1pbmF0ZWRVaW50OEFycmF5ID0gKGVuY29kZXIsIGJ1ZikgPT4ge1xuICBmb3IgKGxldCBpID0gMDsgaSA8IGJ1Zi5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGIgPSBidWZbaV1cbiAgICBpZiAoYiA9PT0gMCB8fCBiID09PSAxKSB7XG4gICAgICB3cml0ZShlbmNvZGVyLCAxKVxuICAgIH1cbiAgICB3cml0ZShlbmNvZGVyLCBidWZbaV0pXG4gIH1cbiAgd3JpdGUoZW5jb2RlciwgMClcbn1cblxuLyoqXG4gKiBXcml0ZSB0aGUgY29udGVudCBvZiBhbm90aGVyIEVuY29kZXIuXG4gKlxuICogQFRPRE86IGNhbiBiZSBpbXByb3ZlZCFcbiAqICAgICAgICAtIE5vdGU6IFNob3VsZCBjb25zaWRlciB0aGF0IHdoZW4gYXBwZW5kaW5nIGEgbG90IG9mIHNtYWxsIEVuY29kZXJzLCB3ZSBzaG91bGQgcmF0aGVyIGNsb25lIHRoYW4gcmVmZXJlbmNpbmcgdGhlIG9sZCBzdHJ1Y3R1cmUuXG4gKiAgICAgICAgICAgICAgICBFbmNvZGVycyBzdGFydCB3aXRoIGEgcmF0aGVyIGJpZyBpbml0aWFsIGJ1ZmZlci5cbiAqXG4gKiBAZnVuY3Rpb25cbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlciBUaGUgZW5VaW50OEFyclxuICogQHBhcmFtIHtFbmNvZGVyfSBhcHBlbmQgVGhlIEJpbmFyeUVuY29kZXIgdG8gYmUgd3JpdHRlbi5cbiAqL1xuZXhwb3J0IGNvbnN0IHdyaXRlQmluYXJ5RW5jb2RlciA9IChlbmNvZGVyLCBhcHBlbmQpID0+IHdyaXRlVWludDhBcnJheShlbmNvZGVyLCB0b1VpbnQ4QXJyYXkoYXBwZW5kKSlcblxuLyoqXG4gKiBBcHBlbmQgZml4ZWQtbGVuZ3RoIFVpbnQ4QXJyYXkgdG8gdGhlIGVuY29kZXIuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0VuY29kZXJ9IGVuY29kZXJcbiAqIEBwYXJhbSB7VWludDhBcnJheX0gdWludDhBcnJheVxuICovXG5leHBvcnQgY29uc3Qgd3JpdGVVaW50OEFycmF5ID0gKGVuY29kZXIsIHVpbnQ4QXJyYXkpID0+IHtcbiAgY29uc3QgYnVmZmVyTGVuID0gZW5jb2Rlci5jYnVmLmxlbmd0aFxuICBjb25zdCBjcG9zID0gZW5jb2Rlci5jcG9zXG4gIGNvbnN0IGxlZnRDb3B5TGVuID0gbWF0aC5taW4oYnVmZmVyTGVuIC0gY3BvcywgdWludDhBcnJheS5sZW5ndGgpXG4gIGNvbnN0IHJpZ2h0Q29weUxlbiA9IHVpbnQ4QXJyYXkubGVuZ3RoIC0gbGVmdENvcHlMZW5cbiAgZW5jb2Rlci5jYnVmLnNldCh1aW50OEFycmF5LnN1YmFycmF5KDAsIGxlZnRDb3B5TGVuKSwgY3BvcylcbiAgZW5jb2Rlci5jcG9zICs9IGxlZnRDb3B5TGVuXG4gIGlmIChyaWdodENvcHlMZW4gPiAwKSB7XG4gICAgLy8gU3RpbGwgc29tZXRoaW5nIHRvIHdyaXRlLCB3cml0ZSByaWdodCBoYWxmLi5cbiAgICAvLyBBcHBlbmQgbmV3IGJ1ZmZlclxuICAgIGVuY29kZXIuYnVmcy5wdXNoKGVuY29kZXIuY2J1ZilcbiAgICAvLyBtdXN0IGhhdmUgYXQgbGVhc3Qgc2l6ZSBvZiByZW1haW5pbmcgYnVmZmVyXG4gICAgZW5jb2Rlci5jYnVmID0gbmV3IFVpbnQ4QXJyYXkobWF0aC5tYXgoYnVmZmVyTGVuICogMiwgcmlnaHRDb3B5TGVuKSlcbiAgICAvLyBjb3B5IGFycmF5XG4gICAgZW5jb2Rlci5jYnVmLnNldCh1aW50OEFycmF5LnN1YmFycmF5KGxlZnRDb3B5TGVuKSlcbiAgICBlbmNvZGVyLmNwb3MgPSByaWdodENvcHlMZW5cbiAgfVxufVxuXG4vKipcbiAqIEFwcGVuZCBhbiBVaW50OEFycmF5IHRvIEVuY29kZXIuXG4gKlxuICogQGZ1bmN0aW9uXG4gKiBAcGFyYW0ge0VuY29kZXJ9IGVuY29kZXJcbiAqIEBwYXJhbSB7VWludDhBcnJheX0gdWludDhBcnJheVxuICovXG5leHBvcnQgY29uc3Qgd3JpdGVWYXJVaW50OEFycmF5ID0gKGVuY29kZXIsIHVpbnQ4QXJyYXkpID0+IHtcbiAgd3JpdGVWYXJVaW50KGVuY29kZXIsIHVpbnQ4QXJyYXkuYnl0ZUxlbmd0aClcbiAgd3JpdGVVaW50OEFycmF5KGVuY29kZXIsIHVpbnQ4QXJyYXkpXG59XG5cbi8qKlxuICogQ3JlYXRlIGFuIERhdGFWaWV3IG9mIHRoZSBuZXh0IGBsZW5gIGJ5dGVzLiBVc2UgaXQgdG8gd3JpdGUgZGF0YSBhZnRlclxuICogY2FsbGluZyB0aGlzIGZ1bmN0aW9uLlxuICpcbiAqIGBgYGpzXG4gKiAvLyB3cml0ZSBmbG9hdDMyIHVzaW5nIERhdGFWaWV3XG4gKiBjb25zdCBkdiA9IHdyaXRlT25EYXRhVmlldyhlbmNvZGVyLCA0KVxuICogZHYuc2V0RmxvYXQzMigwLCAxLjEpXG4gKiAvLyByZWFkIGZsb2F0MzIgdXNpbmcgRGF0YVZpZXdcbiAqIGNvbnN0IGR2ID0gcmVhZEZyb21EYXRhVmlldyhlbmNvZGVyLCA0KVxuICogZHYuZ2V0RmxvYXQzMigwKSAvLyA9PiAxLjEwMDAwMDAyMzg0MTg1OCAobGVhdmluZyBpdCB0byB0aGUgcmVhZGVyIHRvIGZpbmQgb3V0IHdoeSB0aGlzIGlzIHRoZSBjb3JyZWN0IHJlc3VsdClcbiAqIGBgYFxuICpcbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtudW1iZXJ9IGxlblxuICogQHJldHVybiB7RGF0YVZpZXd9XG4gKi9cbmV4cG9ydCBjb25zdCB3cml0ZU9uRGF0YVZpZXcgPSAoZW5jb2RlciwgbGVuKSA9PiB7XG4gIHZlcmlmeUxlbihlbmNvZGVyLCBsZW4pXG4gIGNvbnN0IGR2aWV3ID0gbmV3IERhdGFWaWV3KGVuY29kZXIuY2J1Zi5idWZmZXIsIGVuY29kZXIuY3BvcywgbGVuKVxuICBlbmNvZGVyLmNwb3MgKz0gbGVuXG4gIHJldHVybiBkdmlld1xufVxuXG4vKipcbiAqIEBwYXJhbSB7RW5jb2Rlcn0gZW5jb2RlclxuICogQHBhcmFtIHtudW1iZXJ9IG51bVxuICovXG5leHBvcnQgY29uc3Qgd3JpdGVGbG9hdDMyID0gKGVuY29kZXIsIG51bSkgPT4gd3JpdGVPbkRhdGFWaWV3KGVuY29kZXIsIDQpLnNldEZsb2F0MzIoMCwgbnVtLCBmYWxzZSlcblxuLyoqXG4gKiBAcGFyYW0ge0VuY29kZXJ9IGVuY29kZXJcbiAqIEBwYXJhbSB7bnVtYmVyfSBudW1cbiAqL1xuZXhwb3J0IGNvbnN0IHdyaXRlRmxvYXQ2NCA9IChlbmNvZGVyLCBudW0pID0+IHdyaXRlT25EYXRhVmlldyhlbmNvZGVyLCA4KS5zZXRGbG9hdDY0KDAsIG51bSwgZmFsc2UpXG5cbi8qKlxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge2JpZ2ludH0gbnVtXG4gKi9cbmV4cG9ydCBjb25zdCB3cml0ZUJpZ0ludDY0ID0gKGVuY29kZXIsIG51bSkgPT4gLyoqIEB0eXBlIHthbnl9ICovICh3cml0ZU9uRGF0YVZpZXcoZW5jb2RlciwgOCkpLnNldEJpZ0ludDY0KDAsIG51bSwgZmFsc2UpXG5cbi8qKlxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge2JpZ2ludH0gbnVtXG4gKi9cbmV4cG9ydCBjb25zdCB3cml0ZUJpZ1VpbnQ2NCA9IChlbmNvZGVyLCBudW0pID0+IC8qKiBAdHlwZSB7YW55fSAqLyAod3JpdGVPbkRhdGFWaWV3KGVuY29kZXIsIDgpKS5zZXRCaWdVaW50NjQoMCwgbnVtLCBmYWxzZSlcblxuY29uc3QgZmxvYXRUZXN0QmVkID0gbmV3IERhdGFWaWV3KG5ldyBBcnJheUJ1ZmZlcig0KSlcbi8qKlxuICogQ2hlY2sgaWYgYSBudW1iZXIgY2FuIGJlIGVuY29kZWQgYXMgYSAzMiBiaXQgZmxvYXQuXG4gKlxuICogQHBhcmFtIHtudW1iZXJ9IG51bVxuICogQHJldHVybiB7Ym9vbGVhbn1cbiAqL1xuY29uc3QgaXNGbG9hdDMyID0gbnVtID0+IHtcbiAgZmxvYXRUZXN0QmVkLnNldEZsb2F0MzIoMCwgbnVtKVxuICByZXR1cm4gZmxvYXRUZXN0QmVkLmdldEZsb2F0MzIoMCkgPT09IG51bVxufVxuXG4vKipcbiAqIEVuY29kZSBkYXRhIHdpdGggZWZmaWNpZW50IGJpbmFyeSBmb3JtYXQuXG4gKlxuICogRGlmZmVyZW5jZXMgdG8gSlNPTjpcbiAqIOKAoiBUcmFuc2Zvcm1zIGRhdGEgdG8gYSBiaW5hcnkgZm9ybWF0IChub3QgdG8gYSBzdHJpbmcpXG4gKiDigKIgRW5jb2RlcyB1bmRlZmluZWQsIE5hTiwgYW5kIEFycmF5QnVmZmVyICh0aGVzZSBjYW4ndCBiZSByZXByZXNlbnRlZCBpbiBKU09OKVxuICog4oCiIE51bWJlcnMgYXJlIGVmZmljaWVudGx5IGVuY29kZWQgZWl0aGVyIGFzIGEgdmFyaWFibGUgbGVuZ3RoIGludGVnZXIsIGFzIGFcbiAqICAgMzIgYml0IGZsb2F0LCBhcyBhIDY0IGJpdCBmbG9hdCwgb3IgYXMgYSA2NCBiaXQgYmlnaW50LlxuICpcbiAqIEVuY29kaW5nIHRhYmxlOlxuICpcbiAqIHwgRGF0YSBUeXBlICAgICAgICAgICB8IFByZWZpeCAgIHwgRW5jb2RpbmcgTWV0aG9kICAgIHwgQ29tbWVudCB8XG4gKiB8IC0tLS0tLS0tLS0tLS0tLS0tLS0gfCAtLS0tLS0tLSB8IC0tLS0tLS0tLS0tLS0tLS0tLSB8IC0tLS0tLS0gfFxuICogfCB1bmRlZmluZWQgICAgICAgICAgIHwgMTI3ICAgICAgfCAgICAgICAgICAgICAgICAgICAgfCBGdW5jdGlvbnMsIHN5bWJvbCwgYW5kIGV2ZXJ5dGhpbmcgdGhhdCBjYW5ub3QgYmUgaWRlbnRpZmllZCBpcyBlbmNvZGVkIGFzIHVuZGVmaW5lZCB8XG4gKiB8IG51bGwgICAgICAgICAgICAgICAgfCAxMjYgICAgICB8ICAgICAgICAgICAgICAgICAgICB8IHxcbiAqIHwgaW50ZWdlciAgICAgICAgICAgICB8IDEyNSAgICAgIHwgd3JpdGVWYXJJbnQgICAgICAgIHwgT25seSBlbmNvZGVzIDMyIGJpdCBzaWduZWQgaW50ZWdlcnMgfFxuICogfCBmbG9hdDMyICAgICAgICAgICAgIHwgMTI0ICAgICAgfCB3cml0ZUZsb2F0MzIgICAgICAgfCB8XG4gKiB8IGZsb2F0NjQgICAgICAgICAgICAgfCAxMjMgICAgICB8IHdyaXRlRmxvYXQ2NCAgICAgICB8IHxcbiAqIHwgYmlnaW50ICAgICAgICAgICAgICB8IDEyMiAgICAgIHwgd3JpdGVCaWdJbnQ2NCAgICAgIHwgfFxuICogfCBib29sZWFuIChmYWxzZSkgICAgIHwgMTIxICAgICAgfCAgICAgICAgICAgICAgICAgICAgfCBUcnVlIGFuZCBmYWxzZSBhcmUgZGlmZmVyZW50IGRhdGEgdHlwZXMgc28gd2Ugc2F2ZSB0aGUgZm9sbG93aW5nIGJ5dGUgfFxuICogfCBib29sZWFuICh0cnVlKSAgICAgIHwgMTIwICAgICAgfCAgICAgICAgICAgICAgICAgICAgfCAtIDBiMDExMTEwMDAgc28gdGhlIGxhc3QgYml0IGRldGVybWluZXMgd2hldGhlciB0cnVlIG9yIGZhbHNlIHxcbiAqIHwgc3RyaW5nICAgICAgICAgICAgICB8IDExOSAgICAgIHwgd3JpdGVWYXJTdHJpbmcgICAgIHwgfFxuICogfCBvYmplY3Q8c3RyaW5nLGFueT4gIHwgMTE4ICAgICAgfCBjdXN0b20gICAgICAgICAgICAgfCBXcml0ZXMge2xlbmd0aH0gdGhlbiB7bGVuZ3RofSBrZXktdmFsdWUgcGFpcnMgfFxuICogfCBhcnJheTxhbnk+ICAgICAgICAgIHwgMTE3ICAgICAgfCBjdXN0b20gICAgICAgICAgICAgfCBXcml0ZXMge2xlbmd0aH0gdGhlbiB7bGVuZ3RofSBqc29uIHZhbHVlcyB8XG4gKiB8IFVpbnQ4QXJyYXkgICAgICAgICAgfCAxMTYgICAgICB8IHdyaXRlVmFyVWludDhBcnJheSB8IFdlIHVzZSBVaW50OEFycmF5IGZvciBhbnkga2luZCBvZiBiaW5hcnkgZGF0YSB8XG4gKlxuICogUmVhc29ucyBmb3IgdGhlIGRlY3JlYXNpbmcgcHJlZml4OlxuICogV2UgbmVlZCB0aGUgZmlyc3QgYml0IGZvciBleHRlbmRhYmlsaXR5IChsYXRlciB3ZSBtYXkgd2FudCB0byBlbmNvZGUgdGhlXG4gKiBwcmVmaXggd2l0aCB3cml0ZVZhclVpbnQpLiBUaGUgcmVtYWluaW5nIDcgYml0cyBhcmUgZGl2aWRlZCBhcyBmb2xsb3dzOlxuICogWzAtMzBdICAgdGhlIGJlZ2lubmluZyBvZiB0aGUgZGF0YSByYW5nZSBpcyB1c2VkIGZvciBjdXN0b20gcHVycG9zZXNcbiAqICAgICAgICAgIChkZWZpbmVkIGJ5IHRoZSBmdW5jdGlvbiB0aGF0IHVzZXMgdGhpcyBsaWJyYXJ5KVxuICogWzMxLTEyN10gdGhlIGVuZCBvZiB0aGUgZGF0YSByYW5nZSBpcyB1c2VkIGZvciBkYXRhIGVuY29kaW5nIGJ5XG4gKiAgICAgICAgICBsaWIwL2VuY29kaW5nLmpzXG4gKlxuICogQHBhcmFtIHtFbmNvZGVyfSBlbmNvZGVyXG4gKiBAcGFyYW0ge3VuZGVmaW5lZHxudWxsfG51bWJlcnxiaWdpbnR8Ym9vbGVhbnxzdHJpbmd8T2JqZWN0PHN0cmluZyxhbnk+fEFycmF5PGFueT58VWludDhBcnJheX0gZGF0YVxuICovXG5leHBvcnQgY29uc3Qgd3JpdGVBbnkgPSAoZW5jb2RlciwgZGF0YSkgPT4ge1xuICBzd2l0Y2ggKHR5cGVvZiBkYXRhKSB7XG4gICAgY2FzZSAnc3RyaW5nJzpcbiAgICAgIC8vIFRZUEUgMTE5OiBTVFJJTkdcbiAgICAgIHdyaXRlKGVuY29kZXIsIDExOSlcbiAgICAgIHdyaXRlVmFyU3RyaW5nKGVuY29kZXIsIGRhdGEpXG4gICAgICBicmVha1xuICAgIGNhc2UgJ251bWJlcic6XG4gICAgICBpZiAobnVtYmVyLmlzSW50ZWdlcihkYXRhKSAmJiBtYXRoLmFicyhkYXRhKSA8PSBiaW5hcnkuQklUUzMxKSB7XG4gICAgICAgIC8vIFRZUEUgMTI1OiBJTlRFR0VSXG4gICAgICAgIHdyaXRlKGVuY29kZXIsIDEyNSlcbiAgICAgICAgd3JpdGVWYXJJbnQoZW5jb2RlciwgZGF0YSlcbiAgICAgIH0gZWxzZSBpZiAoaXNGbG9hdDMyKGRhdGEpKSB7XG4gICAgICAgIC8vIFRZUEUgMTI0OiBGTE9BVDMyXG4gICAgICAgIHdyaXRlKGVuY29kZXIsIDEyNClcbiAgICAgICAgd3JpdGVGbG9hdDMyKGVuY29kZXIsIGRhdGEpXG4gICAgICB9IGVsc2Uge1xuICAgICAgICAvLyBUWVBFIDEyMzogRkxPQVQ2NFxuICAgICAgICB3cml0ZShlbmNvZGVyLCAxMjMpXG4gICAgICAgIHdyaXRlRmxvYXQ2NChlbmNvZGVyLCBkYXRhKVxuICAgICAgfVxuICAgICAgYnJlYWtcbiAgICBjYXNlICdiaWdpbnQnOlxuICAgICAgLy8gVFlQRSAxMjI6IEJpZ0ludFxuICAgICAgd3JpdGUoZW5jb2RlciwgMTIyKVxuICAgICAgd3JpdGVCaWdJbnQ2NChlbmNvZGVyLCBkYXRhKVxuICAgICAgYnJlYWtcbiAgICBjYXNlICdvYmplY3QnOlxuICAgICAgaWYgKGRhdGEgPT09IG51bGwpIHtcbiAgICAgICAgLy8gVFlQRSAxMjY6IG51bGxcbiAgICAgICAgd3JpdGUoZW5jb2RlciwgMTI2KVxuICAgICAgfSBlbHNlIGlmIChhcnJheS5pc0FycmF5KGRhdGEpKSB7XG4gICAgICAgIC8vIFRZUEUgMTE3OiBBcnJheVxuICAgICAgICB3cml0ZShlbmNvZGVyLCAxMTcpXG4gICAgICAgIHdyaXRlVmFyVWludChlbmNvZGVyLCBkYXRhLmxlbmd0aClcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBkYXRhLmxlbmd0aDsgaSsrKSB7XG4gICAgICAgICAgd3JpdGVBbnkoZW5jb2RlciwgZGF0YVtpXSlcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChkYXRhIGluc3RhbmNlb2YgVWludDhBcnJheSkge1xuICAgICAgICAvLyBUWVBFIDExNjogQXJyYXlCdWZmZXJcbiAgICAgICAgd3JpdGUoZW5jb2RlciwgMTE2KVxuICAgICAgICB3cml0ZVZhclVpbnQ4QXJyYXkoZW5jb2RlciwgZGF0YSlcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIC8vIFRZUEUgMTE4OiBPYmplY3RcbiAgICAgICAgd3JpdGUoZW5jb2RlciwgMTE4KVxuICAgICAgICBjb25zdCBrZXlzID0gT2JqZWN0LmtleXMoZGF0YSlcbiAgICAgICAgd3JpdGVWYXJVaW50KGVuY29kZXIsIGtleXMubGVuZ3RoKVxuICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGtleXMubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICBjb25zdCBrZXkgPSBrZXlzW2ldXG4gICAgICAgICAgd3JpdGVWYXJTdHJpbmcoZW5jb2Rlciwga2V5KVxuICAgICAgICAgIHdyaXRlQW55KGVuY29kZXIsIGRhdGFba2V5XSlcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgYnJlYWtcbiAgICBjYXNlICdib29sZWFuJzpcbiAgICAgIC8vIFRZUEUgMTIwLzEyMTogYm9vbGVhbiAodHJ1ZS9mYWxzZSlcbiAgICAgIHdyaXRlKGVuY29kZXIsIGRhdGEgPyAxMjAgOiAxMjEpXG4gICAgICBicmVha1xuICAgIGRlZmF1bHQ6XG4gICAgICAvLyBUWVBFIDEyNzogdW5kZWZpbmVkXG4gICAgICB3cml0ZShlbmNvZGVyLCAxMjcpXG4gIH1cbn1cblxuLyoqXG4gKiBOb3cgY29tZSBhIGZldyBzdGF0ZWZ1bCBlbmNvZGVyIHRoYXQgaGF2ZSB0aGVpciBvd24gY2xhc3Nlcy5cbiAqL1xuXG4vKipcbiAqIEJhc2ljIFJ1biBMZW5ndGggRW5jb2RlciAtIGEgYmFzaWMgY29tcHJlc3Npb24gaW1wbGVtZW50YXRpb24uXG4gKlxuICogRW5jb2RlcyBbMSwxLDEsN10gdG8gWzEsMyw3LDFdICgzIHRpbWVzIDEsIDEgdGltZSA3KS4gVGhpcyBlbmNvZGVyIG1pZ2h0IGRvIG1vcmUgaGFybSB0aGFuIGdvb2QgaWYgdGhlcmUgYXJlIGEgbG90IG9mIHZhbHVlcyB0aGF0IGFyZSBub3QgcmVwZWF0ZWQuXG4gKlxuICogSXQgd2FzIG9yaWdpbmFsbHkgdXNlZCBmb3IgaW1hZ2UgY29tcHJlc3Npb24uIENvb2wgLi4gYXJ0aWNsZSBodHRwOi8vY3NicnVjZS5jb20vY2JtL3RyYW5zYWN0b3IvcGRmcy90cmFuc192N19pMDYucGRmXG4gKlxuICogQG5vdGUgVCBtdXN0IG5vdCBiZSBudWxsIVxuICpcbiAqIEB0ZW1wbGF0ZSBUXG4gKi9cbmV4cG9ydCBjbGFzcyBSbGVFbmNvZGVyIGV4dGVuZHMgRW5jb2RlciB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge2Z1bmN0aW9uKEVuY29kZXIsIFQpOnZvaWR9IHdyaXRlclxuICAgKi9cbiAgY29uc3RydWN0b3IgKHdyaXRlcikge1xuICAgIHN1cGVyKClcbiAgICAvKipcbiAgICAgKiBUaGUgd3JpdGVyXG4gICAgICovXG4gICAgdGhpcy53ID0gd3JpdGVyXG4gICAgLyoqXG4gICAgICogQ3VycmVudCBzdGF0ZVxuICAgICAqIEB0eXBlIHtUfG51bGx9XG4gICAgICovXG4gICAgdGhpcy5zID0gbnVsbFxuICAgIHRoaXMuY291bnQgPSAwXG4gIH1cblxuICAvKipcbiAgICogQHBhcmFtIHtUfSB2XG4gICAqL1xuICB3cml0ZSAodikge1xuICAgIGlmICh0aGlzLnMgPT09IHYpIHtcbiAgICAgIHRoaXMuY291bnQrK1xuICAgIH0gZWxzZSB7XG4gICAgICBpZiAodGhpcy5jb3VudCA+IDApIHtcbiAgICAgICAgLy8gZmx1c2ggY291bnRlciwgdW5sZXNzIHRoaXMgaXMgdGhlIGZpcnN0IHZhbHVlIChjb3VudCA9IDApXG4gICAgICAgIHdyaXRlVmFyVWludCh0aGlzLCB0aGlzLmNvdW50IC0gMSkgLy8gc2luY2UgY291bnQgaXMgYWx3YXlzID4gMCwgd2UgY2FuIGRlY3JlbWVudCBieSBvbmUuIG5vbi1zdGFuZGFyZCBlbmNvZGluZyBmdHdcbiAgICAgIH1cbiAgICAgIHRoaXMuY291bnQgPSAxXG4gICAgICAvLyB3cml0ZSBmaXJzdCB2YWx1ZVxuICAgICAgdGhpcy53KHRoaXMsIHYpXG4gICAgICB0aGlzLnMgPSB2XG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogQmFzaWMgZGlmZiBkZWNvZGVyIHVzaW5nIHZhcmlhYmxlIGxlbmd0aCBlbmNvZGluZy5cbiAqXG4gKiBFbmNvZGVzIHRoZSB2YWx1ZXMgWzMsIDExMDAsIDExMDEsIDEwNTAsIDBdIHRvIFszLCAxMDk3LCAxLCAtNTEsIC0xMDUwXSB1c2luZyB3cml0ZVZhckludC5cbiAqL1xuZXhwb3J0IGNsYXNzIEludERpZmZFbmNvZGVyIGV4dGVuZHMgRW5jb2RlciB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge251bWJlcn0gc3RhcnRcbiAgICovXG4gIGNvbnN0cnVjdG9yIChzdGFydCkge1xuICAgIHN1cGVyKClcbiAgICAvKipcbiAgICAgKiBDdXJyZW50IHN0YXRlXG4gICAgICogQHR5cGUge251bWJlcn1cbiAgICAgKi9cbiAgICB0aGlzLnMgPSBzdGFydFxuICB9XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7bnVtYmVyfSB2XG4gICAqL1xuICB3cml0ZSAodikge1xuICAgIHdyaXRlVmFySW50KHRoaXMsIHYgLSB0aGlzLnMpXG4gICAgdGhpcy5zID0gdlxuICB9XG59XG5cbi8qKlxuICogQSBjb21iaW5hdGlvbiBvZiBJbnREaWZmRW5jb2RlciBhbmQgUmxlRW5jb2Rlci5cbiAqXG4gKiBCYXNpY2FsbHkgZmlyc3Qgd3JpdGVzIHRoZSBJbnREaWZmRW5jb2RlciBhbmQgdGhlbiBjb3VudHMgZHVwbGljYXRlIGRpZmZzIHVzaW5nIFJsZUVuY29kaW5nLlxuICpcbiAqIEVuY29kZXMgdGhlIHZhbHVlcyBbMSwxLDEsMiwzLDQsNSw2XSBhcyBbMSwxLDAsMiwxLDVdIChSTEUoWzEsMCwwLDEsMSwxLDEsMV0pIOKHkiBSbGVJbnREaWZmWzEsMSwwLDIsMSw1XSlcbiAqL1xuZXhwb3J0IGNsYXNzIFJsZUludERpZmZFbmNvZGVyIGV4dGVuZHMgRW5jb2RlciB7XG4gIC8qKlxuICAgKiBAcGFyYW0ge251bWJlcn0gc3RhcnRcbiAgICovXG4gIGNvbnN0cnVjdG9yIChzdGFydCkge1xuICAgIHN1cGVyKClcbiAgICAvKipcbiAgICAgKiBDdXJyZW50IHN0YXRlXG4gICAgICogQHR5cGUge251bWJlcn1cbiAgICAgKi9cbiAgICB0aGlzLnMgPSBzdGFydFxuICAgIHRoaXMuY291bnQgPSAwXG4gIH1cblxuICAvKipcbiAgICogQHBhcmFtIHtudW1iZXJ9IHZcbiAgICovXG4gIHdyaXRlICh2KSB7XG4gICAgaWYgKHRoaXMucyA9PT0gdiAmJiB0aGlzLmNvdW50ID4gMCkge1xuICAgICAgdGhpcy5jb3VudCsrXG4gICAgfSBlbHNlIHtcbiAgICAgIGlmICh0aGlzLmNvdW50ID4gMCkge1xuICAgICAgICAvLyBmbHVzaCBjb3VudGVyLCB1bmxlc3MgdGhpcyBpcyB0aGUgZmlyc3QgdmFsdWUgKGNvdW50ID0gMClcbiAgICAgICAgd3JpdGVWYXJVaW50KHRoaXMsIHRoaXMuY291bnQgLSAxKSAvLyBzaW5jZSBjb3VudCBpcyBhbHdheXMgPiAwLCB3ZSBjYW4gZGVjcmVtZW50IGJ5IG9uZS4gbm9uLXN0YW5kYXJkIGVuY29kaW5nIGZ0d1xuICAgICAgfVxuICAgICAgdGhpcy5jb3VudCA9IDFcbiAgICAgIC8vIHdyaXRlIGZpcnN0IHZhbHVlXG4gICAgICB3cml0ZVZhckludCh0aGlzLCB2IC0gdGhpcy5zKVxuICAgICAgdGhpcy5zID0gdlxuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIEBwYXJhbSB7VWludE9wdFJsZUVuY29kZXJ9IGVuY29kZXJcbiAqL1xuY29uc3QgZmx1c2hVaW50T3B0UmxlRW5jb2RlciA9IGVuY29kZXIgPT4ge1xuICBpZiAoZW5jb2Rlci5jb3VudCA+IDApIHtcbiAgICAvLyBmbHVzaCBjb3VudGVyLCB1bmxlc3MgdGhpcyBpcyB0aGUgZmlyc3QgdmFsdWUgKGNvdW50ID0gMClcbiAgICAvLyBjYXNlIDE6IGp1c3QgYSBzaW5nbGUgdmFsdWUuIHNldCBzaWduIHRvIHBvc2l0aXZlXG4gICAgLy8gY2FzZSAyOiB3cml0ZSBzZXZlcmFsIHZhbHVlcy4gc2V0IHNpZ24gdG8gbmVnYXRpdmUgdG8gaW5kaWNhdGUgdGhhdCB0aGVyZSBpcyBhIGxlbmd0aCBjb21pbmdcbiAgICB3cml0ZVZhckludChlbmNvZGVyLmVuY29kZXIsIGVuY29kZXIuY291bnQgPT09IDEgPyBlbmNvZGVyLnMgOiAtZW5jb2Rlci5zKVxuICAgIGlmIChlbmNvZGVyLmNvdW50ID4gMSkge1xuICAgICAgd3JpdGVWYXJVaW50KGVuY29kZXIuZW5jb2RlciwgZW5jb2Rlci5jb3VudCAtIDIpIC8vIHNpbmNlIGNvdW50IGlzIGFsd2F5cyA+IDEsIHdlIGNhbiBkZWNyZW1lbnQgYnkgb25lLiBub24tc3RhbmRhcmQgZW5jb2RpbmcgZnR3XG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogT3B0aW1pemVkIFJsZSBlbmNvZGVyIHRoYXQgZG9lcyBub3Qgc3VmZmVyIGZyb20gdGhlIG1lbnRpb25lZCBwcm9ibGVtIG9mIHRoZSBiYXNpYyBSbGUgZW5jb2Rlci5cbiAqXG4gKiBJbnRlcm5hbGx5IHVzZXMgVmFySW50IGVuY29kZXIgdG8gd3JpdGUgdW5zaWduZWQgaW50ZWdlcnMuIElmIHRoZSBpbnB1dCBvY2N1cnMgbXVsdGlwbGUgdGltZXMsIHdlIHdyaXRlXG4gKiB3cml0ZSBpdCBhcyBhIG5lZ2F0aXZlIG51bWJlci4gVGhlIFVpbnRPcHRSbGVEZWNvZGVyIHRoZW4gdW5kZXJzdGFuZHMgdGhhdCBpdCBuZWVkcyB0byByZWFkIGEgY291bnQuXG4gKlxuICogRW5jb2RlcyBbMSwyLDMsMywzXSBhcyBbMSwyLC0zLDNdIChvbmNlIDEsIG9uY2UgMiwgdGhyZWUgdGltZXMgMylcbiAqL1xuZXhwb3J0IGNsYXNzIFVpbnRPcHRSbGVFbmNvZGVyIHtcbiAgY29uc3RydWN0b3IgKCkge1xuICAgIHRoaXMuZW5jb2RlciA9IG5ldyBFbmNvZGVyKClcbiAgICAvKipcbiAgICAgKiBAdHlwZSB7bnVtYmVyfVxuICAgICAqL1xuICAgIHRoaXMucyA9IDBcbiAgICB0aGlzLmNvdW50ID0gMFxuICB9XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7bnVtYmVyfSB2XG4gICAqL1xuICB3cml0ZSAodikge1xuICAgIGlmICh0aGlzLnMgPT09IHYpIHtcbiAgICAgIHRoaXMuY291bnQrK1xuICAgIH0gZWxzZSB7XG4gICAgICBmbHVzaFVpbnRPcHRSbGVFbmNvZGVyKHRoaXMpXG4gICAgICB0aGlzLmNvdW50ID0gMVxuICAgICAgdGhpcy5zID0gdlxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBGbHVzaCB0aGUgZW5jb2RlZCBzdGF0ZSBhbmQgdHJhbnNmb3JtIHRoaXMgdG8gYSBVaW50OEFycmF5LlxuICAgKlxuICAgKiBOb3RlIHRoYXQgdGhpcyBzaG91bGQgb25seSBiZSBjYWxsZWQgb25jZS5cbiAgICovXG4gIHRvVWludDhBcnJheSAoKSB7XG4gICAgZmx1c2hVaW50T3B0UmxlRW5jb2Rlcih0aGlzKVxuICAgIHJldHVybiB0b1VpbnQ4QXJyYXkodGhpcy5lbmNvZGVyKVxuICB9XG59XG5cbi8qKlxuICogSW5jcmVhc2luZyBVaW50IE9wdGltaXplZCBSTEUgRW5jb2RlclxuICpcbiAqIFRoZSBSTEUgZW5jb2RlciBjb3VudHMgdGhlIG51bWJlciBvZiBzYW1lIG9jY3VyZW5jZXMgb2YgdGhlIHNhbWUgdmFsdWUuXG4gKiBUaGUgSW5jVWludE9wdFJsZSBlbmNvZGVyIGNvdW50cyBpZiB0aGUgdmFsdWUgaW5jcmVhc2VzLlxuICogSS5lLiA3LCA4LCA5LCAxMCB3aWxsIGJlIGVuY29kZWQgYXMgWy03LCA0XS4gMSwgMywgNSB3aWxsIGJlIGVuY29kZWRcbiAqIGFzIFsxLCAzLCA1XS5cbiAqL1xuZXhwb3J0IGNsYXNzIEluY1VpbnRPcHRSbGVFbmNvZGVyIHtcbiAgY29uc3RydWN0b3IgKCkge1xuICAgIHRoaXMuZW5jb2RlciA9IG5ldyBFbmNvZGVyKClcbiAgICAvKipcbiAgICAgKiBAdHlwZSB7bnVtYmVyfVxuICAgICAqL1xuICAgIHRoaXMucyA9IDBcbiAgICB0aGlzLmNvdW50ID0gMFxuICB9XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7bnVtYmVyfSB2XG4gICAqL1xuICB3cml0ZSAodikge1xuICAgIGlmICh0aGlzLnMgKyB0aGlzLmNvdW50ID09PSB2KSB7XG4gICAgICB0aGlzLmNvdW50KytcbiAgICB9IGVsc2Uge1xuICAgICAgZmx1c2hVaW50T3B0UmxlRW5jb2Rlcih0aGlzKVxuICAgICAgdGhpcy5jb3VudCA9IDFcbiAgICAgIHRoaXMucyA9IHZcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRmx1c2ggdGhlIGVuY29kZWQgc3RhdGUgYW5kIHRyYW5zZm9ybSB0aGlzIHRvIGEgVWludDhBcnJheS5cbiAgICpcbiAgICogTm90ZSB0aGF0IHRoaXMgc2hvdWxkIG9ubHkgYmUgY2FsbGVkIG9uY2UuXG4gICAqL1xuICB0b1VpbnQ4QXJyYXkgKCkge1xuICAgIGZsdXNoVWludE9wdFJsZUVuY29kZXIodGhpcylcbiAgICByZXR1cm4gdG9VaW50OEFycmF5KHRoaXMuZW5jb2RlcilcbiAgfVxufVxuXG4vKipcbiAqIEBwYXJhbSB7SW50RGlmZk9wdFJsZUVuY29kZXJ9IGVuY29kZXJcbiAqL1xuY29uc3QgZmx1c2hJbnREaWZmT3B0UmxlRW5jb2RlciA9IGVuY29kZXIgPT4ge1xuICBpZiAoZW5jb2Rlci5jb3VudCA+IDApIHtcbiAgICAvLyAgICAgICAgICAzMSBiaXQgbWFraW5nIHVwIHRoZSBkaWZmIHwgd2V0aGVyIHRvIHdyaXRlIHRoZSBjb3VudGVyXG4gICAgLy8gY29uc3QgZW5jb2RlZERpZmYgPSBlbmNvZGVyLmRpZmYgPDwgMSB8IChlbmNvZGVyLmNvdW50ID09PSAxID8gMCA6IDEpXG4gICAgY29uc3QgZW5jb2RlZERpZmYgPSBlbmNvZGVyLmRpZmYgKiAyICsgKGVuY29kZXIuY291bnQgPT09IDEgPyAwIDogMSlcbiAgICAvLyBmbHVzaCBjb3VudGVyLCB1bmxlc3MgdGhpcyBpcyB0aGUgZmlyc3QgdmFsdWUgKGNvdW50ID0gMClcbiAgICAvLyBjYXNlIDE6IGp1c3QgYSBzaW5nbGUgdmFsdWUuIHNldCBmaXJzdCBiaXQgdG8gcG9zaXRpdmVcbiAgICAvLyBjYXNlIDI6IHdyaXRlIHNldmVyYWwgdmFsdWVzLiBzZXQgZmlyc3QgYml0IHRvIG5lZ2F0aXZlIHRvIGluZGljYXRlIHRoYXQgdGhlcmUgaXMgYSBsZW5ndGggY29taW5nXG4gICAgd3JpdGVWYXJJbnQoZW5jb2Rlci5lbmNvZGVyLCBlbmNvZGVkRGlmZilcbiAgICBpZiAoZW5jb2Rlci5jb3VudCA+IDEpIHtcbiAgICAgIHdyaXRlVmFyVWludChlbmNvZGVyLmVuY29kZXIsIGVuY29kZXIuY291bnQgLSAyKSAvLyBzaW5jZSBjb3VudCBpcyBhbHdheXMgPiAxLCB3ZSBjYW4gZGVjcmVtZW50IGJ5IG9uZS4gbm9uLXN0YW5kYXJkIGVuY29kaW5nIGZ0d1xuICAgIH1cbiAgfVxufVxuXG4vKipcbiAqIEEgY29tYmluYXRpb24gb2YgdGhlIEludERpZmZFbmNvZGVyIGFuZCB0aGUgVWludE9wdFJsZUVuY29kZXIuXG4gKlxuICogVGhlIGNvdW50IGFwcHJvYWNoIGlzIHNpbWlsYXIgdG8gdGhlIFVpbnREaWZmT3B0UmxlRW5jb2RlciwgYnV0IGluc3RlYWQgb2YgdXNpbmcgdGhlIG5lZ2F0aXZlIGJpdGZsYWcsIGl0IGVuY29kZXNcbiAqIGluIHRoZSBMU0Igd2hldGhlciBhIGNvdW50IGlzIHRvIGJlIHJlYWQuIFRoZXJlZm9yZSB0aGlzIEVuY29kZXIgb25seSBzdXBwb3J0cyAzMSBiaXQgaW50ZWdlcnMhXG4gKlxuICogRW5jb2RlcyBbMSwgMiwgMywgMl0gYXMgWzMsIDEsIDYsIC0xXSAobW9yZSBzcGVjaWZpY2FsbHkgWygxIDw8IDEpIHwgMSwgKDMgPDwgMCkgfCAwLCAtMV0pXG4gKlxuICogSW50ZXJuYWxseSB1c2VzIHZhcmlhYmxlIGxlbmd0aCBlbmNvZGluZy4gQ29udHJhcnkgdG8gbm9ybWFsIFVpbnRWYXIgZW5jb2RpbmcsIHRoZSBmaXJzdCBieXRlIGNvbnRhaW5zOlxuICogKiAxIGJpdCB0aGF0IGRlbm90ZXMgd2hldGhlciB0aGUgbmV4dCB2YWx1ZSBpcyBhIGNvdW50IChMU0IpXG4gKiAqIDEgYml0IHRoYXQgZGVub3RlcyB3aGV0aGVyIHRoaXMgdmFsdWUgaXMgbmVnYXRpdmUgKE1TQiAtIDEpXG4gKiAqIDEgYml0IHRoYXQgZGVub3RlcyB3aGV0aGVyIHRvIGNvbnRpbnVlIHJlYWRpbmcgdGhlIHZhcmlhYmxlIGxlbmd0aCBpbnRlZ2VyIChNU0IpXG4gKlxuICogVGhlcmVmb3JlLCBvbmx5IGZpdmUgYml0cyByZW1haW4gdG8gZW5jb2RlIGRpZmYgcmFuZ2VzLlxuICpcbiAqIFVzZSB0aGlzIEVuY29kZXIgb25seSB3aGVuIGFwcHJvcHJpYXRlLiBJbiBtb3N0IGNhc2VzLCB0aGlzIGlzIHByb2JhYmx5IGEgYmFkIGlkZWEuXG4gKi9cbmV4cG9ydCBjbGFzcyBJbnREaWZmT3B0UmxlRW5jb2RlciB7XG4gIGNvbnN0cnVjdG9yICgpIHtcbiAgICB0aGlzLmVuY29kZXIgPSBuZXcgRW5jb2RlcigpXG4gICAgLyoqXG4gICAgICogQHR5cGUge251bWJlcn1cbiAgICAgKi9cbiAgICB0aGlzLnMgPSAwXG4gICAgdGhpcy5jb3VudCA9IDBcbiAgICB0aGlzLmRpZmYgPSAwXG4gIH1cblxuICAvKipcbiAgICogQHBhcmFtIHtudW1iZXJ9IHZcbiAgICovXG4gIHdyaXRlICh2KSB7XG4gICAgaWYgKHRoaXMuZGlmZiA9PT0gdiAtIHRoaXMucykge1xuICAgICAgdGhpcy5zID0gdlxuICAgICAgdGhpcy5jb3VudCsrXG4gICAgfSBlbHNlIHtcbiAgICAgIGZsdXNoSW50RGlmZk9wdFJsZUVuY29kZXIodGhpcylcbiAgICAgIHRoaXMuY291bnQgPSAxXG4gICAgICB0aGlzLmRpZmYgPSB2IC0gdGhpcy5zXG4gICAgICB0aGlzLnMgPSB2XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEZsdXNoIHRoZSBlbmNvZGVkIHN0YXRlIGFuZCB0cmFuc2Zvcm0gdGhpcyB0byBhIFVpbnQ4QXJyYXkuXG4gICAqXG4gICAqIE5vdGUgdGhhdCB0aGlzIHNob3VsZCBvbmx5IGJlIGNhbGxlZCBvbmNlLlxuICAgKi9cbiAgdG9VaW50OEFycmF5ICgpIHtcbiAgICBmbHVzaEludERpZmZPcHRSbGVFbmNvZGVyKHRoaXMpXG4gICAgcmV0dXJuIHRvVWludDhBcnJheSh0aGlzLmVuY29kZXIpXG4gIH1cbn1cblxuLyoqXG4gKiBPcHRpbWl6ZWQgU3RyaW5nIEVuY29kZXIuXG4gKlxuICogRW5jb2RpbmcgbWFueSBzbWFsbCBzdHJpbmdzIGluIGEgc2ltcGxlIEVuY29kZXIgaXMgbm90IHZlcnkgZWZmaWNpZW50LiBUaGUgZnVuY3Rpb24gY2FsbCB0byBkZWNvZGUgYSBzdHJpbmcgdGFrZXMgc29tZSB0aW1lIGFuZCBjcmVhdGVzIHJlZmVyZW5jZXMgdGhhdCBtdXN0IGJlIGV2ZW50dWFsbHkgZGVsZXRlZC5cbiAqIEluIHByYWN0aWNlLCB3aGVuIGRlY29kaW5nIHNldmVyYWwgbWlsbGlvbiBzbWFsbCBzdHJpbmdzLCB0aGUgR0Mgd2lsbCBraWNrIGluIG1vcmUgYW5kIG1vcmUgb2Z0ZW4gdG8gY29sbGVjdCBvcnBoYW5lZCBzdHJpbmcgb2JqZWN0cyAob3IgbWF5YmUgdGhlcmUgaXMgYW5vdGhlciByZWFzb24/KS5cbiAqXG4gKiBUaGlzIHN0cmluZyBlbmNvZGVyIHNvbHZlcyB0aGUgYWJvdmUgcHJvYmxlbS4gQWxsIHN0cmluZ3MgYXJlIGNvbmNhdGVuYXRlZCBhbmQgd3JpdHRlbiBhcyBhIHNpbmdsZSBzdHJpbmcgdXNpbmcgYSBzaW5nbGUgZW5jb2RpbmcgY2FsbC5cbiAqXG4gKiBUaGUgbGVuZ3RocyBhcmUgZW5jb2RlZCB1c2luZyBhIFVpbnRPcHRSbGVFbmNvZGVyLlxuICovXG5leHBvcnQgY2xhc3MgU3RyaW5nRW5jb2RlciB7XG4gIGNvbnN0cnVjdG9yICgpIHtcbiAgICAvKipcbiAgICAgKiBAdHlwZSB7QXJyYXk8c3RyaW5nPn1cbiAgICAgKi9cbiAgICB0aGlzLnNhcnIgPSBbXVxuICAgIHRoaXMucyA9ICcnXG4gICAgdGhpcy5sZW5zRSA9IG5ldyBVaW50T3B0UmxlRW5jb2RlcigpXG4gIH1cblxuICAvKipcbiAgICogQHBhcmFtIHtzdHJpbmd9IHN0cmluZ1xuICAgKi9cbiAgd3JpdGUgKHN0cmluZykge1xuICAgIHRoaXMucyArPSBzdHJpbmdcbiAgICBpZiAodGhpcy5zLmxlbmd0aCA+IDE5KSB7XG4gICAgICB0aGlzLnNhcnIucHVzaCh0aGlzLnMpXG4gICAgICB0aGlzLnMgPSAnJ1xuICAgIH1cbiAgICB0aGlzLmxlbnNFLndyaXRlKHN0cmluZy5sZW5ndGgpXG4gIH1cblxuICB0b1VpbnQ4QXJyYXkgKCkge1xuICAgIGNvbnN0IGVuY29kZXIgPSBuZXcgRW5jb2RlcigpXG4gICAgdGhpcy5zYXJyLnB1c2godGhpcy5zKVxuICAgIHRoaXMucyA9ICcnXG4gICAgd3JpdGVWYXJTdHJpbmcoZW5jb2RlciwgdGhpcy5zYXJyLmpvaW4oJycpKVxuICAgIHdyaXRlVWludDhBcnJheShlbmNvZGVyLCB0aGlzLmxlbnNFLnRvVWludDhBcnJheSgpKVxuICAgIHJldHVybiB0b1VpbnQ4QXJyYXkoZW5jb2RlcilcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/encoding.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/environment.js":
/*!******************************************!*\
  !*** ./node_modules/lib0/environment.js ***!
  \******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensureConf: () => (/* binding */ ensureConf),\n/* harmony export */   getConf: () => (/* binding */ getConf),\n/* harmony export */   getParam: () => (/* binding */ getParam),\n/* harmony export */   getVariable: () => (/* binding */ getVariable),\n/* harmony export */   hasConf: () => (/* binding */ hasConf),\n/* harmony export */   hasParam: () => (/* binding */ hasParam),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   isMac: () => (/* binding */ isMac),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   production: () => (/* binding */ production),\n/* harmony export */   supportsColor: () => (/* binding */ supportsColor)\n/* harmony export */ });\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/lib0/map.js\");\n/* harmony import */ var _string_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./string.js */ \"(ssr)/./node_modules/lib0/string.js\");\n/* harmony import */ var _conditions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./conditions.js */ \"(ssr)/./node_modules/lib0/conditions.js\");\n/* harmony import */ var _storage_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./storage.js */ \"(ssr)/./node_modules/lib0/storage.js\");\n/* harmony import */ var _function_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./function.js */ \"(ssr)/./node_modules/lib0/function.js\");\n/**\n * Isomorphic module to work access the environment (query params, env variables).\n *\n * @module environment\n */\n\n\n\n\n\n\n\n/* c8 ignore next 2 */\n// @ts-ignore\nconst isNode = typeof process !== 'undefined' && process.release && /node|io\\.js/.test(process.release.name) && Object.prototype.toString.call(typeof process !== 'undefined' ? process : 0) === '[object process]'\n\n/* c8 ignore next */\nconst isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && !isNode\n/* c8 ignore next 3 */\nconst isMac = typeof navigator !== 'undefined'\n  ? /Mac/.test(navigator.platform)\n  : false\n\n/**\n * @type {Map<string,string>}\n */\nlet params\nconst args = []\n\n/* c8 ignore start */\nconst computeParams = () => {\n  if (params === undefined) {\n    if (isNode) {\n      params = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n      const pargs = process.argv\n      let currParamName = null\n      for (let i = 0; i < pargs.length; i++) {\n        const parg = pargs[i]\n        if (parg[0] === '-') {\n          if (currParamName !== null) {\n            params.set(currParamName, '')\n          }\n          currParamName = parg\n        } else {\n          if (currParamName !== null) {\n            params.set(currParamName, parg)\n            currParamName = null\n          } else {\n            args.push(parg)\n          }\n        }\n      }\n      if (currParamName !== null) {\n        params.set(currParamName, '')\n      }\n      // in ReactNative for example this would not be true (unless connected to the Remote Debugger)\n    } else if (typeof location === 'object') {\n      params = _map_js__WEBPACK_IMPORTED_MODULE_0__.create(); // eslint-disable-next-line no-undef\n      (location.search || '?').slice(1).split('&').forEach((kv) => {\n        if (kv.length !== 0) {\n          const [key, value] = kv.split('=')\n          params.set(`--${_string_js__WEBPACK_IMPORTED_MODULE_1__.fromCamelCase(key, '-')}`, value)\n          params.set(`-${_string_js__WEBPACK_IMPORTED_MODULE_1__.fromCamelCase(key, '-')}`, value)\n        }\n      })\n    } else {\n      params = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n    }\n  }\n  return params\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} name\n * @return {boolean}\n */\n/* c8 ignore next */\nconst hasParam = (name) => computeParams().has(name)\n\n/**\n * @param {string} name\n * @param {string} defaultVal\n * @return {string}\n */\n/* c8 ignore next 2 */\nconst getParam = (name, defaultVal) =>\n  computeParams().get(name) || defaultVal\n\n/**\n * @param {string} name\n * @return {string|null}\n */\n/* c8 ignore next 4 */\nconst getVariable = (name) =>\n  isNode\n    ? _conditions_js__WEBPACK_IMPORTED_MODULE_2__.undefinedToNull(process.env[name.toUpperCase().replaceAll('-', '_')])\n    : _conditions_js__WEBPACK_IMPORTED_MODULE_2__.undefinedToNull(_storage_js__WEBPACK_IMPORTED_MODULE_3__.varStorage.getItem(name))\n\n/**\n * @param {string} name\n * @return {string|null}\n */\n/* c8 ignore next 2 */\nconst getConf = (name) =>\n  computeParams().get('--' + name) || getVariable(name)\n\n/**\n * @param {string} name\n * @return {string}\n */\n/* c8 ignore next 5 */\nconst ensureConf = (name) => {\n  const c = getConf(name)\n  if (c == null) throw new Error(`Expected configuration \"${name.toUpperCase().replaceAll('-', '_')}\"`)\n  return c\n}\n\n/**\n * @param {string} name\n * @return {boolean}\n */\n/* c8 ignore next 2 */\nconst hasConf = (name) =>\n  hasParam('--' + name) || getVariable(name) !== null\n\n/* c8 ignore next */\nconst production = hasConf('production')\n\n/* c8 ignore next 2 */\nconst forceColor = isNode &&\n  _function_js__WEBPACK_IMPORTED_MODULE_4__.isOneOf(process.env.FORCE_COLOR, ['true', '1', '2'])\n\n/* c8 ignore start */\n/**\n * Color is enabled by default if the terminal supports it.\n *\n * Explicitly enable color using `--color` parameter\n * Disable color using `--no-color` parameter or using `NO_COLOR=1` environment variable.\n * `FORCE_COLOR=1` enables color and takes precedence over all.\n */\nconst supportsColor = forceColor || (\n  !hasParam('--no-colors') && // @todo deprecate --no-colors\n  !hasConf('no-color') &&\n  (!isNode || process.stdout.isTTY) && (\n    !isNode ||\n    hasParam('--color') ||\n    getVariable('COLORTERM') !== null ||\n    (getVariable('TERM') || '').includes('color')\n  )\n)\n/* c8 ignore stop */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/environment.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/error.js":
/*!************************************!*\
  !*** ./node_modules/lib0/error.js ***!
  \************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   methodUnimplemented: () => (/* binding */ methodUnimplemented),\n/* harmony export */   unexpectedCase: () => (/* binding */ unexpectedCase)\n/* harmony export */ });\n/**\n * Error helpers.\n *\n * @module error\n */\n\n/**\n * @param {string} s\n * @return {Error}\n */\n/* c8 ignore next */\nconst create = s => new Error(s)\n\n/**\n * @throws {Error}\n * @return {never}\n */\n/* c8 ignore next 3 */\nconst methodUnimplemented = () => {\n  throw create('Method unimplemented')\n}\n\n/**\n * @throws {Error}\n * @return {never}\n */\n/* c8 ignore next 3 */\nconst unexpectedCase = () => {\n  throw create('Unexpected case')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9lcnJvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFlBQVk7QUFDWjtBQUNBO0FBQ087O0FBRVA7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDTztBQUNQO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2xpYjAvZXJyb3IuanM/MGE3OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEVycm9yIGhlbHBlcnMuXG4gKlxuICogQG1vZHVsZSBlcnJvclxuICovXG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IHNcbiAqIEByZXR1cm4ge0Vycm9yfVxuICovXG4vKiBjOCBpZ25vcmUgbmV4dCAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZSA9IHMgPT4gbmV3IEVycm9yKHMpXG5cbi8qKlxuICogQHRocm93cyB7RXJyb3J9XG4gKiBAcmV0dXJuIHtuZXZlcn1cbiAqL1xuLyogYzggaWdub3JlIG5leHQgMyAqL1xuZXhwb3J0IGNvbnN0IG1ldGhvZFVuaW1wbGVtZW50ZWQgPSAoKSA9PiB7XG4gIHRocm93IGNyZWF0ZSgnTWV0aG9kIHVuaW1wbGVtZW50ZWQnKVxufVxuXG4vKipcbiAqIEB0aHJvd3Mge0Vycm9yfVxuICogQHJldHVybiB7bmV2ZXJ9XG4gKi9cbi8qIGM4IGlnbm9yZSBuZXh0IDMgKi9cbmV4cG9ydCBjb25zdCB1bmV4cGVjdGVkQ2FzZSA9ICgpID0+IHtcbiAgdGhyb3cgY3JlYXRlKCdVbmV4cGVjdGVkIGNhc2UnKVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/error.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/eventloop.js":
/*!****************************************!*\
  !*** ./node_modules/lib0/eventloop.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Animation: () => (/* binding */ Animation),\n/* harmony export */   animationFrame: () => (/* binding */ animationFrame),\n/* harmony export */   createDebouncer: () => (/* binding */ createDebouncer),\n/* harmony export */   enqueue: () => (/* binding */ enqueue),\n/* harmony export */   idleCallback: () => (/* binding */ idleCallback),\n/* harmony export */   interval: () => (/* binding */ interval),\n/* harmony export */   timeout: () => (/* binding */ timeout)\n/* harmony export */ });\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time.js */ \"(ssr)/./node_modules/lib0/time.js\");\n/* global requestIdleCallback, requestAnimationFrame, cancelIdleCallback, cancelAnimationFrame */\n\n\n\n/**\n * Utility module to work with EcmaScript's event loop.\n *\n * @module eventloop\n */\n\n/**\n * @type {Array<function>}\n */\nlet queue = []\n\nconst _runQueue = () => {\n  for (let i = 0; i < queue.length; i++) {\n    queue[i]()\n  }\n  queue = []\n}\n\n/**\n * @param {function():void} f\n */\nconst enqueue = f => {\n  queue.push(f)\n  if (queue.length === 1) {\n    setTimeout(_runQueue, 0)\n  }\n}\n\n/**\n * @typedef {Object} TimeoutObject\n * @property {function} TimeoutObject.destroy\n */\n\n/**\n * @param {function(number):void} clearFunction\n */\nconst createTimeoutClass = clearFunction => class TT {\n  /**\n   * @param {number} timeoutId\n   */\n  constructor (timeoutId) {\n    this._ = timeoutId\n  }\n\n  destroy () {\n    clearFunction(this._)\n  }\n}\n\nconst Timeout = createTimeoutClass(clearTimeout)\n\n/**\n * @param {number} timeout\n * @param {function} callback\n * @return {TimeoutObject}\n */\nconst timeout = (timeout, callback) => new Timeout(setTimeout(callback, timeout))\n\nconst Interval = createTimeoutClass(clearInterval)\n\n/**\n * @param {number} timeout\n * @param {function} callback\n * @return {TimeoutObject}\n */\nconst interval = (timeout, callback) => new Interval(setInterval(callback, timeout))\n\n/* c8 ignore next */\nconst Animation = createTimeoutClass(arg => typeof requestAnimationFrame !== 'undefined' && cancelAnimationFrame(arg))\n\n/**\n * @param {function(number):void} cb\n * @return {TimeoutObject}\n */\n/* c8 ignore next */\nconst animationFrame = cb => typeof requestAnimationFrame === 'undefined' ? timeout(0, cb) : new Animation(requestAnimationFrame(cb))\n\n/* c8 ignore next */\n// @ts-ignore\nconst Idle = createTimeoutClass(arg => typeof cancelIdleCallback !== 'undefined' && cancelIdleCallback(arg))\n\n/**\n * Note: this is experimental and is probably only useful in browsers.\n *\n * @param {function} cb\n * @return {TimeoutObject}\n */\n/* c8 ignore next 2 */\n// @ts-ignore\nconst idleCallback = cb => typeof requestIdleCallback !== 'undefined' ? new Idle(requestIdleCallback(cb)) : timeout(1000, cb)\n\n/**\n * @param {number} timeout Timeout of the debounce action\n * @param {number} triggerAfter Optional. Trigger callback after a certain amount of time\n *                              without waiting for debounce.\n */\nconst createDebouncer = (timeout, triggerAfter = -1) => {\n  let timer = -1\n  /**\n   * @type {number?}\n    */\n  let lastCall = null\n  /**\n   * @param {((...args: any)=>void)?} cb function to trigger after debounce. If null, it will reset the\n   *                         debounce.\n   */\n  return cb => {\n    clearTimeout(timer)\n    if (cb) {\n      if (triggerAfter >= 0) {\n        const now = _time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime()\n        if (lastCall === null) lastCall = now\n        if (now - lastCall > triggerAfter) {\n          lastCall = null\n          timer = /** @type {any} */ (setTimeout(cb, 0))\n          return\n        }\n      }\n      timer = /** @type {any} */ (setTimeout(() => { lastCall = null; cb() }, timeout))\n    } else {\n      lastCall = null\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/eventloop.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/function.js":
/*!***************************************!*\
  !*** ./node_modules/lib0/function.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apply: () => (/* binding */ apply),\n/* harmony export */   callAll: () => (/* binding */ callAll),\n/* harmony export */   equalityDeep: () => (/* binding */ equalityDeep),\n/* harmony export */   equalityFlat: () => (/* binding */ equalityFlat),\n/* harmony export */   equalityStrict: () => (/* binding */ equalityStrict),\n/* harmony export */   id: () => (/* binding */ id),\n/* harmony export */   is: () => (/* binding */ is),\n/* harmony export */   isArray: () => (/* binding */ isArray),\n/* harmony export */   isNumber: () => (/* binding */ isNumber),\n/* harmony export */   isOneOf: () => (/* binding */ isOneOf),\n/* harmony export */   isString: () => (/* binding */ isString),\n/* harmony export */   isTemplate: () => (/* binding */ isTemplate),\n/* harmony export */   nop: () => (/* binding */ nop)\n/* harmony export */ });\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/lib0/array.js\");\n/* harmony import */ var _object_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./object.js */ \"(ssr)/./node_modules/lib0/object.js\");\n/* harmony import */ var _traits_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./traits.js */ \"(ssr)/./node_modules/lib0/traits.js\");\n/**\n * Common functions and function call helpers.\n *\n * @module function\n */\n\n\n\n\n\n/**\n * Calls all functions in `fs` with args. Only throws after all functions were called.\n *\n * @param {Array<function>} fs\n * @param {Array<any>} args\n */\nconst callAll = (fs, args, i = 0) => {\n  try {\n    for (; i < fs.length; i++) {\n      fs[i](...args)\n    }\n  } finally {\n    if (i < fs.length) {\n      callAll(fs, args, i + 1)\n    }\n  }\n}\n\nconst nop = () => {}\n\n/**\n * @template T\n * @param {function():T} f\n * @return {T}\n */\nconst apply = f => f()\n\n/**\n * @template A\n *\n * @param {A} a\n * @return {A}\n */\nconst id = a => a\n\n/**\n * @template T\n *\n * @param {T} a\n * @param {T} b\n * @return {boolean}\n */\nconst equalityStrict = (a, b) => a === b\n\n/**\n * @template T\n *\n * @param {Array<T>|object} a\n * @param {Array<T>|object} b\n * @return {boolean}\n */\nconst equalityFlat = (a, b) => a === b || (a != null && b != null && a.constructor === b.constructor && ((_array_js__WEBPACK_IMPORTED_MODULE_0__.isArray(a) && _array_js__WEBPACK_IMPORTED_MODULE_0__.equalFlat(a, /** @type {Array<T>} */ (b))) || (typeof a === 'object' && _object_js__WEBPACK_IMPORTED_MODULE_1__.equalFlat(a, b))))\n\n/* c8 ignore start */\n\n/**\n * @param {any} a\n * @param {any} b\n * @return {boolean}\n */\nconst equalityDeep = (a, b) => {\n  if (a === b) {\n    return true\n  }\n  if (a == null || b == null || a.constructor !== b.constructor) {\n    return false\n  }\n  if (a[_traits_js__WEBPACK_IMPORTED_MODULE_2__.EqualityTraitSymbol] != null) {\n    return a[_traits_js__WEBPACK_IMPORTED_MODULE_2__.EqualityTraitSymbol](b)\n  }\n  switch (a.constructor) {\n    case ArrayBuffer:\n      a = new Uint8Array(a)\n      b = new Uint8Array(b)\n    // eslint-disable-next-line no-fallthrough\n    case Uint8Array: {\n      if (a.byteLength !== b.byteLength) {\n        return false\n      }\n      for (let i = 0; i < a.length; i++) {\n        if (a[i] !== b[i]) {\n          return false\n        }\n      }\n      break\n    }\n    case Set: {\n      if (a.size !== b.size) {\n        return false\n      }\n      for (const value of a) {\n        if (!b.has(value)) {\n          return false\n        }\n      }\n      break\n    }\n    case Map: {\n      if (a.size !== b.size) {\n        return false\n      }\n      for (const key of a.keys()) {\n        if (!b.has(key) || !equalityDeep(a.get(key), b.get(key))) {\n          return false\n        }\n      }\n      break\n    }\n    case Object:\n      if (_object_js__WEBPACK_IMPORTED_MODULE_1__.length(a) !== _object_js__WEBPACK_IMPORTED_MODULE_1__.length(b)) {\n        return false\n      }\n      for (const key in a) {\n        if (!_object_js__WEBPACK_IMPORTED_MODULE_1__.hasProperty(a, key) || !equalityDeep(a[key], b[key])) {\n          return false\n        }\n      }\n      break\n    case Array:\n      if (a.length !== b.length) {\n        return false\n      }\n      for (let i = 0; i < a.length; i++) {\n        if (!equalityDeep(a[i], b[i])) {\n          return false\n        }\n      }\n      break\n    default:\n      return false\n  }\n  return true\n}\n\n/**\n * @template V\n * @template {V} OPTS\n *\n * @param {V} value\n * @param {Array<OPTS>} options\n */\n// @ts-ignore\nconst isOneOf = (value, options) => options.includes(value)\n/* c8 ignore stop */\n\nconst isArray = _array_js__WEBPACK_IMPORTED_MODULE_0__.isArray\n\n/**\n * @param {any} s\n * @return {s is String}\n */\nconst isString = (s) => s && s.constructor === String\n\n/**\n * @param {any} n\n * @return {n is Number}\n */\nconst isNumber = n => n != null && n.constructor === Number\n\n/**\n * @template {abstract new (...args: any) => any} TYPE\n * @param {any} n\n * @param {TYPE} T\n * @return {n is InstanceType<TYPE>}\n */\nconst is = (n, T) => n && n.constructor === T\n\n/**\n * @template {abstract new (...args: any) => any} TYPE\n * @param {TYPE} T\n */\nconst isTemplate = (T) =>\n  /**\n   * @param {any} n\n   * @return {n is InstanceType<TYPE>}\n   **/\n  n => n && n.constructor === T\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/function.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/indexeddb.js":
/*!****************************************!*\
  !*** ./node_modules/lib0/indexeddb.js ***!
  \****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   addAutoKey: () => (/* binding */ addAutoKey),\n/* harmony export */   count: () => (/* binding */ count),\n/* harmony export */   createIDBKeyRangeBound: () => (/* binding */ createIDBKeyRangeBound),\n/* harmony export */   createIDBKeyRangeLowerBound: () => (/* binding */ createIDBKeyRangeLowerBound),\n/* harmony export */   createIDBKeyRangeUpperBound: () => (/* binding */ createIDBKeyRangeUpperBound),\n/* harmony export */   createStores: () => (/* binding */ createStores),\n/* harmony export */   del: () => (/* binding */ del),\n/* harmony export */   deleteDB: () => (/* binding */ deleteDB),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   getAll: () => (/* binding */ getAll),\n/* harmony export */   getAllKeys: () => (/* binding */ getAllKeys),\n/* harmony export */   getAllKeysValues: () => (/* binding */ getAllKeysValues),\n/* harmony export */   getFirstKey: () => (/* binding */ getFirstKey),\n/* harmony export */   getLastKey: () => (/* binding */ getLastKey),\n/* harmony export */   getStore: () => (/* binding */ getStore),\n/* harmony export */   iterate: () => (/* binding */ iterate),\n/* harmony export */   iterateKeys: () => (/* binding */ iterateKeys),\n/* harmony export */   openDB: () => (/* binding */ openDB),\n/* harmony export */   put: () => (/* binding */ put),\n/* harmony export */   queryFirst: () => (/* binding */ queryFirst),\n/* harmony export */   rtop: () => (/* binding */ rtop),\n/* harmony export */   transact: () => (/* binding */ transact)\n/* harmony export */ });\n/* harmony import */ var _promise_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./promise.js */ \"(ssr)/./node_modules/lib0/promise.js\");\n/* harmony import */ var _error_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error.js */ \"(ssr)/./node_modules/lib0/error.js\");\n/* eslint-env browser */\n\n/**\n * Helpers to work with IndexedDB.\n *\n * @module indexeddb\n */\n\n\n\n\n/* c8 ignore start */\n\n/**\n * IDB Request to Promise transformer\n *\n * @param {IDBRequest} request\n * @return {Promise<any>}\n */\nconst rtop = request => _promise_js__WEBPACK_IMPORTED_MODULE_0__.create((resolve, reject) => {\n  // @ts-ignore\n  request.onerror = event => reject(new Error(event.target.error))\n  // @ts-ignore\n  request.onsuccess = event => resolve(event.target.result)\n})\n\n/**\n * @param {string} name\n * @param {function(IDBDatabase):any} initDB Called when the database is first created\n * @return {Promise<IDBDatabase>}\n */\nconst openDB = (name, initDB) => _promise_js__WEBPACK_IMPORTED_MODULE_0__.create((resolve, reject) => {\n  const request = indexedDB.open(name)\n  /**\n   * @param {any} event\n   */\n  request.onupgradeneeded = event => initDB(event.target.result)\n  /**\n   * @param {any} event\n   */\n  request.onerror = event => reject(_error_js__WEBPACK_IMPORTED_MODULE_1__.create(event.target.error))\n  /**\n   * @param {any} event\n   */\n  request.onsuccess = event => {\n    /**\n     * @type {IDBDatabase}\n     */\n    const db = event.target.result\n    db.onversionchange = () => { db.close() }\n    resolve(db)\n  }\n})\n\n/**\n * @param {string} name\n */\nconst deleteDB = name => rtop(indexedDB.deleteDatabase(name))\n\n/**\n * @param {IDBDatabase} db\n * @param {Array<Array<string>|Array<string|IDBObjectStoreParameters|undefined>>} definitions\n */\nconst createStores = (db, definitions) => definitions.forEach(d =>\n  // @ts-ignore\n  db.createObjectStore.apply(db, d)\n)\n\n/**\n * @param {IDBDatabase} db\n * @param {Array<string>} stores\n * @param {\"readwrite\"|\"readonly\"} [access]\n * @return {Array<IDBObjectStore>}\n */\nconst transact = (db, stores, access = 'readwrite') => {\n  const transaction = db.transaction(stores, access)\n  return stores.map(store => getStore(transaction, store))\n}\n\n/**\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange} [range]\n * @return {Promise<number>}\n */\nconst count = (store, range) =>\n  rtop(store.count(range))\n\n/**\n * @param {IDBObjectStore} store\n * @param {String | number | ArrayBuffer | Date | Array<any> } key\n * @return {Promise<String | number | ArrayBuffer | Date | Array<any>>}\n */\nconst get = (store, key) =>\n  rtop(store.get(key))\n\n/**\n * @param {IDBObjectStore} store\n * @param {String | number | ArrayBuffer | Date | IDBKeyRange | Array<any> } key\n */\nconst del = (store, key) =>\n  rtop(store.delete(key))\n\n/**\n * @param {IDBObjectStore} store\n * @param {String | number | ArrayBuffer | Date | boolean} item\n * @param {String | number | ArrayBuffer | Date | Array<any>} [key]\n */\nconst put = (store, item, key) =>\n  rtop(store.put(item, key))\n\n/**\n * @param {IDBObjectStore} store\n * @param {String | number | ArrayBuffer | Date | boolean}  item\n * @param {String | number | ArrayBuffer | Date | Array<any>}  key\n * @return {Promise<any>}\n */\nconst add = (store, item, key) =>\n  rtop(store.add(item, key))\n\n/**\n * @param {IDBObjectStore} store\n * @param {String | number | ArrayBuffer | Date}  item\n * @return {Promise<number>} Returns the generated key\n */\nconst addAutoKey = (store, item) =>\n  rtop(store.add(item))\n\n/**\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange} [range]\n * @param {number} [limit]\n * @return {Promise<Array<any>>}\n */\nconst getAll = (store, range, limit) =>\n  rtop(store.getAll(range, limit))\n\n/**\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange} [range]\n * @param {number} [limit]\n * @return {Promise<Array<any>>}\n */\nconst getAllKeys = (store, range, limit) =>\n  rtop(store.getAllKeys(range, limit))\n\n/**\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange|null} query\n * @param {'next'|'prev'|'nextunique'|'prevunique'} direction\n * @return {Promise<any>}\n */\nconst queryFirst = (store, query, direction) => {\n  /**\n   * @type {any}\n   */\n  let first = null\n  return iterateKeys(store, query, key => {\n    first = key\n    return false\n  }, direction).then(() => first)\n}\n\n/**\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange?} [range]\n * @return {Promise<any>}\n */\nconst getLastKey = (store, range = null) => queryFirst(store, range, 'prev')\n\n/**\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange?} [range]\n * @return {Promise<any>}\n */\nconst getFirstKey = (store, range = null) => queryFirst(store, range, 'next')\n\n/**\n * @typedef KeyValuePair\n * @type {Object}\n * @property {any} k key\n * @property {any} v Value\n */\n\n/**\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange} [range]\n * @param {number} [limit]\n * @return {Promise<Array<KeyValuePair>>}\n */\nconst getAllKeysValues = (store, range, limit) =>\n  // @ts-ignore\n  _promise_js__WEBPACK_IMPORTED_MODULE_0__.all([getAllKeys(store, range, limit), getAll(store, range, limit)]).then(([ks, vs]) => ks.map((k, i) => ({ k, v: vs[i] })))\n\n/**\n * @param {any} request\n * @param {function(IDBCursorWithValue):void|boolean|Promise<void|boolean>} f\n * @return {Promise<void>}\n */\nconst iterateOnRequest = (request, f) => _promise_js__WEBPACK_IMPORTED_MODULE_0__.create((resolve, reject) => {\n  request.onerror = reject\n  /**\n   * @param {any} event\n   */\n  request.onsuccess = async event => {\n    const cursor = event.target.result\n    if (cursor === null || (await f(cursor)) === false) {\n      return resolve()\n    }\n    cursor.continue()\n  }\n})\n\n/**\n * Iterate on keys and values\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange|null} keyrange\n * @param {function(any,any):void|boolean|Promise<void|boolean>} f Callback that receives (value, key)\n * @param {'next'|'prev'|'nextunique'|'prevunique'} direction\n */\nconst iterate = (store, keyrange, f, direction = 'next') =>\n  iterateOnRequest(store.openCursor(keyrange, direction), cursor => f(cursor.value, cursor.key))\n\n/**\n * Iterate on the keys (no values)\n *\n * @param {IDBObjectStore} store\n * @param {IDBKeyRange|null} keyrange\n * @param {function(any):void|boolean|Promise<void|boolean>} f callback that receives the key\n * @param {'next'|'prev'|'nextunique'|'prevunique'} direction\n */\nconst iterateKeys = (store, keyrange, f, direction = 'next') =>\n  iterateOnRequest(store.openKeyCursor(keyrange, direction), cursor => f(cursor.key))\n\n/**\n * Open store from transaction\n * @param {IDBTransaction} t\n * @param {String} store\n * @returns {IDBObjectStore}\n */\nconst getStore = (t, store) => t.objectStore(store)\n\n/**\n * @param {any} lower\n * @param {any} upper\n * @param {boolean} lowerOpen\n * @param {boolean} upperOpen\n */\nconst createIDBKeyRangeBound = (lower, upper, lowerOpen, upperOpen) => IDBKeyRange.bound(lower, upper, lowerOpen, upperOpen)\n\n/**\n * @param {any} upper\n * @param {boolean} upperOpen\n */\nconst createIDBKeyRangeUpperBound = (upper, upperOpen) => IDBKeyRange.upperBound(upper, upperOpen)\n\n/**\n * @param {any} lower\n * @param {boolean} lowerOpen\n */\nconst createIDBKeyRangeLowerBound = (lower, lowerOpen) => IDBKeyRange.lowerBound(lower, lowerOpen)\n\n/* c8 ignore stop */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/indexeddb.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/iterator.js":
/*!***************************************!*\
  !*** ./node_modules/lib0/iterator.js ***!
  \***************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createIterator: () => (/* binding */ createIterator),\n/* harmony export */   iteratorFilter: () => (/* binding */ iteratorFilter),\n/* harmony export */   iteratorMap: () => (/* binding */ iteratorMap),\n/* harmony export */   mapIterator: () => (/* binding */ mapIterator)\n/* harmony export */ });\n/**\n * Utility module to create and manipulate Iterators.\n *\n * @module iterator\n */\n\n/**\n * @template T,R\n * @param {Iterator<T>} iterator\n * @param {function(T):R} f\n * @return {IterableIterator<R>}\n */\nconst mapIterator = (iterator, f) => ({\n  [Symbol.iterator] () {\n    return this\n  },\n  // @ts-ignore\n  next () {\n    const r = iterator.next()\n    return { value: r.done ? undefined : f(r.value), done: r.done }\n  }\n})\n\n/**\n * @template T\n * @param {function():IteratorResult<T>} next\n * @return {IterableIterator<T>}\n */\nconst createIterator = next => ({\n  /**\n   * @return {IterableIterator<T>}\n   */\n  [Symbol.iterator] () {\n    return this\n  },\n  // @ts-ignore\n  next\n})\n\n/**\n * @template T\n * @param {Iterator<T>} iterator\n * @param {function(T):boolean} filter\n */\nconst iteratorFilter = (iterator, filter) => createIterator(() => {\n  let res\n  do {\n    res = iterator.next()\n  } while (!res.done && !filter(res.value))\n  return res\n})\n\n/**\n * @template T,M\n * @param {Iterator<T>} iterator\n * @param {function(T):M} fmap\n */\nconst iteratorMap = (iterator, fmap) => createIterator(() => {\n  const { done, value } = iterator.next()\n  return { done, value: done ? undefined : fmap(value) }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/iterator.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/json.js":
/*!***********************************!*\
  !*** ./node_modules/lib0/json.js ***!
  \***********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/**\n * JSON utility functions.\n *\n * @module json\n */\n\n/**\n * Transform JavaScript object to JSON.\n *\n * @param {any} object\n * @return {string}\n */\nconst stringify = JSON.stringify\n\n/**\n * Parse JSON object.\n *\n * @param {string} json\n * @return {any}\n */\nconst parse = JSON.parse\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9qc29uLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLEtBQUs7QUFDaEIsWUFBWTtBQUNaO0FBQ087O0FBRVA7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFlBQVk7QUFDWjtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9saWIwL2pzb24uanM/NTA1OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEpTT04gdXRpbGl0eSBmdW5jdGlvbnMuXG4gKlxuICogQG1vZHVsZSBqc29uXG4gKi9cblxuLyoqXG4gKiBUcmFuc2Zvcm0gSmF2YVNjcmlwdCBvYmplY3QgdG8gSlNPTi5cbiAqXG4gKiBAcGFyYW0ge2FueX0gb2JqZWN0XG4gKiBAcmV0dXJuIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBjb25zdCBzdHJpbmdpZnkgPSBKU09OLnN0cmluZ2lmeVxuXG4vKipcbiAqIFBhcnNlIEpTT04gb2JqZWN0LlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBqc29uXG4gKiBAcmV0dXJuIHthbnl9XG4gKi9cbmV4cG9ydCBjb25zdCBwYXJzZSA9IEpTT04ucGFyc2VcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/json.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/logging.common.js":
/*!*********************************************!*\
  !*** ./node_modules/lib0/logging.common.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLUE: () => (/* binding */ BLUE),\n/* harmony export */   BOLD: () => (/* binding */ BOLD),\n/* harmony export */   GREEN: () => (/* binding */ GREEN),\n/* harmony export */   GREY: () => (/* binding */ GREY),\n/* harmony export */   ORANGE: () => (/* binding */ ORANGE),\n/* harmony export */   PURPLE: () => (/* binding */ PURPLE),\n/* harmony export */   RED: () => (/* binding */ RED),\n/* harmony export */   UNBOLD: () => (/* binding */ UNBOLD),\n/* harmony export */   UNCOLOR: () => (/* binding */ UNCOLOR),\n/* harmony export */   computeNoColorLoggingArgs: () => (/* binding */ computeNoColorLoggingArgs),\n/* harmony export */   createModuleLogger: () => (/* binding */ createModuleLogger)\n/* harmony export */ });\n/* harmony import */ var _symbol_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./symbol.js */ \"(ssr)/./node_modules/lib0/symbol.js\");\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./time.js */ \"(ssr)/./node_modules/lib0/time.js\");\n/* harmony import */ var _environment_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./environment.js */ \"(ssr)/./node_modules/lib0/environment.js\");\n/* harmony import */ var _function_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./function.js */ \"(ssr)/./node_modules/lib0/function.js\");\n/* harmony import */ var _json_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./json.js */ \"(ssr)/./node_modules/lib0/json.js\");\n\n\n\n\n\n\nconst BOLD = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst UNBOLD = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst BLUE = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst GREY = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst GREEN = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst RED = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst PURPLE = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst ORANGE = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\nconst UNCOLOR = _symbol_js__WEBPACK_IMPORTED_MODULE_0__.create()\n\n/* c8 ignore start */\n/**\n * @param {Array<undefined|string|Symbol|Object|number|function():any>} args\n * @return {Array<string|object|number|undefined>}\n */\nconst computeNoColorLoggingArgs = args => {\n  if (args.length === 1 && args[0]?.constructor === Function) {\n    args = /** @type {Array<string|Symbol|Object|number>} */ (/** @type {[function]} */ (args)[0]())\n  }\n  const strBuilder = []\n  const logArgs = []\n  // try with formatting until we find something unsupported\n  let i = 0\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (arg === undefined) {\n      break\n    } else if (arg.constructor === String || arg.constructor === Number) {\n      strBuilder.push(arg)\n    } else if (arg.constructor === Object) {\n      break\n    }\n  }\n  if (i > 0) {\n    // create logArgs with what we have so far\n    logArgs.push(strBuilder.join(''))\n  }\n  // append the rest\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (!(arg instanceof Symbol)) {\n      logArgs.push(arg)\n    }\n  }\n  return logArgs\n}\n/* c8 ignore stop */\n\nconst loggingColors = [GREEN, PURPLE, ORANGE, BLUE]\nlet nextColor = 0\nlet lastLoggingTime = _time_js__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n\n/* c8 ignore start */\n/**\n * @param {function(...any):void} _print\n * @param {string} moduleName\n * @return {function(...any):void}\n */\nconst createModuleLogger = (_print, moduleName) => {\n  const color = loggingColors[nextColor]\n  const debugRegexVar = _environment_js__WEBPACK_IMPORTED_MODULE_2__.getVariable('log')\n  const doLogging = debugRegexVar !== null &&\n    (debugRegexVar === '*' || debugRegexVar === 'true' ||\n      new RegExp(debugRegexVar, 'gi').test(moduleName))\n  nextColor = (nextColor + 1) % loggingColors.length\n  moduleName += ': '\n  return !doLogging\n    ? _function_js__WEBPACK_IMPORTED_MODULE_3__.nop\n    : (...args) => {\n        if (args.length === 1 && args[0]?.constructor === Function) {\n          args = args[0]()\n        }\n        const timeNow = _time_js__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n        const timeDiff = timeNow - lastLoggingTime\n        lastLoggingTime = timeNow\n        _print(\n          color,\n          moduleName,\n          UNCOLOR,\n          ...args.map((arg) => {\n            if (arg != null && arg.constructor === Uint8Array) {\n              arg = Array.from(arg)\n            }\n            const t = typeof arg\n            switch (t) {\n              case 'string':\n              case 'symbol':\n                return arg\n              default: {\n                return _json_js__WEBPACK_IMPORTED_MODULE_4__.stringify(arg)\n              }\n            }\n          }),\n          color,\n          ' +' + timeDiff + 'ms'\n        )\n      }\n}\n/* c8 ignore stop */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/logging.common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/logging.node.js":
/*!*******************************************!*\
  !*** ./node_modules/lib0/logging.node.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BLUE: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.BLUE),\n/* harmony export */   BOLD: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.BOLD),\n/* harmony export */   GREEN: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.GREEN),\n/* harmony export */   GREY: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.GREY),\n/* harmony export */   ORANGE: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.ORANGE),\n/* harmony export */   PURPLE: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.PURPLE),\n/* harmony export */   RED: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.RED),\n/* harmony export */   UNBOLD: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.UNBOLD),\n/* harmony export */   UNCOLOR: () => (/* reexport safe */ _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.UNCOLOR),\n/* harmony export */   createModuleLogger: () => (/* binding */ createModuleLogger),\n/* harmony export */   createVConsole: () => (/* binding */ createVConsole),\n/* harmony export */   group: () => (/* binding */ group),\n/* harmony export */   groupCollapsed: () => (/* binding */ groupCollapsed),\n/* harmony export */   groupEnd: () => (/* binding */ groupEnd),\n/* harmony export */   print: () => (/* binding */ print),\n/* harmony export */   printCanvas: () => (/* binding */ printCanvas),\n/* harmony export */   printDom: () => (/* binding */ printDom),\n/* harmony export */   printError: () => (/* binding */ printError),\n/* harmony export */   printImg: () => (/* binding */ printImg),\n/* harmony export */   printImgBase64: () => (/* binding */ printImgBase64),\n/* harmony export */   warn: () => (/* binding */ warn)\n/* harmony export */ });\n/* harmony import */ var _environment_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./environment.js */ \"(ssr)/./node_modules/lib0/environment.js\");\n/* harmony import */ var _logging_common_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./logging.common.js */ \"(ssr)/./node_modules/lib0/logging.common.js\");\n/**\n * Isomorphic logging module with support for colors!\n *\n * @module logging\n */\n\n\n\n\n\n\nconst _nodeStyleMap = {\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.BOLD]: '\\u001b[1m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.UNBOLD]: '\\u001b[2m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.BLUE]: '\\x1b[34m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.GREEN]: '\\x1b[32m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.GREY]: '\\u001b[37m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.RED]: '\\x1b[31m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.PURPLE]: '\\x1b[35m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.ORANGE]: '\\x1b[38;5;208m',\n  [_logging_common_js__WEBPACK_IMPORTED_MODULE_0__.UNCOLOR]: '\\x1b[0m'\n}\n\n/* c8 ignore start */\n/**\n * @param {Array<string|undefined|Symbol|Object|number|function():Array<any>>} args\n * @return {Array<string|object|number|undefined>}\n */\nconst computeNodeLoggingArgs = (args) => {\n  if (args.length === 1 && args[0]?.constructor === Function) {\n    args = /** @type {Array<string|Symbol|Object|number>} */ (/** @type {[function]} */ (args)[0]())\n  }\n  const strBuilder = []\n  const logArgs = []\n  // try with formatting until we find something unsupported\n  let i = 0\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    // @ts-ignore\n    const style = _nodeStyleMap[arg]\n    if (style !== undefined) {\n      strBuilder.push(style)\n    } else {\n      if (arg === undefined) {\n        break\n      } else if (arg.constructor === String || arg.constructor === Number) {\n        strBuilder.push(arg)\n      } else {\n        break\n      }\n    }\n  }\n  if (i > 0) {\n    // create logArgs with what we have so far\n    strBuilder.push('\\x1b[0m')\n    logArgs.push(strBuilder.join(''))\n  }\n  // append the rest\n  for (; i < args.length; i++) {\n    const arg = args[i]\n    if (!(arg instanceof Symbol)) {\n      logArgs.push(arg)\n    }\n  }\n  return logArgs\n}\n/* c8 ignore stop */\n\n/* c8 ignore start */\nconst computeLoggingArgs = _environment_js__WEBPACK_IMPORTED_MODULE_1__.supportsColor\n  ? computeNodeLoggingArgs\n  : _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.computeNoColorLoggingArgs\n/* c8 ignore stop */\n\n/**\n * @param {Array<string|Symbol|Object|number|undefined>} args\n */\nconst print = (...args) => {\n  console.log(...computeLoggingArgs(args))\n}\n\n/* c8 ignore start */\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\nconst warn = (...args) => {\n  console.warn(...computeLoggingArgs(args))\n}\n/* c8 ignore stop */\n\n/**\n * @param {Error} err\n */\n/* c8 ignore start */\nconst printError = (err) => {\n  console.error(err)\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} _url image location\n * @param {number} _height height of the image in pixel\n */\n/* c8 ignore start */\nconst printImg = (_url, _height) => {\n  // console.log('%c                ', `font-size: ${height}x; background: url(${url}) no-repeat;`)\n}\n/* c8 ignore stop */\n\n/**\n * @param {string} base64\n * @param {number} height\n */\n/* c8 ignore next 2 */\nconst printImgBase64 = (base64, height) =>\n  printImg(`data:image/gif;base64,${base64}`, height)\n\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\n/* c8 ignore next 3 */\nconst group = (...args) => {\n  console.group(...computeLoggingArgs(args))\n}\n\n/**\n * @param {Array<string|Symbol|Object|number>} args\n */\n/* c8 ignore next 3 */\nconst groupCollapsed = (...args) => {\n  console.groupCollapsed(...computeLoggingArgs(args))\n}\n\n/* c8 ignore next 3 */\nconst groupEnd = () => {\n  console.groupEnd()\n}\n\n/**\n * @param {function():Node} _createNode\n */\n/* c8 ignore next 2 */\nconst printDom = (_createNode) => {}\n\n/**\n * @param {HTMLCanvasElement} canvas\n * @param {number} height\n */\n/* c8 ignore next 2 */\nconst printCanvas = (canvas, height) =>\n  printImg(canvas.toDataURL(), height)\n\n/**\n * @param {Element} _dom\n */\n/* c8 ignore next */\nconst createVConsole = (_dom) => {}\n\n/**\n * @param {string} moduleName\n * @return {function(...any):void}\n */\n/* c8 ignore next */\nconst createModuleLogger = (moduleName) => _logging_common_js__WEBPACK_IMPORTED_MODULE_0__.createModuleLogger(print, moduleName)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/logging.node.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/map.js":
/*!**********************************!*\
  !*** ./node_modules/lib0/map.js ***!
  \**********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   any: () => (/* binding */ any),\n/* harmony export */   copy: () => (/* binding */ copy),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   setIfUndefined: () => (/* binding */ setIfUndefined)\n/* harmony export */ });\n/**\n * Utility module to work with key-value stores.\n *\n * @module map\n */\n\n/**\n * Creates a new Map instance.\n *\n * @function\n * @return {Map<any, any>}\n *\n * @function\n */\nconst create = () => new Map()\n\n/**\n * Copy a Map object into a fresh Map object.\n *\n * @function\n * @template K,V\n * @param {Map<K,V>} m\n * @return {Map<K,V>}\n */\nconst copy = m => {\n  const r = create()\n  m.forEach((v, k) => { r.set(k, v) })\n  return r\n}\n\n/**\n * Get map property. Create T if property is undefined and set T on map.\n *\n * ```js\n * const listeners = map.setIfUndefined(events, 'eventName', set.create)\n * listeners.add(listener)\n * ```\n *\n * @function\n * @template {Map<any, any>} MAP\n * @template {MAP extends Map<any,infer V> ? function():V : unknown} CF\n * @param {MAP} map\n * @param {MAP extends Map<infer K,any> ? K : unknown} key\n * @param {CF} createT\n * @return {ReturnType<CF>}\n */\nconst setIfUndefined = (map, key, createT) => {\n  let set = map.get(key)\n  if (set === undefined) {\n    map.set(key, set = createT())\n  }\n  return set\n}\n\n/**\n * Creates an Array and populates it with the content of all key-value pairs using the `f(value, key)` function.\n *\n * @function\n * @template K\n * @template V\n * @template R\n * @param {Map<K,V>} m\n * @param {function(V,K):R} f\n * @return {Array<R>}\n */\nconst map = (m, f) => {\n  const res = []\n  for (const [key, value] of m) {\n    res.push(f(value, key))\n  }\n  return res\n}\n\n/**\n * Tests whether any key-value pairs pass the test implemented by `f(value, key)`.\n *\n * @todo should rename to some - similarly to Array.some\n *\n * @function\n * @template K\n * @template V\n * @param {Map<K,V>} m\n * @param {function(V,K):boolean} f\n * @return {boolean}\n */\nconst any = (m, f) => {\n  for (const [key, value] of m) {\n    if (f(value, key)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * Tests whether all key-value pairs pass the test implemented by `f(value, key)`.\n *\n * @function\n * @template K\n * @template V\n * @param {Map<K,V>} m\n * @param {function(V,K):boolean} f\n * @return {boolean}\n */\nconst all = (m, f) => {\n  for (const [key, value] of m) {\n    if (!f(value, key)) {\n      return false\n    }\n  }\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/math.js":
/*!***********************************!*\
  !*** ./node_modules/lib0/math.js ***!
  \***********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   abs: () => (/* binding */ abs),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   ceil: () => (/* binding */ ceil),\n/* harmony export */   exp10: () => (/* binding */ exp10),\n/* harmony export */   floor: () => (/* binding */ floor),\n/* harmony export */   imul: () => (/* binding */ imul),\n/* harmony export */   isNaN: () => (/* binding */ isNaN),\n/* harmony export */   isNegativeZero: () => (/* binding */ isNegativeZero),\n/* harmony export */   log: () => (/* binding */ log),\n/* harmony export */   log10: () => (/* binding */ log10),\n/* harmony export */   log2: () => (/* binding */ log2),\n/* harmony export */   max: () => (/* binding */ max),\n/* harmony export */   min: () => (/* binding */ min),\n/* harmony export */   pow: () => (/* binding */ pow),\n/* harmony export */   round: () => (/* binding */ round),\n/* harmony export */   sign: () => (/* binding */ sign),\n/* harmony export */   sqrt: () => (/* binding */ sqrt)\n/* harmony export */ });\n/**\n * Common Math expressions.\n *\n * @module math\n */\n\nconst floor = Math.floor\nconst ceil = Math.ceil\nconst abs = Math.abs\nconst imul = Math.imul\nconst round = Math.round\nconst log10 = Math.log10\nconst log2 = Math.log2\nconst log = Math.log\nconst sqrt = Math.sqrt\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The sum of a and b\n */\nconst add = (a, b) => a + b\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The smaller element of a and b\n */\nconst min = (a, b) => a < b ? a : b\n\n/**\n * @function\n * @param {number} a\n * @param {number} b\n * @return {number} The bigger element of a and b\n */\nconst max = (a, b) => a > b ? a : b\n\nconst isNaN = Number.isNaN\n\nconst pow = Math.pow\n/**\n * Base 10 exponential function. Returns the value of 10 raised to the power of pow.\n *\n * @param {number} exp\n * @return {number}\n */\nconst exp10 = exp => Math.pow(10, exp)\n\nconst sign = Math.sign\n\n/**\n * @param {number} n\n * @return {boolean} Wether n is negative. This function also differentiates between -0 and +0\n */\nconst isNegativeZero = n => n !== 0 ? n < 0 : 1 / n < 0\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/math.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/metric.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/metric.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   atto: () => (/* binding */ atto),\n/* harmony export */   centi: () => (/* binding */ centi),\n/* harmony export */   deca: () => (/* binding */ deca),\n/* harmony export */   deci: () => (/* binding */ deci),\n/* harmony export */   exa: () => (/* binding */ exa),\n/* harmony export */   femto: () => (/* binding */ femto),\n/* harmony export */   giga: () => (/* binding */ giga),\n/* harmony export */   hecto: () => (/* binding */ hecto),\n/* harmony export */   kilo: () => (/* binding */ kilo),\n/* harmony export */   mega: () => (/* binding */ mega),\n/* harmony export */   micro: () => (/* binding */ micro),\n/* harmony export */   milli: () => (/* binding */ milli),\n/* harmony export */   nano: () => (/* binding */ nano),\n/* harmony export */   peta: () => (/* binding */ peta),\n/* harmony export */   pico: () => (/* binding */ pico),\n/* harmony export */   prefix: () => (/* binding */ prefix),\n/* harmony export */   tera: () => (/* binding */ tera),\n/* harmony export */   yocto: () => (/* binding */ yocto),\n/* harmony export */   yotta: () => (/* binding */ yotta),\n/* harmony export */   zepto: () => (/* binding */ zepto),\n/* harmony export */   zetta: () => (/* binding */ zetta)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/lib0/math.js\");\n/**\n * Utility module to convert metric values.\n *\n * @module metric\n */\n\n\n\nconst yotta = 1e24\nconst zetta = 1e21\nconst exa = 1e18\nconst peta = 1e15\nconst tera = 1e12\nconst giga = 1e9\nconst mega = 1e6\nconst kilo = 1e3\nconst hecto = 1e2\nconst deca = 10\nconst deci = 0.1\nconst centi = 0.01\nconst milli = 1e-3\nconst micro = 1e-6\nconst nano = 1e-9\nconst pico = 1e-12\nconst femto = 1e-15\nconst atto = 1e-18\nconst zepto = 1e-21\nconst yocto = 1e-24\n\nconst prefixUp = ['', 'k', 'M', 'G', 'T', 'P', 'E', 'Z', 'Y']\nconst prefixDown = ['', 'm', 'μ', 'n', 'p', 'f', 'a', 'z', 'y']\n\n/**\n * Calculate the metric prefix for a number. Assumes E.g. `prefix(1000) = { n: 1, prefix: 'k' }`\n *\n * @param {number} n\n * @param {number} [baseMultiplier] Multiplier of the base (10^(3*baseMultiplier)). E.g. `convert(time, -3)` if time is already in milli seconds\n * @return {{n:number,prefix:string}}\n */\nconst prefix = (n, baseMultiplier = 0) => {\n  const nPow = n === 0 ? 0 : _math_js__WEBPACK_IMPORTED_MODULE_0__.log10(n)\n  let mult = 0\n  while (nPow < mult * 3 && baseMultiplier > -8) {\n    baseMultiplier--\n    mult--\n  }\n  while (nPow >= 3 + mult * 3 && baseMultiplier < 8) {\n    baseMultiplier++\n    mult++\n  }\n  const prefix = baseMultiplier < 0 ? prefixDown[-baseMultiplier] : prefixUp[baseMultiplier]\n  return {\n    n: _math_js__WEBPACK_IMPORTED_MODULE_0__.round((mult > 0 ? n / _math_js__WEBPACK_IMPORTED_MODULE_0__.exp10(mult * 3) : n * _math_js__WEBPACK_IMPORTED_MODULE_0__.exp10(mult * -3)) * 1e12) / 1e12,\n    prefix\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/metric.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/mutex.js":
/*!************************************!*\
  !*** ./node_modules/lib0/mutex.js ***!
  \************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMutex: () => (/* binding */ createMutex)\n/* harmony export */ });\n/**\n * Mutual exclude for JavaScript.\n *\n * @module mutex\n */\n\n/**\n * @callback mutex\n * @param {function():void} cb Only executed when this mutex is not in the current stack\n * @param {function():void} [elseCb] Executed when this mutex is in the current stack\n */\n\n/**\n * Creates a mutual exclude function with the following property:\n *\n * ```js\n * const mutex = createMutex()\n * mutex(() => {\n *   // This function is immediately executed\n *   mutex(() => {\n *     // This function is not executed, as the mutex is already active.\n *   })\n * })\n * ```\n *\n * @return {mutex} A mutual exclude function\n * @public\n */\nconst createMutex = () => {\n  let token = true\n  return (f, g) => {\n    if (token) {\n      token = false\n      try {\n        f()\n      } finally {\n        token = true\n      }\n    } else if (g !== undefined) {\n      g()\n    }\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9tdXRleC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxpQkFBaUI7QUFDNUIsV0FBVyxpQkFBaUI7QUFDNUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLElBQUk7QUFDSjtBQUNBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9saWIwL211dGV4LmpzP2NjNjUiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBNdXR1YWwgZXhjbHVkZSBmb3IgSmF2YVNjcmlwdC5cbiAqXG4gKiBAbW9kdWxlIG11dGV4XG4gKi9cblxuLyoqXG4gKiBAY2FsbGJhY2sgbXV0ZXhcbiAqIEBwYXJhbSB7ZnVuY3Rpb24oKTp2b2lkfSBjYiBPbmx5IGV4ZWN1dGVkIHdoZW4gdGhpcyBtdXRleCBpcyBub3QgaW4gdGhlIGN1cnJlbnQgc3RhY2tcbiAqIEBwYXJhbSB7ZnVuY3Rpb24oKTp2b2lkfSBbZWxzZUNiXSBFeGVjdXRlZCB3aGVuIHRoaXMgbXV0ZXggaXMgaW4gdGhlIGN1cnJlbnQgc3RhY2tcbiAqL1xuXG4vKipcbiAqIENyZWF0ZXMgYSBtdXR1YWwgZXhjbHVkZSBmdW5jdGlvbiB3aXRoIHRoZSBmb2xsb3dpbmcgcHJvcGVydHk6XG4gKlxuICogYGBganNcbiAqIGNvbnN0IG11dGV4ID0gY3JlYXRlTXV0ZXgoKVxuICogbXV0ZXgoKCkgPT4ge1xuICogICAvLyBUaGlzIGZ1bmN0aW9uIGlzIGltbWVkaWF0ZWx5IGV4ZWN1dGVkXG4gKiAgIG11dGV4KCgpID0+IHtcbiAqICAgICAvLyBUaGlzIGZ1bmN0aW9uIGlzIG5vdCBleGVjdXRlZCwgYXMgdGhlIG11dGV4IGlzIGFscmVhZHkgYWN0aXZlLlxuICogICB9KVxuICogfSlcbiAqIGBgYFxuICpcbiAqIEByZXR1cm4ge211dGV4fSBBIG11dHVhbCBleGNsdWRlIGZ1bmN0aW9uXG4gKiBAcHVibGljXG4gKi9cbmV4cG9ydCBjb25zdCBjcmVhdGVNdXRleCA9ICgpID0+IHtcbiAgbGV0IHRva2VuID0gdHJ1ZVxuICByZXR1cm4gKGYsIGcpID0+IHtcbiAgICBpZiAodG9rZW4pIHtcbiAgICAgIHRva2VuID0gZmFsc2VcbiAgICAgIHRyeSB7XG4gICAgICAgIGYoKVxuICAgICAgfSBmaW5hbGx5IHtcbiAgICAgICAgdG9rZW4gPSB0cnVlXG4gICAgICB9XG4gICAgfSBlbHNlIGlmIChnICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGcoKVxuICAgIH1cbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/mutex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/number.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/number.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HIGHEST_INT32: () => (/* binding */ HIGHEST_INT32),\n/* harmony export */   HIGHEST_UINT32: () => (/* binding */ HIGHEST_UINT32),\n/* harmony export */   LOWEST_INT32: () => (/* binding */ LOWEST_INT32),\n/* harmony export */   MAX_SAFE_INTEGER: () => (/* binding */ MAX_SAFE_INTEGER),\n/* harmony export */   MIN_SAFE_INTEGER: () => (/* binding */ MIN_SAFE_INTEGER),\n/* harmony export */   countBits: () => (/* binding */ countBits),\n/* harmony export */   isInteger: () => (/* binding */ isInteger),\n/* harmony export */   isNaN: () => (/* binding */ isNaN),\n/* harmony export */   parseInt: () => (/* binding */ parseInt)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/./node_modules/lib0/binary.js\");\n/**\n * Utility helpers for working with numbers.\n *\n * @module number\n */\n\n\n\n\nconst MAX_SAFE_INTEGER = Number.MAX_SAFE_INTEGER\nconst MIN_SAFE_INTEGER = Number.MIN_SAFE_INTEGER\n\nconst LOWEST_INT32 = 1 << 31\nconst HIGHEST_INT32 = _binary_js__WEBPACK_IMPORTED_MODULE_0__.BITS31\nconst HIGHEST_UINT32 = _binary_js__WEBPACK_IMPORTED_MODULE_0__.BITS32\n\n/* c8 ignore next */\nconst isInteger = Number.isInteger || (num => typeof num === 'number' && isFinite(num) && _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(num) === num)\nconst isNaN = Number.isNaN\nconst parseInt = Number.parseInt\n\n/**\n * Count the number of \"1\" bits in an unsigned 32bit number.\n *\n * Super fun bitcount algorithm by Brian Kernighan.\n *\n * @param {number} n\n */\nconst countBits = n => {\n  n &= _binary_js__WEBPACK_IMPORTED_MODULE_0__.BITS32\n  let count = 0\n  while (n) {\n    n &= (n - 1)\n    count++\n  }\n  return count\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9udW1iZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVpQztBQUNJOztBQUU5QjtBQUNBOztBQUVBO0FBQ0Esc0JBQXNCLDhDQUFhO0FBQ25DLHVCQUF1Qiw4Q0FBYTs7QUFFM0M7QUFDTywwRkFBMEYsMkNBQVU7QUFDcEc7QUFDQTs7QUFFUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CO0FBQ087QUFDUCxPQUFPLDhDQUFhO0FBQ3BCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9saWIwL251bWJlci5qcz81OTlhIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVXRpbGl0eSBoZWxwZXJzIGZvciB3b3JraW5nIHdpdGggbnVtYmVycy5cbiAqXG4gKiBAbW9kdWxlIG51bWJlclxuICovXG5cbmltcG9ydCAqIGFzIG1hdGggZnJvbSAnLi9tYXRoLmpzJ1xuaW1wb3J0ICogYXMgYmluYXJ5IGZyb20gJy4vYmluYXJ5LmpzJ1xuXG5leHBvcnQgY29uc3QgTUFYX1NBRkVfSU5URUdFUiA9IE51bWJlci5NQVhfU0FGRV9JTlRFR0VSXG5leHBvcnQgY29uc3QgTUlOX1NBRkVfSU5URUdFUiA9IE51bWJlci5NSU5fU0FGRV9JTlRFR0VSXG5cbmV4cG9ydCBjb25zdCBMT1dFU1RfSU5UMzIgPSAxIDw8IDMxXG5leHBvcnQgY29uc3QgSElHSEVTVF9JTlQzMiA9IGJpbmFyeS5CSVRTMzFcbmV4cG9ydCBjb25zdCBISUdIRVNUX1VJTlQzMiA9IGJpbmFyeS5CSVRTMzJcblxuLyogYzggaWdub3JlIG5leHQgKi9cbmV4cG9ydCBjb25zdCBpc0ludGVnZXIgPSBOdW1iZXIuaXNJbnRlZ2VyIHx8IChudW0gPT4gdHlwZW9mIG51bSA9PT0gJ251bWJlcicgJiYgaXNGaW5pdGUobnVtKSAmJiBtYXRoLmZsb29yKG51bSkgPT09IG51bSlcbmV4cG9ydCBjb25zdCBpc05hTiA9IE51bWJlci5pc05hTlxuZXhwb3J0IGNvbnN0IHBhcnNlSW50ID0gTnVtYmVyLnBhcnNlSW50XG5cbi8qKlxuICogQ291bnQgdGhlIG51bWJlciBvZiBcIjFcIiBiaXRzIGluIGFuIHVuc2lnbmVkIDMyYml0IG51bWJlci5cbiAqXG4gKiBTdXBlciBmdW4gYml0Y291bnQgYWxnb3JpdGhtIGJ5IEJyaWFuIEtlcm5pZ2hhbi5cbiAqXG4gKiBAcGFyYW0ge251bWJlcn0gblxuICovXG5leHBvcnQgY29uc3QgY291bnRCaXRzID0gbiA9PiB7XG4gIG4gJj0gYmluYXJ5LkJJVFMzMlxuICBsZXQgY291bnQgPSAwXG4gIHdoaWxlIChuKSB7XG4gICAgbiAmPSAobiAtIDEpXG4gICAgY291bnQrK1xuICB9XG4gIHJldHVybiBjb3VudFxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/object.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/object.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assign: () => (/* binding */ assign),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   deepFreeze: () => (/* binding */ deepFreeze),\n/* harmony export */   equalFlat: () => (/* binding */ equalFlat),\n/* harmony export */   every: () => (/* binding */ every),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   freeze: () => (/* binding */ freeze),\n/* harmony export */   hasProperty: () => (/* binding */ hasProperty),\n/* harmony export */   isEmpty: () => (/* binding */ isEmpty),\n/* harmony export */   keys: () => (/* binding */ keys),\n/* harmony export */   length: () => (/* binding */ length),\n/* harmony export */   map: () => (/* binding */ map),\n/* harmony export */   size: () => (/* binding */ size),\n/* harmony export */   some: () => (/* binding */ some)\n/* harmony export */ });\n/**\n * Utility functions for working with EcmaScript objects.\n *\n * @module object\n */\n\n/**\n * @return {Object<string,any>} obj\n */\nconst create = () => Object.create(null)\n\n/**\n * Object.assign\n */\nconst assign = Object.assign\n\n/**\n * @param {Object<string,any>} obj\n */\nconst keys = Object.keys\n\n/**\n * @template V\n * @param {{[k:string]:V}} obj\n * @param {function(V,string):any} f\n */\nconst forEach = (obj, f) => {\n  for (const key in obj) {\n    f(obj[key], key)\n  }\n}\n\n/**\n * @todo implement mapToArray & map\n *\n * @template R\n * @param {Object<string,any>} obj\n * @param {function(any,string):R} f\n * @return {Array<R>}\n */\nconst map = (obj, f) => {\n  const results = []\n  for (const key in obj) {\n    results.push(f(obj[key], key))\n  }\n  return results\n}\n\n/**\n * @deprecated use object.size instead\n * @param {Object<string,any>} obj\n * @return {number}\n */\nconst length = obj => keys(obj).length\n\n/**\n * @param {Object<string,any>} obj\n * @return {number}\n */\nconst size = obj => keys(obj).length\n\n/**\n * @param {Object<string,any>} obj\n * @param {function(any,string):boolean} f\n * @return {boolean}\n */\nconst some = (obj, f) => {\n  for (const key in obj) {\n    if (f(obj[key], key)) {\n      return true\n    }\n  }\n  return false\n}\n\n/**\n * @param {Object|null|undefined} obj\n */\nconst isEmpty = obj => {\n  // eslint-disable-next-line no-unreachable-loop\n  for (const _k in obj) {\n    return false\n  }\n  return true\n}\n\n/**\n * @param {Object<string,any>} obj\n * @param {function(any,string):boolean} f\n * @return {boolean}\n */\nconst every = (obj, f) => {\n  for (const key in obj) {\n    if (!f(obj[key], key)) {\n      return false\n    }\n  }\n  return true\n}\n\n/**\n * Calls `Object.prototype.hasOwnProperty`.\n *\n * @param {any} obj\n * @param {string|symbol} key\n * @return {boolean}\n */\nconst hasProperty = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key)\n\n/**\n * @param {Object<string,any>} a\n * @param {Object<string,any>} b\n * @return {boolean}\n */\nconst equalFlat = (a, b) => a === b || (size(a) === size(b) && every(a, (val, key) => (val !== undefined || hasProperty(b, key)) && b[key] === val))\n\n/**\n * Make an object immutable. This hurts performance and is usually not needed if you perform good\n * coding practices.\n */\nconst freeze = Object.freeze\n\n/**\n * Make an object and all its children immutable.\n * This *really* hurts performance and is usually not needed if you perform good coding practices.\n *\n * @template {any} T\n * @param {T} o\n * @return {Readonly<T>}\n */\nconst deepFreeze = (o) => {\n  for (const key in o) {\n    const c = o[key]\n    if (typeof c === 'object' || typeof c === 'function') {\n      deepFreeze(o[key])\n    }\n  }\n  return freeze(o)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/observable.js":
/*!*****************************************!*\
  !*** ./node_modules/lib0/observable.js ***!
  \*****************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Observable: () => (/* binding */ Observable),\n/* harmony export */   ObservableV2: () => (/* binding */ ObservableV2)\n/* harmony export */ });\n/* harmony import */ var _map_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./map.js */ \"(ssr)/./node_modules/lib0/map.js\");\n/* harmony import */ var _set_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./set.js */ \"(ssr)/./node_modules/lib0/set.js\");\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/lib0/array.js\");\n/**\n * Observable class prototype.\n *\n * @module observable\n */\n\n\n\n\n\n/**\n * Handles named events.\n * @experimental\n *\n * This is basically a (better typed) duplicate of Observable, which will replace Observable in the\n * next release.\n *\n * @template {{[key in keyof EVENTS]: function(...any):void}} EVENTS\n */\nclass ObservableV2 {\n  constructor () {\n    /**\n     * Some desc.\n     * @type {Map<string, Set<any>>}\n     */\n    this._observers = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  on (name, f) {\n    _map_js__WEBPACK_IMPORTED_MODULE_0__.setIfUndefined(this._observers, /** @type {string} */ (name), _set_js__WEBPACK_IMPORTED_MODULE_1__.create).add(f)\n    return f\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  once (name, f) {\n    /**\n     * @param  {...any} args\n     */\n    const _f = (...args) => {\n      this.off(name, /** @type {any} */ (_f))\n      f(...args)\n    }\n    this.on(name, /** @type {any} */ (_f))\n  }\n\n  /**\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name\n   * @param {EVENTS[NAME]} f\n   */\n  off (name, f) {\n    const observers = this._observers.get(name)\n    if (observers !== undefined) {\n      observers.delete(f)\n      if (observers.size === 0) {\n        this._observers.delete(name)\n      }\n    }\n  }\n\n  /**\n   * Emit a named event. All registered event listeners that listen to the\n   * specified name will receive the event.\n   *\n   * @todo This should catch exceptions\n   *\n   * @template {keyof EVENTS & string} NAME\n   * @param {NAME} name The event name.\n   * @param {Parameters<EVENTS[NAME]>} args The arguments that are applied to the event listener.\n   */\n  emit (name, args) {\n    // copy all listeners to an array first to make sure that no event is emitted to listeners that are subscribed while the event handler is called.\n    return _array_js__WEBPACK_IMPORTED_MODULE_2__.from((this._observers.get(name) || _map_js__WEBPACK_IMPORTED_MODULE_0__.create()).values()).forEach(f => f(...args))\n  }\n\n  destroy () {\n    this._observers = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  }\n}\n\n/* c8 ignore start */\n/**\n * Handles named events.\n *\n * @deprecated\n * @template N\n */\nclass Observable {\n  constructor () {\n    /**\n     * Some desc.\n     * @type {Map<N, any>}\n     */\n    this._observers = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  on (name, f) {\n    _map_js__WEBPACK_IMPORTED_MODULE_0__.setIfUndefined(this._observers, name, _set_js__WEBPACK_IMPORTED_MODULE_1__.create).add(f)\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  once (name, f) {\n    /**\n     * @param  {...any} args\n     */\n    const _f = (...args) => {\n      this.off(name, _f)\n      f(...args)\n    }\n    this.on(name, _f)\n  }\n\n  /**\n   * @param {N} name\n   * @param {function} f\n   */\n  off (name, f) {\n    const observers = this._observers.get(name)\n    if (observers !== undefined) {\n      observers.delete(f)\n      if (observers.size === 0) {\n        this._observers.delete(name)\n      }\n    }\n  }\n\n  /**\n   * Emit a named event. All registered event listeners that listen to the\n   * specified name will receive the event.\n   *\n   * @todo This should catch exceptions\n   *\n   * @param {N} name The event name.\n   * @param {Array<any>} args The arguments that are applied to the event listener.\n   */\n  emit (name, args) {\n    // copy all listeners to an array first to make sure that no event is emitted to listeners that are subscribed while the event handler is called.\n    return _array_js__WEBPACK_IMPORTED_MODULE_2__.from((this._observers.get(name) || _map_js__WEBPACK_IMPORTED_MODULE_0__.create()).values()).forEach(f => f(...args))\n  }\n\n  destroy () {\n    this._observers = _map_js__WEBPACK_IMPORTED_MODULE_0__.create()\n  }\n}\n/* c8 ignore end */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9vYnNlcnZhYmxlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFK0I7QUFDQTtBQUNJOztBQUVuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsK0NBQStDO0FBQzlEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQSxzQkFBc0IsMkNBQVU7QUFDaEM7O0FBRUE7QUFDQSxnQkFBZ0IsdUJBQXVCO0FBQ3ZDLGFBQWEsTUFBTTtBQUNuQixhQUFhLGNBQWM7QUFDM0I7QUFDQTtBQUNBLElBQUksbURBQWtCLDZCQUE2QixRQUFRLFdBQVcsMkNBQVU7QUFDaEY7QUFDQTs7QUFFQTtBQUNBLGdCQUFnQix1QkFBdUI7QUFDdkMsYUFBYSxNQUFNO0FBQ25CLGFBQWEsY0FBYztBQUMzQjtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsUUFBUTtBQUN4QjtBQUNBO0FBQ0EsZ0NBQWdDLEtBQUs7QUFDckM7QUFDQTtBQUNBLDZCQUE2QixLQUFLO0FBQ2xDOztBQUVBO0FBQ0EsZ0JBQWdCLHVCQUF1QjtBQUN2QyxhQUFhLE1BQU07QUFDbkIsYUFBYSxjQUFjO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQix1QkFBdUI7QUFDdkMsYUFBYSxNQUFNO0FBQ25CLGFBQWEsMEJBQTBCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLFdBQVcsMkNBQVUsK0JBQStCLDJDQUFVO0FBQzlEOztBQUVBO0FBQ0Esc0JBQXNCLDJDQUFVO0FBQ2hDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBLHNCQUFzQiwyQ0FBVTtBQUNoQzs7QUFFQTtBQUNBLGFBQWEsR0FBRztBQUNoQixhQUFhLFVBQVU7QUFDdkI7QUFDQTtBQUNBLElBQUksbURBQWtCLHdCQUF3QiwyQ0FBVTtBQUN4RDs7QUFFQTtBQUNBLGFBQWEsR0FBRztBQUNoQixhQUFhLFVBQVU7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLFFBQVE7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLEdBQUc7QUFDaEIsYUFBYSxVQUFVO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsR0FBRztBQUNoQixhQUFhLFlBQVk7QUFDekI7QUFDQTtBQUNBO0FBQ0EsV0FBVywyQ0FBVSwrQkFBK0IsMkNBQVU7QUFDOUQ7O0FBRUE7QUFDQSxzQkFBc0IsMkNBQVU7QUFDaEM7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9saWIwL29ic2VydmFibGUuanM/MzdmMyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIE9ic2VydmFibGUgY2xhc3MgcHJvdG90eXBlLlxuICpcbiAqIEBtb2R1bGUgb2JzZXJ2YWJsZVxuICovXG5cbmltcG9ydCAqIGFzIG1hcCBmcm9tICcuL21hcC5qcydcbmltcG9ydCAqIGFzIHNldCBmcm9tICcuL3NldC5qcydcbmltcG9ydCAqIGFzIGFycmF5IGZyb20gJy4vYXJyYXkuanMnXG5cbi8qKlxuICogSGFuZGxlcyBuYW1lZCBldmVudHMuXG4gKiBAZXhwZXJpbWVudGFsXG4gKlxuICogVGhpcyBpcyBiYXNpY2FsbHkgYSAoYmV0dGVyIHR5cGVkKSBkdXBsaWNhdGUgb2YgT2JzZXJ2YWJsZSwgd2hpY2ggd2lsbCByZXBsYWNlIE9ic2VydmFibGUgaW4gdGhlXG4gKiBuZXh0IHJlbGVhc2UuXG4gKlxuICogQHRlbXBsYXRlIHt7W2tleSBpbiBrZXlvZiBFVkVOVFNdOiBmdW5jdGlvbiguLi5hbnkpOnZvaWR9fSBFVkVOVFNcbiAqL1xuZXhwb3J0IGNsYXNzIE9ic2VydmFibGVWMiB7XG4gIGNvbnN0cnVjdG9yICgpIHtcbiAgICAvKipcbiAgICAgKiBTb21lIGRlc2MuXG4gICAgICogQHR5cGUge01hcDxzdHJpbmcsIFNldDxhbnk+Pn1cbiAgICAgKi9cbiAgICB0aGlzLl9vYnNlcnZlcnMgPSBtYXAuY3JlYXRlKClcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGVtcGxhdGUge2tleW9mIEVWRU5UUyAmIHN0cmluZ30gTkFNRVxuICAgKiBAcGFyYW0ge05BTUV9IG5hbWVcbiAgICogQHBhcmFtIHtFVkVOVFNbTkFNRV19IGZcbiAgICovXG4gIG9uIChuYW1lLCBmKSB7XG4gICAgbWFwLnNldElmVW5kZWZpbmVkKHRoaXMuX29ic2VydmVycywgLyoqIEB0eXBlIHtzdHJpbmd9ICovIChuYW1lKSwgc2V0LmNyZWF0ZSkuYWRkKGYpXG4gICAgcmV0dXJuIGZcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGVtcGxhdGUge2tleW9mIEVWRU5UUyAmIHN0cmluZ30gTkFNRVxuICAgKiBAcGFyYW0ge05BTUV9IG5hbWVcbiAgICogQHBhcmFtIHtFVkVOVFNbTkFNRV19IGZcbiAgICovXG4gIG9uY2UgKG5hbWUsIGYpIHtcbiAgICAvKipcbiAgICAgKiBAcGFyYW0gIHsuLi5hbnl9IGFyZ3NcbiAgICAgKi9cbiAgICBjb25zdCBfZiA9ICguLi5hcmdzKSA9PiB7XG4gICAgICB0aGlzLm9mZihuYW1lLCAvKiogQHR5cGUge2FueX0gKi8gKF9mKSlcbiAgICAgIGYoLi4uYXJncylcbiAgICB9XG4gICAgdGhpcy5vbihuYW1lLCAvKiogQHR5cGUge2FueX0gKi8gKF9mKSlcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGVtcGxhdGUge2tleW9mIEVWRU5UUyAmIHN0cmluZ30gTkFNRVxuICAgKiBAcGFyYW0ge05BTUV9IG5hbWVcbiAgICogQHBhcmFtIHtFVkVOVFNbTkFNRV19IGZcbiAgICovXG4gIG9mZiAobmFtZSwgZikge1xuICAgIGNvbnN0IG9ic2VydmVycyA9IHRoaXMuX29ic2VydmVycy5nZXQobmFtZSlcbiAgICBpZiAob2JzZXJ2ZXJzICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIG9ic2VydmVycy5kZWxldGUoZilcbiAgICAgIGlmIChvYnNlcnZlcnMuc2l6ZSA9PT0gMCkge1xuICAgICAgICB0aGlzLl9vYnNlcnZlcnMuZGVsZXRlKG5hbWUpXG4gICAgICB9XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEVtaXQgYSBuYW1lZCBldmVudC4gQWxsIHJlZ2lzdGVyZWQgZXZlbnQgbGlzdGVuZXJzIHRoYXQgbGlzdGVuIHRvIHRoZVxuICAgKiBzcGVjaWZpZWQgbmFtZSB3aWxsIHJlY2VpdmUgdGhlIGV2ZW50LlxuICAgKlxuICAgKiBAdG9kbyBUaGlzIHNob3VsZCBjYXRjaCBleGNlcHRpb25zXG4gICAqXG4gICAqIEB0ZW1wbGF0ZSB7a2V5b2YgRVZFTlRTICYgc3RyaW5nfSBOQU1FXG4gICAqIEBwYXJhbSB7TkFNRX0gbmFtZSBUaGUgZXZlbnQgbmFtZS5cbiAgICogQHBhcmFtIHtQYXJhbWV0ZXJzPEVWRU5UU1tOQU1FXT59IGFyZ3MgVGhlIGFyZ3VtZW50cyB0aGF0IGFyZSBhcHBsaWVkIHRvIHRoZSBldmVudCBsaXN0ZW5lci5cbiAgICovXG4gIGVtaXQgKG5hbWUsIGFyZ3MpIHtcbiAgICAvLyBjb3B5IGFsbCBsaXN0ZW5lcnMgdG8gYW4gYXJyYXkgZmlyc3QgdG8gbWFrZSBzdXJlIHRoYXQgbm8gZXZlbnQgaXMgZW1pdHRlZCB0byBsaXN0ZW5lcnMgdGhhdCBhcmUgc3Vic2NyaWJlZCB3aGlsZSB0aGUgZXZlbnQgaGFuZGxlciBpcyBjYWxsZWQuXG4gICAgcmV0dXJuIGFycmF5LmZyb20oKHRoaXMuX29ic2VydmVycy5nZXQobmFtZSkgfHwgbWFwLmNyZWF0ZSgpKS52YWx1ZXMoKSkuZm9yRWFjaChmID0+IGYoLi4uYXJncykpXG4gIH1cblxuICBkZXN0cm95ICgpIHtcbiAgICB0aGlzLl9vYnNlcnZlcnMgPSBtYXAuY3JlYXRlKClcbiAgfVxufVxuXG4vKiBjOCBpZ25vcmUgc3RhcnQgKi9cbi8qKlxuICogSGFuZGxlcyBuYW1lZCBldmVudHMuXG4gKlxuICogQGRlcHJlY2F0ZWRcbiAqIEB0ZW1wbGF0ZSBOXG4gKi9cbmV4cG9ydCBjbGFzcyBPYnNlcnZhYmxlIHtcbiAgY29uc3RydWN0b3IgKCkge1xuICAgIC8qKlxuICAgICAqIFNvbWUgZGVzYy5cbiAgICAgKiBAdHlwZSB7TWFwPE4sIGFueT59XG4gICAgICovXG4gICAgdGhpcy5fb2JzZXJ2ZXJzID0gbWFwLmNyZWF0ZSgpXG4gIH1cblxuICAvKipcbiAgICogQHBhcmFtIHtOfSBuYW1lXG4gICAqIEBwYXJhbSB7ZnVuY3Rpb259IGZcbiAgICovXG4gIG9uIChuYW1lLCBmKSB7XG4gICAgbWFwLnNldElmVW5kZWZpbmVkKHRoaXMuX29ic2VydmVycywgbmFtZSwgc2V0LmNyZWF0ZSkuYWRkKGYpXG4gIH1cblxuICAvKipcbiAgICogQHBhcmFtIHtOfSBuYW1lXG4gICAqIEBwYXJhbSB7ZnVuY3Rpb259IGZcbiAgICovXG4gIG9uY2UgKG5hbWUsIGYpIHtcbiAgICAvKipcbiAgICAgKiBAcGFyYW0gIHsuLi5hbnl9IGFyZ3NcbiAgICAgKi9cbiAgICBjb25zdCBfZiA9ICguLi5hcmdzKSA9PiB7XG4gICAgICB0aGlzLm9mZihuYW1lLCBfZilcbiAgICAgIGYoLi4uYXJncylcbiAgICB9XG4gICAgdGhpcy5vbihuYW1lLCBfZilcbiAgfVxuXG4gIC8qKlxuICAgKiBAcGFyYW0ge059IG5hbWVcbiAgICogQHBhcmFtIHtmdW5jdGlvbn0gZlxuICAgKi9cbiAgb2ZmIChuYW1lLCBmKSB7XG4gICAgY29uc3Qgb2JzZXJ2ZXJzID0gdGhpcy5fb2JzZXJ2ZXJzLmdldChuYW1lKVxuICAgIGlmIChvYnNlcnZlcnMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgb2JzZXJ2ZXJzLmRlbGV0ZShmKVxuICAgICAgaWYgKG9ic2VydmVycy5zaXplID09PSAwKSB7XG4gICAgICAgIHRoaXMuX29ic2VydmVycy5kZWxldGUobmFtZSlcbiAgICAgIH1cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogRW1pdCBhIG5hbWVkIGV2ZW50LiBBbGwgcmVnaXN0ZXJlZCBldmVudCBsaXN0ZW5lcnMgdGhhdCBsaXN0ZW4gdG8gdGhlXG4gICAqIHNwZWNpZmllZCBuYW1lIHdpbGwgcmVjZWl2ZSB0aGUgZXZlbnQuXG4gICAqXG4gICAqIEB0b2RvIFRoaXMgc2hvdWxkIGNhdGNoIGV4Y2VwdGlvbnNcbiAgICpcbiAgICogQHBhcmFtIHtOfSBuYW1lIFRoZSBldmVudCBuYW1lLlxuICAgKiBAcGFyYW0ge0FycmF5PGFueT59IGFyZ3MgVGhlIGFyZ3VtZW50cyB0aGF0IGFyZSBhcHBsaWVkIHRvIHRoZSBldmVudCBsaXN0ZW5lci5cbiAgICovXG4gIGVtaXQgKG5hbWUsIGFyZ3MpIHtcbiAgICAvLyBjb3B5IGFsbCBsaXN0ZW5lcnMgdG8gYW4gYXJyYXkgZmlyc3QgdG8gbWFrZSBzdXJlIHRoYXQgbm8gZXZlbnQgaXMgZW1pdHRlZCB0byBsaXN0ZW5lcnMgdGhhdCBhcmUgc3Vic2NyaWJlZCB3aGlsZSB0aGUgZXZlbnQgaGFuZGxlciBpcyBjYWxsZWQuXG4gICAgcmV0dXJuIGFycmF5LmZyb20oKHRoaXMuX29ic2VydmVycy5nZXQobmFtZSkgfHwgbWFwLmNyZWF0ZSgpKS52YWx1ZXMoKSkuZm9yRWFjaChmID0+IGYoLi4uYXJncykpXG4gIH1cblxuICBkZXN0cm95ICgpIHtcbiAgICB0aGlzLl9vYnNlcnZlcnMgPSBtYXAuY3JlYXRlKClcbiAgfVxufVxuLyogYzggaWdub3JlIGVuZCAqL1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/observable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/pair.js":
/*!***********************************!*\
  !*** ./node_modules/lib0/pair.js ***!
  \***********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Pair: () => (/* binding */ Pair),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   createReversed: () => (/* binding */ createReversed),\n/* harmony export */   forEach: () => (/* binding */ forEach),\n/* harmony export */   map: () => (/* binding */ map)\n/* harmony export */ });\n/**\n * Working with value pairs.\n *\n * @module pair\n */\n\n/**\n * @template L,R\n */\nclass Pair {\n  /**\n   * @param {L} left\n   * @param {R} right\n   */\n  constructor (left, right) {\n    this.left = left\n    this.right = right\n  }\n}\n\n/**\n * @template L,R\n * @param {L} left\n * @param {R} right\n * @return {Pair<L,R>}\n */\nconst create = (left, right) => new Pair(left, right)\n\n/**\n * @template L,R\n * @param {R} right\n * @param {L} left\n * @return {Pair<L,R>}\n */\nconst createReversed = (right, left) => new Pair(left, right)\n\n/**\n * @template L,R\n * @param {Array<Pair<L,R>>} arr\n * @param {function(L, R):any} f\n */\nconst forEach = (arr, f) => arr.forEach(p => f(p.left, p.right))\n\n/**\n * @template L,R,X\n * @param {Array<Pair<L,R>>} arr\n * @param {function(L, R):X} f\n * @return {Array<X>}\n */\nconst map = (arr, f) => arr.map(p => f(p.left, p.right))\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/pair.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/promise.js":
/*!**************************************!*\
  !*** ./node_modules/lib0/promise.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   all: () => (/* binding */ all),\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   createEmpty: () => (/* binding */ createEmpty),\n/* harmony export */   isPromise: () => (/* binding */ isPromise),\n/* harmony export */   reject: () => (/* binding */ reject),\n/* harmony export */   resolve: () => (/* binding */ resolve),\n/* harmony export */   resolveWith: () => (/* binding */ resolveWith),\n/* harmony export */   until: () => (/* binding */ until),\n/* harmony export */   untilAsync: () => (/* binding */ untilAsync),\n/* harmony export */   wait: () => (/* binding */ wait)\n/* harmony export */ });\n/* harmony import */ var _time_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./time.js */ \"(ssr)/./node_modules/lib0/time.js\");\n/**\n * Utility helpers to work with promises.\n *\n * @module promise\n */\n\n\n\n/**\n * @template T\n * @callback PromiseResolve\n * @param {T|PromiseLike<T>} [result]\n */\n\n/**\n * @template T\n * @param {function(PromiseResolve<T>,function(Error):void):any} f\n * @return {Promise<T>}\n */\nconst create = f => /** @type {Promise<T>} */ (new Promise(f))\n\n/**\n * @param {function(function():void,function(Error):void):void} f\n * @return {Promise<void>}\n */\nconst createEmpty = f => new Promise(f)\n\n/**\n * `Promise.all` wait for all promises in the array to resolve and return the result\n * @template {unknown[] | []} PS\n *\n * @param {PS} ps\n * @return {Promise<{ -readonly [P in keyof PS]: Awaited<PS[P]> }>}\n */\nconst all = Promise.all.bind(Promise)\n\n/**\n * @param {Error} [reason]\n * @return {Promise<never>}\n */\nconst reject = reason => Promise.reject(reason)\n\n/**\n * @template T\n * @param {T|void} res\n * @return {Promise<T|void>}\n */\nconst resolve = res => Promise.resolve(res)\n\n/**\n * @template T\n * @param {T} res\n * @return {Promise<T>}\n */\nconst resolveWith = res => Promise.resolve(res)\n\n/**\n * @todo Next version, reorder parameters: check, [timeout, [intervalResolution]]\n * @deprecated use untilAsync instead\n *\n * @param {number} timeout\n * @param {function():boolean} check\n * @param {number} [intervalResolution]\n * @return {Promise<void>}\n */\nconst until = (timeout, check, intervalResolution = 10) => create((resolve, reject) => {\n  const startTime = _time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime()\n  const hasTimeout = timeout > 0\n  const untilInterval = () => {\n    if (check()) {\n      clearInterval(intervalHandle)\n      resolve()\n    } else if (hasTimeout) {\n      /* c8 ignore else */\n      if (_time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime() - startTime > timeout) {\n        clearInterval(intervalHandle)\n        reject(new Error('Timeout'))\n      }\n    }\n  }\n  const intervalHandle = setInterval(untilInterval, intervalResolution)\n})\n\n/**\n * @param {()=>Promise<boolean>|boolean} check\n * @param {number} timeout\n * @param {number} intervalResolution\n * @return {Promise<void>}\n */\nconst untilAsync = async (check, timeout = 0, intervalResolution = 10) => {\n  const startTime = _time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime()\n  const noTimeout = timeout <= 0\n  // eslint-disable-next-line no-unmodified-loop-condition\n  while (noTimeout || _time_js__WEBPACK_IMPORTED_MODULE_0__.getUnixTime() - startTime <= timeout) {\n    if (await check()) return\n    await wait(intervalResolution)\n  }\n  throw new Error('Timeout')\n}\n\n/**\n * @param {number} timeout\n * @return {Promise<undefined>}\n */\nconst wait = timeout => create((resolve, _reject) => setTimeout(resolve, timeout))\n\n/**\n * Checks if an object is a promise using ducktyping.\n *\n * Promises are often polyfilled, so it makes sense to add some additional guarantees if the user of this\n * library has some insane environment where global Promise objects are overwritten.\n *\n * @param {any} p\n * @return {boolean}\n */\nconst isPromise = p => p instanceof Promise || (p && p.then && p.catch && p.finally)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/promise.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/random.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/random.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   oneOf: () => (/* binding */ oneOf),\n/* harmony export */   rand: () => (/* binding */ rand),\n/* harmony export */   uint32: () => (/* binding */ uint32),\n/* harmony export */   uint53: () => (/* binding */ uint53),\n/* harmony export */   uuidv4: () => (/* binding */ uuidv4)\n/* harmony export */ });\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var _binary_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./binary.js */ \"(ssr)/./node_modules/lib0/binary.js\");\n/* harmony import */ var lib0_webcrypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/webcrypto */ \"(ssr)/./node_modules/lib0/webcrypto.node.js\");\n/**\n * Isomorphic module for true random numbers / buffers / uuids.\n *\n * Attention: falls back to Math.random if the browser does not support crypto.\n *\n * @module random\n */\n\n\n\n\n\nconst rand = Math.random\n\nconst uint32 = () => (0,lib0_webcrypto__WEBPACK_IMPORTED_MODULE_0__.getRandomValues)(new Uint32Array(1))[0]\n\nconst uint53 = () => {\n  const arr = (0,lib0_webcrypto__WEBPACK_IMPORTED_MODULE_0__.getRandomValues)(new Uint32Array(8))\n  return (arr[0] & _binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS21) * (_binary_js__WEBPACK_IMPORTED_MODULE_1__.BITS32 + 1) + (arr[1] >>> 0)\n}\n\n/**\n * @template T\n * @param {Array<T>} arr\n * @return {T}\n */\nconst oneOf = arr => arr[_math_js__WEBPACK_IMPORTED_MODULE_2__.floor(rand() * arr.length)]\n\n// @ts-ignore\nconst uuidv4Template = [1e7] + -1e3 + -4e3 + -8e3 + -1e11\n\n/**\n * @return {string}\n */\nconst uuidv4 = () => uuidv4Template.replace(/[018]/g, /** @param {number} c */ c =>\n  (c ^ uint32() & 15 >> c / 4).toString(16)\n)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9yYW5kb20uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFaUM7QUFDSTtBQUNXOztBQUV6Qzs7QUFFQSxxQkFBcUIsK0RBQWU7O0FBRXBDO0FBQ1AsY0FBYywrREFBZTtBQUM3QixtQkFBbUIsOENBQWEsS0FBSyw4Q0FBYTtBQUNsRDs7QUFFQTtBQUNBO0FBQ0EsV0FBVyxVQUFVO0FBQ3JCLFlBQVk7QUFDWjtBQUNPLHlCQUF5QiwyQ0FBVTs7QUFFMUM7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWjtBQUNPLGtFQUFrRSxRQUFRO0FBQ2pGO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2xpYjAvcmFuZG9tLmpzP2YwZmMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBJc29tb3JwaGljIG1vZHVsZSBmb3IgdHJ1ZSByYW5kb20gbnVtYmVycyAvIGJ1ZmZlcnMgLyB1dWlkcy5cbiAqXG4gKiBBdHRlbnRpb246IGZhbGxzIGJhY2sgdG8gTWF0aC5yYW5kb20gaWYgdGhlIGJyb3dzZXIgZG9lcyBub3Qgc3VwcG9ydCBjcnlwdG8uXG4gKlxuICogQG1vZHVsZSByYW5kb21cbiAqL1xuXG5pbXBvcnQgKiBhcyBtYXRoIGZyb20gJy4vbWF0aC5qcydcbmltcG9ydCAqIGFzIGJpbmFyeSBmcm9tICcuL2JpbmFyeS5qcydcbmltcG9ydCB7IGdldFJhbmRvbVZhbHVlcyB9IGZyb20gJ2xpYjAvd2ViY3J5cHRvJ1xuXG5leHBvcnQgY29uc3QgcmFuZCA9IE1hdGgucmFuZG9tXG5cbmV4cG9ydCBjb25zdCB1aW50MzIgPSAoKSA9PiBnZXRSYW5kb21WYWx1ZXMobmV3IFVpbnQzMkFycmF5KDEpKVswXVxuXG5leHBvcnQgY29uc3QgdWludDUzID0gKCkgPT4ge1xuICBjb25zdCBhcnIgPSBnZXRSYW5kb21WYWx1ZXMobmV3IFVpbnQzMkFycmF5KDgpKVxuICByZXR1cm4gKGFyclswXSAmIGJpbmFyeS5CSVRTMjEpICogKGJpbmFyeS5CSVRTMzIgKyAxKSArIChhcnJbMV0gPj4+IDApXG59XG5cbi8qKlxuICogQHRlbXBsYXRlIFRcbiAqIEBwYXJhbSB7QXJyYXk8VD59IGFyclxuICogQHJldHVybiB7VH1cbiAqL1xuZXhwb3J0IGNvbnN0IG9uZU9mID0gYXJyID0+IGFyclttYXRoLmZsb29yKHJhbmQoKSAqIGFyci5sZW5ndGgpXVxuXG4vLyBAdHMtaWdub3JlXG5jb25zdCB1dWlkdjRUZW1wbGF0ZSA9IFsxZTddICsgLTFlMyArIC00ZTMgKyAtOGUzICsgLTFlMTFcblxuLyoqXG4gKiBAcmV0dXJuIHtzdHJpbmd9XG4gKi9cbmV4cG9ydCBjb25zdCB1dWlkdjQgPSAoKSA9PiB1dWlkdjRUZW1wbGF0ZS5yZXBsYWNlKC9bMDE4XS9nLCAvKiogQHBhcmFtIHtudW1iZXJ9IGMgKi8gYyA9PlxuICAoYyBeIHVpbnQzMigpICYgMTUgPj4gYyAvIDQpLnRvU3RyaW5nKDE2KVxuKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/random.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/set.js":
/*!**********************************!*\
  !*** ./node_modules/lib0/set.js ***!
  \**********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   first: () => (/* binding */ first),\n/* harmony export */   from: () => (/* binding */ from),\n/* harmony export */   toArray: () => (/* binding */ toArray)\n/* harmony export */ });\n/**\n * Utility module to work with sets.\n *\n * @module set\n */\n\nconst create = () => new Set()\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {Array<T>}\n */\nconst toArray = set => Array.from(set)\n\n/**\n * @template T\n * @param {Set<T>} set\n * @return {T}\n */\nconst first = set =>\n  set.values().next().value ?? undefined\n\n/**\n * @template T\n * @param {Iterable<T>} entries\n * @return {Set<T>}\n */\nconst from = entries => new Set(entries)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9zZXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRU87O0FBRVA7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDTzs7QUFFUDtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFlBQVk7QUFDWjtBQUNPO0FBQ1A7O0FBRUE7QUFDQTtBQUNBLFdBQVcsYUFBYTtBQUN4QixZQUFZO0FBQ1o7QUFDTyIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvbGliMC9zZXQuanM/MzEwMSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFV0aWxpdHkgbW9kdWxlIHRvIHdvcmsgd2l0aCBzZXRzLlxuICpcbiAqIEBtb2R1bGUgc2V0XG4gKi9cblxuZXhwb3J0IGNvbnN0IGNyZWF0ZSA9ICgpID0+IG5ldyBTZXQoKVxuXG4vKipcbiAqIEB0ZW1wbGF0ZSBUXG4gKiBAcGFyYW0ge1NldDxUPn0gc2V0XG4gKiBAcmV0dXJuIHtBcnJheTxUPn1cbiAqL1xuZXhwb3J0IGNvbnN0IHRvQXJyYXkgPSBzZXQgPT4gQXJyYXkuZnJvbShzZXQpXG5cbi8qKlxuICogQHRlbXBsYXRlIFRcbiAqIEBwYXJhbSB7U2V0PFQ+fSBzZXRcbiAqIEByZXR1cm4ge1R9XG4gKi9cbmV4cG9ydCBjb25zdCBmaXJzdCA9IHNldCA9PlxuICBzZXQudmFsdWVzKCkubmV4dCgpLnZhbHVlID8/IHVuZGVmaW5lZFxuXG4vKipcbiAqIEB0ZW1wbGF0ZSBUXG4gKiBAcGFyYW0ge0l0ZXJhYmxlPFQ+fSBlbnRyaWVzXG4gKiBAcmV0dXJuIHtTZXQ8VD59XG4gKi9cbmV4cG9ydCBjb25zdCBmcm9tID0gZW50cmllcyA9PiBuZXcgU2V0KGVudHJpZXMpXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/set.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/storage.js":
/*!**************************************!*\
  !*** ./node_modules/lib0/storage.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   offChange: () => (/* binding */ offChange),\n/* harmony export */   onChange: () => (/* binding */ onChange),\n/* harmony export */   varStorage: () => (/* binding */ varStorage)\n/* harmony export */ });\n/* eslint-env browser */\n\n/**\n * Isomorphic variable storage.\n *\n * Uses LocalStorage in the browser and falls back to in-memory storage.\n *\n * @module storage\n */\n\n/* c8 ignore start */\nclass VarStoragePolyfill {\n  constructor () {\n    this.map = new Map()\n  }\n\n  /**\n   * @param {string} key\n   * @param {any} newValue\n   */\n  setItem (key, newValue) {\n    this.map.set(key, newValue)\n  }\n\n  /**\n   * @param {string} key\n   */\n  getItem (key) {\n    return this.map.get(key)\n  }\n}\n/* c8 ignore stop */\n\n/**\n * @type {any}\n */\nlet _localStorage = new VarStoragePolyfill()\nlet usePolyfill = true\n\n/* c8 ignore start */\ntry {\n  // if the same-origin rule is violated, accessing localStorage might thrown an error\n  if (typeof localStorage !== 'undefined' && localStorage) {\n    _localStorage = localStorage\n    usePolyfill = false\n  }\n} catch (e) { }\n/* c8 ignore stop */\n\n/**\n * This is basically localStorage in browser, or a polyfill in nodejs\n */\n/* c8 ignore next */\nconst varStorage = _localStorage\n\n/**\n * A polyfill for `addEventListener('storage', event => {..})` that does nothing if the polyfill is being used.\n *\n * @param {function({ key: string, newValue: string, oldValue: string }): void} eventHandler\n * @function\n */\n/* c8 ignore next */\nconst onChange = eventHandler => usePolyfill || addEventListener('storage', /** @type {any} */ (eventHandler))\n\n/**\n * A polyfill for `removeEventListener('storage', event => {..})` that does nothing if the polyfill is being used.\n *\n * @param {function({ key: string, newValue: string, oldValue: string }): void} eventHandler\n * @function\n */\n/* c8 ignore next */\nconst offChange = eventHandler => usePolyfill || removeEventListener('storage', /** @type {any} */ (eventHandler))\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/storage.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/string.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/string.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MAX_UTF16_CHARACTER: () => (/* binding */ MAX_UTF16_CHARACTER),\n/* harmony export */   _decodeUtf8Native: () => (/* binding */ _decodeUtf8Native),\n/* harmony export */   _decodeUtf8Polyfill: () => (/* binding */ _decodeUtf8Polyfill),\n/* harmony export */   _encodeUtf8Native: () => (/* binding */ _encodeUtf8Native),\n/* harmony export */   _encodeUtf8Polyfill: () => (/* binding */ _encodeUtf8Polyfill),\n/* harmony export */   decodeUtf8: () => (/* binding */ decodeUtf8),\n/* harmony export */   encodeUtf8: () => (/* binding */ encodeUtf8),\n/* harmony export */   escapeHTML: () => (/* binding */ escapeHTML),\n/* harmony export */   fromCamelCase: () => (/* binding */ fromCamelCase),\n/* harmony export */   fromCharCode: () => (/* binding */ fromCharCode),\n/* harmony export */   fromCodePoint: () => (/* binding */ fromCodePoint),\n/* harmony export */   repeat: () => (/* binding */ repeat),\n/* harmony export */   splice: () => (/* binding */ splice),\n/* harmony export */   trimLeft: () => (/* binding */ trimLeft),\n/* harmony export */   unescapeHTML: () => (/* binding */ unescapeHTML),\n/* harmony export */   utf8ByteLength: () => (/* binding */ utf8ByteLength),\n/* harmony export */   utf8TextDecoder: () => (/* binding */ utf8TextDecoder),\n/* harmony export */   utf8TextEncoder: () => (/* binding */ utf8TextEncoder)\n/* harmony export */ });\n/* harmony import */ var _array_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./array.js */ \"(ssr)/./node_modules/lib0/array.js\");\n\n\n/**\n * Utility module to work with strings.\n *\n * @module string\n */\n\nconst fromCharCode = String.fromCharCode\nconst fromCodePoint = String.fromCodePoint\n\n/**\n * The largest utf16 character.\n * Corresponds to Uint8Array([255, 255]) or charcodeof(2x2^8)\n */\nconst MAX_UTF16_CHARACTER = fromCharCode(65535)\n\n/**\n * @param {string} s\n * @return {string}\n */\nconst toLowerCase = s => s.toLowerCase()\n\nconst trimLeftRegex = /^\\s*/g\n\n/**\n * @param {string} s\n * @return {string}\n */\nconst trimLeft = s => s.replace(trimLeftRegex, '')\n\nconst fromCamelCaseRegex = /([A-Z])/g\n\n/**\n * @param {string} s\n * @param {string} separator\n * @return {string}\n */\nconst fromCamelCase = (s, separator) => trimLeft(s.replace(fromCamelCaseRegex, match => `${separator}${toLowerCase(match)}`))\n\n/**\n * Compute the utf8ByteLength\n * @param {string} str\n * @return {number}\n */\nconst utf8ByteLength = str => unescape(encodeURIComponent(str)).length\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\nconst _encodeUtf8Polyfill = str => {\n  const encodedString = unescape(encodeURIComponent(str))\n  const len = encodedString.length\n  const buf = new Uint8Array(len)\n  for (let i = 0; i < len; i++) {\n    buf[i] = /** @type {number} */ (encodedString.codePointAt(i))\n  }\n  return buf\n}\n\n/* c8 ignore next */\nconst utf8TextEncoder = /** @type {TextEncoder} */ (typeof TextEncoder !== 'undefined' ? new TextEncoder() : null)\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\nconst _encodeUtf8Native = str => utf8TextEncoder.encode(str)\n\n/**\n * @param {string} str\n * @return {Uint8Array}\n */\n/* c8 ignore next */\nconst encodeUtf8 = utf8TextEncoder ? _encodeUtf8Native : _encodeUtf8Polyfill\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\nconst _decodeUtf8Polyfill = buf => {\n  let remainingLen = buf.length\n  let encodedString = ''\n  let bufPos = 0\n  while (remainingLen > 0) {\n    const nextLen = remainingLen < 10000 ? remainingLen : 10000\n    const bytes = buf.subarray(bufPos, bufPos + nextLen)\n    bufPos += nextLen\n    // Starting with ES5.1 we can supply a generic array-like object as arguments\n    encodedString += String.fromCodePoint.apply(null, /** @type {any} */ (bytes))\n    remainingLen -= nextLen\n  }\n  return decodeURIComponent(escape(encodedString))\n}\n\n/* c8 ignore next */\nlet utf8TextDecoder = typeof TextDecoder === 'undefined' ? null : new TextDecoder('utf-8', { fatal: true, ignoreBOM: true })\n\n/* c8 ignore start */\nif (utf8TextDecoder && utf8TextDecoder.decode(new Uint8Array()).length === 1) {\n  // Safari doesn't handle BOM correctly.\n  // This fixes a bug in Safari 13.0.5 where it produces a BOM the first time it is called.\n  // utf8TextDecoder.decode(new Uint8Array()).length === 1 on the first call and\n  // utf8TextDecoder.decode(new Uint8Array()).length === 1 on the second call\n  // Another issue is that from then on no BOM chars are recognized anymore\n  /* c8 ignore next */\n  utf8TextDecoder = null\n}\n/* c8 ignore stop */\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\nconst _decodeUtf8Native = buf => /** @type {TextDecoder} */ (utf8TextDecoder).decode(buf)\n\n/**\n * @param {Uint8Array} buf\n * @return {string}\n */\n/* c8 ignore next */\nconst decodeUtf8 = utf8TextDecoder ? _decodeUtf8Native : _decodeUtf8Polyfill\n\n/**\n * @param {string} str The initial string\n * @param {number} index Starting position\n * @param {number} remove Number of characters to remove\n * @param {string} insert New content to insert\n */\nconst splice = (str, index, remove, insert = '') => str.slice(0, index) + insert + str.slice(index + remove)\n\n/**\n * @param {string} source\n * @param {number} n\n */\nconst repeat = (source, n) => _array_js__WEBPACK_IMPORTED_MODULE_0__.unfold(n, () => source).join('')\n\n/**\n * Escape HTML characters &,<,>,',\" to their respective HTML entities &amp;,&lt;,&gt;,&#39;,&quot;\n *\n * @param {string} str\n */\nconst escapeHTML = str =>\n  str.replace(/[&<>'\"]/g, r => /** @type {string} */ ({\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    \"'\": '&#39;',\n    '\"': '&quot;'\n  }[r]))\n\n/**\n * Reverse of `escapeHTML`\n *\n * @param {string} str\n */\nconst unescapeHTML = str =>\n  str.replace(/&amp;|&lt;|&gt;|&#39;|&quot;/g, r => /** @type {string} */ ({\n    '&amp;': '&',\n    '&lt;': '<',\n    '&gt;': '>',\n    '&#39;': \"'\",\n    '&quot;': '\"'\n  }[r]))\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/symbol.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/symbol.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   create: () => (/* binding */ create),\n/* harmony export */   isSymbol: () => (/* binding */ isSymbol)\n/* harmony export */ });\n/**\n * Utility module to work with EcmaScript Symbols.\n *\n * @module symbol\n */\n\n/**\n * Return fresh symbol.\n */\nconst create = Symbol\n\n/**\n * @param {any} s\n * @return {boolean}\n */\nconst isSymbol = s => typeof s === 'symbol'\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC9zeW1ib2wuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNPOztBQUVQO0FBQ0EsV0FBVyxLQUFLO0FBQ2hCLFlBQVk7QUFDWjtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9saWIwL3N5bWJvbC5qcz8yOTIzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogVXRpbGl0eSBtb2R1bGUgdG8gd29yayB3aXRoIEVjbWFTY3JpcHQgU3ltYm9scy5cbiAqXG4gKiBAbW9kdWxlIHN5bWJvbFxuICovXG5cbi8qKlxuICogUmV0dXJuIGZyZXNoIHN5bWJvbC5cbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZSA9IFN5bWJvbFxuXG4vKipcbiAqIEBwYXJhbSB7YW55fSBzXG4gKiBAcmV0dXJuIHtib29sZWFufVxuICovXG5leHBvcnQgY29uc3QgaXNTeW1ib2wgPSBzID0+IHR5cGVvZiBzID09PSAnc3ltYm9sJ1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/symbol.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/time.js":
/*!***********************************!*\
  !*** ./node_modules/lib0/time.js ***!
  \***********************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getDate: () => (/* binding */ getDate),\n/* harmony export */   getUnixTime: () => (/* binding */ getUnixTime),\n/* harmony export */   humanizeDuration: () => (/* binding */ humanizeDuration)\n/* harmony export */ });\n/* harmony import */ var _metric_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./metric.js */ \"(ssr)/./node_modules/lib0/metric.js\");\n/* harmony import */ var _math_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./math.js */ \"(ssr)/./node_modules/lib0/math.js\");\n/**\n * Utility module to work with time.\n *\n * @module time\n */\n\n\n\n\n/**\n * Return current time.\n *\n * @return {Date}\n */\nconst getDate = () => new Date()\n\n/**\n * Return current unix time.\n *\n * @return {number}\n */\nconst getUnixTime = Date.now\n\n/**\n * Transform time (in ms) to a human readable format. E.g. 1100 => 1.1s. 60s => 1min. .001 => 10μs.\n *\n * @param {number} d duration in milliseconds\n * @return {string} humanized approximation of time\n */\nconst humanizeDuration = d => {\n  if (d < 60000) {\n    const p = _metric_js__WEBPACK_IMPORTED_MODULE_0__.prefix(d, -1)\n    return _math_js__WEBPACK_IMPORTED_MODULE_1__.round(p.n * 100) / 100 + p.prefix + 's'\n  }\n  d = _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(d / 1000)\n  const seconds = d % 60\n  const minutes = _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(d / 60) % 60\n  const hours = _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(d / 3600) % 24\n  const days = _math_js__WEBPACK_IMPORTED_MODULE_1__.floor(d / 86400)\n  if (days > 0) {\n    return days + 'd' + ((hours > 0 || minutes > 30) ? ' ' + (minutes > 30 ? hours + 1 : hours) + 'h' : '')\n  }\n  if (hours > 0) {\n    /* c8 ignore next */\n    return hours + 'h' + ((minutes > 0 || seconds > 30) ? ' ' + (seconds > 30 ? minutes + 1 : minutes) + 'min' : '')\n  }\n  return minutes + 'min' + (seconds > 0 ? ' ' + seconds + 's' : '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/time.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/traits.js":
/*!*************************************!*\
  !*** ./node_modules/lib0/traits.js ***!
  \*************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EqualityTraitSymbol: () => (/* binding */ EqualityTraitSymbol)\n/* harmony export */ });\nconst EqualityTraitSymbol = Symbol('Equality')\n\n/**\n * @typedef {{ [EqualityTraitSymbol]:(other:EqualityTrait)=>boolean }} EqualityTrait\n */\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC90cmFpdHMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFPOztBQUVQO0FBQ0EsZUFBZSx3REFBd0Q7QUFDdkUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2xpYjAvdHJhaXRzLmpzPzI3ODQiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IEVxdWFsaXR5VHJhaXRTeW1ib2wgPSBTeW1ib2woJ0VxdWFsaXR5JylcblxuLyoqXG4gKiBAdHlwZWRlZiB7eyBbRXF1YWxpdHlUcmFpdFN5bWJvbF06KG90aGVyOkVxdWFsaXR5VHJhaXQpPT5ib29sZWFuIH19IEVxdWFsaXR5VHJhaXRcbiAqL1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/traits.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/lib0/webcrypto.node.js":
/*!*********************************************!*\
  !*** ./node_modules/lib0/webcrypto.node.js ***!
  \*********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRandomValues: () => (/* binding */ getRandomValues),\n/* harmony export */   subtle: () => (/* binding */ subtle)\n/* harmony export */ });\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n\n\nconst subtle = /** @type {any} */ (node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto).subtle\nconst getRandomValues = /** @type {any} */ (node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto).getRandomValues.bind(node_crypto__WEBPACK_IMPORTED_MODULE_0__.webcrypto)\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbGliMC93ZWJjcnlwdG8ubm9kZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBdUM7O0FBRWhDLDBCQUEwQixLQUFLLElBQUksa0RBQVM7QUFDNUMsbUNBQW1DLEtBQUssSUFBSSxrREFBUyx1QkFBdUIsa0RBQVMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL2xpYjAvd2ViY3J5cHRvLm5vZGUuanM/MDllNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB3ZWJjcnlwdG8gfSBmcm9tICdub2RlOmNyeXB0bydcblxuZXhwb3J0IGNvbnN0IHN1YnRsZSA9IC8qKiBAdHlwZSB7YW55fSAqLyAod2ViY3J5cHRvKS5zdWJ0bGVcbmV4cG9ydCBjb25zdCBnZXRSYW5kb21WYWx1ZXMgPSAvKiogQHR5cGUge2FueX0gKi8gKHdlYmNyeXB0bykuZ2V0UmFuZG9tVmFsdWVzLmJpbmQod2ViY3J5cHRvKVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/lib0/webcrypto.node.js\n");

/***/ })

};
;