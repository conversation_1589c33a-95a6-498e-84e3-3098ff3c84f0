"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-from-markdown";
exports.ids = ["vendor-chunks/mdast-util-from-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-from-markdown/dev/lib/index.js":
/*!****************************************************************!*\
  !*** ./node_modules/mdast-util-from-markdown/dev/lib/index.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromMarkdown: () => (/* binding */ fromMarkdown)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! mdast-util-to-string */ \"(ssr)/./node_modules/mdast-util-to-string/lib/index.js\");\n/* harmony import */ var micromark_lib_parse_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark/lib/parse.js */ \"(ssr)/./node_modules/micromark/dev/lib/parse.js\");\n/* harmony import */ var micromark_lib_preprocess_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark/lib/preprocess.js */ \"(ssr)/./node_modules/micromark/dev/lib/preprocess.js\");\n/* harmony import */ var micromark_lib_postprocess_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark/lib/postprocess.js */ \"(ssr)/./node_modules/micromark/dev/lib/postprocess.js\");\n/* harmony import */ var micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! micromark-util-decode-numeric-character-reference */ \"(ssr)/./node_modules/micromark-util-decode-numeric-character-reference/dev/index.js\");\n/* harmony import */ var micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! micromark-util-decode-string */ \"(ssr)/./node_modules/micromark-util-decode-string/dev/index.js\");\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/decode-named-character-reference/index.js\");\n/* harmony import */ var unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! unist-util-stringify-position */ \"(ssr)/./node_modules/unist-util-stringify-position/lib/index.js\");\n/**\n * @typedef {import('micromark-util-types').Encoding} Encoding\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').ParseOptions} ParseOptions\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Value} Value\n *\n * @typedef {import('unist').Parent} UnistParent\n * @typedef {import('unist').Point} Point\n *\n * @typedef {import('mdast').PhrasingContent} PhrasingContent\n * @typedef {import('mdast').StaticPhrasingContent} StaticPhrasingContent\n * @typedef {import('mdast').Content} Content\n * @typedef {import('mdast').Break} Break\n * @typedef {import('mdast').Blockquote} Blockquote\n * @typedef {import('mdast').Code} Code\n * @typedef {import('mdast').Definition} Definition\n * @typedef {import('mdast').Emphasis} Emphasis\n * @typedef {import('mdast').Heading} Heading\n * @typedef {import('mdast').HTML} HTML\n * @typedef {import('mdast').Image} Image\n * @typedef {import('mdast').ImageReference} ImageReference\n * @typedef {import('mdast').InlineCode} InlineCode\n * @typedef {import('mdast').Link} Link\n * @typedef {import('mdast').LinkReference} LinkReference\n * @typedef {import('mdast').List} List\n * @typedef {import('mdast').ListItem} ListItem\n * @typedef {import('mdast').Paragraph} Paragraph\n * @typedef {import('mdast').Root} Root\n * @typedef {import('mdast').Strong} Strong\n * @typedef {import('mdast').Text} Text\n * @typedef {import('mdast').ThematicBreak} ThematicBreak\n * @typedef {import('mdast').ReferenceType} ReferenceType\n * @typedef {import('../index.js').CompileData} CompileData\n */\n\n/**\n * @typedef {Root | Content} Node\n * @typedef {Extract<Node, UnistParent>} Parent\n *\n * @typedef {Omit<UnistParent, 'type' | 'children'> & {type: 'fragment', children: Array<PhrasingContent>}} Fragment\n */\n\n/**\n * @callback Transform\n *   Extra transform, to change the AST afterwards.\n * @param {Root} tree\n *   Tree to transform.\n * @returns {Root | undefined | null | void}\n *   New tree or nothing (in which case the current tree is used).\n *\n * @callback Handle\n *   Handle a token.\n * @param {CompileContext} this\n *   Context.\n * @param {Token} token\n *   Current token.\n * @returns {void}\n *   Nothing.\n *\n * @typedef {Record<string, Handle>} Handles\n *   Token types mapping to handles\n *\n * @callback OnEnterError\n *   Handle the case where the `right` token is open, but it is closed (by the\n *   `left` token) or because we reached the end of the document.\n * @param {Omit<CompileContext, 'sliceSerialize'>} this\n *   Context.\n * @param {Token | undefined} left\n *   Left token.\n * @param {Token} right\n *   Right token.\n * @returns {void}\n *   Nothing.\n *\n * @callback OnExitError\n *   Handle the case where the `right` token is open but it is closed by\n *   exiting the `left` token.\n * @param {Omit<CompileContext, 'sliceSerialize'>} this\n *   Context.\n * @param {Token} left\n *   Left token.\n * @param {Token} right\n *   Right token.\n * @returns {void}\n *   Nothing.\n *\n * @typedef {[Token, OnEnterError | undefined]} TokenTuple\n *   Open token on the stack, with an optional error handler for when\n *   that token isn’t closed properly.\n */\n\n/**\n * @typedef Config\n *   Configuration.\n *\n *   We have our defaults, but extensions will add more.\n * @property {Array<string>} canContainEols\n *   Token types where line endings are used.\n * @property {Handles} enter\n *   Opening handles.\n * @property {Handles} exit\n *   Closing handles.\n * @property {Array<Transform>} transforms\n *   Tree transforms.\n *\n * @typedef {Partial<Config>} Extension\n *   Change how markdown tokens from micromark are turned into mdast.\n *\n * @typedef CompileContext\n *   mdast compiler context.\n * @property {Array<Node | Fragment>} stack\n *   Stack of nodes.\n * @property {Array<TokenTuple>} tokenStack\n *   Stack of tokens.\n * @property {<Key extends keyof CompileData>(key: Key) => CompileData[Key]} getData\n *   Get data from the key/value store.\n * @property {<Key extends keyof CompileData>(key: Key, value?: CompileData[Key]) => void} setData\n *   Set data into the key/value store.\n * @property {(this: CompileContext) => void} buffer\n *   Capture some of the output data.\n * @property {(this: CompileContext) => string} resume\n *   Stop capturing and access the output data.\n * @property {<Kind extends Node>(this: CompileContext, node: Kind, token: Token, onError?: OnEnterError) => Kind} enter\n *   Enter a token.\n * @property {(this: CompileContext, token: Token, onError?: OnExitError) => Node} exit\n *   Exit a token.\n * @property {TokenizeContext['sliceSerialize']} sliceSerialize\n *   Get the string value of a token.\n * @property {Config} config\n *   Configuration.\n *\n * @typedef FromMarkdownOptions\n *   Configuration for how to build mdast.\n * @property {Array<Extension | Array<Extension>> | null | undefined} [mdastExtensions]\n *   Extensions for this utility to change how tokens are turned into a tree.\n *\n * @typedef {ParseOptions & FromMarkdownOptions} Options\n *   Configuration.\n */\n\n// To do: micromark: create a registry of tokens?\n// To do: next major: don’t return given `Node` from `enter`.\n// To do: next major: remove setter/getter.\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * @param value\n *   Markdown to parse.\n * @param encoding\n *   Character encoding for when `value` is `Buffer`.\n * @param options\n *   Configuration.\n * @returns\n *   mdast tree.\n */\nconst fromMarkdown =\n  /**\n   * @type {(\n   *   ((value: Value, encoding: Encoding, options?: Options | null | undefined) => Root) &\n   *   ((value: Value, options?: Options | null | undefined) => Root)\n   * )}\n   */\n  (\n    /**\n     * @param {Value} value\n     * @param {Encoding | Options | null | undefined} [encoding]\n     * @param {Options | null | undefined} [options]\n     * @returns {Root}\n     */\n    function (value, encoding, options) {\n      if (typeof encoding !== 'string') {\n        options = encoding\n        encoding = undefined\n      }\n\n      return compiler(options)(\n        (0,micromark_lib_postprocess_js__WEBPACK_IMPORTED_MODULE_1__.postprocess)(\n          (0,micromark_lib_parse_js__WEBPACK_IMPORTED_MODULE_2__.parse)(options).document().write((0,micromark_lib_preprocess_js__WEBPACK_IMPORTED_MODULE_3__.preprocess)()(value, encoding, true))\n        )\n      )\n    }\n  )\n\n/**\n * Note this compiler only understand complete buffering, not streaming.\n *\n * @param {Options | null | undefined} [options]\n */\nfunction compiler(options) {\n  /** @type {Config} */\n  const config = {\n    transforms: [],\n    canContainEols: ['emphasis', 'fragment', 'heading', 'paragraph', 'strong'],\n    enter: {\n      autolink: opener(link),\n      autolinkProtocol: onenterdata,\n      autolinkEmail: onenterdata,\n      atxHeading: opener(heading),\n      blockQuote: opener(blockQuote),\n      characterEscape: onenterdata,\n      characterReference: onenterdata,\n      codeFenced: opener(codeFlow),\n      codeFencedFenceInfo: buffer,\n      codeFencedFenceMeta: buffer,\n      codeIndented: opener(codeFlow, buffer),\n      codeText: opener(codeText, buffer),\n      codeTextData: onenterdata,\n      data: onenterdata,\n      codeFlowValue: onenterdata,\n      definition: opener(definition),\n      definitionDestinationString: buffer,\n      definitionLabelString: buffer,\n      definitionTitleString: buffer,\n      emphasis: opener(emphasis),\n      hardBreakEscape: opener(hardBreak),\n      hardBreakTrailing: opener(hardBreak),\n      htmlFlow: opener(html, buffer),\n      htmlFlowData: onenterdata,\n      htmlText: opener(html, buffer),\n      htmlTextData: onenterdata,\n      image: opener(image),\n      label: buffer,\n      link: opener(link),\n      listItem: opener(listItem),\n      listItemValue: onenterlistitemvalue,\n      listOrdered: opener(list, onenterlistordered),\n      listUnordered: opener(list),\n      paragraph: opener(paragraph),\n      reference: onenterreference,\n      referenceString: buffer,\n      resourceDestinationString: buffer,\n      resourceTitleString: buffer,\n      setextHeading: opener(heading),\n      strong: opener(strong),\n      thematicBreak: opener(thematicBreak)\n    },\n    exit: {\n      atxHeading: closer(),\n      atxHeadingSequence: onexitatxheadingsequence,\n      autolink: closer(),\n      autolinkEmail: onexitautolinkemail,\n      autolinkProtocol: onexitautolinkprotocol,\n      blockQuote: closer(),\n      characterEscapeValue: onexitdata,\n      characterReferenceMarkerHexadecimal: onexitcharacterreferencemarker,\n      characterReferenceMarkerNumeric: onexitcharacterreferencemarker,\n      characterReferenceValue: onexitcharacterreferencevalue,\n      codeFenced: closer(onexitcodefenced),\n      codeFencedFence: onexitcodefencedfence,\n      codeFencedFenceInfo: onexitcodefencedfenceinfo,\n      codeFencedFenceMeta: onexitcodefencedfencemeta,\n      codeFlowValue: onexitdata,\n      codeIndented: closer(onexitcodeindented),\n      codeText: closer(onexitcodetext),\n      codeTextData: onexitdata,\n      data: onexitdata,\n      definition: closer(),\n      definitionDestinationString: onexitdefinitiondestinationstring,\n      definitionLabelString: onexitdefinitionlabelstring,\n      definitionTitleString: onexitdefinitiontitlestring,\n      emphasis: closer(),\n      hardBreakEscape: closer(onexithardbreak),\n      hardBreakTrailing: closer(onexithardbreak),\n      htmlFlow: closer(onexithtmlflow),\n      htmlFlowData: onexitdata,\n      htmlText: closer(onexithtmltext),\n      htmlTextData: onexitdata,\n      image: closer(onexitimage),\n      label: onexitlabel,\n      labelText: onexitlabeltext,\n      lineEnding: onexitlineending,\n      link: closer(onexitlink),\n      listItem: closer(),\n      listOrdered: closer(),\n      listUnordered: closer(),\n      paragraph: closer(),\n      referenceString: onexitreferencestring,\n      resourceDestinationString: onexitresourcedestinationstring,\n      resourceTitleString: onexitresourcetitlestring,\n      resource: onexitresource,\n      setextHeading: closer(onexitsetextheading),\n      setextHeadingLineSequence: onexitsetextheadinglinesequence,\n      setextHeadingText: onexitsetextheadingtext,\n      strong: closer(),\n      thematicBreak: closer()\n    }\n  }\n\n  configure(config, (options || {}).mdastExtensions || [])\n\n  /** @type {CompileData} */\n  const data = {}\n\n  return compile\n\n  /**\n   * Turn micromark events into an mdast tree.\n   *\n   * @param {Array<Event>} events\n   *   Events.\n   * @returns {Root}\n   *   mdast tree.\n   */\n  function compile(events) {\n    /** @type {Root} */\n    let tree = {type: 'root', children: []}\n    /** @type {Omit<CompileContext, 'sliceSerialize'>} */\n    const context = {\n      stack: [tree],\n      tokenStack: [],\n      config,\n      enter,\n      exit,\n      buffer,\n      resume,\n      setData,\n      getData\n    }\n    /** @type {Array<number>} */\n    const listStack = []\n    let index = -1\n\n    while (++index < events.length) {\n      // We preprocess lists to add `listItem` tokens, and to infer whether\n      // items the list itself are spread out.\n      if (\n        events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listOrdered ||\n        events[index][1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listUnordered\n      ) {\n        if (events[index][0] === 'enter') {\n          listStack.push(index)\n        } else {\n          const tail = listStack.pop()\n          ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tail === 'number', 'expected list ot be open')\n          index = prepareList(events, tail, index)\n        }\n      }\n    }\n\n    index = -1\n\n    while (++index < events.length) {\n      const handler = config[events[index][0]]\n\n      if (own.call(handler, events[index][1].type)) {\n        handler[events[index][1].type].call(\n          Object.assign(\n            {sliceSerialize: events[index][2].sliceSerialize},\n            context\n          ),\n          events[index][1]\n        )\n      }\n    }\n\n    // Handle tokens still being open.\n    if (context.tokenStack.length > 0) {\n      const tail = context.tokenStack[context.tokenStack.length - 1]\n      const handler = tail[1] || defaultOnError\n      handler.call(context, undefined, tail[0])\n    }\n\n    // Figure out `root` position.\n    tree.position = {\n      start: point(\n        events.length > 0 ? events[0][1].start : {line: 1, column: 1, offset: 0}\n      ),\n      end: point(\n        events.length > 0\n          ? events[events.length - 2][1].end\n          : {line: 1, column: 1, offset: 0}\n      )\n    }\n\n    // Call transforms.\n    index = -1\n    while (++index < config.transforms.length) {\n      tree = config.transforms[index](tree) || tree\n    }\n\n    return tree\n  }\n\n  /**\n   * @param {Array<Event>} events\n   * @param {number} start\n   * @param {number} length\n   * @returns {number}\n   */\n  function prepareList(events, start, length) {\n    let index = start - 1\n    let containerBalance = -1\n    let listSpread = false\n    /** @type {Token | undefined} */\n    let listItem\n    /** @type {number | undefined} */\n    let lineIndex\n    /** @type {number | undefined} */\n    let firstBlankLineIndex\n    /** @type {boolean | undefined} */\n    let atMarker\n\n    while (++index <= length) {\n      const event = events[index]\n\n      if (\n        event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listUnordered ||\n        event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listOrdered ||\n        event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.blockQuote\n      ) {\n        if (event[0] === 'enter') {\n          containerBalance++\n        } else {\n          containerBalance--\n        }\n\n        atMarker = undefined\n      } else if (event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.lineEndingBlank) {\n        if (event[0] === 'enter') {\n          if (\n            listItem &&\n            !atMarker &&\n            !containerBalance &&\n            !firstBlankLineIndex\n          ) {\n            firstBlankLineIndex = index\n          }\n\n          atMarker = undefined\n        }\n      } else if (\n        event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix ||\n        event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listItemValue ||\n        event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listItemMarker ||\n        event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listItemPrefix ||\n        event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listItemPrefixWhitespace\n      ) {\n        // Empty.\n      } else {\n        atMarker = undefined\n      }\n\n      if (\n        (!containerBalance &&\n          event[0] === 'enter' &&\n          event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listItemPrefix) ||\n        (containerBalance === -1 &&\n          event[0] === 'exit' &&\n          (event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listUnordered ||\n            event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listOrdered))\n      ) {\n        if (listItem) {\n          let tailIndex = index\n          lineIndex = undefined\n\n          while (tailIndex--) {\n            const tailEvent = events[tailIndex]\n\n            if (\n              tailEvent[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding ||\n              tailEvent[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.lineEndingBlank\n            ) {\n              if (tailEvent[0] === 'exit') continue\n\n              if (lineIndex) {\n                events[lineIndex][1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.lineEndingBlank\n                listSpread = true\n              }\n\n              tailEvent[1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.lineEnding\n              lineIndex = tailIndex\n            } else if (\n              tailEvent[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.linePrefix ||\n              tailEvent[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.blockQuotePrefix ||\n              tailEvent[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.blockQuotePrefixWhitespace ||\n              tailEvent[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.blockQuoteMarker ||\n              tailEvent[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listItemIndent\n            ) {\n              // Empty\n            } else {\n              break\n            }\n          }\n\n          if (\n            firstBlankLineIndex &&\n            (!lineIndex || firstBlankLineIndex < lineIndex)\n          ) {\n            listItem._spread = true\n          }\n\n          // Fix position.\n          listItem.end = Object.assign(\n            {},\n            lineIndex ? events[lineIndex][1].start : event[1].end\n          )\n\n          events.splice(lineIndex || index, 0, ['exit', listItem, event[2]])\n          index++\n          length++\n        }\n\n        // Create a new list item.\n        if (event[1].type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.listItemPrefix) {\n          listItem = {\n            type: 'listItem',\n            _spread: false,\n            start: Object.assign({}, event[1].start),\n            // @ts-expect-error: we’ll add `end` in a second.\n            end: undefined\n          }\n          // @ts-expect-error: `listItem` is most definitely defined, TS...\n          events.splice(index, 0, ['enter', listItem, event[2]])\n          index++\n          length++\n          firstBlankLineIndex = undefined\n          atMarker = true\n        }\n      }\n    }\n\n    events[start][1]._spread = listSpread\n    return length\n  }\n\n  /**\n   * Set data.\n   *\n   * @template {keyof CompileData} Key\n   *   Field type.\n   * @param {Key} key\n   *   Key of field.\n   * @param {CompileData[Key]} [value]\n   *   New value.\n   * @returns {void}\n   *   Nothing.\n   */\n  function setData(key, value) {\n    data[key] = value\n  }\n\n  /**\n   * Get data.\n   *\n   * @template {keyof CompileData} Key\n   *   Field type.\n   * @param {Key} key\n   *   Key of field.\n   * @returns {CompileData[Key]}\n   *   Value.\n   */\n  function getData(key) {\n    return data[key]\n  }\n\n  /**\n   * Create an opener handle.\n   *\n   * @param {(token: Token) => Node} create\n   *   Create a node.\n   * @param {Handle} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function opener(create, and) {\n    return open\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {void}\n     */\n    function open(token) {\n      enter.call(this, create(token), token)\n      if (and) and.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @returns {void}\n   */\n  function buffer() {\n    this.stack.push({type: 'fragment', children: []})\n  }\n\n  /**\n   * @template {Node} Kind\n   *   Node type.\n   * @this {CompileContext}\n   *   Context.\n   * @param {Kind} node\n   *   Node to enter.\n   * @param {Token} token\n   *   Corresponding token.\n   * @param {OnEnterError | undefined} [errorHandler]\n   *   Handle the case where this token is open, but it is closed by something else.\n   * @returns {Kind}\n   *   The given node.\n   */\n  function enter(node, token, errorHandler) {\n    const parent = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(parent, 'expected `parent`')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)('children' in parent, 'expected `parent`')\n    // @ts-expect-error: Assume `Node` can exist as a child of `parent`.\n    parent.children.push(node)\n    this.stack.push(node)\n    this.tokenStack.push([token, errorHandler])\n    // @ts-expect-error: `end` will be patched later.\n    node.position = {start: point(token.start)}\n    return node\n  }\n\n  /**\n   * Create a closer handle.\n   *\n   * @param {Handle} [and]\n   *   Optional function to also run.\n   * @returns {Handle}\n   *   Handle.\n   */\n  function closer(and) {\n    return close\n\n    /**\n     * @this {CompileContext}\n     * @param {Token} token\n     * @returns {void}\n     */\n    function close(token) {\n      if (and) and.call(this, token)\n      exit.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   *   Context.\n   * @param {Token} token\n   *   Corresponding token.\n   * @param {OnExitError | undefined} [onExitError]\n   *   Handle the case where another token is open.\n   * @returns {Node}\n   *   The closed node.\n   */\n  function exit(token, onExitError) {\n    const node = this.stack.pop()\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected `node`')\n    const open = this.tokenStack.pop()\n\n    if (!open) {\n      throw new Error(\n        'Cannot close `' +\n          token.type +\n          '` (' +\n          (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: token.start, end: token.end}) +\n          '): it’s not open'\n      )\n    } else if (open[0].type !== token.type) {\n      if (onExitError) {\n        onExitError.call(this, token, open[0])\n      } else {\n        const handler = open[1] || defaultOnError\n        handler.call(this, token, open[0])\n      }\n    }\n\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type !== 'fragment', 'unexpected fragment `exit`ed')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.position, 'expected `position` to be defined')\n    node.position.end = point(token.end)\n    return node\n  }\n\n  /**\n   * @this {CompileContext}\n   * @returns {string}\n   */\n  function resume() {\n    return (0,mdast_util_to_string__WEBPACK_IMPORTED_MODULE_6__.toString)(this.stack.pop())\n  }\n\n  //\n  // Handlers.\n  //\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistordered() {\n    setData('expectingFirstListItemValue', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onenterlistitemvalue(token) {\n    if (getData('expectingFirstListItemValue')) {\n      const ancestor = this.stack[this.stack.length - 2]\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(ancestor, 'expected nodes on stack')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(ancestor.type === 'list', 'expected list on stack')\n      ancestor.start = Number.parseInt(\n        this.sliceSerialize(token),\n        micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal\n      )\n      setData('expectingFirstListItemValue')\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfenceinfo() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'code', 'expected code on stack')\n    node.lang = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfencemeta() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'code', 'expected code on stack')\n    node.meta = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefencedfence() {\n    // Exit if this is the closing fence.\n    if (getData('flowCodeInside')) return\n    this.buffer()\n    setData('flowCodeInside', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodefenced() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/^(\\r?\\n|\\r)|(\\r?\\n|\\r)$/g, '')\n    setData('flowCodeInside')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcodeindented() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'code', 'expected code on stack')\n\n    node.value = data.replace(/(\\r?\\n|\\r)$/g, '')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitionlabelstring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.label = label\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiontitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitdefinitiondestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'definition', 'expected definition on stack')\n\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitatxheadingsequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'heading', 'expected heading on stack')\n\n    if (!node.depth) {\n      const depth = this.sliceSerialize(token).length\n\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n        depth === 1 ||\n          depth === 2 ||\n          depth === 3 ||\n          depth === 4 ||\n          depth === 5 ||\n          depth === 6,\n        'expected `depth` between `1` and `6`'\n      )\n\n      node.depth = depth\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadingtext() {\n    setData('setextHeadingSlurpLineEnding', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheadinglinesequence(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'heading', 'expected heading on stack')\n\n    node.depth =\n      this.sliceSerialize(token).charCodeAt(0) === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_9__.codes.equalsTo ? 1 : 2\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitsetextheading() {\n    setData('setextHeadingSlurpLineEnding')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterdata(token) {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)('children' in node, 'expected parent on stack')\n\n    let tail = node.children[node.children.length - 1]\n\n    if (!tail || tail.type !== 'text') {\n      // Add a new text node.\n      tail = text()\n      // @ts-expect-error: we’ll add `end` later.\n      tail.position = {start: point(token.start)}\n      // @ts-expect-error: Assume `parent` accepts `text`.\n      node.children.push(tail)\n    }\n\n    this.stack.push(tail)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitdata(token) {\n    const tail = this.stack.pop()\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tail, 'expected a `node` to be on the stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)('value' in tail, 'expected a `literal` to be on the stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tail.position, 'expected `node` to have an open position')\n    tail.value += this.sliceSerialize(token)\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlineending(token) {\n    const context = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(context, 'expected `node`')\n\n    // If we’re at a hard break, include the line ending in there.\n    if (getData('atHardBreak')) {\n      (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)('children' in context, 'expected `parent`')\n      const tail = context.children[context.children.length - 1]\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tail.position, 'expected tail to have a starting position')\n      tail.position.end = point(token.end)\n      setData('atHardBreak')\n      return\n    }\n\n    if (\n      !getData('setextHeadingSlurpLineEnding') &&\n      config.canContainEols.includes(context.type)\n    ) {\n      onenterdata.call(this, token)\n      onexitdata.call(this, token)\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithardbreak() {\n    setData('atHardBreak', true)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmlflow() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexithtmltext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'html', 'expected html on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcodetext() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'inlineCode', 'expected inline code on stack')\n\n    node.value = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlink() {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'link', 'expected link on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (getData('inReference')) {\n      /** @type {ReferenceType} */\n      const referenceType = getData('referenceType') || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    setData('referenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitimage() {\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'image', 'expected image on stack')\n\n    // Note: there are also `identifier` and `label` fields on this link node!\n    // These are used / cleaned here.\n\n    // To do: clean.\n    if (getData('inReference')) {\n      /** @type {ReferenceType} */\n      const referenceType = getData('referenceType') || 'shortcut'\n\n      node.type += 'Reference'\n      // @ts-expect-error: mutate.\n      node.referenceType = referenceType\n      // @ts-expect-error: mutate.\n      delete node.url\n      delete node.title\n    } else {\n      // @ts-expect-error: mutate.\n      delete node.identifier\n      // @ts-expect-error: mutate.\n      delete node.label\n    }\n\n    setData('referenceType')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabeltext(token) {\n    const string = this.sliceSerialize(token)\n    const ancestor = this.stack[this.stack.length - 2]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(ancestor, 'expected ancestor on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      ancestor.type === 'image' || ancestor.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    ancestor.label = (0,micromark_util_decode_string__WEBPACK_IMPORTED_MODULE_10__.decodeString)(string)\n    // @ts-expect-error: same as above.\n    ancestor.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(string).toLowerCase()\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitlabel() {\n    const fragment = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(fragment, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(fragment.type === 'fragment', 'expected fragment on stack')\n    const value = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n\n    // Assume a reference.\n    setData('inReference', true)\n\n    if (node.type === 'link') {\n      /** @type {Array<StaticPhrasingContent>} */\n      // @ts-expect-error: Assume static phrasing content.\n      const children = fragment.children\n\n      node.children = children\n    } else {\n      node.alt = value\n    }\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcedestinationstring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.url = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresourcetitlestring() {\n    const data = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image or link on stack'\n    )\n    node.title = data\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitresource() {\n    setData('inReference')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onenterreference() {\n    setData('referenceType', 'collapsed')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitreferencestring(token) {\n    const label = this.resume()\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      node.type === 'image' || node.type === 'link',\n      'expected image reference or link reference on stack'\n    )\n\n    // @ts-expect-error: stash this on the node, as it might become a reference\n    // later.\n    node.label = label\n    // @ts-expect-error: same as above.\n    node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_8__.normalizeIdentifier)(\n      this.sliceSerialize(token)\n    ).toLowerCase()\n    setData('referenceType', 'full')\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n\n  function onexitcharacterreferencemarker(token) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(\n      token.type === 'characterReferenceMarkerNumeric' ||\n        token.type === 'characterReferenceMarkerHexadecimal'\n    )\n    setData('characterReferenceType', token.type)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitcharacterreferencevalue(token) {\n    const data = this.sliceSerialize(token)\n    const type = getData('characterReferenceType')\n    /** @type {string} */\n    let value\n\n    if (type) {\n      value = (0,micromark_util_decode_numeric_character_reference__WEBPACK_IMPORTED_MODULE_11__.decodeNumericCharacterReference)(\n        data,\n        type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_4__.types.characterReferenceMarkerNumeric\n          ? micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseDecimal\n          : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_7__.constants.numericBaseHexadecimal\n      )\n      setData('characterReferenceType')\n    } else {\n      const result = (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_12__.decodeNamedCharacterReference)(data)\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(result !== false, 'expected reference to decode')\n      value = result\n    }\n\n    const tail = this.stack.pop()\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tail, 'expected `node`')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tail.position, 'expected `node.position`')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)('value' in tail, 'expected `node.value`')\n    tail.value += value\n    tail.position.end = point(token.end)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkprotocol(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'link', 'expected link on stack')\n\n    node.url = this.sliceSerialize(token)\n  }\n\n  /**\n   * @this {CompileContext}\n   * @type {Handle}\n   */\n  function onexitautolinkemail(token) {\n    onexitdata.call(this, token)\n    const node = this.stack[this.stack.length - 1]\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node, 'expected node on stack')\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(node.type === 'link', 'expected link on stack')\n\n    node.url = 'mailto:' + this.sliceSerialize(token)\n  }\n\n  //\n  // Creaters.\n  //\n\n  /** @returns {Blockquote} */\n  function blockQuote() {\n    return {type: 'blockquote', children: []}\n  }\n\n  /** @returns {Code} */\n  function codeFlow() {\n    return {type: 'code', lang: null, meta: null, value: ''}\n  }\n\n  /** @returns {InlineCode} */\n  function codeText() {\n    return {type: 'inlineCode', value: ''}\n  }\n\n  /** @returns {Definition} */\n  function definition() {\n    return {\n      type: 'definition',\n      identifier: '',\n      label: null,\n      title: null,\n      url: ''\n    }\n  }\n\n  /** @returns {Emphasis} */\n  function emphasis() {\n    return {type: 'emphasis', children: []}\n  }\n\n  /** @returns {Heading} */\n  function heading() {\n    // @ts-expect-error `depth` will be set later.\n    return {type: 'heading', depth: undefined, children: []}\n  }\n\n  /** @returns {Break} */\n  function hardBreak() {\n    return {type: 'break'}\n  }\n\n  /** @returns {HTML} */\n  function html() {\n    return {type: 'html', value: ''}\n  }\n\n  /** @returns {Image} */\n  function image() {\n    return {type: 'image', title: null, url: '', alt: null}\n  }\n\n  /** @returns {Link} */\n  function link() {\n    return {type: 'link', title: null, url: '', children: []}\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {List}\n   */\n  function list(token) {\n    return {\n      type: 'list',\n      ordered: token.type === 'listOrdered',\n      start: null,\n      spread: token._spread,\n      children: []\n    }\n  }\n\n  /**\n   * @param {Token} token\n   * @returns {ListItem}\n   */\n  function listItem(token) {\n    return {\n      type: 'listItem',\n      spread: token._spread,\n      checked: null,\n      children: []\n    }\n  }\n\n  /** @returns {Paragraph} */\n  function paragraph() {\n    return {type: 'paragraph', children: []}\n  }\n\n  /** @returns {Strong} */\n  function strong() {\n    return {type: 'strong', children: []}\n  }\n\n  /** @returns {Text} */\n  function text() {\n    return {type: 'text', value: ''}\n  }\n\n  /** @returns {ThematicBreak} */\n  function thematicBreak() {\n    return {type: 'thematicBreak'}\n  }\n}\n\n/**\n * Copy a point-like value.\n *\n * @param {Point} d\n *   Point-like value.\n * @returns {Point}\n *   unist point.\n */\nfunction point(d) {\n  return {line: d.line, column: d.column, offset: d.offset}\n}\n\n/**\n * @param {Config} combined\n * @param {Array<Extension | Array<Extension>>} extensions\n * @returns {void}\n */\nfunction configure(combined, extensions) {\n  let index = -1\n\n  while (++index < extensions.length) {\n    const value = extensions[index]\n\n    if (Array.isArray(value)) {\n      configure(combined, value)\n    } else {\n      extension(combined, value)\n    }\n  }\n}\n\n/**\n * @param {Config} combined\n * @param {Extension} extension\n * @returns {void}\n */\nfunction extension(combined, extension) {\n  /** @type {keyof Extension} */\n  let key\n\n  for (key in extension) {\n    if (own.call(extension, key)) {\n      if (key === 'canContainEols') {\n        const right = extension[key]\n        if (right) {\n          combined[key].push(...right)\n        }\n      } else if (key === 'transforms') {\n        const right = extension[key]\n        if (right) {\n          combined[key].push(...right)\n        }\n      } else if (key === 'enter' || key === 'exit') {\n        const right = extension[key]\n        if (right) {\n          Object.assign(combined[key], right)\n        }\n      }\n    }\n  }\n}\n\n/** @type {OnEnterError} */\nfunction defaultOnError(left, right) {\n  if (left) {\n    throw new Error(\n      'Cannot close `' +\n        left.type +\n        '` (' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: left.start, end: left.end}) +\n        '): a different token (`' +\n        right.type +\n        '`, ' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: right.start, end: right.end}) +\n        ') is open'\n    )\n  } else {\n    throw new Error(\n      'Cannot close document, a token (`' +\n        right.type +\n        '`, ' +\n        (0,unist_util_stringify_position__WEBPACK_IMPORTED_MODULE_5__.stringifyPosition)({start: right.start, end: right.end}) +\n        ') is still open'\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbWRhc3QtdXRpbC1mcm9tLW1hcmtkb3duL2Rldi9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBLGFBQWEseUNBQXlDO0FBQ3RELGFBQWEsc0NBQXNDO0FBQ25ELGFBQWEsNkNBQTZDO0FBQzFELGFBQWEsc0NBQXNDO0FBQ25ELGFBQWEsZ0RBQWdEO0FBQzdELGFBQWEsc0NBQXNDO0FBQ25EO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckMsYUFBYSx1QkFBdUI7QUFDcEM7QUFDQSxhQUFhLGlDQUFpQztBQUM5QyxhQUFhLHVDQUF1QztBQUNwRCxhQUFhLHlCQUF5QjtBQUN0QyxhQUFhLHVCQUF1QjtBQUNwQyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLDBCQUEwQjtBQUN2QyxhQUFhLHlCQUF5QjtBQUN0QyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLHVCQUF1QjtBQUNwQyxhQUFhLGdDQUFnQztBQUM3QyxhQUFhLDRCQUE0QjtBQUN6QyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLDBCQUEwQjtBQUN2QyxhQUFhLDJCQUEyQjtBQUN4QyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLHdCQUF3QjtBQUNyQyxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLCtCQUErQjtBQUM1QyxhQUFhLG1DQUFtQztBQUNoRDs7QUFFQTtBQUNBLGFBQWEsZ0JBQWdCO0FBQzdCLGFBQWEsNEJBQTRCO0FBQ3pDO0FBQ0EsYUFBYSwwQ0FBMEMscURBQXFEO0FBQzVHOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsZ0JBQWdCO0FBQzNCO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhLHdCQUF3QjtBQUNyQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx3Q0FBd0M7QUFDbkQ7QUFDQSxXQUFXLG1CQUFtQjtBQUM5QjtBQUNBLFdBQVcsT0FBTztBQUNsQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyx3Q0FBd0M7QUFDbkQ7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBLGFBQWEsbUNBQW1DO0FBQ2hEO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxlQUFlO0FBQzdCO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0EsY0FBYyxTQUFTO0FBQ3ZCO0FBQ0EsY0FBYyxrQkFBa0I7QUFDaEM7QUFDQTtBQUNBLGFBQWEsaUJBQWlCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyx3QkFBd0I7QUFDdEM7QUFDQSxjQUFjLG1CQUFtQjtBQUNqQztBQUNBLGNBQWMsK0RBQStEO0FBQzdFO0FBQ0EsY0FBYyw2RUFBNkU7QUFDM0Y7QUFDQSxjQUFjLGdDQUFnQztBQUM5QztBQUNBLGNBQWMsa0NBQWtDO0FBQ2hEO0FBQ0EsY0FBYyxxR0FBcUc7QUFDbkg7QUFDQSxjQUFjLHFFQUFxRTtBQUNuRjtBQUNBLGNBQWMsbUNBQW1DO0FBQ2pEO0FBQ0EsY0FBYyxRQUFRO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyx3REFBd0Q7QUFDdEU7QUFDQTtBQUNBLGFBQWEsb0NBQW9DO0FBQ2pEO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUV1QztBQUNNO0FBQ0Q7QUFDVTtBQUNFO0FBQ3lDO0FBQ3hDO0FBQ2M7QUFDbkI7QUFDUTtBQUNSO0FBQzBCO0FBQ2Y7O0FBRS9ELGNBQWM7O0FBRWQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsT0FBTztBQUN0QixlQUFlLHVDQUF1QztBQUN0RCxlQUFlLDRCQUE0QjtBQUMzQyxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsUUFBUSx5RUFBVztBQUNuQixVQUFVLDZEQUFLLDJCQUEyQix1RUFBVTtBQUNwRDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLDRCQUE0QjtBQUN2QztBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsa0NBQWtDOztBQUVsQyxhQUFhLGFBQWE7QUFDMUI7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYSxjQUFjO0FBQzNCO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBLGVBQWUsTUFBTTtBQUNyQixnQkFBZ0I7QUFDaEIsZUFBZSx3Q0FBd0M7QUFDdkQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsZUFBZTtBQUM5QjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0NBQWtDLGlFQUFLO0FBQ3ZDLGtDQUFrQyxpRUFBSztBQUN2QztBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxVQUFVLCtDQUFNO0FBQ2hCO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYSxnREFBZ0Q7QUFDN0Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0Q7QUFDbEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSxhQUFhLGNBQWM7QUFDM0IsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsbUJBQW1CO0FBQ2xDO0FBQ0EsZUFBZSxvQkFBb0I7QUFDbkM7QUFDQSxlQUFlLG9CQUFvQjtBQUNuQztBQUNBLGVBQWUscUJBQXFCO0FBQ3BDOztBQUVBO0FBQ0E7O0FBRUE7QUFDQSwwQkFBMEIsaUVBQUs7QUFDL0IsMEJBQTBCLGlFQUFLO0FBQy9CLDBCQUEwQixpRUFBSztBQUMvQjtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTs7QUFFQTtBQUNBLFFBQVEsMkJBQTJCLGlFQUFLO0FBQ3hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsUUFBUTtBQUNSLDBCQUEwQixpRUFBSztBQUMvQiwwQkFBMEIsaUVBQUs7QUFDL0IsMEJBQTBCLGlFQUFLO0FBQy9CLDBCQUEwQixpRUFBSztBQUMvQiwwQkFBMEIsaUVBQUs7QUFDL0I7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixpRUFBSztBQUNqQztBQUNBO0FBQ0EsNkJBQTZCLGlFQUFLO0FBQ2xDLDhCQUE4QixpRUFBSztBQUNuQztBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0Esb0NBQW9DLGlFQUFLO0FBQ3pDLG9DQUFvQyxpRUFBSztBQUN6QztBQUNBOztBQUVBO0FBQ0EsNENBQTRDLGlFQUFLO0FBQ2pEO0FBQ0E7O0FBRUEsa0NBQWtDLGlFQUFLO0FBQ3ZDO0FBQ0EsY0FBYztBQUNkLG9DQUFvQyxpRUFBSztBQUN6QyxvQ0FBb0MsaUVBQUs7QUFDekMsb0NBQW9DLGlFQUFLO0FBQ3pDLG9DQUFvQyxpRUFBSztBQUN6QyxvQ0FBb0MsaUVBQUs7QUFDekM7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLDhCQUE4QixpRUFBSztBQUNuQztBQUNBO0FBQ0E7QUFDQSxtQ0FBbUM7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQW1CO0FBQ25DO0FBQ0EsYUFBYSxLQUFLO0FBQ2xCO0FBQ0EsYUFBYSxrQkFBa0I7QUFDL0I7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsbUJBQW1CO0FBQ25DO0FBQ0EsYUFBYSxLQUFLO0FBQ2xCO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYSx3QkFBd0I7QUFDckM7QUFDQSxhQUFhLFFBQVE7QUFDckI7QUFDQSxlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxjQUFjO0FBQ2QsZUFBZSxPQUFPO0FBQ3RCLGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osZUFBZTtBQUNmO0FBQ0E7QUFDQSxxQkFBcUIsK0JBQStCO0FBQ3BEOztBQUVBO0FBQ0EsZ0JBQWdCLE1BQU07QUFDdEI7QUFDQSxZQUFZO0FBQ1o7QUFDQSxhQUFhLE1BQU07QUFDbkI7QUFDQSxhQUFhLE9BQU87QUFDcEI7QUFDQSxhQUFhLDBCQUEwQjtBQUN2QztBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksK0NBQU07QUFDVixJQUFJLCtDQUFNO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQjtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGFBQWEsUUFBUTtBQUNyQjtBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGNBQWM7QUFDZCxlQUFlLE9BQU87QUFDdEIsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWjtBQUNBLGFBQWEsT0FBTztBQUNwQjtBQUNBLGFBQWEseUJBQXlCO0FBQ3RDO0FBQ0EsZUFBZTtBQUNmO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrQ0FBTTtBQUNWOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLGdGQUFpQixFQUFFLG1DQUFtQztBQUNoRTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsSUFBSSw4Q0FBTTtBQUNWLElBQUksK0NBQU07QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osZUFBZTtBQUNmO0FBQ0E7QUFDQSxXQUFXLDhEQUFRO0FBQ25COztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSwrQ0FBTTtBQUNaLE1BQU0sK0NBQU07QUFDWjtBQUNBO0FBQ0EsUUFBUSx5RUFBUztBQUNqQjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTtBQUNWO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrQ0FBTTtBQUNWLElBQUksK0NBQU07QUFDVjtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTs7QUFFVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrQ0FBTTtBQUNWLElBQUksK0NBQU07O0FBRVY7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTs7QUFFVjtBQUNBLHNCQUFzQix3RkFBbUI7QUFDekM7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksK0NBQU07QUFDVixJQUFJLCtDQUFNOztBQUVWO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrQ0FBTTtBQUNWLElBQUksK0NBQU07O0FBRVY7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrQ0FBTTtBQUNWLElBQUksK0NBQU07O0FBRVY7QUFDQTs7QUFFQSxNQUFNLCtDQUFNO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTs7QUFFVjtBQUNBLG1EQUFtRCxpRUFBSztBQUN4RDs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaOztBQUVBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTs7QUFFVjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVCQUF1QjtBQUN2QjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7O0FBRUE7QUFDQTtBQUNBLElBQUksK0NBQU07QUFDVixJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7O0FBRUE7QUFDQTtBQUNBLElBQUksK0NBQU07O0FBRVY7QUFDQTtBQUNBLE1BQU0sOENBQU07QUFDWjtBQUNBLE1BQU0sK0NBQU07QUFDWjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUksK0NBQU07QUFDVixJQUFJLCtDQUFNOztBQUVWO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUksK0NBQU07QUFDVixJQUFJLCtDQUFNOztBQUVWO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaOztBQUVBO0FBQ0E7QUFDQTtBQUNBLElBQUksK0NBQU07QUFDVixJQUFJLCtDQUFNOztBQUVWO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaOztBQUVBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTs7QUFFVjtBQUNBOztBQUVBO0FBQ0E7QUFDQSxpQkFBaUIsZUFBZTtBQUNoQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjs7QUFFQTtBQUNBO0FBQ0EsSUFBSSwrQ0FBTTtBQUNWLElBQUksK0NBQU07O0FBRVY7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUJBQWlCLGVBQWU7QUFDaEM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrQ0FBTTtBQUNWLElBQUksK0NBQU07QUFDVjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHFCQUFxQiwyRUFBWTtBQUNqQztBQUNBLDBCQUEwQix3RkFBbUI7QUFDN0M7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaOztBQUVBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTtBQUNWO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0EsaUJBQWlCLDhCQUE4QjtBQUMvQztBQUNBOztBQUVBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsSUFBSSwrQ0FBTTtBQUNWLElBQUksK0NBQU07QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQix3RkFBbUI7QUFDekM7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaOztBQUVBO0FBQ0EsSUFBSSw4Q0FBTTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxZQUFZO0FBQ1osWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxRQUFRO0FBQ3ZCOztBQUVBO0FBQ0EsY0FBYyxtSEFBK0I7QUFDN0M7QUFDQSxpQkFBaUIsaUVBQUs7QUFDdEIsWUFBWSx5RUFBUztBQUNyQixZQUFZLHlFQUFTO0FBQ3JCO0FBQ0E7QUFDQSxNQUFNO0FBQ04scUJBQXFCLGdHQUE2QjtBQUNsRCxNQUFNLCtDQUFNO0FBQ1o7QUFDQTs7QUFFQTtBQUNBLElBQUksK0NBQU07QUFDVixJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTtBQUNWO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFlBQVk7QUFDWixZQUFZO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJLCtDQUFNO0FBQ1YsSUFBSSwrQ0FBTTs7QUFFVjtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksK0NBQU07QUFDVixJQUFJLCtDQUFNOztBQUVWO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGdCQUFnQixZQUFZO0FBQzVCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixNQUFNO0FBQ3RCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixZQUFZO0FBQzVCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixZQUFZO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxnQkFBZ0IsVUFBVTtBQUMxQjtBQUNBLFlBQVk7QUFDWjs7QUFFQSxnQkFBZ0IsU0FBUztBQUN6QjtBQUNBO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixPQUFPO0FBQ3ZCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixNQUFNO0FBQ3RCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixPQUFPO0FBQ3ZCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixNQUFNO0FBQ3RCO0FBQ0EsWUFBWTtBQUNaOztBQUVBO0FBQ0EsYUFBYSxPQUFPO0FBQ3BCLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLGFBQWEsT0FBTztBQUNwQixlQUFlO0FBQ2Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLGdCQUFnQixXQUFXO0FBQzNCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixRQUFRO0FBQ3hCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixNQUFNO0FBQ3RCO0FBQ0EsWUFBWTtBQUNaOztBQUVBLGdCQUFnQixlQUFlO0FBQy9CO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjs7QUFFQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLHFDQUFxQztBQUNoRCxhQUFhO0FBQ2I7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixXQUFXLFdBQVc7QUFDdEIsYUFBYTtBQUNiO0FBQ0E7QUFDQSxhQUFhLGlCQUFpQjtBQUM5Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLFdBQVcsY0FBYztBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGdGQUFpQixFQUFFLGlDQUFpQztBQUM1RDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGdGQUFpQixFQUFFLG1DQUFtQztBQUM5RDtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSxnRkFBaUIsRUFBRSxtQ0FBbUM7QUFDOUQ7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL21kYXN0LXV0aWwtZnJvbS1tYXJrZG93bi9kZXYvbGliL2luZGV4LmpzPzIzZjIiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLkVuY29kaW5nfSBFbmNvZGluZ1xuICogQHR5cGVkZWYge2ltcG9ydCgnbWljcm9tYXJrLXV0aWwtdHlwZXMnKS5FdmVudH0gRXZlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuUGFyc2VPcHRpb25zfSBQYXJzZU9wdGlvbnNcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21pY3JvbWFyay11dGlsLXR5cGVzJykuVG9rZW59IFRva2VuXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLlRva2VuaXplQ29udGV4dH0gVG9rZW5pemVDb250ZXh0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtaWNyb21hcmstdXRpbC10eXBlcycpLlZhbHVlfSBWYWx1ZVxuICpcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ3VuaXN0JykuUGFyZW50fSBVbmlzdFBhcmVudFxuICogQHR5cGVkZWYge2ltcG9ydCgndW5pc3QnKS5Qb2ludH0gUG9pbnRcbiAqXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlBocmFzaW5nQ29udGVudH0gUGhyYXNpbmdDb250ZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlN0YXRpY1BocmFzaW5nQ29udGVudH0gU3RhdGljUGhyYXNpbmdDb250ZW50XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkNvbnRlbnR9IENvbnRlbnRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuQnJlYWt9IEJyZWFrXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkJsb2NrcXVvdGV9IEJsb2NrcXVvdGVcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuQ29kZX0gQ29kZVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5EZWZpbml0aW9ufSBEZWZpbml0aW9uXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkVtcGhhc2lzfSBFbXBoYXNpc1xuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5IZWFkaW5nfSBIZWFkaW5nXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkhUTUx9IEhUTUxcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuSW1hZ2V9IEltYWdlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkltYWdlUmVmZXJlbmNlfSBJbWFnZVJlZmVyZW5jZVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5JbmxpbmVDb2RlfSBJbmxpbmVDb2RlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkxpbmt9IExpbmtcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuTGlua1JlZmVyZW5jZX0gTGlua1JlZmVyZW5jZVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5MaXN0fSBMaXN0XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLkxpc3RJdGVtfSBMaXN0SXRlbVxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5QYXJhZ3JhcGh9IFBhcmFncmFwaFxuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5Sb290fSBSb290XG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlN0cm9uZ30gU3Ryb25nXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCdtZGFzdCcpLlRleHR9IFRleHRcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ21kYXN0JykuVGhlbWF0aWNCcmVha30gVGhlbWF0aWNCcmVha1xuICogQHR5cGVkZWYge2ltcG9ydCgnbWRhc3QnKS5SZWZlcmVuY2VUeXBlfSBSZWZlcmVuY2VUeXBlXG4gKiBAdHlwZWRlZiB7aW1wb3J0KCcuLi9pbmRleC5qcycpLkNvbXBpbGVEYXRhfSBDb21waWxlRGF0YVxuICovXG5cbi8qKlxuICogQHR5cGVkZWYge1Jvb3QgfCBDb250ZW50fSBOb2RlXG4gKiBAdHlwZWRlZiB7RXh0cmFjdDxOb2RlLCBVbmlzdFBhcmVudD59IFBhcmVudFxuICpcbiAqIEB0eXBlZGVmIHtPbWl0PFVuaXN0UGFyZW50LCAndHlwZScgfCAnY2hpbGRyZW4nPiAmIHt0eXBlOiAnZnJhZ21lbnQnLCBjaGlsZHJlbjogQXJyYXk8UGhyYXNpbmdDb250ZW50Pn19IEZyYWdtZW50XG4gKi9cblxuLyoqXG4gKiBAY2FsbGJhY2sgVHJhbnNmb3JtXG4gKiAgIEV4dHJhIHRyYW5zZm9ybSwgdG8gY2hhbmdlIHRoZSBBU1QgYWZ0ZXJ3YXJkcy5cbiAqIEBwYXJhbSB7Um9vdH0gdHJlZVxuICogICBUcmVlIHRvIHRyYW5zZm9ybS5cbiAqIEByZXR1cm5zIHtSb290IHwgdW5kZWZpbmVkIHwgbnVsbCB8IHZvaWR9XG4gKiAgIE5ldyB0cmVlIG9yIG5vdGhpbmcgKGluIHdoaWNoIGNhc2UgdGhlIGN1cnJlbnQgdHJlZSBpcyB1c2VkKS5cbiAqXG4gKiBAY2FsbGJhY2sgSGFuZGxlXG4gKiAgIEhhbmRsZSBhIHRva2VuLlxuICogQHBhcmFtIHtDb21waWxlQ29udGV4dH0gdGhpc1xuICogICBDb250ZXh0LlxuICogQHBhcmFtIHtUb2tlbn0gdG9rZW5cbiAqICAgQ3VycmVudCB0b2tlbi5cbiAqIEByZXR1cm5zIHt2b2lkfVxuICogICBOb3RoaW5nLlxuICpcbiAqIEB0eXBlZGVmIHtSZWNvcmQ8c3RyaW5nLCBIYW5kbGU+fSBIYW5kbGVzXG4gKiAgIFRva2VuIHR5cGVzIG1hcHBpbmcgdG8gaGFuZGxlc1xuICpcbiAqIEBjYWxsYmFjayBPbkVudGVyRXJyb3JcbiAqICAgSGFuZGxlIHRoZSBjYXNlIHdoZXJlIHRoZSBgcmlnaHRgIHRva2VuIGlzIG9wZW4sIGJ1dCBpdCBpcyBjbG9zZWQgKGJ5IHRoZVxuICogICBgbGVmdGAgdG9rZW4pIG9yIGJlY2F1c2Ugd2UgcmVhY2hlZCB0aGUgZW5kIG9mIHRoZSBkb2N1bWVudC5cbiAqIEBwYXJhbSB7T21pdDxDb21waWxlQ29udGV4dCwgJ3NsaWNlU2VyaWFsaXplJz59IHRoaXNcbiAqICAgQ29udGV4dC5cbiAqIEBwYXJhbSB7VG9rZW4gfCB1bmRlZmluZWR9IGxlZnRcbiAqICAgTGVmdCB0b2tlbi5cbiAqIEBwYXJhbSB7VG9rZW59IHJpZ2h0XG4gKiAgIFJpZ2h0IHRva2VuLlxuICogQHJldHVybnMge3ZvaWR9XG4gKiAgIE5vdGhpbmcuXG4gKlxuICogQGNhbGxiYWNrIE9uRXhpdEVycm9yXG4gKiAgIEhhbmRsZSB0aGUgY2FzZSB3aGVyZSB0aGUgYHJpZ2h0YCB0b2tlbiBpcyBvcGVuIGJ1dCBpdCBpcyBjbG9zZWQgYnlcbiAqICAgZXhpdGluZyB0aGUgYGxlZnRgIHRva2VuLlxuICogQHBhcmFtIHtPbWl0PENvbXBpbGVDb250ZXh0LCAnc2xpY2VTZXJpYWxpemUnPn0gdGhpc1xuICogICBDb250ZXh0LlxuICogQHBhcmFtIHtUb2tlbn0gbGVmdFxuICogICBMZWZ0IHRva2VuLlxuICogQHBhcmFtIHtUb2tlbn0gcmlnaHRcbiAqICAgUmlnaHQgdG9rZW4uXG4gKiBAcmV0dXJucyB7dm9pZH1cbiAqICAgTm90aGluZy5cbiAqXG4gKiBAdHlwZWRlZiB7W1Rva2VuLCBPbkVudGVyRXJyb3IgfCB1bmRlZmluZWRdfSBUb2tlblR1cGxlXG4gKiAgIE9wZW4gdG9rZW4gb24gdGhlIHN0YWNrLCB3aXRoIGFuIG9wdGlvbmFsIGVycm9yIGhhbmRsZXIgZm9yIHdoZW5cbiAqICAgdGhhdCB0b2tlbiBpc27igJl0IGNsb3NlZCBwcm9wZXJseS5cbiAqL1xuXG4vKipcbiAqIEB0eXBlZGVmIENvbmZpZ1xuICogICBDb25maWd1cmF0aW9uLlxuICpcbiAqICAgV2UgaGF2ZSBvdXIgZGVmYXVsdHMsIGJ1dCBleHRlbnNpb25zIHdpbGwgYWRkIG1vcmUuXG4gKiBAcHJvcGVydHkge0FycmF5PHN0cmluZz59IGNhbkNvbnRhaW5Fb2xzXG4gKiAgIFRva2VuIHR5cGVzIHdoZXJlIGxpbmUgZW5kaW5ncyBhcmUgdXNlZC5cbiAqIEBwcm9wZXJ0eSB7SGFuZGxlc30gZW50ZXJcbiAqICAgT3BlbmluZyBoYW5kbGVzLlxuICogQHByb3BlcnR5IHtIYW5kbGVzfSBleGl0XG4gKiAgIENsb3NpbmcgaGFuZGxlcy5cbiAqIEBwcm9wZXJ0eSB7QXJyYXk8VHJhbnNmb3JtPn0gdHJhbnNmb3Jtc1xuICogICBUcmVlIHRyYW5zZm9ybXMuXG4gKlxuICogQHR5cGVkZWYge1BhcnRpYWw8Q29uZmlnPn0gRXh0ZW5zaW9uXG4gKiAgIENoYW5nZSBob3cgbWFya2Rvd24gdG9rZW5zIGZyb20gbWljcm9tYXJrIGFyZSB0dXJuZWQgaW50byBtZGFzdC5cbiAqXG4gKiBAdHlwZWRlZiBDb21waWxlQ29udGV4dFxuICogICBtZGFzdCBjb21waWxlciBjb250ZXh0LlxuICogQHByb3BlcnR5IHtBcnJheTxOb2RlIHwgRnJhZ21lbnQ+fSBzdGFja1xuICogICBTdGFjayBvZiBub2Rlcy5cbiAqIEBwcm9wZXJ0eSB7QXJyYXk8VG9rZW5UdXBsZT59IHRva2VuU3RhY2tcbiAqICAgU3RhY2sgb2YgdG9rZW5zLlxuICogQHByb3BlcnR5IHs8S2V5IGV4dGVuZHMga2V5b2YgQ29tcGlsZURhdGE+KGtleTogS2V5KSA9PiBDb21waWxlRGF0YVtLZXldfSBnZXREYXRhXG4gKiAgIEdldCBkYXRhIGZyb20gdGhlIGtleS92YWx1ZSBzdG9yZS5cbiAqIEBwcm9wZXJ0eSB7PEtleSBleHRlbmRzIGtleW9mIENvbXBpbGVEYXRhPihrZXk6IEtleSwgdmFsdWU/OiBDb21waWxlRGF0YVtLZXldKSA9PiB2b2lkfSBzZXREYXRhXG4gKiAgIFNldCBkYXRhIGludG8gdGhlIGtleS92YWx1ZSBzdG9yZS5cbiAqIEBwcm9wZXJ0eSB7KHRoaXM6IENvbXBpbGVDb250ZXh0KSA9PiB2b2lkfSBidWZmZXJcbiAqICAgQ2FwdHVyZSBzb21lIG9mIHRoZSBvdXRwdXQgZGF0YS5cbiAqIEBwcm9wZXJ0eSB7KHRoaXM6IENvbXBpbGVDb250ZXh0KSA9PiBzdHJpbmd9IHJlc3VtZVxuICogICBTdG9wIGNhcHR1cmluZyBhbmQgYWNjZXNzIHRoZSBvdXRwdXQgZGF0YS5cbiAqIEBwcm9wZXJ0eSB7PEtpbmQgZXh0ZW5kcyBOb2RlPih0aGlzOiBDb21waWxlQ29udGV4dCwgbm9kZTogS2luZCwgdG9rZW46IFRva2VuLCBvbkVycm9yPzogT25FbnRlckVycm9yKSA9PiBLaW5kfSBlbnRlclxuICogICBFbnRlciBhIHRva2VuLlxuICogQHByb3BlcnR5IHsodGhpczogQ29tcGlsZUNvbnRleHQsIHRva2VuOiBUb2tlbiwgb25FcnJvcj86IE9uRXhpdEVycm9yKSA9PiBOb2RlfSBleGl0XG4gKiAgIEV4aXQgYSB0b2tlbi5cbiAqIEBwcm9wZXJ0eSB7VG9rZW5pemVDb250ZXh0WydzbGljZVNlcmlhbGl6ZSddfSBzbGljZVNlcmlhbGl6ZVxuICogICBHZXQgdGhlIHN0cmluZyB2YWx1ZSBvZiBhIHRva2VuLlxuICogQHByb3BlcnR5IHtDb25maWd9IGNvbmZpZ1xuICogICBDb25maWd1cmF0aW9uLlxuICpcbiAqIEB0eXBlZGVmIEZyb21NYXJrZG93bk9wdGlvbnNcbiAqICAgQ29uZmlndXJhdGlvbiBmb3IgaG93IHRvIGJ1aWxkIG1kYXN0LlxuICogQHByb3BlcnR5IHtBcnJheTxFeHRlbnNpb24gfCBBcnJheTxFeHRlbnNpb24+PiB8IG51bGwgfCB1bmRlZmluZWR9IFttZGFzdEV4dGVuc2lvbnNdXG4gKiAgIEV4dGVuc2lvbnMgZm9yIHRoaXMgdXRpbGl0eSB0byBjaGFuZ2UgaG93IHRva2VucyBhcmUgdHVybmVkIGludG8gYSB0cmVlLlxuICpcbiAqIEB0eXBlZGVmIHtQYXJzZU9wdGlvbnMgJiBGcm9tTWFya2Rvd25PcHRpb25zfSBPcHRpb25zXG4gKiAgIENvbmZpZ3VyYXRpb24uXG4gKi9cblxuLy8gVG8gZG86IG1pY3JvbWFyazogY3JlYXRlIGEgcmVnaXN0cnkgb2YgdG9rZW5zP1xuLy8gVG8gZG86IG5leHQgbWFqb3I6IGRvbuKAmXQgcmV0dXJuIGdpdmVuIGBOb2RlYCBmcm9tIGBlbnRlcmAuXG4vLyBUbyBkbzogbmV4dCBtYWpvcjogcmVtb3ZlIHNldHRlci9nZXR0ZXIuXG5cbmltcG9ydCB7b2sgYXMgYXNzZXJ0fSBmcm9tICd1dnUvYXNzZXJ0J1xuaW1wb3J0IHt0b1N0cmluZ30gZnJvbSAnbWRhc3QtdXRpbC10by1zdHJpbmcnXG5pbXBvcnQge3BhcnNlfSBmcm9tICdtaWNyb21hcmsvbGliL3BhcnNlLmpzJ1xuaW1wb3J0IHtwcmVwcm9jZXNzfSBmcm9tICdtaWNyb21hcmsvbGliL3ByZXByb2Nlc3MuanMnXG5pbXBvcnQge3Bvc3Rwcm9jZXNzfSBmcm9tICdtaWNyb21hcmsvbGliL3Bvc3Rwcm9jZXNzLmpzJ1xuaW1wb3J0IHtkZWNvZGVOdW1lcmljQ2hhcmFjdGVyUmVmZXJlbmNlfSBmcm9tICdtaWNyb21hcmstdXRpbC1kZWNvZGUtbnVtZXJpYy1jaGFyYWN0ZXItcmVmZXJlbmNlJ1xuaW1wb3J0IHtkZWNvZGVTdHJpbmd9IGZyb20gJ21pY3JvbWFyay11dGlsLWRlY29kZS1zdHJpbmcnXG5pbXBvcnQge25vcm1hbGl6ZUlkZW50aWZpZXJ9IGZyb20gJ21pY3JvbWFyay11dGlsLW5vcm1hbGl6ZS1pZGVudGlmaWVyJ1xuaW1wb3J0IHtjb2Rlc30gZnJvbSAnbWljcm9tYXJrLXV0aWwtc3ltYm9sL2NvZGVzLmpzJ1xuaW1wb3J0IHtjb25zdGFudHN9IGZyb20gJ21pY3JvbWFyay11dGlsLXN5bWJvbC9jb25zdGFudHMuanMnXG5pbXBvcnQge3R5cGVzfSBmcm9tICdtaWNyb21hcmstdXRpbC1zeW1ib2wvdHlwZXMuanMnXG5pbXBvcnQge2RlY29kZU5hbWVkQ2hhcmFjdGVyUmVmZXJlbmNlfSBmcm9tICdkZWNvZGUtbmFtZWQtY2hhcmFjdGVyLXJlZmVyZW5jZSdcbmltcG9ydCB7c3RyaW5naWZ5UG9zaXRpb259IGZyb20gJ3VuaXN0LXV0aWwtc3RyaW5naWZ5LXBvc2l0aW9uJ1xuXG5jb25zdCBvd24gPSB7fS5oYXNPd25Qcm9wZXJ0eVxuXG4vKipcbiAqIEBwYXJhbSB2YWx1ZVxuICogICBNYXJrZG93biB0byBwYXJzZS5cbiAqIEBwYXJhbSBlbmNvZGluZ1xuICogICBDaGFyYWN0ZXIgZW5jb2RpbmcgZm9yIHdoZW4gYHZhbHVlYCBpcyBgQnVmZmVyYC5cbiAqIEBwYXJhbSBvcHRpb25zXG4gKiAgIENvbmZpZ3VyYXRpb24uXG4gKiBAcmV0dXJuc1xuICogICBtZGFzdCB0cmVlLlxuICovXG5leHBvcnQgY29uc3QgZnJvbU1hcmtkb3duID1cbiAgLyoqXG4gICAqIEB0eXBlIHsoXG4gICAqICAgKCh2YWx1ZTogVmFsdWUsIGVuY29kaW5nOiBFbmNvZGluZywgb3B0aW9ucz86IE9wdGlvbnMgfCBudWxsIHwgdW5kZWZpbmVkKSA9PiBSb290KSAmXG4gICAqICAgKCh2YWx1ZTogVmFsdWUsIG9wdGlvbnM/OiBPcHRpb25zIHwgbnVsbCB8IHVuZGVmaW5lZCkgPT4gUm9vdClcbiAgICogKX1cbiAgICovXG4gIChcbiAgICAvKipcbiAgICAgKiBAcGFyYW0ge1ZhbHVlfSB2YWx1ZVxuICAgICAqIEBwYXJhbSB7RW5jb2RpbmcgfCBPcHRpb25zIHwgbnVsbCB8IHVuZGVmaW5lZH0gW2VuY29kaW5nXVxuICAgICAqIEBwYXJhbSB7T3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IFtvcHRpb25zXVxuICAgICAqIEByZXR1cm5zIHtSb290fVxuICAgICAqL1xuICAgIGZ1bmN0aW9uICh2YWx1ZSwgZW5jb2RpbmcsIG9wdGlvbnMpIHtcbiAgICAgIGlmICh0eXBlb2YgZW5jb2RpbmcgIT09ICdzdHJpbmcnKSB7XG4gICAgICAgIG9wdGlvbnMgPSBlbmNvZGluZ1xuICAgICAgICBlbmNvZGluZyA9IHVuZGVmaW5lZFxuICAgICAgfVxuXG4gICAgICByZXR1cm4gY29tcGlsZXIob3B0aW9ucykoXG4gICAgICAgIHBvc3Rwcm9jZXNzKFxuICAgICAgICAgIHBhcnNlKG9wdGlvbnMpLmRvY3VtZW50KCkud3JpdGUocHJlcHJvY2VzcygpKHZhbHVlLCBlbmNvZGluZywgdHJ1ZSkpXG4gICAgICAgIClcbiAgICAgIClcbiAgICB9XG4gIClcblxuLyoqXG4gKiBOb3RlIHRoaXMgY29tcGlsZXIgb25seSB1bmRlcnN0YW5kIGNvbXBsZXRlIGJ1ZmZlcmluZywgbm90IHN0cmVhbWluZy5cbiAqXG4gKiBAcGFyYW0ge09wdGlvbnMgfCBudWxsIHwgdW5kZWZpbmVkfSBbb3B0aW9uc11cbiAqL1xuZnVuY3Rpb24gY29tcGlsZXIob3B0aW9ucykge1xuICAvKiogQHR5cGUge0NvbmZpZ30gKi9cbiAgY29uc3QgY29uZmlnID0ge1xuICAgIHRyYW5zZm9ybXM6IFtdLFxuICAgIGNhbkNvbnRhaW5Fb2xzOiBbJ2VtcGhhc2lzJywgJ2ZyYWdtZW50JywgJ2hlYWRpbmcnLCAncGFyYWdyYXBoJywgJ3N0cm9uZyddLFxuICAgIGVudGVyOiB7XG4gICAgICBhdXRvbGluazogb3BlbmVyKGxpbmspLFxuICAgICAgYXV0b2xpbmtQcm90b2NvbDogb25lbnRlcmRhdGEsXG4gICAgICBhdXRvbGlua0VtYWlsOiBvbmVudGVyZGF0YSxcbiAgICAgIGF0eEhlYWRpbmc6IG9wZW5lcihoZWFkaW5nKSxcbiAgICAgIGJsb2NrUXVvdGU6IG9wZW5lcihibG9ja1F1b3RlKSxcbiAgICAgIGNoYXJhY3RlckVzY2FwZTogb25lbnRlcmRhdGEsXG4gICAgICBjaGFyYWN0ZXJSZWZlcmVuY2U6IG9uZW50ZXJkYXRhLFxuICAgICAgY29kZUZlbmNlZDogb3BlbmVyKGNvZGVGbG93KSxcbiAgICAgIGNvZGVGZW5jZWRGZW5jZUluZm86IGJ1ZmZlcixcbiAgICAgIGNvZGVGZW5jZWRGZW5jZU1ldGE6IGJ1ZmZlcixcbiAgICAgIGNvZGVJbmRlbnRlZDogb3BlbmVyKGNvZGVGbG93LCBidWZmZXIpLFxuICAgICAgY29kZVRleHQ6IG9wZW5lcihjb2RlVGV4dCwgYnVmZmVyKSxcbiAgICAgIGNvZGVUZXh0RGF0YTogb25lbnRlcmRhdGEsXG4gICAgICBkYXRhOiBvbmVudGVyZGF0YSxcbiAgICAgIGNvZGVGbG93VmFsdWU6IG9uZW50ZXJkYXRhLFxuICAgICAgZGVmaW5pdGlvbjogb3BlbmVyKGRlZmluaXRpb24pLFxuICAgICAgZGVmaW5pdGlvbkRlc3RpbmF0aW9uU3RyaW5nOiBidWZmZXIsXG4gICAgICBkZWZpbml0aW9uTGFiZWxTdHJpbmc6IGJ1ZmZlcixcbiAgICAgIGRlZmluaXRpb25UaXRsZVN0cmluZzogYnVmZmVyLFxuICAgICAgZW1waGFzaXM6IG9wZW5lcihlbXBoYXNpcyksXG4gICAgICBoYXJkQnJlYWtFc2NhcGU6IG9wZW5lcihoYXJkQnJlYWspLFxuICAgICAgaGFyZEJyZWFrVHJhaWxpbmc6IG9wZW5lcihoYXJkQnJlYWspLFxuICAgICAgaHRtbEZsb3c6IG9wZW5lcihodG1sLCBidWZmZXIpLFxuICAgICAgaHRtbEZsb3dEYXRhOiBvbmVudGVyZGF0YSxcbiAgICAgIGh0bWxUZXh0OiBvcGVuZXIoaHRtbCwgYnVmZmVyKSxcbiAgICAgIGh0bWxUZXh0RGF0YTogb25lbnRlcmRhdGEsXG4gICAgICBpbWFnZTogb3BlbmVyKGltYWdlKSxcbiAgICAgIGxhYmVsOiBidWZmZXIsXG4gICAgICBsaW5rOiBvcGVuZXIobGluayksXG4gICAgICBsaXN0SXRlbTogb3BlbmVyKGxpc3RJdGVtKSxcbiAgICAgIGxpc3RJdGVtVmFsdWU6IG9uZW50ZXJsaXN0aXRlbXZhbHVlLFxuICAgICAgbGlzdE9yZGVyZWQ6IG9wZW5lcihsaXN0LCBvbmVudGVybGlzdG9yZGVyZWQpLFxuICAgICAgbGlzdFVub3JkZXJlZDogb3BlbmVyKGxpc3QpLFxuICAgICAgcGFyYWdyYXBoOiBvcGVuZXIocGFyYWdyYXBoKSxcbiAgICAgIHJlZmVyZW5jZTogb25lbnRlcnJlZmVyZW5jZSxcbiAgICAgIHJlZmVyZW5jZVN0cmluZzogYnVmZmVyLFxuICAgICAgcmVzb3VyY2VEZXN0aW5hdGlvblN0cmluZzogYnVmZmVyLFxuICAgICAgcmVzb3VyY2VUaXRsZVN0cmluZzogYnVmZmVyLFxuICAgICAgc2V0ZXh0SGVhZGluZzogb3BlbmVyKGhlYWRpbmcpLFxuICAgICAgc3Ryb25nOiBvcGVuZXIoc3Ryb25nKSxcbiAgICAgIHRoZW1hdGljQnJlYWs6IG9wZW5lcih0aGVtYXRpY0JyZWFrKVxuICAgIH0sXG4gICAgZXhpdDoge1xuICAgICAgYXR4SGVhZGluZzogY2xvc2VyKCksXG4gICAgICBhdHhIZWFkaW5nU2VxdWVuY2U6IG9uZXhpdGF0eGhlYWRpbmdzZXF1ZW5jZSxcbiAgICAgIGF1dG9saW5rOiBjbG9zZXIoKSxcbiAgICAgIGF1dG9saW5rRW1haWw6IG9uZXhpdGF1dG9saW5rZW1haWwsXG4gICAgICBhdXRvbGlua1Byb3RvY29sOiBvbmV4aXRhdXRvbGlua3Byb3RvY29sLFxuICAgICAgYmxvY2tRdW90ZTogY2xvc2VyKCksXG4gICAgICBjaGFyYWN0ZXJFc2NhcGVWYWx1ZTogb25leGl0ZGF0YSxcbiAgICAgIGNoYXJhY3RlclJlZmVyZW5jZU1hcmtlckhleGFkZWNpbWFsOiBvbmV4aXRjaGFyYWN0ZXJyZWZlcmVuY2VtYXJrZXIsXG4gICAgICBjaGFyYWN0ZXJSZWZlcmVuY2VNYXJrZXJOdW1lcmljOiBvbmV4aXRjaGFyYWN0ZXJyZWZlcmVuY2VtYXJrZXIsXG4gICAgICBjaGFyYWN0ZXJSZWZlcmVuY2VWYWx1ZTogb25leGl0Y2hhcmFjdGVycmVmZXJlbmNldmFsdWUsXG4gICAgICBjb2RlRmVuY2VkOiBjbG9zZXIob25leGl0Y29kZWZlbmNlZCksXG4gICAgICBjb2RlRmVuY2VkRmVuY2U6IG9uZXhpdGNvZGVmZW5jZWRmZW5jZSxcbiAgICAgIGNvZGVGZW5jZWRGZW5jZUluZm86IG9uZXhpdGNvZGVmZW5jZWRmZW5jZWluZm8sXG4gICAgICBjb2RlRmVuY2VkRmVuY2VNZXRhOiBvbmV4aXRjb2RlZmVuY2VkZmVuY2VtZXRhLFxuICAgICAgY29kZUZsb3dWYWx1ZTogb25leGl0ZGF0YSxcbiAgICAgIGNvZGVJbmRlbnRlZDogY2xvc2VyKG9uZXhpdGNvZGVpbmRlbnRlZCksXG4gICAgICBjb2RlVGV4dDogY2xvc2VyKG9uZXhpdGNvZGV0ZXh0KSxcbiAgICAgIGNvZGVUZXh0RGF0YTogb25leGl0ZGF0YSxcbiAgICAgIGRhdGE6IG9uZXhpdGRhdGEsXG4gICAgICBkZWZpbml0aW9uOiBjbG9zZXIoKSxcbiAgICAgIGRlZmluaXRpb25EZXN0aW5hdGlvblN0cmluZzogb25leGl0ZGVmaW5pdGlvbmRlc3RpbmF0aW9uc3RyaW5nLFxuICAgICAgZGVmaW5pdGlvbkxhYmVsU3RyaW5nOiBvbmV4aXRkZWZpbml0aW9ubGFiZWxzdHJpbmcsXG4gICAgICBkZWZpbml0aW9uVGl0bGVTdHJpbmc6IG9uZXhpdGRlZmluaXRpb250aXRsZXN0cmluZyxcbiAgICAgIGVtcGhhc2lzOiBjbG9zZXIoKSxcbiAgICAgIGhhcmRCcmVha0VzY2FwZTogY2xvc2VyKG9uZXhpdGhhcmRicmVhayksXG4gICAgICBoYXJkQnJlYWtUcmFpbGluZzogY2xvc2VyKG9uZXhpdGhhcmRicmVhayksXG4gICAgICBodG1sRmxvdzogY2xvc2VyKG9uZXhpdGh0bWxmbG93KSxcbiAgICAgIGh0bWxGbG93RGF0YTogb25leGl0ZGF0YSxcbiAgICAgIGh0bWxUZXh0OiBjbG9zZXIob25leGl0aHRtbHRleHQpLFxuICAgICAgaHRtbFRleHREYXRhOiBvbmV4aXRkYXRhLFxuICAgICAgaW1hZ2U6IGNsb3NlcihvbmV4aXRpbWFnZSksXG4gICAgICBsYWJlbDogb25leGl0bGFiZWwsXG4gICAgICBsYWJlbFRleHQ6IG9uZXhpdGxhYmVsdGV4dCxcbiAgICAgIGxpbmVFbmRpbmc6IG9uZXhpdGxpbmVlbmRpbmcsXG4gICAgICBsaW5rOiBjbG9zZXIob25leGl0bGluayksXG4gICAgICBsaXN0SXRlbTogY2xvc2VyKCksXG4gICAgICBsaXN0T3JkZXJlZDogY2xvc2VyKCksXG4gICAgICBsaXN0VW5vcmRlcmVkOiBjbG9zZXIoKSxcbiAgICAgIHBhcmFncmFwaDogY2xvc2VyKCksXG4gICAgICByZWZlcmVuY2VTdHJpbmc6IG9uZXhpdHJlZmVyZW5jZXN0cmluZyxcbiAgICAgIHJlc291cmNlRGVzdGluYXRpb25TdHJpbmc6IG9uZXhpdHJlc291cmNlZGVzdGluYXRpb25zdHJpbmcsXG4gICAgICByZXNvdXJjZVRpdGxlU3RyaW5nOiBvbmV4aXRyZXNvdXJjZXRpdGxlc3RyaW5nLFxuICAgICAgcmVzb3VyY2U6IG9uZXhpdHJlc291cmNlLFxuICAgICAgc2V0ZXh0SGVhZGluZzogY2xvc2VyKG9uZXhpdHNldGV4dGhlYWRpbmcpLFxuICAgICAgc2V0ZXh0SGVhZGluZ0xpbmVTZXF1ZW5jZTogb25leGl0c2V0ZXh0aGVhZGluZ2xpbmVzZXF1ZW5jZSxcbiAgICAgIHNldGV4dEhlYWRpbmdUZXh0OiBvbmV4aXRzZXRleHRoZWFkaW5ndGV4dCxcbiAgICAgIHN0cm9uZzogY2xvc2VyKCksXG4gICAgICB0aGVtYXRpY0JyZWFrOiBjbG9zZXIoKVxuICAgIH1cbiAgfVxuXG4gIGNvbmZpZ3VyZShjb25maWcsIChvcHRpb25zIHx8IHt9KS5tZGFzdEV4dGVuc2lvbnMgfHwgW10pXG5cbiAgLyoqIEB0eXBlIHtDb21waWxlRGF0YX0gKi9cbiAgY29uc3QgZGF0YSA9IHt9XG5cbiAgcmV0dXJuIGNvbXBpbGVcblxuICAvKipcbiAgICogVHVybiBtaWNyb21hcmsgZXZlbnRzIGludG8gYW4gbWRhc3QgdHJlZS5cbiAgICpcbiAgICogQHBhcmFtIHtBcnJheTxFdmVudD59IGV2ZW50c1xuICAgKiAgIEV2ZW50cy5cbiAgICogQHJldHVybnMge1Jvb3R9XG4gICAqICAgbWRhc3QgdHJlZS5cbiAgICovXG4gIGZ1bmN0aW9uIGNvbXBpbGUoZXZlbnRzKSB7XG4gICAgLyoqIEB0eXBlIHtSb290fSAqL1xuICAgIGxldCB0cmVlID0ge3R5cGU6ICdyb290JywgY2hpbGRyZW46IFtdfVxuICAgIC8qKiBAdHlwZSB7T21pdDxDb21waWxlQ29udGV4dCwgJ3NsaWNlU2VyaWFsaXplJz59ICovXG4gICAgY29uc3QgY29udGV4dCA9IHtcbiAgICAgIHN0YWNrOiBbdHJlZV0sXG4gICAgICB0b2tlblN0YWNrOiBbXSxcbiAgICAgIGNvbmZpZyxcbiAgICAgIGVudGVyLFxuICAgICAgZXhpdCxcbiAgICAgIGJ1ZmZlcixcbiAgICAgIHJlc3VtZSxcbiAgICAgIHNldERhdGEsXG4gICAgICBnZXREYXRhXG4gICAgfVxuICAgIC8qKiBAdHlwZSB7QXJyYXk8bnVtYmVyPn0gKi9cbiAgICBjb25zdCBsaXN0U3RhY2sgPSBbXVxuICAgIGxldCBpbmRleCA9IC0xXG5cbiAgICB3aGlsZSAoKytpbmRleCA8IGV2ZW50cy5sZW5ndGgpIHtcbiAgICAgIC8vIFdlIHByZXByb2Nlc3MgbGlzdHMgdG8gYWRkIGBsaXN0SXRlbWAgdG9rZW5zLCBhbmQgdG8gaW5mZXIgd2hldGhlclxuICAgICAgLy8gaXRlbXMgdGhlIGxpc3QgaXRzZWxmIGFyZSBzcHJlYWQgb3V0LlxuICAgICAgaWYgKFxuICAgICAgICBldmVudHNbaW5kZXhdWzFdLnR5cGUgPT09IHR5cGVzLmxpc3RPcmRlcmVkIHx8XG4gICAgICAgIGV2ZW50c1tpbmRleF1bMV0udHlwZSA9PT0gdHlwZXMubGlzdFVub3JkZXJlZFxuICAgICAgKSB7XG4gICAgICAgIGlmIChldmVudHNbaW5kZXhdWzBdID09PSAnZW50ZXInKSB7XG4gICAgICAgICAgbGlzdFN0YWNrLnB1c2goaW5kZXgpXG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc3QgdGFpbCA9IGxpc3RTdGFjay5wb3AoKVxuICAgICAgICAgIGFzc2VydCh0eXBlb2YgdGFpbCA9PT0gJ251bWJlcicsICdleHBlY3RlZCBsaXN0IG90IGJlIG9wZW4nKVxuICAgICAgICAgIGluZGV4ID0gcHJlcGFyZUxpc3QoZXZlbnRzLCB0YWlsLCBpbmRleClcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cblxuICAgIGluZGV4ID0gLTFcblxuICAgIHdoaWxlICgrK2luZGV4IDwgZXZlbnRzLmxlbmd0aCkge1xuICAgICAgY29uc3QgaGFuZGxlciA9IGNvbmZpZ1tldmVudHNbaW5kZXhdWzBdXVxuXG4gICAgICBpZiAob3duLmNhbGwoaGFuZGxlciwgZXZlbnRzW2luZGV4XVsxXS50eXBlKSkge1xuICAgICAgICBoYW5kbGVyW2V2ZW50c1tpbmRleF1bMV0udHlwZV0uY2FsbChcbiAgICAgICAgICBPYmplY3QuYXNzaWduKFxuICAgICAgICAgICAge3NsaWNlU2VyaWFsaXplOiBldmVudHNbaW5kZXhdWzJdLnNsaWNlU2VyaWFsaXplfSxcbiAgICAgICAgICAgIGNvbnRleHRcbiAgICAgICAgICApLFxuICAgICAgICAgIGV2ZW50c1tpbmRleF1bMV1cbiAgICAgICAgKVxuICAgICAgfVxuICAgIH1cblxuICAgIC8vIEhhbmRsZSB0b2tlbnMgc3RpbGwgYmVpbmcgb3Blbi5cbiAgICBpZiAoY29udGV4dC50b2tlblN0YWNrLmxlbmd0aCA+IDApIHtcbiAgICAgIGNvbnN0IHRhaWwgPSBjb250ZXh0LnRva2VuU3RhY2tbY29udGV4dC50b2tlblN0YWNrLmxlbmd0aCAtIDFdXG4gICAgICBjb25zdCBoYW5kbGVyID0gdGFpbFsxXSB8fCBkZWZhdWx0T25FcnJvclxuICAgICAgaGFuZGxlci5jYWxsKGNvbnRleHQsIHVuZGVmaW5lZCwgdGFpbFswXSlcbiAgICB9XG5cbiAgICAvLyBGaWd1cmUgb3V0IGByb290YCBwb3NpdGlvbi5cbiAgICB0cmVlLnBvc2l0aW9uID0ge1xuICAgICAgc3RhcnQ6IHBvaW50KFxuICAgICAgICBldmVudHMubGVuZ3RoID4gMCA/IGV2ZW50c1swXVsxXS5zdGFydCA6IHtsaW5lOiAxLCBjb2x1bW46IDEsIG9mZnNldDogMH1cbiAgICAgICksXG4gICAgICBlbmQ6IHBvaW50KFxuICAgICAgICBldmVudHMubGVuZ3RoID4gMFxuICAgICAgICAgID8gZXZlbnRzW2V2ZW50cy5sZW5ndGggLSAyXVsxXS5lbmRcbiAgICAgICAgICA6IHtsaW5lOiAxLCBjb2x1bW46IDEsIG9mZnNldDogMH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBDYWxsIHRyYW5zZm9ybXMuXG4gICAgaW5kZXggPSAtMVxuICAgIHdoaWxlICgrK2luZGV4IDwgY29uZmlnLnRyYW5zZm9ybXMubGVuZ3RoKSB7XG4gICAgICB0cmVlID0gY29uZmlnLnRyYW5zZm9ybXNbaW5kZXhdKHRyZWUpIHx8IHRyZWVcbiAgICB9XG5cbiAgICByZXR1cm4gdHJlZVxuICB9XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7QXJyYXk8RXZlbnQ+fSBldmVudHNcbiAgICogQHBhcmFtIHtudW1iZXJ9IHN0YXJ0XG4gICAqIEBwYXJhbSB7bnVtYmVyfSBsZW5ndGhcbiAgICogQHJldHVybnMge251bWJlcn1cbiAgICovXG4gIGZ1bmN0aW9uIHByZXBhcmVMaXN0KGV2ZW50cywgc3RhcnQsIGxlbmd0aCkge1xuICAgIGxldCBpbmRleCA9IHN0YXJ0IC0gMVxuICAgIGxldCBjb250YWluZXJCYWxhbmNlID0gLTFcbiAgICBsZXQgbGlzdFNwcmVhZCA9IGZhbHNlXG4gICAgLyoqIEB0eXBlIHtUb2tlbiB8IHVuZGVmaW5lZH0gKi9cbiAgICBsZXQgbGlzdEl0ZW1cbiAgICAvKiogQHR5cGUge251bWJlciB8IHVuZGVmaW5lZH0gKi9cbiAgICBsZXQgbGluZUluZGV4XG4gICAgLyoqIEB0eXBlIHtudW1iZXIgfCB1bmRlZmluZWR9ICovXG4gICAgbGV0IGZpcnN0QmxhbmtMaW5lSW5kZXhcbiAgICAvKiogQHR5cGUge2Jvb2xlYW4gfCB1bmRlZmluZWR9ICovXG4gICAgbGV0IGF0TWFya2VyXG5cbiAgICB3aGlsZSAoKytpbmRleCA8PSBsZW5ndGgpIHtcbiAgICAgIGNvbnN0IGV2ZW50ID0gZXZlbnRzW2luZGV4XVxuXG4gICAgICBpZiAoXG4gICAgICAgIGV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpc3RVbm9yZGVyZWQgfHxcbiAgICAgICAgZXZlbnRbMV0udHlwZSA9PT0gdHlwZXMubGlzdE9yZGVyZWQgfHxcbiAgICAgICAgZXZlbnRbMV0udHlwZSA9PT0gdHlwZXMuYmxvY2tRdW90ZVxuICAgICAgKSB7XG4gICAgICAgIGlmIChldmVudFswXSA9PT0gJ2VudGVyJykge1xuICAgICAgICAgIGNvbnRhaW5lckJhbGFuY2UrK1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnRhaW5lckJhbGFuY2UtLVxuICAgICAgICB9XG5cbiAgICAgICAgYXRNYXJrZXIgPSB1bmRlZmluZWRcbiAgICAgIH0gZWxzZSBpZiAoZXZlbnRbMV0udHlwZSA9PT0gdHlwZXMubGluZUVuZGluZ0JsYW5rKSB7XG4gICAgICAgIGlmIChldmVudFswXSA9PT0gJ2VudGVyJykge1xuICAgICAgICAgIGlmIChcbiAgICAgICAgICAgIGxpc3RJdGVtICYmXG4gICAgICAgICAgICAhYXRNYXJrZXIgJiZcbiAgICAgICAgICAgICFjb250YWluZXJCYWxhbmNlICYmXG4gICAgICAgICAgICAhZmlyc3RCbGFua0xpbmVJbmRleFxuICAgICAgICAgICkge1xuICAgICAgICAgICAgZmlyc3RCbGFua0xpbmVJbmRleCA9IGluZGV4XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgYXRNYXJrZXIgPSB1bmRlZmluZWRcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChcbiAgICAgICAgZXZlbnRbMV0udHlwZSA9PT0gdHlwZXMubGluZVByZWZpeCB8fFxuICAgICAgICBldmVudFsxXS50eXBlID09PSB0eXBlcy5saXN0SXRlbVZhbHVlIHx8XG4gICAgICAgIGV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpc3RJdGVtTWFya2VyIHx8XG4gICAgICAgIGV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpc3RJdGVtUHJlZml4IHx8XG4gICAgICAgIGV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpc3RJdGVtUHJlZml4V2hpdGVzcGFjZVxuICAgICAgKSB7XG4gICAgICAgIC8vIEVtcHR5LlxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgYXRNYXJrZXIgPSB1bmRlZmluZWRcbiAgICAgIH1cblxuICAgICAgaWYgKFxuICAgICAgICAoIWNvbnRhaW5lckJhbGFuY2UgJiZcbiAgICAgICAgICBldmVudFswXSA9PT0gJ2VudGVyJyAmJlxuICAgICAgICAgIGV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpc3RJdGVtUHJlZml4KSB8fFxuICAgICAgICAoY29udGFpbmVyQmFsYW5jZSA9PT0gLTEgJiZcbiAgICAgICAgICBldmVudFswXSA9PT0gJ2V4aXQnICYmXG4gICAgICAgICAgKGV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpc3RVbm9yZGVyZWQgfHxcbiAgICAgICAgICAgIGV2ZW50WzFdLnR5cGUgPT09IHR5cGVzLmxpc3RPcmRlcmVkKSlcbiAgICAgICkge1xuICAgICAgICBpZiAobGlzdEl0ZW0pIHtcbiAgICAgICAgICBsZXQgdGFpbEluZGV4ID0gaW5kZXhcbiAgICAgICAgICBsaW5lSW5kZXggPSB1bmRlZmluZWRcblxuICAgICAgICAgIHdoaWxlICh0YWlsSW5kZXgtLSkge1xuICAgICAgICAgICAgY29uc3QgdGFpbEV2ZW50ID0gZXZlbnRzW3RhaWxJbmRleF1cblxuICAgICAgICAgICAgaWYgKFxuICAgICAgICAgICAgICB0YWlsRXZlbnRbMV0udHlwZSA9PT0gdHlwZXMubGluZUVuZGluZyB8fFxuICAgICAgICAgICAgICB0YWlsRXZlbnRbMV0udHlwZSA9PT0gdHlwZXMubGluZUVuZGluZ0JsYW5rXG4gICAgICAgICAgICApIHtcbiAgICAgICAgICAgICAgaWYgKHRhaWxFdmVudFswXSA9PT0gJ2V4aXQnKSBjb250aW51ZVxuXG4gICAgICAgICAgICAgIGlmIChsaW5lSW5kZXgpIHtcbiAgICAgICAgICAgICAgICBldmVudHNbbGluZUluZGV4XVsxXS50eXBlID0gdHlwZXMubGluZUVuZGluZ0JsYW5rXG4gICAgICAgICAgICAgICAgbGlzdFNwcmVhZCA9IHRydWVcbiAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgIHRhaWxFdmVudFsxXS50eXBlID0gdHlwZXMubGluZUVuZGluZ1xuICAgICAgICAgICAgICBsaW5lSW5kZXggPSB0YWlsSW5kZXhcbiAgICAgICAgICAgIH0gZWxzZSBpZiAoXG4gICAgICAgICAgICAgIHRhaWxFdmVudFsxXS50eXBlID09PSB0eXBlcy5saW5lUHJlZml4IHx8XG4gICAgICAgICAgICAgIHRhaWxFdmVudFsxXS50eXBlID09PSB0eXBlcy5ibG9ja1F1b3RlUHJlZml4IHx8XG4gICAgICAgICAgICAgIHRhaWxFdmVudFsxXS50eXBlID09PSB0eXBlcy5ibG9ja1F1b3RlUHJlZml4V2hpdGVzcGFjZSB8fFxuICAgICAgICAgICAgICB0YWlsRXZlbnRbMV0udHlwZSA9PT0gdHlwZXMuYmxvY2tRdW90ZU1hcmtlciB8fFxuICAgICAgICAgICAgICB0YWlsRXZlbnRbMV0udHlwZSA9PT0gdHlwZXMubGlzdEl0ZW1JbmRlbnRcbiAgICAgICAgICAgICkge1xuICAgICAgICAgICAgICAvLyBFbXB0eVxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgYnJlYWtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICBpZiAoXG4gICAgICAgICAgICBmaXJzdEJsYW5rTGluZUluZGV4ICYmXG4gICAgICAgICAgICAoIWxpbmVJbmRleCB8fCBmaXJzdEJsYW5rTGluZUluZGV4IDwgbGluZUluZGV4KVxuICAgICAgICAgICkge1xuICAgICAgICAgICAgbGlzdEl0ZW0uX3NwcmVhZCA9IHRydWVcbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBGaXggcG9zaXRpb24uXG4gICAgICAgICAgbGlzdEl0ZW0uZW5kID0gT2JqZWN0LmFzc2lnbihcbiAgICAgICAgICAgIHt9LFxuICAgICAgICAgICAgbGluZUluZGV4ID8gZXZlbnRzW2xpbmVJbmRleF1bMV0uc3RhcnQgOiBldmVudFsxXS5lbmRcbiAgICAgICAgICApXG5cbiAgICAgICAgICBldmVudHMuc3BsaWNlKGxpbmVJbmRleCB8fCBpbmRleCwgMCwgWydleGl0JywgbGlzdEl0ZW0sIGV2ZW50WzJdXSlcbiAgICAgICAgICBpbmRleCsrXG4gICAgICAgICAgbGVuZ3RoKytcbiAgICAgICAgfVxuXG4gICAgICAgIC8vIENyZWF0ZSBhIG5ldyBsaXN0IGl0ZW0uXG4gICAgICAgIGlmIChldmVudFsxXS50eXBlID09PSB0eXBlcy5saXN0SXRlbVByZWZpeCkge1xuICAgICAgICAgIGxpc3RJdGVtID0ge1xuICAgICAgICAgICAgdHlwZTogJ2xpc3RJdGVtJyxcbiAgICAgICAgICAgIF9zcHJlYWQ6IGZhbHNlLFxuICAgICAgICAgICAgc3RhcnQ6IE9iamVjdC5hc3NpZ24oe30sIGV2ZW50WzFdLnN0YXJ0KSxcbiAgICAgICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IHdl4oCZbGwgYWRkIGBlbmRgIGluIGEgc2Vjb25kLlxuICAgICAgICAgICAgZW5kOiB1bmRlZmluZWRcbiAgICAgICAgICB9XG4gICAgICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogYGxpc3RJdGVtYCBpcyBtb3N0IGRlZmluaXRlbHkgZGVmaW5lZCwgVFMuLi5cbiAgICAgICAgICBldmVudHMuc3BsaWNlKGluZGV4LCAwLCBbJ2VudGVyJywgbGlzdEl0ZW0sIGV2ZW50WzJdXSlcbiAgICAgICAgICBpbmRleCsrXG4gICAgICAgICAgbGVuZ3RoKytcbiAgICAgICAgICBmaXJzdEJsYW5rTGluZUluZGV4ID0gdW5kZWZpbmVkXG4gICAgICAgICAgYXRNYXJrZXIgPSB0cnVlXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG5cbiAgICBldmVudHNbc3RhcnRdWzFdLl9zcHJlYWQgPSBsaXN0U3ByZWFkXG4gICAgcmV0dXJuIGxlbmd0aFxuICB9XG5cbiAgLyoqXG4gICAqIFNldCBkYXRhLlxuICAgKlxuICAgKiBAdGVtcGxhdGUge2tleW9mIENvbXBpbGVEYXRhfSBLZXlcbiAgICogICBGaWVsZCB0eXBlLlxuICAgKiBAcGFyYW0ge0tleX0ga2V5XG4gICAqICAgS2V5IG9mIGZpZWxkLlxuICAgKiBAcGFyYW0ge0NvbXBpbGVEYXRhW0tleV19IFt2YWx1ZV1cbiAgICogICBOZXcgdmFsdWUuXG4gICAqIEByZXR1cm5zIHt2b2lkfVxuICAgKiAgIE5vdGhpbmcuXG4gICAqL1xuICBmdW5jdGlvbiBzZXREYXRhKGtleSwgdmFsdWUpIHtcbiAgICBkYXRhW2tleV0gPSB2YWx1ZVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBkYXRhLlxuICAgKlxuICAgKiBAdGVtcGxhdGUge2tleW9mIENvbXBpbGVEYXRhfSBLZXlcbiAgICogICBGaWVsZCB0eXBlLlxuICAgKiBAcGFyYW0ge0tleX0ga2V5XG4gICAqICAgS2V5IG9mIGZpZWxkLlxuICAgKiBAcmV0dXJucyB7Q29tcGlsZURhdGFbS2V5XX1cbiAgICogICBWYWx1ZS5cbiAgICovXG4gIGZ1bmN0aW9uIGdldERhdGEoa2V5KSB7XG4gICAgcmV0dXJuIGRhdGFba2V5XVxuICB9XG5cbiAgLyoqXG4gICAqIENyZWF0ZSBhbiBvcGVuZXIgaGFuZGxlLlxuICAgKlxuICAgKiBAcGFyYW0geyh0b2tlbjogVG9rZW4pID0+IE5vZGV9IGNyZWF0ZVxuICAgKiAgIENyZWF0ZSBhIG5vZGUuXG4gICAqIEBwYXJhbSB7SGFuZGxlfSBbYW5kXVxuICAgKiAgIE9wdGlvbmFsIGZ1bmN0aW9uIHRvIGFsc28gcnVuLlxuICAgKiBAcmV0dXJucyB7SGFuZGxlfVxuICAgKiAgIEhhbmRsZS5cbiAgICovXG4gIGZ1bmN0aW9uIG9wZW5lcihjcmVhdGUsIGFuZCkge1xuICAgIHJldHVybiBvcGVuXG5cbiAgICAvKipcbiAgICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAgICogQHBhcmFtIHtUb2tlbn0gdG9rZW5cbiAgICAgKiBAcmV0dXJucyB7dm9pZH1cbiAgICAgKi9cbiAgICBmdW5jdGlvbiBvcGVuKHRva2VuKSB7XG4gICAgICBlbnRlci5jYWxsKHRoaXMsIGNyZWF0ZSh0b2tlbiksIHRva2VuKVxuICAgICAgaWYgKGFuZCkgYW5kLmNhbGwodGhpcywgdG9rZW4pXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHJldHVybnMge3ZvaWR9XG4gICAqL1xuICBmdW5jdGlvbiBidWZmZXIoKSB7XG4gICAgdGhpcy5zdGFjay5wdXNoKHt0eXBlOiAnZnJhZ21lbnQnLCBjaGlsZHJlbjogW119KVxuICB9XG5cbiAgLyoqXG4gICAqIEB0ZW1wbGF0ZSB7Tm9kZX0gS2luZFxuICAgKiAgIE5vZGUgdHlwZS5cbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiAgIENvbnRleHQuXG4gICAqIEBwYXJhbSB7S2luZH0gbm9kZVxuICAgKiAgIE5vZGUgdG8gZW50ZXIuXG4gICAqIEBwYXJhbSB7VG9rZW59IHRva2VuXG4gICAqICAgQ29ycmVzcG9uZGluZyB0b2tlbi5cbiAgICogQHBhcmFtIHtPbkVudGVyRXJyb3IgfCB1bmRlZmluZWR9IFtlcnJvckhhbmRsZXJdXG4gICAqICAgSGFuZGxlIHRoZSBjYXNlIHdoZXJlIHRoaXMgdG9rZW4gaXMgb3BlbiwgYnV0IGl0IGlzIGNsb3NlZCBieSBzb21ldGhpbmcgZWxzZS5cbiAgICogQHJldHVybnMge0tpbmR9XG4gICAqICAgVGhlIGdpdmVuIG5vZGUuXG4gICAqL1xuICBmdW5jdGlvbiBlbnRlcihub2RlLCB0b2tlbiwgZXJyb3JIYW5kbGVyKSB7XG4gICAgY29uc3QgcGFyZW50ID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KHBhcmVudCwgJ2V4cGVjdGVkIGBwYXJlbnRgJylcbiAgICBhc3NlcnQoJ2NoaWxkcmVuJyBpbiBwYXJlbnQsICdleHBlY3RlZCBgcGFyZW50YCcpXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogQXNzdW1lIGBOb2RlYCBjYW4gZXhpc3QgYXMgYSBjaGlsZCBvZiBgcGFyZW50YC5cbiAgICBwYXJlbnQuY2hpbGRyZW4ucHVzaChub2RlKVxuICAgIHRoaXMuc3RhY2sucHVzaChub2RlKVxuICAgIHRoaXMudG9rZW5TdGFjay5wdXNoKFt0b2tlbiwgZXJyb3JIYW5kbGVyXSlcbiAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBgZW5kYCB3aWxsIGJlIHBhdGNoZWQgbGF0ZXIuXG4gICAgbm9kZS5wb3NpdGlvbiA9IHtzdGFydDogcG9pbnQodG9rZW4uc3RhcnQpfVxuICAgIHJldHVybiBub2RlXG4gIH1cblxuICAvKipcbiAgICogQ3JlYXRlIGEgY2xvc2VyIGhhbmRsZS5cbiAgICpcbiAgICogQHBhcmFtIHtIYW5kbGV9IFthbmRdXG4gICAqICAgT3B0aW9uYWwgZnVuY3Rpb24gdG8gYWxzbyBydW4uXG4gICAqIEByZXR1cm5zIHtIYW5kbGV9XG4gICAqICAgSGFuZGxlLlxuICAgKi9cbiAgZnVuY3Rpb24gY2xvc2VyKGFuZCkge1xuICAgIHJldHVybiBjbG9zZVxuXG4gICAgLyoqXG4gICAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgICAqIEBwYXJhbSB7VG9rZW59IHRva2VuXG4gICAgICogQHJldHVybnMge3ZvaWR9XG4gICAgICovXG4gICAgZnVuY3Rpb24gY2xvc2UodG9rZW4pIHtcbiAgICAgIGlmIChhbmQpIGFuZC5jYWxsKHRoaXMsIHRva2VuKVxuICAgICAgZXhpdC5jYWxsKHRoaXMsIHRva2VuKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqICAgQ29udGV4dC5cbiAgICogQHBhcmFtIHtUb2tlbn0gdG9rZW5cbiAgICogICBDb3JyZXNwb25kaW5nIHRva2VuLlxuICAgKiBAcGFyYW0ge09uRXhpdEVycm9yIHwgdW5kZWZpbmVkfSBbb25FeGl0RXJyb3JdXG4gICAqICAgSGFuZGxlIHRoZSBjYXNlIHdoZXJlIGFub3RoZXIgdG9rZW4gaXMgb3Blbi5cbiAgICogQHJldHVybnMge05vZGV9XG4gICAqICAgVGhlIGNsb3NlZCBub2RlLlxuICAgKi9cbiAgZnVuY3Rpb24gZXhpdCh0b2tlbiwgb25FeGl0RXJyb3IpIHtcbiAgICBjb25zdCBub2RlID0gdGhpcy5zdGFjay5wb3AoKVxuICAgIGFzc2VydChub2RlLCAnZXhwZWN0ZWQgYG5vZGVgJylcbiAgICBjb25zdCBvcGVuID0gdGhpcy50b2tlblN0YWNrLnBvcCgpXG5cbiAgICBpZiAoIW9wZW4pIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgJ0Nhbm5vdCBjbG9zZSBgJyArXG4gICAgICAgICAgdG9rZW4udHlwZSArXG4gICAgICAgICAgJ2AgKCcgK1xuICAgICAgICAgIHN0cmluZ2lmeVBvc2l0aW9uKHtzdGFydDogdG9rZW4uc3RhcnQsIGVuZDogdG9rZW4uZW5kfSkgK1xuICAgICAgICAgICcpOiBpdOKAmXMgbm90IG9wZW4nXG4gICAgICApXG4gICAgfSBlbHNlIGlmIChvcGVuWzBdLnR5cGUgIT09IHRva2VuLnR5cGUpIHtcbiAgICAgIGlmIChvbkV4aXRFcnJvcikge1xuICAgICAgICBvbkV4aXRFcnJvci5jYWxsKHRoaXMsIHRva2VuLCBvcGVuWzBdKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc3QgaGFuZGxlciA9IG9wZW5bMV0gfHwgZGVmYXVsdE9uRXJyb3JcbiAgICAgICAgaGFuZGxlci5jYWxsKHRoaXMsIHRva2VuLCBvcGVuWzBdKVxuICAgICAgfVxuICAgIH1cblxuICAgIGFzc2VydChub2RlLnR5cGUgIT09ICdmcmFnbWVudCcsICd1bmV4cGVjdGVkIGZyYWdtZW50IGBleGl0YGVkJylcbiAgICBhc3NlcnQobm9kZS5wb3NpdGlvbiwgJ2V4cGVjdGVkIGBwb3NpdGlvbmAgdG8gYmUgZGVmaW5lZCcpXG4gICAgbm9kZS5wb3NpdGlvbi5lbmQgPSBwb2ludCh0b2tlbi5lbmQpXG4gICAgcmV0dXJuIG5vZGVcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEByZXR1cm5zIHtzdHJpbmd9XG4gICAqL1xuICBmdW5jdGlvbiByZXN1bWUoKSB7XG4gICAgcmV0dXJuIHRvU3RyaW5nKHRoaXMuc3RhY2sucG9wKCkpXG4gIH1cblxuICAvL1xuICAvLyBIYW5kbGVycy5cbiAgLy9cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb25lbnRlcmxpc3RvcmRlcmVkKCkge1xuICAgIHNldERhdGEoJ2V4cGVjdGluZ0ZpcnN0TGlzdEl0ZW1WYWx1ZScsIHRydWUpXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb25lbnRlcmxpc3RpdGVtdmFsdWUodG9rZW4pIHtcbiAgICBpZiAoZ2V0RGF0YSgnZXhwZWN0aW5nRmlyc3RMaXN0SXRlbVZhbHVlJykpIHtcbiAgICAgIGNvbnN0IGFuY2VzdG9yID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDJdXG4gICAgICBhc3NlcnQoYW5jZXN0b3IsICdleHBlY3RlZCBub2RlcyBvbiBzdGFjaycpXG4gICAgICBhc3NlcnQoYW5jZXN0b3IudHlwZSA9PT0gJ2xpc3QnLCAnZXhwZWN0ZWQgbGlzdCBvbiBzdGFjaycpXG4gICAgICBhbmNlc3Rvci5zdGFydCA9IE51bWJlci5wYXJzZUludChcbiAgICAgICAgdGhpcy5zbGljZVNlcmlhbGl6ZSh0b2tlbiksXG4gICAgICAgIGNvbnN0YW50cy5udW1lcmljQmFzZURlY2ltYWxcbiAgICAgIClcbiAgICAgIHNldERhdGEoJ2V4cGVjdGluZ0ZpcnN0TGlzdEl0ZW1WYWx1ZScpXG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9uZXhpdGNvZGVmZW5jZWRmZW5jZWluZm8oKSB7XG4gICAgY29uc3QgZGF0YSA9IHRoaXMucmVzdW1lKClcbiAgICBjb25zdCBub2RlID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KG5vZGUsICdleHBlY3RlZCBub2RlIG9uIHN0YWNrJylcbiAgICBhc3NlcnQobm9kZS50eXBlID09PSAnY29kZScsICdleHBlY3RlZCBjb2RlIG9uIHN0YWNrJylcbiAgICBub2RlLmxhbmcgPSBkYXRhXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb25leGl0Y29kZWZlbmNlZGZlbmNlbWV0YSgpIHtcbiAgICBjb25zdCBkYXRhID0gdGhpcy5yZXN1bWUoKVxuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChub2RlLnR5cGUgPT09ICdjb2RlJywgJ2V4cGVjdGVkIGNvZGUgb24gc3RhY2snKVxuICAgIG5vZGUubWV0YSA9IGRhdGFcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBvbmV4aXRjb2RlZmVuY2VkZmVuY2UoKSB7XG4gICAgLy8gRXhpdCBpZiB0aGlzIGlzIHRoZSBjbG9zaW5nIGZlbmNlLlxuICAgIGlmIChnZXREYXRhKCdmbG93Q29kZUluc2lkZScpKSByZXR1cm5cbiAgICB0aGlzLmJ1ZmZlcigpXG4gICAgc2V0RGF0YSgnZmxvd0NvZGVJbnNpZGUnLCB0cnVlKVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9uZXhpdGNvZGVmZW5jZWQoKSB7XG4gICAgY29uc3QgZGF0YSA9IHRoaXMucmVzdW1lKClcbiAgICBjb25zdCBub2RlID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KG5vZGUsICdleHBlY3RlZCBub2RlIG9uIHN0YWNrJylcbiAgICBhc3NlcnQobm9kZS50eXBlID09PSAnY29kZScsICdleHBlY3RlZCBjb2RlIG9uIHN0YWNrJylcblxuICAgIG5vZGUudmFsdWUgPSBkYXRhLnJlcGxhY2UoL14oXFxyP1xcbnxcXHIpfChcXHI/XFxufFxccikkL2csICcnKVxuICAgIHNldERhdGEoJ2Zsb3dDb2RlSW5zaWRlJylcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBvbmV4aXRjb2RlaW5kZW50ZWQoKSB7XG4gICAgY29uc3QgZGF0YSA9IHRoaXMucmVzdW1lKClcbiAgICBjb25zdCBub2RlID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KG5vZGUsICdleHBlY3RlZCBub2RlIG9uIHN0YWNrJylcbiAgICBhc3NlcnQobm9kZS50eXBlID09PSAnY29kZScsICdleHBlY3RlZCBjb2RlIG9uIHN0YWNrJylcblxuICAgIG5vZGUudmFsdWUgPSBkYXRhLnJlcGxhY2UoLyhcXHI/XFxufFxccikkL2csICcnKVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9uZXhpdGRlZmluaXRpb25sYWJlbHN0cmluZyh0b2tlbikge1xuICAgIGNvbnN0IGxhYmVsID0gdGhpcy5yZXN1bWUoKVxuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChub2RlLnR5cGUgPT09ICdkZWZpbml0aW9uJywgJ2V4cGVjdGVkIGRlZmluaXRpb24gb24gc3RhY2snKVxuXG4gICAgbm9kZS5sYWJlbCA9IGxhYmVsXG4gICAgbm9kZS5pZGVudGlmaWVyID0gbm9ybWFsaXplSWRlbnRpZmllcihcbiAgICAgIHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG4gICAgKS50b0xvd2VyQ2FzZSgpXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb25leGl0ZGVmaW5pdGlvbnRpdGxlc3RyaW5nKCkge1xuICAgIGNvbnN0IGRhdGEgPSB0aGlzLnJlc3VtZSgpXG4gICAgY29uc3Qgbm9kZSA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAxXVxuICAgIGFzc2VydChub2RlLCAnZXhwZWN0ZWQgbm9kZSBvbiBzdGFjaycpXG4gICAgYXNzZXJ0KG5vZGUudHlwZSA9PT0gJ2RlZmluaXRpb24nLCAnZXhwZWN0ZWQgZGVmaW5pdGlvbiBvbiBzdGFjaycpXG5cbiAgICBub2RlLnRpdGxlID0gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9uZXhpdGRlZmluaXRpb25kZXN0aW5hdGlvbnN0cmluZygpIHtcbiAgICBjb25zdCBkYXRhID0gdGhpcy5yZXN1bWUoKVxuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChub2RlLnR5cGUgPT09ICdkZWZpbml0aW9uJywgJ2V4cGVjdGVkIGRlZmluaXRpb24gb24gc3RhY2snKVxuXG4gICAgbm9kZS51cmwgPSBkYXRhXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb25leGl0YXR4aGVhZGluZ3NlcXVlbmNlKHRva2VuKSB7XG4gICAgY29uc3Qgbm9kZSA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAxXVxuICAgIGFzc2VydChub2RlLCAnZXhwZWN0ZWQgbm9kZSBvbiBzdGFjaycpXG4gICAgYXNzZXJ0KG5vZGUudHlwZSA9PT0gJ2hlYWRpbmcnLCAnZXhwZWN0ZWQgaGVhZGluZyBvbiBzdGFjaycpXG5cbiAgICBpZiAoIW5vZGUuZGVwdGgpIHtcbiAgICAgIGNvbnN0IGRlcHRoID0gdGhpcy5zbGljZVNlcmlhbGl6ZSh0b2tlbikubGVuZ3RoXG5cbiAgICAgIGFzc2VydChcbiAgICAgICAgZGVwdGggPT09IDEgfHxcbiAgICAgICAgICBkZXB0aCA9PT0gMiB8fFxuICAgICAgICAgIGRlcHRoID09PSAzIHx8XG4gICAgICAgICAgZGVwdGggPT09IDQgfHxcbiAgICAgICAgICBkZXB0aCA9PT0gNSB8fFxuICAgICAgICAgIGRlcHRoID09PSA2LFxuICAgICAgICAnZXhwZWN0ZWQgYGRlcHRoYCBiZXR3ZWVuIGAxYCBhbmQgYDZgJ1xuICAgICAgKVxuXG4gICAgICBub2RlLmRlcHRoID0gZGVwdGhcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb25leGl0c2V0ZXh0aGVhZGluZ3RleHQoKSB7XG4gICAgc2V0RGF0YSgnc2V0ZXh0SGVhZGluZ1NsdXJwTGluZUVuZGluZycsIHRydWUpXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb25leGl0c2V0ZXh0aGVhZGluZ2xpbmVzZXF1ZW5jZSh0b2tlbikge1xuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChub2RlLnR5cGUgPT09ICdoZWFkaW5nJywgJ2V4cGVjdGVkIGhlYWRpbmcgb24gc3RhY2snKVxuXG4gICAgbm9kZS5kZXB0aCA9XG4gICAgICB0aGlzLnNsaWNlU2VyaWFsaXplKHRva2VuKS5jaGFyQ29kZUF0KDApID09PSBjb2Rlcy5lcXVhbHNUbyA/IDEgOiAyXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cbiAgZnVuY3Rpb24gb25leGl0c2V0ZXh0aGVhZGluZygpIHtcbiAgICBzZXREYXRhKCdzZXRleHRIZWFkaW5nU2x1cnBMaW5lRW5kaW5nJylcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuXG4gIGZ1bmN0aW9uIG9uZW50ZXJkYXRhKHRva2VuKSB7XG4gICAgY29uc3Qgbm9kZSA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAxXVxuICAgIGFzc2VydChub2RlLCAnZXhwZWN0ZWQgbm9kZSBvbiBzdGFjaycpXG4gICAgYXNzZXJ0KCdjaGlsZHJlbicgaW4gbm9kZSwgJ2V4cGVjdGVkIHBhcmVudCBvbiBzdGFjaycpXG5cbiAgICBsZXQgdGFpbCA9IG5vZGUuY2hpbGRyZW5bbm9kZS5jaGlsZHJlbi5sZW5ndGggLSAxXVxuXG4gICAgaWYgKCF0YWlsIHx8IHRhaWwudHlwZSAhPT0gJ3RleHQnKSB7XG4gICAgICAvLyBBZGQgYSBuZXcgdGV4dCBub2RlLlxuICAgICAgdGFpbCA9IHRleHQoKVxuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogd2XigJlsbCBhZGQgYGVuZGAgbGF0ZXIuXG4gICAgICB0YWlsLnBvc2l0aW9uID0ge3N0YXJ0OiBwb2ludCh0b2tlbi5zdGFydCl9XG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBBc3N1bWUgYHBhcmVudGAgYWNjZXB0cyBgdGV4dGAuXG4gICAgICBub2RlLmNoaWxkcmVuLnB1c2godGFpbClcbiAgICB9XG5cbiAgICB0aGlzLnN0YWNrLnB1c2godGFpbClcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuXG4gIGZ1bmN0aW9uIG9uZXhpdGRhdGEodG9rZW4pIHtcbiAgICBjb25zdCB0YWlsID0gdGhpcy5zdGFjay5wb3AoKVxuICAgIGFzc2VydCh0YWlsLCAnZXhwZWN0ZWQgYSBgbm9kZWAgdG8gYmUgb24gdGhlIHN0YWNrJylcbiAgICBhc3NlcnQoJ3ZhbHVlJyBpbiB0YWlsLCAnZXhwZWN0ZWQgYSBgbGl0ZXJhbGAgdG8gYmUgb24gdGhlIHN0YWNrJylcbiAgICBhc3NlcnQodGFpbC5wb3NpdGlvbiwgJ2V4cGVjdGVkIGBub2RlYCB0byBoYXZlIGFuIG9wZW4gcG9zaXRpb24nKVxuICAgIHRhaWwudmFsdWUgKz0gdGhpcy5zbGljZVNlcmlhbGl6ZSh0b2tlbilcbiAgICB0YWlsLnBvc2l0aW9uLmVuZCA9IHBvaW50KHRva2VuLmVuZClcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuXG4gIGZ1bmN0aW9uIG9uZXhpdGxpbmVlbmRpbmcodG9rZW4pIHtcbiAgICBjb25zdCBjb250ZXh0ID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KGNvbnRleHQsICdleHBlY3RlZCBgbm9kZWAnKVxuXG4gICAgLy8gSWYgd2XigJlyZSBhdCBhIGhhcmQgYnJlYWssIGluY2x1ZGUgdGhlIGxpbmUgZW5kaW5nIGluIHRoZXJlLlxuICAgIGlmIChnZXREYXRhKCdhdEhhcmRCcmVhaycpKSB7XG4gICAgICBhc3NlcnQoJ2NoaWxkcmVuJyBpbiBjb250ZXh0LCAnZXhwZWN0ZWQgYHBhcmVudGAnKVxuICAgICAgY29uc3QgdGFpbCA9IGNvbnRleHQuY2hpbGRyZW5bY29udGV4dC5jaGlsZHJlbi5sZW5ndGggLSAxXVxuICAgICAgYXNzZXJ0KHRhaWwucG9zaXRpb24sICdleHBlY3RlZCB0YWlsIHRvIGhhdmUgYSBzdGFydGluZyBwb3NpdGlvbicpXG4gICAgICB0YWlsLnBvc2l0aW9uLmVuZCA9IHBvaW50KHRva2VuLmVuZClcbiAgICAgIHNldERhdGEoJ2F0SGFyZEJyZWFrJylcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIGlmIChcbiAgICAgICFnZXREYXRhKCdzZXRleHRIZWFkaW5nU2x1cnBMaW5lRW5kaW5nJykgJiZcbiAgICAgIGNvbmZpZy5jYW5Db250YWluRW9scy5pbmNsdWRlcyhjb250ZXh0LnR5cGUpXG4gICAgKSB7XG4gICAgICBvbmVudGVyZGF0YS5jYWxsKHRoaXMsIHRva2VuKVxuICAgICAgb25leGl0ZGF0YS5jYWxsKHRoaXMsIHRva2VuKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuXG4gIGZ1bmN0aW9uIG9uZXhpdGhhcmRicmVhaygpIHtcbiAgICBzZXREYXRhKCdhdEhhcmRCcmVhaycsIHRydWUpXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cblxuICBmdW5jdGlvbiBvbmV4aXRodG1sZmxvdygpIHtcbiAgICBjb25zdCBkYXRhID0gdGhpcy5yZXN1bWUoKVxuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChub2RlLnR5cGUgPT09ICdodG1sJywgJ2V4cGVjdGVkIGh0bWwgb24gc3RhY2snKVxuXG4gICAgbm9kZS52YWx1ZSA9IGRhdGFcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuXG4gIGZ1bmN0aW9uIG9uZXhpdGh0bWx0ZXh0KCkge1xuICAgIGNvbnN0IGRhdGEgPSB0aGlzLnJlc3VtZSgpXG4gICAgY29uc3Qgbm9kZSA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAxXVxuICAgIGFzc2VydChub2RlLCAnZXhwZWN0ZWQgbm9kZSBvbiBzdGFjaycpXG4gICAgYXNzZXJ0KG5vZGUudHlwZSA9PT0gJ2h0bWwnLCAnZXhwZWN0ZWQgaHRtbCBvbiBzdGFjaycpXG5cbiAgICBub2RlLnZhbHVlID0gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG5cbiAgZnVuY3Rpb24gb25leGl0Y29kZXRleHQoKSB7XG4gICAgY29uc3QgZGF0YSA9IHRoaXMucmVzdW1lKClcbiAgICBjb25zdCBub2RlID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KG5vZGUsICdleHBlY3RlZCBub2RlIG9uIHN0YWNrJylcbiAgICBhc3NlcnQobm9kZS50eXBlID09PSAnaW5saW5lQ29kZScsICdleHBlY3RlZCBpbmxpbmUgY29kZSBvbiBzdGFjaycpXG5cbiAgICBub2RlLnZhbHVlID0gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG5cbiAgZnVuY3Rpb24gb25leGl0bGluaygpIHtcbiAgICBjb25zdCBub2RlID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KG5vZGUsICdleHBlY3RlZCBub2RlIG9uIHN0YWNrJylcbiAgICBhc3NlcnQobm9kZS50eXBlID09PSAnbGluaycsICdleHBlY3RlZCBsaW5rIG9uIHN0YWNrJylcblxuICAgIC8vIE5vdGU6IHRoZXJlIGFyZSBhbHNvIGBpZGVudGlmaWVyYCBhbmQgYGxhYmVsYCBmaWVsZHMgb24gdGhpcyBsaW5rIG5vZGUhXG4gICAgLy8gVGhlc2UgYXJlIHVzZWQgLyBjbGVhbmVkIGhlcmUuXG5cbiAgICAvLyBUbyBkbzogY2xlYW4uXG4gICAgaWYgKGdldERhdGEoJ2luUmVmZXJlbmNlJykpIHtcbiAgICAgIC8qKiBAdHlwZSB7UmVmZXJlbmNlVHlwZX0gKi9cbiAgICAgIGNvbnN0IHJlZmVyZW5jZVR5cGUgPSBnZXREYXRhKCdyZWZlcmVuY2VUeXBlJykgfHwgJ3Nob3J0Y3V0J1xuXG4gICAgICBub2RlLnR5cGUgKz0gJ1JlZmVyZW5jZSdcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IG11dGF0ZS5cbiAgICAgIG5vZGUucmVmZXJlbmNlVHlwZSA9IHJlZmVyZW5jZVR5cGVcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IG11dGF0ZS5cbiAgICAgIGRlbGV0ZSBub2RlLnVybFxuICAgICAgZGVsZXRlIG5vZGUudGl0bGVcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogbXV0YXRlLlxuICAgICAgZGVsZXRlIG5vZGUuaWRlbnRpZmllclxuICAgICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogbXV0YXRlLlxuICAgICAgZGVsZXRlIG5vZGUubGFiZWxcbiAgICB9XG5cbiAgICBzZXREYXRhKCdyZWZlcmVuY2VUeXBlJylcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuXG4gIGZ1bmN0aW9uIG9uZXhpdGltYWdlKCkge1xuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChub2RlLnR5cGUgPT09ICdpbWFnZScsICdleHBlY3RlZCBpbWFnZSBvbiBzdGFjaycpXG5cbiAgICAvLyBOb3RlOiB0aGVyZSBhcmUgYWxzbyBgaWRlbnRpZmllcmAgYW5kIGBsYWJlbGAgZmllbGRzIG9uIHRoaXMgbGluayBub2RlIVxuICAgIC8vIFRoZXNlIGFyZSB1c2VkIC8gY2xlYW5lZCBoZXJlLlxuXG4gICAgLy8gVG8gZG86IGNsZWFuLlxuICAgIGlmIChnZXREYXRhKCdpblJlZmVyZW5jZScpKSB7XG4gICAgICAvKiogQHR5cGUge1JlZmVyZW5jZVR5cGV9ICovXG4gICAgICBjb25zdCByZWZlcmVuY2VUeXBlID0gZ2V0RGF0YSgncmVmZXJlbmNlVHlwZScpIHx8ICdzaG9ydGN1dCdcblxuICAgICAgbm9kZS50eXBlICs9ICdSZWZlcmVuY2UnXG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBtdXRhdGUuXG4gICAgICBub2RlLnJlZmVyZW5jZVR5cGUgPSByZWZlcmVuY2VUeXBlXG4gICAgICAvLyBAdHMtZXhwZWN0LWVycm9yOiBtdXRhdGUuXG4gICAgICBkZWxldGUgbm9kZS51cmxcbiAgICAgIGRlbGV0ZSBub2RlLnRpdGxlXG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IG11dGF0ZS5cbiAgICAgIGRlbGV0ZSBub2RlLmlkZW50aWZpZXJcbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IG11dGF0ZS5cbiAgICAgIGRlbGV0ZSBub2RlLmxhYmVsXG4gICAgfVxuXG4gICAgc2V0RGF0YSgncmVmZXJlbmNlVHlwZScpXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cblxuICBmdW5jdGlvbiBvbmV4aXRsYWJlbHRleHQodG9rZW4pIHtcbiAgICBjb25zdCBzdHJpbmcgPSB0aGlzLnNsaWNlU2VyaWFsaXplKHRva2VuKVxuICAgIGNvbnN0IGFuY2VzdG9yID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDJdXG4gICAgYXNzZXJ0KGFuY2VzdG9yLCAnZXhwZWN0ZWQgYW5jZXN0b3Igb24gc3RhY2snKVxuICAgIGFzc2VydChcbiAgICAgIGFuY2VzdG9yLnR5cGUgPT09ICdpbWFnZScgfHwgYW5jZXN0b3IudHlwZSA9PT0gJ2xpbmsnLFxuICAgICAgJ2V4cGVjdGVkIGltYWdlIG9yIGxpbmsgb24gc3RhY2snXG4gICAgKVxuXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvcjogc3Rhc2ggdGhpcyBvbiB0aGUgbm9kZSwgYXMgaXQgbWlnaHQgYmVjb21lIGEgcmVmZXJlbmNlXG4gICAgLy8gbGF0ZXIuXG4gICAgYW5jZXN0b3IubGFiZWwgPSBkZWNvZGVTdHJpbmcoc3RyaW5nKVxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IHNhbWUgYXMgYWJvdmUuXG4gICAgYW5jZXN0b3IuaWRlbnRpZmllciA9IG5vcm1hbGl6ZUlkZW50aWZpZXIoc3RyaW5nKS50b0xvd2VyQ2FzZSgpXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cblxuICBmdW5jdGlvbiBvbmV4aXRsYWJlbCgpIHtcbiAgICBjb25zdCBmcmFnbWVudCA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAxXVxuICAgIGFzc2VydChmcmFnbWVudCwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChmcmFnbWVudC50eXBlID09PSAnZnJhZ21lbnQnLCAnZXhwZWN0ZWQgZnJhZ21lbnQgb24gc3RhY2snKVxuICAgIGNvbnN0IHZhbHVlID0gdGhpcy5yZXN1bWUoKVxuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChcbiAgICAgIG5vZGUudHlwZSA9PT0gJ2ltYWdlJyB8fCBub2RlLnR5cGUgPT09ICdsaW5rJyxcbiAgICAgICdleHBlY3RlZCBpbWFnZSBvciBsaW5rIG9uIHN0YWNrJ1xuICAgIClcblxuICAgIC8vIEFzc3VtZSBhIHJlZmVyZW5jZS5cbiAgICBzZXREYXRhKCdpblJlZmVyZW5jZScsIHRydWUpXG5cbiAgICBpZiAobm9kZS50eXBlID09PSAnbGluaycpIHtcbiAgICAgIC8qKiBAdHlwZSB7QXJyYXk8U3RhdGljUGhyYXNpbmdDb250ZW50Pn0gKi9cbiAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IEFzc3VtZSBzdGF0aWMgcGhyYXNpbmcgY29udGVudC5cbiAgICAgIGNvbnN0IGNoaWxkcmVuID0gZnJhZ21lbnQuY2hpbGRyZW5cblxuICAgICAgbm9kZS5jaGlsZHJlbiA9IGNoaWxkcmVuXG4gICAgfSBlbHNlIHtcbiAgICAgIG5vZGUuYWx0ID0gdmFsdWVcbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cblxuICBmdW5jdGlvbiBvbmV4aXRyZXNvdXJjZWRlc3RpbmF0aW9uc3RyaW5nKCkge1xuICAgIGNvbnN0IGRhdGEgPSB0aGlzLnJlc3VtZSgpXG4gICAgY29uc3Qgbm9kZSA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAxXVxuICAgIGFzc2VydChub2RlLCAnZXhwZWN0ZWQgbm9kZSBvbiBzdGFjaycpXG4gICAgYXNzZXJ0KFxuICAgICAgbm9kZS50eXBlID09PSAnaW1hZ2UnIHx8IG5vZGUudHlwZSA9PT0gJ2xpbmsnLFxuICAgICAgJ2V4cGVjdGVkIGltYWdlIG9yIGxpbmsgb24gc3RhY2snXG4gICAgKVxuICAgIG5vZGUudXJsID0gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG5cbiAgZnVuY3Rpb24gb25leGl0cmVzb3VyY2V0aXRsZXN0cmluZygpIHtcbiAgICBjb25zdCBkYXRhID0gdGhpcy5yZXN1bWUoKVxuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChcbiAgICAgIG5vZGUudHlwZSA9PT0gJ2ltYWdlJyB8fCBub2RlLnR5cGUgPT09ICdsaW5rJyxcbiAgICAgICdleHBlY3RlZCBpbWFnZSBvciBsaW5rIG9uIHN0YWNrJ1xuICAgIClcbiAgICBub2RlLnRpdGxlID0gZGF0YVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG5cbiAgZnVuY3Rpb24gb25leGl0cmVzb3VyY2UoKSB7XG4gICAgc2V0RGF0YSgnaW5SZWZlcmVuY2UnKVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG5cbiAgZnVuY3Rpb24gb25lbnRlcnJlZmVyZW5jZSgpIHtcbiAgICBzZXREYXRhKCdyZWZlcmVuY2VUeXBlJywgJ2NvbGxhcHNlZCcpXG4gIH1cblxuICAvKipcbiAgICogQHRoaXMge0NvbXBpbGVDb250ZXh0fVxuICAgKiBAdHlwZSB7SGFuZGxlfVxuICAgKi9cblxuICBmdW5jdGlvbiBvbmV4aXRyZWZlcmVuY2VzdHJpbmcodG9rZW4pIHtcbiAgICBjb25zdCBsYWJlbCA9IHRoaXMucmVzdW1lKClcbiAgICBjb25zdCBub2RlID0gdGhpcy5zdGFja1t0aGlzLnN0YWNrLmxlbmd0aCAtIDFdXG4gICAgYXNzZXJ0KG5vZGUsICdleHBlY3RlZCBub2RlIG9uIHN0YWNrJylcbiAgICBhc3NlcnQoXG4gICAgICBub2RlLnR5cGUgPT09ICdpbWFnZScgfHwgbm9kZS50eXBlID09PSAnbGluaycsXG4gICAgICAnZXhwZWN0ZWQgaW1hZ2UgcmVmZXJlbmNlIG9yIGxpbmsgcmVmZXJlbmNlIG9uIHN0YWNrJ1xuICAgIClcblxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IHN0YXNoIHRoaXMgb24gdGhlIG5vZGUsIGFzIGl0IG1pZ2h0IGJlY29tZSBhIHJlZmVyZW5jZVxuICAgIC8vIGxhdGVyLlxuICAgIG5vZGUubGFiZWwgPSBsYWJlbFxuICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IHNhbWUgYXMgYWJvdmUuXG4gICAgbm9kZS5pZGVudGlmaWVyID0gbm9ybWFsaXplSWRlbnRpZmllcihcbiAgICAgIHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG4gICAgKS50b0xvd2VyQ2FzZSgpXG4gICAgc2V0RGF0YSgncmVmZXJlbmNlVHlwZScsICdmdWxsJylcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuXG4gIGZ1bmN0aW9uIG9uZXhpdGNoYXJhY3RlcnJlZmVyZW5jZW1hcmtlcih0b2tlbikge1xuICAgIGFzc2VydChcbiAgICAgIHRva2VuLnR5cGUgPT09ICdjaGFyYWN0ZXJSZWZlcmVuY2VNYXJrZXJOdW1lcmljJyB8fFxuICAgICAgICB0b2tlbi50eXBlID09PSAnY2hhcmFjdGVyUmVmZXJlbmNlTWFya2VySGV4YWRlY2ltYWwnXG4gICAgKVxuICAgIHNldERhdGEoJ2NoYXJhY3RlclJlZmVyZW5jZVR5cGUnLCB0b2tlbi50eXBlKVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9uZXhpdGNoYXJhY3RlcnJlZmVyZW5jZXZhbHVlKHRva2VuKSB7XG4gICAgY29uc3QgZGF0YSA9IHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG4gICAgY29uc3QgdHlwZSA9IGdldERhdGEoJ2NoYXJhY3RlclJlZmVyZW5jZVR5cGUnKVxuICAgIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICAgIGxldCB2YWx1ZVxuXG4gICAgaWYgKHR5cGUpIHtcbiAgICAgIHZhbHVlID0gZGVjb2RlTnVtZXJpY0NoYXJhY3RlclJlZmVyZW5jZShcbiAgICAgICAgZGF0YSxcbiAgICAgICAgdHlwZSA9PT0gdHlwZXMuY2hhcmFjdGVyUmVmZXJlbmNlTWFya2VyTnVtZXJpY1xuICAgICAgICAgID8gY29uc3RhbnRzLm51bWVyaWNCYXNlRGVjaW1hbFxuICAgICAgICAgIDogY29uc3RhbnRzLm51bWVyaWNCYXNlSGV4YWRlY2ltYWxcbiAgICAgIClcbiAgICAgIHNldERhdGEoJ2NoYXJhY3RlclJlZmVyZW5jZVR5cGUnKVxuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCByZXN1bHQgPSBkZWNvZGVOYW1lZENoYXJhY3RlclJlZmVyZW5jZShkYXRhKVxuICAgICAgYXNzZXJ0KHJlc3VsdCAhPT0gZmFsc2UsICdleHBlY3RlZCByZWZlcmVuY2UgdG8gZGVjb2RlJylcbiAgICAgIHZhbHVlID0gcmVzdWx0XG4gICAgfVxuXG4gICAgY29uc3QgdGFpbCA9IHRoaXMuc3RhY2sucG9wKClcbiAgICBhc3NlcnQodGFpbCwgJ2V4cGVjdGVkIGBub2RlYCcpXG4gICAgYXNzZXJ0KHRhaWwucG9zaXRpb24sICdleHBlY3RlZCBgbm9kZS5wb3NpdGlvbmAnKVxuICAgIGFzc2VydCgndmFsdWUnIGluIHRhaWwsICdleHBlY3RlZCBgbm9kZS52YWx1ZWAnKVxuICAgIHRhaWwudmFsdWUgKz0gdmFsdWVcbiAgICB0YWlsLnBvc2l0aW9uLmVuZCA9IHBvaW50KHRva2VuLmVuZClcbiAgfVxuXG4gIC8qKlxuICAgKiBAdGhpcyB7Q29tcGlsZUNvbnRleHR9XG4gICAqIEB0eXBlIHtIYW5kbGV9XG4gICAqL1xuICBmdW5jdGlvbiBvbmV4aXRhdXRvbGlua3Byb3RvY29sKHRva2VuKSB7XG4gICAgb25leGl0ZGF0YS5jYWxsKHRoaXMsIHRva2VuKVxuICAgIGNvbnN0IG5vZGUgPSB0aGlzLnN0YWNrW3RoaXMuc3RhY2subGVuZ3RoIC0gMV1cbiAgICBhc3NlcnQobm9kZSwgJ2V4cGVjdGVkIG5vZGUgb24gc3RhY2snKVxuICAgIGFzc2VydChub2RlLnR5cGUgPT09ICdsaW5rJywgJ2V4cGVjdGVkIGxpbmsgb24gc3RhY2snKVxuXG4gICAgbm9kZS51cmwgPSB0aGlzLnNsaWNlU2VyaWFsaXplKHRva2VuKVxuICB9XG5cbiAgLyoqXG4gICAqIEB0aGlzIHtDb21waWxlQ29udGV4dH1cbiAgICogQHR5cGUge0hhbmRsZX1cbiAgICovXG4gIGZ1bmN0aW9uIG9uZXhpdGF1dG9saW5rZW1haWwodG9rZW4pIHtcbiAgICBvbmV4aXRkYXRhLmNhbGwodGhpcywgdG9rZW4pXG4gICAgY29uc3Qgbm9kZSA9IHRoaXMuc3RhY2tbdGhpcy5zdGFjay5sZW5ndGggLSAxXVxuICAgIGFzc2VydChub2RlLCAnZXhwZWN0ZWQgbm9kZSBvbiBzdGFjaycpXG4gICAgYXNzZXJ0KG5vZGUudHlwZSA9PT0gJ2xpbmsnLCAnZXhwZWN0ZWQgbGluayBvbiBzdGFjaycpXG5cbiAgICBub2RlLnVybCA9ICdtYWlsdG86JyArIHRoaXMuc2xpY2VTZXJpYWxpemUodG9rZW4pXG4gIH1cblxuICAvL1xuICAvLyBDcmVhdGVycy5cbiAgLy9cblxuICAvKiogQHJldHVybnMge0Jsb2NrcXVvdGV9ICovXG4gIGZ1bmN0aW9uIGJsb2NrUXVvdGUoKSB7XG4gICAgcmV0dXJuIHt0eXBlOiAnYmxvY2txdW90ZScsIGNoaWxkcmVuOiBbXX1cbiAgfVxuXG4gIC8qKiBAcmV0dXJucyB7Q29kZX0gKi9cbiAgZnVuY3Rpb24gY29kZUZsb3coKSB7XG4gICAgcmV0dXJuIHt0eXBlOiAnY29kZScsIGxhbmc6IG51bGwsIG1ldGE6IG51bGwsIHZhbHVlOiAnJ31cbiAgfVxuXG4gIC8qKiBAcmV0dXJucyB7SW5saW5lQ29kZX0gKi9cbiAgZnVuY3Rpb24gY29kZVRleHQoKSB7XG4gICAgcmV0dXJuIHt0eXBlOiAnaW5saW5lQ29kZScsIHZhbHVlOiAnJ31cbiAgfVxuXG4gIC8qKiBAcmV0dXJucyB7RGVmaW5pdGlvbn0gKi9cbiAgZnVuY3Rpb24gZGVmaW5pdGlvbigpIHtcbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogJ2RlZmluaXRpb24nLFxuICAgICAgaWRlbnRpZmllcjogJycsXG4gICAgICBsYWJlbDogbnVsbCxcbiAgICAgIHRpdGxlOiBudWxsLFxuICAgICAgdXJsOiAnJ1xuICAgIH1cbiAgfVxuXG4gIC8qKiBAcmV0dXJucyB7RW1waGFzaXN9ICovXG4gIGZ1bmN0aW9uIGVtcGhhc2lzKCkge1xuICAgIHJldHVybiB7dHlwZTogJ2VtcGhhc2lzJywgY2hpbGRyZW46IFtdfVxuICB9XG5cbiAgLyoqIEByZXR1cm5zIHtIZWFkaW5nfSAqL1xuICBmdW5jdGlvbiBoZWFkaW5nKCkge1xuICAgIC8vIEB0cy1leHBlY3QtZXJyb3IgYGRlcHRoYCB3aWxsIGJlIHNldCBsYXRlci5cbiAgICByZXR1cm4ge3R5cGU6ICdoZWFkaW5nJywgZGVwdGg6IHVuZGVmaW5lZCwgY2hpbGRyZW46IFtdfVxuICB9XG5cbiAgLyoqIEByZXR1cm5zIHtCcmVha30gKi9cbiAgZnVuY3Rpb24gaGFyZEJyZWFrKCkge1xuICAgIHJldHVybiB7dHlwZTogJ2JyZWFrJ31cbiAgfVxuXG4gIC8qKiBAcmV0dXJucyB7SFRNTH0gKi9cbiAgZnVuY3Rpb24gaHRtbCgpIHtcbiAgICByZXR1cm4ge3R5cGU6ICdodG1sJywgdmFsdWU6ICcnfVxuICB9XG5cbiAgLyoqIEByZXR1cm5zIHtJbWFnZX0gKi9cbiAgZnVuY3Rpb24gaW1hZ2UoKSB7XG4gICAgcmV0dXJuIHt0eXBlOiAnaW1hZ2UnLCB0aXRsZTogbnVsbCwgdXJsOiAnJywgYWx0OiBudWxsfVxuICB9XG5cbiAgLyoqIEByZXR1cm5zIHtMaW5rfSAqL1xuICBmdW5jdGlvbiBsaW5rKCkge1xuICAgIHJldHVybiB7dHlwZTogJ2xpbmsnLCB0aXRsZTogbnVsbCwgdXJsOiAnJywgY2hpbGRyZW46IFtdfVxuICB9XG5cbiAgLyoqXG4gICAqIEBwYXJhbSB7VG9rZW59IHRva2VuXG4gICAqIEByZXR1cm5zIHtMaXN0fVxuICAgKi9cbiAgZnVuY3Rpb24gbGlzdCh0b2tlbikge1xuICAgIHJldHVybiB7XG4gICAgICB0eXBlOiAnbGlzdCcsXG4gICAgICBvcmRlcmVkOiB0b2tlbi50eXBlID09PSAnbGlzdE9yZGVyZWQnLFxuICAgICAgc3RhcnQ6IG51bGwsXG4gICAgICBzcHJlYWQ6IHRva2VuLl9zcHJlYWQsXG4gICAgICBjaGlsZHJlbjogW11cbiAgICB9XG4gIH1cblxuICAvKipcbiAgICogQHBhcmFtIHtUb2tlbn0gdG9rZW5cbiAgICogQHJldHVybnMge0xpc3RJdGVtfVxuICAgKi9cbiAgZnVuY3Rpb24gbGlzdEl0ZW0odG9rZW4pIHtcbiAgICByZXR1cm4ge1xuICAgICAgdHlwZTogJ2xpc3RJdGVtJyxcbiAgICAgIHNwcmVhZDogdG9rZW4uX3NwcmVhZCxcbiAgICAgIGNoZWNrZWQ6IG51bGwsXG4gICAgICBjaGlsZHJlbjogW11cbiAgICB9XG4gIH1cblxuICAvKiogQHJldHVybnMge1BhcmFncmFwaH0gKi9cbiAgZnVuY3Rpb24gcGFyYWdyYXBoKCkge1xuICAgIHJldHVybiB7dHlwZTogJ3BhcmFncmFwaCcsIGNoaWxkcmVuOiBbXX1cbiAgfVxuXG4gIC8qKiBAcmV0dXJucyB7U3Ryb25nfSAqL1xuICBmdW5jdGlvbiBzdHJvbmcoKSB7XG4gICAgcmV0dXJuIHt0eXBlOiAnc3Ryb25nJywgY2hpbGRyZW46IFtdfVxuICB9XG5cbiAgLyoqIEByZXR1cm5zIHtUZXh0fSAqL1xuICBmdW5jdGlvbiB0ZXh0KCkge1xuICAgIHJldHVybiB7dHlwZTogJ3RleHQnLCB2YWx1ZTogJyd9XG4gIH1cblxuICAvKiogQHJldHVybnMge1RoZW1hdGljQnJlYWt9ICovXG4gIGZ1bmN0aW9uIHRoZW1hdGljQnJlYWsoKSB7XG4gICAgcmV0dXJuIHt0eXBlOiAndGhlbWF0aWNCcmVhayd9XG4gIH1cbn1cblxuLyoqXG4gKiBDb3B5IGEgcG9pbnQtbGlrZSB2YWx1ZS5cbiAqXG4gKiBAcGFyYW0ge1BvaW50fSBkXG4gKiAgIFBvaW50LWxpa2UgdmFsdWUuXG4gKiBAcmV0dXJucyB7UG9pbnR9XG4gKiAgIHVuaXN0IHBvaW50LlxuICovXG5mdW5jdGlvbiBwb2ludChkKSB7XG4gIHJldHVybiB7bGluZTogZC5saW5lLCBjb2x1bW46IGQuY29sdW1uLCBvZmZzZXQ6IGQub2Zmc2V0fVxufVxuXG4vKipcbiAqIEBwYXJhbSB7Q29uZmlnfSBjb21iaW5lZFxuICogQHBhcmFtIHtBcnJheTxFeHRlbnNpb24gfCBBcnJheTxFeHRlbnNpb24+Pn0gZXh0ZW5zaW9uc1xuICogQHJldHVybnMge3ZvaWR9XG4gKi9cbmZ1bmN0aW9uIGNvbmZpZ3VyZShjb21iaW5lZCwgZXh0ZW5zaW9ucykge1xuICBsZXQgaW5kZXggPSAtMVxuXG4gIHdoaWxlICgrK2luZGV4IDwgZXh0ZW5zaW9ucy5sZW5ndGgpIHtcbiAgICBjb25zdCB2YWx1ZSA9IGV4dGVuc2lvbnNbaW5kZXhdXG5cbiAgICBpZiAoQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgIGNvbmZpZ3VyZShjb21iaW5lZCwgdmFsdWUpXG4gICAgfSBlbHNlIHtcbiAgICAgIGV4dGVuc2lvbihjb21iaW5lZCwgdmFsdWUpXG4gICAgfVxuICB9XG59XG5cbi8qKlxuICogQHBhcmFtIHtDb25maWd9IGNvbWJpbmVkXG4gKiBAcGFyYW0ge0V4dGVuc2lvbn0gZXh0ZW5zaW9uXG4gKiBAcmV0dXJucyB7dm9pZH1cbiAqL1xuZnVuY3Rpb24gZXh0ZW5zaW9uKGNvbWJpbmVkLCBleHRlbnNpb24pIHtcbiAgLyoqIEB0eXBlIHtrZXlvZiBFeHRlbnNpb259ICovXG4gIGxldCBrZXlcblxuICBmb3IgKGtleSBpbiBleHRlbnNpb24pIHtcbiAgICBpZiAob3duLmNhbGwoZXh0ZW5zaW9uLCBrZXkpKSB7XG4gICAgICBpZiAoa2V5ID09PSAnY2FuQ29udGFpbkVvbHMnKSB7XG4gICAgICAgIGNvbnN0IHJpZ2h0ID0gZXh0ZW5zaW9uW2tleV1cbiAgICAgICAgaWYgKHJpZ2h0KSB7XG4gICAgICAgICAgY29tYmluZWRba2V5XS5wdXNoKC4uLnJpZ2h0KVxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKGtleSA9PT0gJ3RyYW5zZm9ybXMnKSB7XG4gICAgICAgIGNvbnN0IHJpZ2h0ID0gZXh0ZW5zaW9uW2tleV1cbiAgICAgICAgaWYgKHJpZ2h0KSB7XG4gICAgICAgICAgY29tYmluZWRba2V5XS5wdXNoKC4uLnJpZ2h0KVxuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKGtleSA9PT0gJ2VudGVyJyB8fCBrZXkgPT09ICdleGl0Jykge1xuICAgICAgICBjb25zdCByaWdodCA9IGV4dGVuc2lvbltrZXldXG4gICAgICAgIGlmIChyaWdodCkge1xuICAgICAgICAgIE9iamVjdC5hc3NpZ24oY29tYmluZWRba2V5XSwgcmlnaHQpXG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH1cbn1cblxuLyoqIEB0eXBlIHtPbkVudGVyRXJyb3J9ICovXG5mdW5jdGlvbiBkZWZhdWx0T25FcnJvcihsZWZ0LCByaWdodCkge1xuICBpZiAobGVmdCkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICdDYW5ub3QgY2xvc2UgYCcgK1xuICAgICAgICBsZWZ0LnR5cGUgK1xuICAgICAgICAnYCAoJyArXG4gICAgICAgIHN0cmluZ2lmeVBvc2l0aW9uKHtzdGFydDogbGVmdC5zdGFydCwgZW5kOiBsZWZ0LmVuZH0pICtcbiAgICAgICAgJyk6IGEgZGlmZmVyZW50IHRva2VuIChgJyArXG4gICAgICAgIHJpZ2h0LnR5cGUgK1xuICAgICAgICAnYCwgJyArXG4gICAgICAgIHN0cmluZ2lmeVBvc2l0aW9uKHtzdGFydDogcmlnaHQuc3RhcnQsIGVuZDogcmlnaHQuZW5kfSkgK1xuICAgICAgICAnKSBpcyBvcGVuJ1xuICAgIClcbiAgfSBlbHNlIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAnQ2Fubm90IGNsb3NlIGRvY3VtZW50LCBhIHRva2VuIChgJyArXG4gICAgICAgIHJpZ2h0LnR5cGUgK1xuICAgICAgICAnYCwgJyArXG4gICAgICAgIHN0cmluZ2lmeVBvc2l0aW9uKHtzdGFydDogcmlnaHQuc3RhcnQsIGVuZDogcmlnaHQuZW5kfSkgK1xuICAgICAgICAnKSBpcyBzdGlsbCBvcGVuJ1xuICAgIClcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-from-markdown/dev/lib/index.js\n");

/***/ })

};
;