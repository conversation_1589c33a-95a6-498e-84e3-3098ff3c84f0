"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdast-util-gfm-footnote";
exports.ids = ["vendor-chunks/mdast-util-gfm-footnote"];
exports.modules = {

/***/ "(ssr)/./node_modules/mdast-util-gfm-footnote/lib/index.js":
/*!***********************************************************!*\
  !*** ./node_modules/mdast-util-gfm-footnote/lib/index.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmFootnoteFromMarkdown: () => (/* binding */ gfmFootnoteFromMarkdown),\n/* harmony export */   gfmFootnoteToMarkdown: () => (/* binding */ gfmFootnoteToMarkdown)\n/* harmony export */ });\n/* harmony import */ var micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-normalize-identifier */ \"(ssr)/./node_modules/micromark-util-normalize-identifier/dev/index.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_association_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/association.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/association.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_container_flow_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/container-flow.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/container-flow.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_indent_lines_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/indent-lines.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/indent-lines.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_safe_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/safe.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/safe.js\");\n/* harmony import */ var mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! mdast-util-to-markdown/lib/util/track.js */ \"(ssr)/./node_modules/mdast-util-to-markdown/lib/util/track.js\");\n/**\n * @typedef {import('mdast').FootnoteReference} FootnoteReference\n * @typedef {import('mdast').FootnoteDefinition} FootnoteDefinition\n * @typedef {import('mdast-util-from-markdown').CompileContext} CompileContext\n * @typedef {import('mdast-util-from-markdown').Extension} FromMarkdownExtension\n * @typedef {import('mdast-util-from-markdown').Handle} FromMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Options} ToMarkdownExtension\n * @typedef {import('mdast-util-to-markdown').Handle} ToMarkdownHandle\n * @typedef {import('mdast-util-to-markdown').Map} Map\n */\n\n\n\n\n\n\n\n\nfootnoteReference.peek = footnoteReferencePeek\n\n// To do: next major: rename `context` -> `state`, `safeOptions` to `info`, use\n// utilities on `state`.\n\n/**\n * Create an extension for `mdast-util-from-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @returns {FromMarkdownExtension}\n *   Extension for `mdast-util-from-markdown`.\n */\nfunction gfmFootnoteFromMarkdown() {\n  return {\n    enter: {\n      gfmFootnoteDefinition: enterFootnoteDefinition,\n      gfmFootnoteDefinitionLabelString: enterFootnoteDefinitionLabelString,\n      gfmFootnoteCall: enterFootnoteCall,\n      gfmFootnoteCallString: enterFootnoteCallString\n    },\n    exit: {\n      gfmFootnoteDefinition: exitFootnoteDefinition,\n      gfmFootnoteDefinitionLabelString: exitFootnoteDefinitionLabelString,\n      gfmFootnoteCall: exitFootnoteCall,\n      gfmFootnoteCallString: exitFootnoteCallString\n    }\n  }\n}\n\n/**\n * Create an extension for `mdast-util-to-markdown` to enable GFM footnotes\n * in markdown.\n *\n * @returns {ToMarkdownExtension}\n *   Extension for `mdast-util-to-markdown`.\n */\nfunction gfmFootnoteToMarkdown() {\n  return {\n    // This is on by default already.\n    unsafe: [{character: '[', inConstruct: ['phrasing', 'label', 'reference']}],\n    handlers: {footnoteDefinition, footnoteReference}\n  }\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteDefinition(token) {\n  this.enter(\n    {type: 'footnoteDefinition', identifier: '', label: '', children: []},\n    token\n  )\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteDefinitionLabelString() {\n  this.buffer()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteDefinitionLabelString(token) {\n  const label = this.resume()\n  const node = /** @type {FootnoteDefinition} */ (\n    this.stack[this.stack.length - 1]\n  )\n  node.label = label\n  node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_0__.normalizeIdentifier)(\n    this.sliceSerialize(token)\n  ).toLowerCase()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteDefinition(token) {\n  this.exit(token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteCall(token) {\n  this.enter({type: 'footnoteReference', identifier: '', label: ''}, token)\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction enterFootnoteCallString() {\n  this.buffer()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteCallString(token) {\n  const label = this.resume()\n  const node = /** @type {FootnoteDefinition} */ (\n    this.stack[this.stack.length - 1]\n  )\n  node.label = label\n  node.identifier = (0,micromark_util_normalize_identifier__WEBPACK_IMPORTED_MODULE_0__.normalizeIdentifier)(\n    this.sliceSerialize(token)\n  ).toLowerCase()\n}\n\n/**\n * @this {CompileContext}\n * @type {FromMarkdownHandle}\n */\nfunction exitFootnoteCall(token) {\n  this.exit(token)\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {FootnoteReference} node\n */\nfunction footnoteReference(node, _, context, safeOptions) {\n  const tracker = (0,mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_1__.track)(safeOptions)\n  let value = tracker.move('[^')\n  const exit = context.enter('footnoteReference')\n  const subexit = context.enter('reference')\n  value += tracker.move(\n    (0,mdast_util_to_markdown_lib_util_safe_js__WEBPACK_IMPORTED_MODULE_2__.safe)(context, (0,mdast_util_to_markdown_lib_util_association_js__WEBPACK_IMPORTED_MODULE_3__.association)(node), {\n      ...tracker.current(),\n      before: value,\n      after: ']'\n    })\n  )\n  subexit()\n  exit()\n  value += tracker.move(']')\n  return value\n}\n\n/** @type {ToMarkdownHandle} */\nfunction footnoteReferencePeek() {\n  return '['\n}\n\n/**\n * @type {ToMarkdownHandle}\n * @param {FootnoteDefinition} node\n */\nfunction footnoteDefinition(node, _, context, safeOptions) {\n  const tracker = (0,mdast_util_to_markdown_lib_util_track_js__WEBPACK_IMPORTED_MODULE_1__.track)(safeOptions)\n  let value = tracker.move('[^')\n  const exit = context.enter('footnoteDefinition')\n  const subexit = context.enter('label')\n  value += tracker.move(\n    (0,mdast_util_to_markdown_lib_util_safe_js__WEBPACK_IMPORTED_MODULE_2__.safe)(context, (0,mdast_util_to_markdown_lib_util_association_js__WEBPACK_IMPORTED_MODULE_3__.association)(node), {\n      ...tracker.current(),\n      before: value,\n      after: ']'\n    })\n  )\n  subexit()\n  value += tracker.move(\n    ']:' + (node.children && node.children.length > 0 ? ' ' : '')\n  )\n  tracker.shift(4)\n  value += tracker.move(\n    (0,mdast_util_to_markdown_lib_util_indent_lines_js__WEBPACK_IMPORTED_MODULE_4__.indentLines)((0,mdast_util_to_markdown_lib_util_container_flow_js__WEBPACK_IMPORTED_MODULE_5__.containerFlow)(node, context, tracker.current()), map)\n  )\n  exit()\n\n  return value\n}\n\n/** @type {Map} */\nfunction map(line, index, blank) {\n  if (index === 0) {\n    return line\n  }\n\n  return (blank ? '' : '    ') + line\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/mdast-util-gfm-footnote/lib/index.js\n");

/***/ })

};
;