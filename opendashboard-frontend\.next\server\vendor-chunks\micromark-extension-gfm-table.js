"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-table";
exports.ids = ["vendor-chunks/micromark-extension-gfm-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js":
/*!************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditMap: () => (/* binding */ EditMap)\n/* harmony export */ });\n/**\n * @typedef {import('micromark-util-types').Event} Event\n */\n\n// Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */\n\n/**\n * Tracks a bunch of edits.\n */\nclass EditMap {\n  /**\n   * Create a new edit map.\n   */\n  constructor() {\n    /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */\n    this.map = []\n  }\n\n  /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {void}\n   */\n  add(index, remove, add) {\n    addImpl(this, index, remove, add)\n  }\n\n  // To do: not used here.\n  // /**\n  //  * Create an edit: but insert `add` before existing additions.\n  //  *\n  //  * @param {number} index\n  //  * @param {number} remove\n  //  * @param {Array<Event>} add\n  //  * @returns {void}\n  //  */\n  // addBefore(index, remove, add) {\n  //   addImpl(this, index, remove, add, true)\n  // }\n\n  /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {void}\n   */\n  consume(events) {\n    this.map.sort((a, b) => a[0] - b[0])\n\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n    if (this.map.length === 0) {\n      return\n    }\n\n    // To do: if links are added in events, like they are in `markdown-rs`,\n    // this is needed.\n    // // Calculate jumps: where items in the current list move to.\n    // /** @type {Array<Jump>} */\n    // const jumps = []\n    // let index = 0\n    // let addAcc = 0\n    // let removeAcc = 0\n    // while (index < this.map.length) {\n    //   const [at, remove, add] = this.map[index]\n    //   removeAcc += remove\n    //   addAcc += add.length\n    //   jumps.push([at, removeAcc, addAcc])\n    //   index += 1\n    // }\n    //\n    // . shiftLinks(events, jumps)\n\n    let index = this.map.length\n    /** @type {Array<Array<Event>>} */\n    const vecs = []\n    while (index > 0) {\n      index -= 1\n      vecs.push(events.slice(this.map[index][0] + this.map[index][1]))\n      // eslint-disable-next-line unicorn/no-array-push-push\n      vecs.push(this.map[index][2])\n\n      // Truncate rest.\n      events.length = this.map[index][0]\n    }\n\n    vecs.push([...events])\n    events.length = 0\n\n    let slice = vecs.pop()\n\n    while (slice) {\n      events.push(...slice)\n      slice = vecs.pop()\n    }\n\n    // Truncate everything.\n    this.map.length = 0\n  }\n}\n\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {void}\n */\nfunction addImpl(editMap, at, remove, add) {\n  let index = 0\n\n  /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n  if (remove === 0 && add.length === 0) {\n    return\n  }\n\n  while (index < editMap.map.length) {\n    if (editMap.map[index][0] === at) {\n      editMap.map[index][1] += remove\n\n      // To do: before not used.\n      // if (before) {\n      //   add.push(...editMap.map[index][2])\n      //   editMap.map[index][2] = add\n      // } else {\n      editMap.map[index][2].push(...add)\n      // }\n\n      return\n    }\n\n    index += 1\n  }\n\n  editMap.map.push([at, remove, add])\n}\n\n// /**\n//  * Shift `previous` and `next` links according to `jumps`.\n//  *\n//  * This fixes links in case there are events removed or added between them.\n//  *\n//  * @param {Array<Event>} events\n//  * @param {Array<Jump>} jumps\n//  */\n// function shiftLinks(events, jumps) {\n//   let jumpIndex = 0\n//   let index = 0\n//   let add = 0\n//   let rm = 0\n\n//   while (index < events.length) {\n//     const rmCurr = rm\n\n//     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n//       add = jumps[jumpIndex][2]\n//       rm = jumps[jumpIndex][1]\n//       jumpIndex += 1\n//     }\n\n//     // Ignore items that will be removed.\n//     if (rm > rmCurr) {\n//       index += rm - rmCurr\n//     } else {\n//       console.log('to do: links?', add, rmCurr)\n//       // ?\n//       // if let Some(link) = &events[index].link {\n//       //     if let Some(next) = link.next {\n//       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n//       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n//       //             add = jumps[jumpIndex].2;\n//       //             rm = jumps[jumpIndex].1;\n//       //             jumpIndex += 1;\n//       //         }\n//       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n//       //         index = next;\n//       //         continue;\n//       //     }\n//       // }\n//       index += 1\n//     }\n//   }\n// }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/html.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableHtml: () => (/* binding */ gfmTableHtml)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').HtmlExtension} HtmlExtension\n */\n\n/**\n * @typedef {import('./infer.js').Align} Align\n */\n\n\n\nconst alignment = {\n  none: '',\n  left: ' align=\"left\"',\n  right: ' align=\"right\"',\n  center: ' align=\"center\"'\n}\n\n// To do: next major: expose functions.\n// To do: next major: use `infer` here, when all events are exposed.\n\n/**\n * Extension for `micromark` that can be passed in `htmlExtensions` to support\n * GFM tables when serializing to HTML.\n *\n * @type {HtmlExtension}\n */\nconst gfmTableHtml = {\n  enter: {\n    table(token) {\n      const tableAlign = token._align\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `_align`')\n      this.lineEndingIfNeeded()\n      this.tag('<table>')\n      this.setData('tableAlign', tableAlign)\n    },\n    tableBody() {\n      this.tag('<tbody>')\n    },\n    tableData() {\n      const tableAlign = this.getData('tableAlign')\n      const tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n      const align = alignment[tableAlign[tableColumn]]\n\n      if (align === undefined) {\n        // Capture results to ignore them.\n        this.buffer()\n      } else {\n        this.lineEndingIfNeeded()\n        this.tag('<td' + align + '>')\n      }\n    },\n    tableHead() {\n      this.lineEndingIfNeeded()\n      this.tag('<thead>')\n    },\n    tableHeader() {\n      const tableAlign = this.getData('tableAlign')\n      const tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n      const align = alignment[tableAlign[tableColumn]]\n      this.lineEndingIfNeeded()\n      this.tag('<th' + align + '>')\n    },\n    tableRow() {\n      this.setData('tableColumn', 0)\n      this.lineEndingIfNeeded()\n      this.tag('<tr>')\n    }\n  },\n  exit: {\n    // Overwrite the default code text data handler to unescape escaped pipes when\n    // they are in tables.\n    codeTextData(token) {\n      let value = this.sliceSerialize(token)\n\n      if (this.getData('tableAlign')) {\n        value = value.replace(/\\\\([\\\\|])/g, replace)\n      }\n\n      this.raw(this.encode(value))\n    },\n    table() {\n      this.setData('tableAlign')\n      // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n      // but we do need to reset it to match a funky newline GH generates for\n      // list items combined with tables.\n      this.setData('slurpAllLineEndings')\n      this.lineEndingIfNeeded()\n      this.tag('</table>')\n    },\n    tableBody() {\n      this.lineEndingIfNeeded()\n      this.tag('</tbody>')\n    },\n    tableData() {\n      const tableAlign = this.getData('tableAlign')\n      const tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n      if (tableColumn in tableAlign) {\n        this.tag('</td>')\n        this.setData('tableColumn', tableColumn + 1)\n      } else {\n        // Stop capturing.\n        this.resume()\n      }\n    },\n    tableHead() {\n      this.lineEndingIfNeeded()\n      this.tag('</thead>')\n    },\n    tableHeader() {\n      const tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n      this.tag('</th>')\n      this.setData('tableColumn', tableColumn + 1)\n    },\n    tableRow() {\n      const tableAlign = this.getData('tableAlign')\n      let tableColumn = this.getData('tableColumn')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n      ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n      while (tableColumn < tableAlign.length) {\n        this.lineEndingIfNeeded()\n        this.tag('<td' + alignment[tableAlign[tableColumn]] + '></td>')\n        tableColumn++\n      }\n\n      this.setData('tableColumn', tableColumn)\n      this.lineEndingIfNeeded()\n      this.tag('</tr>')\n    }\n  }\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/infer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableAlign: () => (/* binding */ gfmTableAlign)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/**\n * @typedef {import('micromark-util-types').Event} Event\n */\n\n/**\n * @typedef {'left' | 'center' | 'right' | 'none'} Align\n */\n\n\n\n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Array<Event>} events\n * @param {number} index\n * @returns {Array<Align>}\n */\nfunction gfmTableAlign(events, index) {\n  (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(events[index][1].type === 'table', 'expected table')\n  let inDelimiterRow = false\n  /** @type {Array<Align>} */\n  const align = []\n\n  while (index < events.length) {\n    const event = events[index]\n\n    if (inDelimiterRow) {\n      if (event[0] === 'enter') {\n        // Start of alignment value: set a new column.\n        // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n        if (event[1].type === 'tableContent') {\n          align.push(\n            events[index + 1][1].type === 'tableDelimiterMarker'\n              ? 'left'\n              : 'none'\n          )\n        }\n      }\n      // Exits:\n      // End of alignment value: change the column.\n      // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n      else if (event[1].type === 'tableContent') {\n        if (events[index - 1][1].type === 'tableDelimiterMarker') {\n          const alignIndex = align.length - 1\n\n          align[alignIndex] = align[alignIndex] === 'left' ? 'center' : 'right'\n        }\n      }\n      // Done!\n      else if (event[1].type === 'tableDelimiterRow') {\n        break\n      }\n    } else if (event[0] === 'enter' && event[1].type === 'tableDelimiterRow') {\n      inDelimiterRow = true\n    }\n\n    index += 1\n  }\n\n  return align\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTable: () => (/* binding */ gfmTable)\n/* harmony export */ });\n/* harmony import */ var uvu_assert__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uvu/assert */ \"(ssr)/./node_modules/uvu/assert/index.mjs\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-symbol/codes.js */ \"(ssr)/./node_modules/micromark-util-symbol/codes.js\");\n/* harmony import */ var micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol/constants.js */ \"(ssr)/./node_modules/micromark-util-symbol/constants.js\");\n/* harmony import */ var micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol/types.js */ \"(ssr)/./node_modules/micromark-util-symbol/types.js\");\n/* harmony import */ var _edit_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./edit-map.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\");\n/* harmony import */ var _infer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infer.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\");\n/**\n * @typedef {import('micromark-util-types').Event} Event\n * @typedef {import('micromark-util-types').Extension} Extension\n * @typedef {import('micromark-util-types').Point} Point\n * @typedef {import('micromark-util-types').Resolver} Resolver\n * @typedef {import('micromark-util-types').State} State\n * @typedef {import('micromark-util-types').Token} Token\n * @typedef {import('micromark-util-types').TokenizeContext} TokenizeContext\n * @typedef {import('micromark-util-types').Tokenizer} Tokenizer\n */\n\n/**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */\n\n\n\n\n\n\n\n\n\n\n// To do: next major: expose functions.\n\n/**\n * Extension for `micromark` that can be passed in `extensions` to enable GFM\n * table syntax.\n *\n * @type {Extension}\n */\nconst gfmTable = {\n  flow: {null: {tokenize: tokenizeTable, resolveAll: resolveTable}}\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTable(effects, ok, nok) {\n  const self = this\n  let size = 0\n  let sizeB = 0\n  /** @type {boolean | undefined} */\n  let seen\n\n  return start\n\n  /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length - 1\n\n    while (index > -1) {\n      const type = self.events[index][1].type\n      if (\n        type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding ||\n        // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n        type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix\n      )\n        index--\n      else break\n    }\n\n    const tail = index > -1 ? self.events[index][1].type : null\n\n    const next =\n      tail === 'tableHead' || tail === 'tableRow' ? bodyRowStart : headRowBefore\n\n    // Don’t allow lazy body rows.\n    if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    return next(code)\n  }\n\n  /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBefore(code) {\n    effects.enter('tableHead')\n    effects.enter('tableRow')\n    return headRowStart(code)\n  }\n\n  /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowStart(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      return headRowBreak(code)\n    }\n\n    // To do: micromark-js should let us parse our own whitespace in extensions,\n    // like `markdown-rs`:\n    //\n    // ```js\n    // // 4+ spaces.\n    // if (markdownSpace(code)) {\n    //   return nok(code)\n    // }\n    // ```\n\n    seen = true\n    // Count the first character, that isn’t a pipe, double.\n    sizeB += 1\n    return headRowBreak(code)\n  }\n\n  /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBreak(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof) {\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n      if (sizeB > 1) {\n        sizeB = 0\n        // To do: check if this works.\n        // Feel free to interrupt:\n        self.interrupt = true\n        effects.exit('tableRow')\n        effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n        effects.consume(code)\n        effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.lineEnding)\n        return headDelimiterStart\n      }\n\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      // To do: check if this is fine.\n      // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n      // State::Retry(space_or_tab(tokenizer))\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, headRowBreak, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace)(code)\n    }\n\n    sizeB += 1\n\n    if (seen) {\n      seen = false\n      // Header cell count.\n      size += 1\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      // Whether a delimiter was seen.\n      seen = true\n      return headRowBreak\n    }\n\n    // Anything else is cell data.\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data)\n    return headRowData(code)\n  }\n\n  /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowData(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data)\n      return headRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.backslash ? headRowEscape : headRowData\n  }\n\n  /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowEscape(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.backslash || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      effects.consume(code)\n      return headRowData\n    }\n\n    return headRowData(code)\n  }\n\n  /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterStart(code) {\n    // Reset `interrupt`.\n    self.interrupt = false\n\n    // Note: in `markdown-rs`, we need to handle piercing here too.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    effects.enter('tableDelimiterRow')\n    // Track if we’ve seen a `:` or `|`.\n    seen = false\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(self.parser.constructs.disable.null, 'expected `disabled.null`')\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(\n        effects,\n        headDelimiterBefore,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n      )(code)\n    }\n\n    return headDelimiterBefore(code)\n  }\n\n  /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterBefore(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {\n      return headDelimiterValueBefore(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      seen = true\n      // If we start with a pipe, we open a cell marker.\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return headDelimiterCellBefore\n    }\n\n    // More whitespace / empty row not allowed at start.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellBefore(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(\n        effects,\n        headDelimiterValueBefore,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterValueBefore(code)\n  }\n\n  /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterValueBefore(code) {\n    // Align: left.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {\n      sizeB += 1\n      seen = true\n\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterLeftAlignmentAfter\n    }\n\n    // Align: none.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      sizeB += 1\n      // To do: seems weird that this *isn’t* left aligned, but that state is used?\n      return headDelimiterLeftAlignmentAfter(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      return headDelimiterCellAfter(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterLeftAlignmentAfter(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.enter('tableDelimiterFiller')\n      return headDelimiterFiller(code)\n    }\n\n    // Anything else is not ok after the left-align colon.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterFiller(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.dash) {\n      effects.consume(code)\n      return headDelimiterFiller\n    }\n\n    // Align is `center` if it was `left`, `right` otherwise.\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.colon) {\n      seen = true\n      effects.exit('tableDelimiterFiller')\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterRightAlignmentAfter\n    }\n\n    effects.exit('tableDelimiterFiller')\n    return headDelimiterRightAlignmentAfter(code)\n  }\n\n  /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterRightAlignmentAfter(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(\n        effects,\n        headDelimiterCellAfter,\n        micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterCellAfter(code)\n  }\n\n  /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellAfter(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      return headDelimiterBefore(code)\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      // Exit when:\n      // * there was no `:` or `|` at all (it’s a thematic break or setext\n      //   underline instead)\n      // * the header cell count is not the delimiter cell count\n      if (!seen || size !== sizeB) {\n        return headDelimiterNok(code)\n      }\n\n      // Note: in markdown-rs`, a reset is needed here.\n      effects.exit('tableDelimiterRow')\n      effects.exit('tableHead')\n      // To do: in `markdown-rs`, resolvers need to be registered manually.\n      // effects.register_resolver(ResolveName::GfmTable)\n      return ok(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterNok(code) {\n    // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n    return nok(code)\n  }\n\n  /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowStart(code) {\n    // Note: in `markdown-rs` we need to manually take care of a prefix,\n    // but in `micromark-js` that is done for us, so if we’re here, we’re\n    // never at whitespace.\n    effects.enter('tableRow')\n    return bodyRowBreak(code)\n  }\n\n  /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowBreak(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return bodyRowBreak\n    }\n\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEnding)(code)) {\n      effects.exit('tableRow')\n      return ok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_4__.factorySpace)(effects, bodyRowBreak, micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.whitespace)(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data)\n    return bodyRowData(code)\n  }\n\n  /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowData(code) {\n    if (\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.eof ||\n      code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_3__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data)\n      return bodyRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.backslash ? bodyRowEscape : bodyRowData\n  }\n\n  /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowEscape(code) {\n    if (code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.backslash || code === micromark_util_symbol_codes_js__WEBPACK_IMPORTED_MODULE_2__.codes.verticalBar) {\n      effects.consume(code)\n      return bodyRowData\n    }\n\n    return bodyRowData(code)\n  }\n}\n\n/** @type {Resolver} */\n// eslint-disable-next-line complexity\nfunction resolveTable(events, context) {\n  let index = -1\n  let inFirstCellAwaitingPipe = true\n  /** @type {RowKind} */\n  let rowKind = 0\n  /** @type {Range} */\n  let lastCell = [0, 0, 0, 0]\n  /** @type {Range} */\n  let cell = [0, 0, 0, 0]\n  let afterHeadAwaitingFirstBodyRow = false\n  let lastTableEnd = 0\n  /** @type {Token | undefined} */\n  let currentTable\n  /** @type {Token | undefined} */\n  let currentBody\n  /** @type {Token | undefined} */\n  let currentCell\n\n  const map = new _edit_map_js__WEBPACK_IMPORTED_MODULE_6__.EditMap()\n\n  while (++index < events.length) {\n    const event = events[index]\n    const token = event[1]\n\n    if (event[0] === 'enter') {\n      // Start of head.\n      if (token.type === 'tableHead') {\n        afterHeadAwaitingFirstBodyRow = false\n\n        // Inject previous (body end and) table end.\n        if (lastTableEnd !== 0) {\n          (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(currentTable, 'there should be a table opening')\n          flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n          currentBody = undefined\n          lastTableEnd = 0\n        }\n\n        // Inject table start.\n        currentTable = {\n          type: 'table',\n          start: Object.assign({}, token.start),\n          // Note: correct end is set later.\n          end: Object.assign({}, token.end)\n        }\n        map.add(index, 0, [['enter', currentTable, context]])\n      } else if (\n        token.type === 'tableRow' ||\n        token.type === 'tableDelimiterRow'\n      ) {\n        inFirstCellAwaitingPipe = true\n        currentCell = undefined\n        lastCell = [0, 0, 0, 0]\n        cell = [0, index + 1, 0, 0]\n\n        // Inject table body start.\n        if (afterHeadAwaitingFirstBodyRow) {\n          afterHeadAwaitingFirstBodyRow = false\n          currentBody = {\n            type: 'tableBody',\n            start: Object.assign({}, token.start),\n            // Note: correct end is set later.\n            end: Object.assign({}, token.end)\n          }\n          map.add(index, 0, [['enter', currentBody, context]])\n        }\n\n        rowKind = token.type === 'tableDelimiterRow' ? 2 : currentBody ? 3 : 1\n      }\n      // Cell data.\n      else if (\n        rowKind &&\n        (token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data ||\n          token.type === 'tableDelimiterMarker' ||\n          token.type === 'tableDelimiterFiller')\n      ) {\n        inFirstCellAwaitingPipe = false\n\n        // First value in cell.\n        if (cell[2] === 0) {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n            lastCell = [0, 0, 0, 0]\n          }\n\n          cell[2] = index\n        }\n      } else if (token.type === 'tableCellDivider') {\n        if (inFirstCellAwaitingPipe) {\n          inFirstCellAwaitingPipe = false\n        } else {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n          }\n\n          lastCell = cell\n          cell = [lastCell[1], index, 0, 0]\n        }\n      }\n    }\n    // Exit events.\n    else if (token.type === 'tableHead') {\n      afterHeadAwaitingFirstBodyRow = true\n      lastTableEnd = index\n    } else if (\n      token.type === 'tableRow' ||\n      token.type === 'tableDelimiterRow'\n    ) {\n      lastTableEnd = index\n\n      if (lastCell[1] !== 0) {\n        cell[0] = cell[1]\n        currentCell = flushCell(\n          map,\n          context,\n          lastCell,\n          rowKind,\n          index,\n          currentCell\n        )\n      } else if (cell[1] !== 0) {\n        currentCell = flushCell(map, context, cell, rowKind, index, currentCell)\n      }\n\n      rowKind = 0\n    } else if (\n      rowKind &&\n      (token.type === micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.data ||\n        token.type === 'tableDelimiterMarker' ||\n        token.type === 'tableDelimiterFiller')\n    ) {\n      cell[3] = index\n    }\n  }\n\n  if (lastTableEnd !== 0) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(currentTable, 'expected table opening')\n    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n  }\n\n  map.consume(context.events)\n\n  // To do: move this into `html`, when events are exposed there.\n  // That’s what `markdown-rs` does.\n  // That needs updates to `mdast-util-gfm-table`.\n  index = -1\n  while (++index < context.events.length) {\n    const event = context.events[index]\n    if (event[0] === 'enter' && event[1].type === 'table') {\n      event[1]._align = (0,_infer_js__WEBPACK_IMPORTED_MODULE_7__.gfmTableAlign)(context.events, index)\n    }\n  }\n\n  return events\n}\n\n/// Generate a cell.\n/**\n *\n * @param {EditMap} map\n * @param {TokenizeContext} context\n * @param {Range} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */\n// eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n  const groupName =\n    rowKind === 1\n      ? 'tableHeader'\n      : rowKind === 2\n      ? 'tableDelimiter'\n      : 'tableData'\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n  const valueName = 'tableContent'\n\n  // Insert an exit for the previous cell, if there is one.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //          ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[0] !== 0) {\n    (0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(previousCell, 'expected previous cell enter')\n    previousCell.end = Object.assign({}, getPoint(context.events, range[0]))\n    map.add(range[0], 0, [['exit', previousCell, context]])\n  }\n\n  // Insert enter of this cell.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //           ^-- enter\n  //           ^^^^-- this cell\n  // ```\n  const now = getPoint(context.events, range[1])\n  previousCell = {\n    type: groupName,\n    start: Object.assign({}, now),\n    // Note: correct end is set later.\n    end: Object.assign({}, now)\n  }\n  map.add(range[1], 0, [['enter', previousCell, context]])\n\n  // Insert text start at first data start and end at last data end, and\n  // remove events between.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //            ^-- enter\n  //             ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[2] !== 0) {\n    const relatedStart = getPoint(context.events, range[2])\n    const relatedEnd = getPoint(context.events, range[3])\n    /** @type {Token} */\n    const valueToken = {\n      type: valueName,\n      start: Object.assign({}, relatedStart),\n      end: Object.assign({}, relatedEnd)\n    }\n    map.add(range[2], 0, [['enter', valueToken, context]])\n    ;(0,uvu_assert__WEBPACK_IMPORTED_MODULE_0__.ok)(range[3] !== 0)\n\n    if (rowKind !== 2) {\n      // Fix positional info on remaining events\n      const start = context.events[range[2]]\n      const end = context.events[range[3]]\n      start[1].end = Object.assign({}, end[1].end)\n      start[1].type = micromark_util_symbol_types_js__WEBPACK_IMPORTED_MODULE_1__.types.chunkText\n      start[1].contentType = micromark_util_symbol_constants_js__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText\n\n      // Remove if needed.\n      if (range[3] > range[2] + 1) {\n        const a = range[2] + 1\n        const b = range[3] - range[2] - 1\n        map.add(a, b, [])\n      }\n    }\n\n    map.add(range[3] + 1, 0, [['exit', valueToken, context]])\n  }\n\n  // Insert an exit for the last cell, if at the row end.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //                    ^-- exit\n  //               ^^^^^^-- this cell (the last one contains two “between” parts)\n  // ```\n  if (rowEnd !== undefined) {\n    previousCell.end = Object.assign({}, getPoint(context.events, rowEnd))\n    map.add(rowEnd, 0, [['exit', previousCell, context]])\n    previousCell = undefined\n  }\n\n  return previousCell\n}\n\n/**\n * Generate table end (and table body end).\n *\n * @param {EditMap} map\n * @param {TokenizeContext} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */\n// eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n  /** @type {Array<Event>} */\n  const exits = []\n  const related = getPoint(context.events, index)\n\n  if (tableBody) {\n    tableBody.end = Object.assign({}, related)\n    exits.push(['exit', tableBody, context])\n  }\n\n  table.end = Object.assign({}, related)\n  exits.push(['exit', table, context])\n\n  map.add(index + 1, 0, exits)\n}\n\n/**\n * @param {Array<Event>} events\n * @param {number} index\n * @returns {readonly Point}\n */\nfunction getPoint(events, index) {\n  const event = events[index]\n  const side = event[0] === 'enter' ? 'start' : 'end'\n  return event[1][side]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\n");

/***/ })

};
;