"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/opendb-app-db-utils";
exports.ids = ["vendor-chunks/opendb-app-db-utils"];
exports.modules = {

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/index.js":
/*!*******************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/index.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    var desc = Object.getOwnPropertyDescriptor(m, k);\r\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\r\n      desc = { enumerable: true, get: function() { return m[k]; } };\r\n    }\r\n    Object.defineProperty(o, k2, desc);\r\n}) : (function(o, m, k, k2) {\r\n    if (k2 === undefined) k2 = k;\r\n    o[k2] = m[k];\r\n}));\r\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\r\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.Greeter = void 0;\r\nconst Greeter = (name) => `Hello ${name}`;\r\nexports.Greeter = Greeter;\r\n__exportStar(__webpack_require__(/*! ./methods/array */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/array.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./methods/string */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/string.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./methods/number */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/number.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./methods/object */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/object.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./methods/tag */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/tag.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./methods/date */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/date.js\"), exports);\r\n__exportStar(__webpack_require__(/*! ./methods/suggest */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/suggest.js\"), exports);\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/methods/array.js":
/*!***************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/methods/array.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.shuffleArray = exports.arrayContainsNoneOf = exports.arrayContainsAnyOf = exports.arraysHaveSameElements = exports.arrayContains = exports.arrayRemoveElementAtIndex = exports.arrayAddElementAdjacent = exports.arrayDiff = exports.removeAllArrayItem = exports.removeArrayItem = exports.arrayDeDuplicate = exports.arrayToChunks = void 0;\r\nconst arrayToChunks = (array, chunkSize) => {\r\n    const R = [];\r\n    for (let i = 0, len = array.length; i < len; i += chunkSize)\r\n        R.push(array.slice(i, i + chunkSize));\r\n    return R;\r\n};\r\nexports.arrayToChunks = arrayToChunks;\r\nconst arrayDeDuplicate = (array) => {\r\n    return array.filter((value, index, self) => {\r\n        return self.indexOf(value) === index;\r\n    });\r\n};\r\nexports.arrayDeDuplicate = arrayDeDuplicate;\r\nconst removeArrayItem = (arr, value) => {\r\n    const index = arr.indexOf(value);\r\n    if (index > -1) {\r\n        arr.splice(index, 1);\r\n    }\r\n    return arr;\r\n};\r\nexports.removeArrayItem = removeArrayItem;\r\nconst removeAllArrayItem = (arr, value) => {\r\n    let i = 0;\r\n    while (i < arr.length) {\r\n        if (arr[i] === value) {\r\n            arr.splice(i, 1);\r\n        }\r\n        else {\r\n            ++i;\r\n        }\r\n    }\r\n    return arr;\r\n};\r\nexports.removeAllArrayItem = removeAllArrayItem;\r\n/**\r\n * Returns an array containing the items in arrayA that are not present in arrayB.\r\n *\r\n * @template T - The type of elements in the arrays.\r\n * @param {T[]} arrayA - The source array from which to filter elements.\r\n * @param {T[]} arrayB - The array to compare against.\r\n * @returns {T[]} - An array containing the elements in arrayA that are not present in arrayB.\r\n */\r\nconst arrayDiff = (arrayA, arrayB) => {\r\n    return arrayA.filter((item) => !arrayB.includes(item));\r\n};\r\nexports.arrayDiff = arrayDiff;\r\nconst arrayAddElementAdjacent = (array, targetElement, newElement, position) => {\r\n    const index = array.indexOf(targetElement);\r\n    if (index === -1) {\r\n        // Target element not found, return the original array\r\n        return array;\r\n    }\r\n    const newArray = [...array];\r\n    if (position === 'before') {\r\n        newArray.splice(index, 0, newElement);\r\n    }\r\n    else if (position === 'after') {\r\n        newArray.splice(index + 1, 0, newElement);\r\n    }\r\n    return newArray;\r\n};\r\nexports.arrayAddElementAdjacent = arrayAddElementAdjacent;\r\nfunction arrayRemoveElementAtIndex(array, index) {\r\n    if (index < 0 || index >= array.length) {\r\n        return array;\r\n    }\r\n    const newArray = [...array];\r\n    newArray.splice(index, 1);\r\n    return newArray;\r\n}\r\nexports.arrayRemoveElementAtIndex = arrayRemoveElementAtIndex;\r\n/**\r\n * Check if an array contains another array.\r\n * @param {T[]} sourceArray - The source array.\r\n * @param {T[]} targetArray - The array to check for containment.\r\n * @returns {boolean} - True if the source array contains the target array, false otherwise.\r\n */\r\nfunction arrayContains(sourceArray, targetArray) {\r\n    return targetArray.every((item) => sourceArray.includes(item));\r\n}\r\nexports.arrayContains = arrayContains;\r\n/**\r\n * Check if two arrays have the same elements (order-independent).\r\n * @param {T[]} array1 - The first array to compare.\r\n * @param {T[]} array2 - The second array to compare.\r\n * @returns {boolean} - True if the arrays have the same elements, regardless of order, false otherwise.\r\n */\r\nfunction arraysHaveSameElements(array1, array2) {\r\n    if (array1.length !== array2.length) {\r\n        return false;\r\n    }\r\n    const sortedArray1 = [...array1].sort();\r\n    const sortedArray2 = [...array2].sort();\r\n    return sortedArray1.every((item, index) => item === sortedArray2[index]);\r\n}\r\nexports.arraysHaveSameElements = arraysHaveSameElements;\r\n/**\r\n * Checks if any element in the array is present in the specified values.\r\n * @param array The array to check.\r\n * @param values The values to check for.\r\n * @returns True if any element in the array is present in the values, otherwise false.\r\n */\r\nfunction arrayContainsAnyOf(array, values) {\r\n    return array.some((element) => values.includes(element));\r\n}\r\nexports.arrayContainsAnyOf = arrayContainsAnyOf;\r\n/**\r\n * Checks if none of the elements in the array are present in the specified values.\r\n * @param array The array to check.\r\n * @param values The values to check for.\r\n * @returns True if none of the elements in the array are present in the values, otherwise false.\r\n */\r\nfunction arrayContainsNoneOf(array, values) {\r\n    return !array.some((element) => values.includes(element));\r\n}\r\nexports.arrayContainsNoneOf = arrayContainsNoneOf;\r\nfunction shuffleArray(array) {\r\n    // Create a copy of the original array\r\n    const shuffledArray = [...array];\r\n    // Iterate over the elements of the array\r\n    for (let i = shuffledArray.length - 1; i > 0; i--) {\r\n        // Generate a random index between 0 and i (inclusive)\r\n        const randomIndex = Math.floor(Math.random() * (i + 1));\r\n        // Swap the current element with the element at the random index\r\n        [shuffledArray[i], shuffledArray[randomIndex]] = [shuffledArray[randomIndex], shuffledArray[i]];\r\n    }\r\n    return shuffledArray;\r\n}\r\nexports.shuffleArray = shuffleArray;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/methods/array.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/methods/compare.js":
/*!*****************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/methods/compare.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.compareFields = exports.SingleOperandOperators = exports.CompareOperator = void 0;\r\nconst string_1 = __webpack_require__(/*! ./string */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/string.js\");\r\nconst date_1 = __webpack_require__(/*! ./date */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/date.js\");\r\nconst db_1 = __webpack_require__(/*! ../utils/db */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/utils/db.js\");\r\nvar CompareOperator;\r\n(function (CompareOperator) {\r\n    CompareOperator[\"Equals\"] = \"equals\";\r\n    CompareOperator[\"NotEquals\"] = \"not_equals\";\r\n    CompareOperator[\"IsEmpty\"] = \"is_empty\";\r\n    CompareOperator[\"IsNotEmpty\"] = \"is_not_empty\";\r\n    CompareOperator[\"IsChecked\"] = \"is_checked\";\r\n    CompareOperator[\"IsNotChecked\"] = \"is_not_checked\";\r\n    CompareOperator[\"Contains\"] = \"contains\";\r\n    CompareOperator[\"DoesNotContain\"] = \"does_not_contain\";\r\n    CompareOperator[\"StartsWith\"] = \"starts_with\";\r\n    CompareOperator[\"EndsWith\"] = \"ends_with\";\r\n    CompareOperator[\"GreaterThan\"] = \"greater_than\";\r\n    CompareOperator[\"LessThan\"] = \"less_than\";\r\n    CompareOperator[\"IsSameDayAs\"] = \"is_same_day_as\";\r\n    CompareOperator[\"IsEarlierThan\"] = \"is_earlier_than\";\r\n    CompareOperator[\"IsLaterThan\"] = \"is_later_than\";\r\n    CompareOperator[\"IsDateAnniversary\"] = \"is_date_anniversary\";\r\n    CompareOperator[\"IsAnyOf\"] = \"is_any_of\";\r\n    CompareOperator[\"IsNoneOf\"] = \"is_none_of\";\r\n})(CompareOperator = exports.CompareOperator || (exports.CompareOperator = {}));\r\nexports.SingleOperandOperators = [\r\n    CompareOperator.IsEmpty, CompareOperator.IsNotEmpty, CompareOperator.IsNotChecked, CompareOperator.IsChecked\r\n];\r\nconst compareFields = (field1, op, field2) => {\r\n    var _a, _b;\r\n    field1 = field1 !== null && field1 !== void 0 ? field1 : '';\r\n    if (typeof field1 === 'object') {\r\n        field1 = String((_a = (0, db_1.recordValueToText)(field1)) !== null && _a !== void 0 ? _a : '');\r\n    }\r\n    field2 = field2 !== null && field2 !== void 0 ? field2 : '';\r\n    if (typeof field2 === 'object') {\r\n        field2 = String((_b = (0, db_1.recordValueToText)(field2)) !== null && _b !== void 0 ? _b : '');\r\n    }\r\n    field1 = field1.toString().toLowerCase().trim();\r\n    field2 = field2.toString().toLowerCase().trim();\r\n    const date1 = new Date(field1);\r\n    const date2 = new Date(field2);\r\n    switch (op) {\r\n        case CompareOperator.StartsWith:\r\n            return (0, string_1.startsWith)(field1, field2);\r\n        case CompareOperator.EndsWith:\r\n            return (0, string_1.endsWith)(field1, field2);\r\n        case CompareOperator.Contains:\r\n            return (0, string_1.contains)(field1, field2);\r\n        case CompareOperator.DoesNotContain:\r\n            return !(0, string_1.contains)(field1, field2);\r\n        case CompareOperator.Equals:\r\n            return field1 === field2;\r\n        case CompareOperator.NotEquals:\r\n            return field1 !== field2;\r\n        case CompareOperator.IsEmpty:\r\n            return (0, string_1.isEmpty)(field1);\r\n        case CompareOperator.IsNotEmpty:\r\n            return !(0, string_1.isEmpty)(field1);\r\n        case CompareOperator.IsChecked:\r\n            return !!field1;\r\n        case CompareOperator.IsNotChecked:\r\n            return !field1;\r\n        case CompareOperator.GreaterThan:\r\n            return Number(field1) > Number(field2);\r\n        case CompareOperator.LessThan:\r\n            return Number(field1) < Number(field2);\r\n        case CompareOperator.IsSameDayAs:\r\n            return (0, date_1.isSameDay)(date1, date2);\r\n        case CompareOperator.IsDateAnniversary:\r\n            return (0, date_1.isDateAnniversary)(date1, date2);\r\n        case CompareOperator.IsEarlierThan:\r\n            return date1.getTime() < date2.getTime();\r\n        case CompareOperator.IsLaterThan:\r\n            return date1.getTime() > date2.getTime();\r\n    }\r\n    return false;\r\n};\r\nexports.compareFields = compareFields;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/methods/compare.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/methods/date.js":
/*!**************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/methods/date.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.dateFromStr = exports.formatCurrency = exports.timeDifferenceInUnits = exports.isLeapYear = exports.formatDate = exports.isDateAnniversary = exports.resolveRelativeDate = exports.RelativeDate = exports.secondsToHumanReadable = exports.initDateWithTimezone = exports.initDateWithTz = exports.getNextDayOfTheWeek = exports.daysOfWeek = exports.daysDiffBetweenDates = exports.MySQLToJsDate = exports.isSameDay = exports.dateMinusDays = exports.tsToDate = exports.datePlusYears = exports.datePlusMonths = exports.datePlusDays = exports.datePlusHours = exports.datePlusMinutes = exports.datePlusSeconds = exports.dateToMySQL = exports.getCurrentDate = exports.getMySQLCurrentDate = exports.getDaysInMonth = exports.isDateObjValid = exports.isDateValid = void 0;\r\n// import * as dayjs from 'dayjs'\r\nconst dayjs_1 = __importDefault(__webpack_require__(/*! dayjs */ \"(ssr)/./node_modules/dayjs/dayjs.min.js\")); // ES 2015\r\n// dayjs().format()\r\nconst utc_1 = __importDefault(__webpack_require__(/*! dayjs/plugin/utc */ \"(ssr)/./node_modules/dayjs/plugin/utc.js\")); // ES 2015\r\nconst timezone_1 = __importDefault(__webpack_require__(/*! dayjs/plugin/timezone */ \"(ssr)/./node_modules/dayjs/plugin/timezone.js\")); // ES 2015// dependent on utc plugin\r\ndayjs_1.default.extend(utc_1.default);\r\ndayjs_1.default.extend(timezone_1.default);\r\nconst isDateValid = (dateStr) => {\r\n    const date = new Date(dateStr);\r\n    return date instanceof Date && !Number.isNaN(date.getTime());\r\n};\r\nexports.isDateValid = isDateValid;\r\nconst isDateObjValid = (date) => date instanceof Date && date.getTime() === date.getTime();\r\nexports.isDateObjValid = isDateObjValid;\r\nconst getDaysInMonth = (year, month) => {\r\n    return [31, (0, exports.isLeapYear)(year) ? 29 : 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][month];\r\n};\r\nexports.getDaysInMonth = getDaysInMonth;\r\nconst getMySQLCurrentDate = () => {\r\n    return (0, exports.dateToMySQL)((0, exports.getCurrentDate)());\r\n};\r\nexports.getMySQLCurrentDate = getMySQLCurrentDate;\r\nconst getCurrentDate = () => {\r\n    return new Date();\r\n};\r\nexports.getCurrentDate = getCurrentDate;\r\nconst dateToMySQL = (date) => {\r\n    return date.toISOString().slice(0, 19).replace('T', ' ');\r\n};\r\nexports.dateToMySQL = dateToMySQL;\r\nconst datePlusSeconds = (oldDate, seconds) => {\r\n    const date = new Date(oldDate.getTime());\r\n    date.setTime(date.getTime() + seconds * 1000);\r\n    return date;\r\n};\r\nexports.datePlusSeconds = datePlusSeconds;\r\nconst datePlusMinutes = (oldDate, minutes) => {\r\n    const date = new Date(oldDate.getTime());\r\n    date.setTime(date.getTime() + minutes * 60 * 1000);\r\n    return date;\r\n};\r\nexports.datePlusMinutes = datePlusMinutes;\r\nconst datePlusHours = (oldDate, hours) => {\r\n    const date = new Date(oldDate.getTime());\r\n    date.setTime(date.getTime() + hours * 60 * 60 * 1000);\r\n    return date;\r\n};\r\nexports.datePlusHours = datePlusHours;\r\nconst datePlusDays = (oldDate, days) => {\r\n    const date = new Date(oldDate.getTime());\r\n    date.setDate(date.getDate() + days);\r\n    return date;\r\n};\r\nexports.datePlusDays = datePlusDays;\r\nconst datePlusMonths = (oldDate, months) => {\r\n    const date = new Date(oldDate.getTime());\r\n    date.setMonth(date.getMonth() + months);\r\n    return date;\r\n};\r\nexports.datePlusMonths = datePlusMonths;\r\nconst datePlusYears = (oldDate, years) => {\r\n    const date = new Date(oldDate.getTime());\r\n    date.setFullYear(date.getFullYear() + years);\r\n    return date;\r\n};\r\nexports.datePlusYears = datePlusYears;\r\nconst tsToDate = (timestampInSeconds) => {\r\n    return new Date(timestampInSeconds * 1000); // convert to milliseconds\r\n};\r\nexports.tsToDate = tsToDate;\r\nconst dateMinusDays = (oldDate, days) => {\r\n    const date = new Date(oldDate.getTime());\r\n    date.setDate(date.getDate() - days);\r\n    return date;\r\n};\r\nexports.dateMinusDays = dateMinusDays;\r\nconst isSameDay = (d1, d2) => {\r\n    return d1.getUTCFullYear() === d2.getUTCFullYear() &&\r\n        d1.getUTCMonth() === d2.getUTCMonth() &&\r\n        d1.getUTCDate() === d2.getUTCDate();\r\n};\r\nexports.isSameDay = isSameDay;\r\nconst MySQLToJsDate = (mysqlDate) => {\r\n    const t = mysqlDate.toString().split(/[- :]/);\r\n    return new Date(Date.UTC(Number(t[0]), Number(t[1]) - 1, Number(t[2]), Number(t[3]), Number(t[4]), Number(t[5])));\r\n};\r\nexports.MySQLToJsDate = MySQLToJsDate;\r\nconst daysDiffBetweenDates = (date1, date2) => {\r\n    const timeDiff = date2.getTime() - date1.getTime();\r\n    const daysDiff = timeDiff / (1000 * 3600 * 24);\r\n    return Math.abs(daysDiff);\r\n};\r\nexports.daysDiffBetweenDates = daysDiffBetweenDates;\r\nconst daysOfWeek = () => ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];\r\nexports.daysOfWeek = daysOfWeek;\r\nconst getNextDayOfTheWeek = (dayName, excludeToday = true, refDate = new Date()) => {\r\n    const dayOfWeek = (0, exports.daysOfWeek)().indexOf(dayName.slice(0, 3).toLowerCase());\r\n    if (dayOfWeek < 0)\r\n        return;\r\n    refDate.setHours(0, 0, 0, 0);\r\n    refDate.setDate(refDate.getDate() + +!!excludeToday + ((dayOfWeek + 7 - refDate.getDay() - +!!excludeToday) % 7));\r\n    return refDate;\r\n};\r\nexports.getNextDayOfTheWeek = getNextDayOfTheWeek;\r\nconst initDateWithTz = (date, tz) => {\r\n    const dateStr = date.toString();\r\n    return (0, exports.initDateWithTimezone)(dateStr, tz);\r\n};\r\nexports.initDateWithTz = initDateWithTz;\r\nconst initDateWithTimezone = (date, tz) => {\r\n    let dateStr;\r\n    try {\r\n        dateStr = dayjs_1.default.tz(date, tz).toISOString();\r\n    }\r\n    catch (e) {\r\n        return undefined;\r\n    }\r\n    return new Date(dateStr);\r\n};\r\nexports.initDateWithTimezone = initDateWithTimezone;\r\nconst secondsToHumanReadable = (seconds, abbreviate = false) => {\r\n    if (seconds === 0)\r\n        return abbreviate ? '0s' : '0 seconds';\r\n    const units = [\r\n        { label: abbreviate ? 'y' : 'year', seconds: 365 * 24 * 60 * 60 },\r\n        { label: abbreviate ? 'mo' : 'month', seconds: 30 * 24 * 60 * 60 },\r\n        { label: abbreviate ? 'w' : 'week', seconds: 7 * 24 * 60 * 60 },\r\n        { label: abbreviate ? 'd' : 'day', seconds: 24 * 60 * 60 },\r\n        { label: abbreviate ? 'h' : 'hour', seconds: 60 * 60 },\r\n        { label: abbreviate ? 'm' : 'minute', seconds: 60 },\r\n        { label: abbreviate ? 's' : 'second', seconds: 1 }\r\n    ];\r\n    const result = [];\r\n    for (const { label, seconds: unitSeconds } of units) {\r\n        const unitValue = Math.floor(seconds / unitSeconds);\r\n        if (unitValue > 0) {\r\n            result.push(`${unitValue} ${label}${unitValue > 1 && !abbreviate ? 's' : ''}`);\r\n            seconds %= unitSeconds;\r\n        }\r\n    }\r\n    return result.join(' ').trim();\r\n};\r\nexports.secondsToHumanReadable = secondsToHumanReadable;\r\nvar RelativeDate;\r\n(function (RelativeDate) {\r\n    RelativeDate[\"Today\"] = \"today\";\r\n    RelativeDate[\"Tomorrow\"] = \"tomorrow\";\r\n    RelativeDate[\"Yesterday\"] = \"yesterday\";\r\n    RelativeDate[\"_7DaysAgo\"] = \"7_days_ago\";\r\n    RelativeDate[\"_7DaysFromNow\"] = \"7_days_from_now\";\r\n    RelativeDate[\"_30DaysAgo\"] = \"30_days_ago\";\r\n    RelativeDate[\"_30DaysFromNow\"] = \"30_days_from_now\";\r\n    RelativeDate[\"_90DaysAgo\"] = \"90_days_ago\";\r\n    RelativeDate[\"_90DaysFromNow\"] = \"90_days_from_now\";\r\n})(RelativeDate = exports.RelativeDate || (exports.RelativeDate = {}));\r\nconst resolveRelativeDate = (date, referenceDate = new Date()) => {\r\n    const currentDate = new Date(referenceDate); // Create a copy of the reference date\r\n    switch (date) {\r\n        case RelativeDate.Today:\r\n            return currentDate;\r\n        case RelativeDate.Tomorrow:\r\n            currentDate.setDate(currentDate.getDate() + 1);\r\n            return currentDate;\r\n        case RelativeDate.Yesterday:\r\n            currentDate.setDate(currentDate.getDate() - 1);\r\n            return currentDate;\r\n        case RelativeDate._7DaysAgo:\r\n            currentDate.setDate(currentDate.getDate() - 7);\r\n            return currentDate;\r\n        case RelativeDate._7DaysFromNow:\r\n            currentDate.setDate(currentDate.getDate() + 7);\r\n            return currentDate;\r\n        case RelativeDate._30DaysAgo:\r\n            currentDate.setDate(currentDate.getDate() - 30);\r\n            return currentDate;\r\n        case RelativeDate._30DaysFromNow:\r\n            currentDate.setDate(currentDate.getDate() + 30);\r\n            return currentDate;\r\n        case RelativeDate._90DaysAgo:\r\n            currentDate.setDate(currentDate.getDate() - 90);\r\n            return currentDate;\r\n        case RelativeDate._90DaysFromNow:\r\n            currentDate.setDate(currentDate.getDate() + 90);\r\n            return currentDate;\r\n        default:\r\n            return currentDate;\r\n    }\r\n};\r\nexports.resolveRelativeDate = resolveRelativeDate;\r\nconst isDateAnniversary = (date, referenceDate) => {\r\n    // Use referenceDate if provided; otherwise default to today's date.\r\n    const refDate = referenceDate ? new Date(referenceDate) : new Date();\r\n    const givenDate = new Date(date);\r\n    // If either date is invalid, let the Date constructor produce an \"Invalid Date\"\r\n    if (isNaN(givenDate.getTime()) || isNaN(refDate.getTime())) {\r\n        return false;\r\n    }\r\n    // Compare month and day only.\r\n    return givenDate.getMonth() === refDate.getMonth() && givenDate.getDate() === refDate.getDate();\r\n};\r\nexports.isDateAnniversary = isDateAnniversary;\r\n/**\r\n * Formats a date string or Date object to a string based on the provided format.\r\n * @param dateInput - The date string or Date object to format.\r\n * @param format - The format string to use.\r\n *\r\n * Supported tokens include:\r\n * - YYYY: 4-digit year (e.g., 2025)\r\n * - MMMM: Full month name (e.g., January)\r\n * - MMM: Abbreviated month name (e.g., Jan)\r\n * - MM: Month number with leading zero (e.g., 01)\r\n * - M: Month number without leading zero (e.g., 1)\r\n * - dddd: Full weekday name (e.g., Thursday)\r\n * - ddd: Abbreviated weekday name (e.g., Thu)\r\n * - DD: Day of month with leading zero (e.g., 02)\r\n * - D: Day of month without padding (e.g., 2)\r\n * - Do: Day of month with ordinal suffix (e.g., 2nd)\r\n *\r\n * If the date is provided in the 'YYYY-MM-DD' format, it is treated as UTC.\r\n */\r\nconst formatDate = (dateInput, format) => {\r\n    let date;\r\n    // If dateInput is in \"YYYY-MM-DD\" format, treat it as UTC.\r\n    if (typeof dateInput === \"string\" && /^\\d{4}-\\d{2}-\\d{2}$/.test(dateInput)) {\r\n        date = new Date(dateInput + \"T00:00:00.000Z\");\r\n    }\r\n    else {\r\n        date = new Date(dateInput);\r\n    }\r\n    // Basic tokens\r\n    const year = date.getUTCFullYear().toString();\r\n    const month = (date.getUTCMonth() + 1).toString().padStart(2, \"0\");\r\n    const monthNoPad = (date.getUTCMonth() + 1).toString();\r\n    const day = date.getUTCDate().toString().padStart(2, \"0\");\r\n    const dayNoPad = date.getUTCDate().toString();\r\n    // Weekday tokens\r\n    const daysShort = [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"];\r\n    const daysLong = [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\r\n    const weekdayShort = daysShort[date.getUTCDay()];\r\n    const weekdayLong = daysLong[date.getUTCDay()];\r\n    // Month name tokens\r\n    const monthsShort = [\"Jan\", \"Feb\", \"Mar\", \"Apr\", \"May\", \"Jun\", \"Jul\", \"Aug\", \"Sep\", \"Oct\", \"Nov\", \"Dec\"];\r\n    const monthsLong = [\r\n        \"January\", \"February\", \"March\", \"April\", \"May\", \"June\",\r\n        \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"\r\n    ];\r\n    const monthNameShort = monthsShort[date.getUTCMonth()];\r\n    const monthNameLong = monthsLong[date.getUTCMonth()];\r\n    // Ordinal day (Do)\r\n    const getOrdinal = (n) => {\r\n        if (n % 10 === 1 && n % 100 !== 11)\r\n            return n + \"st\";\r\n        if (n % 10 === 2 && n % 100 !== 12)\r\n            return n + \"nd\";\r\n        if (n % 10 === 3 && n % 100 !== 13)\r\n            return n + \"rd\";\r\n        return n + \"th\";\r\n    };\r\n    const dayOrdinal = getOrdinal(date.getUTCDate());\r\n    // Replace tokens in order (longest tokens first)\r\n    let formatted = format;\r\n    formatted = formatted.replace(/YYYY/g, year);\r\n    formatted = formatted.replace(/dddd/g, weekdayLong);\r\n    formatted = formatted.replace(/ddd/g, weekdayShort);\r\n    formatted = formatted.replace(/MMMM/g, monthNameLong);\r\n    formatted = formatted.replace(/MMM/g, monthNameShort);\r\n    formatted = formatted.replace(/Do/g, dayOrdinal);\r\n    formatted = formatted.replace(/DD/g, day);\r\n    // Replace D that is not part of Do or DD.\r\n    formatted = formatted.replace(/(?<!D)D(?!o)/g, dayNoPad);\r\n    formatted = formatted.replace(/MM/g, month);\r\n    formatted = formatted.replace(/M(?![a-zA-Z])/g, monthNoPad);\r\n    return formatted;\r\n};\r\nexports.formatDate = formatDate;\r\nconst isLeapYear = (input) => {\r\n    let year;\r\n    if (input instanceof Date) {\r\n        year = input.getFullYear();\r\n    }\r\n    else if (typeof input === \"string\") {\r\n        const date = new Date(input);\r\n        if (isNaN(date.getTime())) {\r\n            return false;\r\n        }\r\n        year = date.getFullYear();\r\n    }\r\n    else if (typeof input === \"number\") {\r\n        year = input;\r\n    }\r\n    else {\r\n        year = new Date().getFullYear();\r\n    }\r\n    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);\r\n};\r\nexports.isLeapYear = isLeapYear;\r\n/**\r\n * Returns the difference between two dates in the specified unit.\r\n * @param startDate\r\n * @param unit - The unit of time to measure the difference (e.g., \"seconds\", \"minutes\", \"hours\", \"days\", \"weeks\", \"months\", \"years\").\r\n * @param referenceDate\r\n * @returns The number of elapsed units between the current date and the given start date.\r\n * @example\r\n * timeDifferenceInUnits(\"2024-03-01T00:00:00.000Z\", \"days\") // returns 20\r\n */\r\nconst timeDifferenceInUnits = (startDate, unit, referenceDate = new Date() // Default to current time if no referenceDate is provided\r\n) => {\r\n    const start = new Date(startDate);\r\n    const reference = new Date(referenceDate);\r\n    const timeDiff = reference.getTime() - start.getTime(); // Difference in milliseconds\r\n    switch (unit.toLowerCase()) {\r\n        case \"seconds\":\r\n            return Math.floor(timeDiff / 1000);\r\n        case \"minutes\":\r\n            return Math.floor(timeDiff / (1000 * 60));\r\n        case \"hours\":\r\n            return Math.floor(timeDiff / (1000 * 60 * 60));\r\n        case \"days\":\r\n            return Math.floor(timeDiff / (1000 * 60 * 60 * 24));\r\n        case \"weeks\":\r\n            return Math.floor(timeDiff / (1000 * 60 * 60 * 24 * 7));\r\n        case \"months\":\r\n            return reference.getMonth() - start.getMonth() + (12 * (reference.getFullYear() - start.getFullYear()));\r\n        case \"years\":\r\n            return reference.getFullYear() - start.getFullYear();\r\n        default:\r\n            throw new Error(\"Invalid unit\");\r\n    }\r\n};\r\nexports.timeDifferenceInUnits = timeDifferenceInUnits;\r\nfunction formatCurrency(amount, currencyCode, locale = 'en-US') {\r\n    return new Intl.NumberFormat(locale, {\r\n        style: 'currency',\r\n        currency: currencyCode\r\n    }).format(amount);\r\n}\r\nexports.formatCurrency = formatCurrency;\r\nfunction dateFromStr(dateString) {\r\n    // Check if the date is in dd/mm/yyyy format\r\n    const regex = /^(\\d{2})\\/(\\d{2})\\/(\\d{4})$/;\r\n    const match = dateString.match(regex);\r\n    if (match) {\r\n        // If it matches, rearrange it to yyyy-mm-dd\r\n        const day = match[1];\r\n        const month = match[2];\r\n        const year = match[3];\r\n        // Return a Date object\r\n        return new Date(`${year}-${month}-${day}`);\r\n    }\r\n    // If it's not in dd/mm/yyyy, attempt to create a date object directly\r\n    return new Date(dateString);\r\n}\r\nexports.dateFromStr = dateFromStr;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/methods/date.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/methods/number.js":
/*!****************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/methods/number.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.resolveAggregate = exports.NumberAggregateFunction = exports.DateAggregateFunction = exports.CheckPercentAggregateFunction = exports.CheckboxAggregateFunction = exports.PercentAggregateFunction = exports.CountAggregateFunction = exports.ShowValuesAggregateFunction = exports.roundToDecimal = void 0;\r\nconst db_1 = __webpack_require__(/*! ../utils/db */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/utils/db.js\");\r\nconst roundToDecimal = (value, decimals) => Number(Math.round(Number(value + 'e' + decimals)) + 'e-' + decimals).toFixed(decimals);\r\nexports.roundToDecimal = roundToDecimal;\r\nvar ShowValuesAggregateFunction;\r\n(function (ShowValuesAggregateFunction) {\r\n    ShowValuesAggregateFunction[\"ShowOriginal\"] = \"show_original\";\r\n    ShowValuesAggregateFunction[\"ShowUnique\"] = \"show_unique\";\r\n})(ShowValuesAggregateFunction = exports.ShowValuesAggregateFunction || (exports.ShowValuesAggregateFunction = {}));\r\nvar CountAggregateFunction;\r\n(function (CountAggregateFunction) {\r\n    CountAggregateFunction[\"CountAll\"] = \"count_all\";\r\n    CountAggregateFunction[\"CountValues\"] = \"count_values\";\r\n    CountAggregateFunction[\"CountUnique\"] = \"count_unique\";\r\n    CountAggregateFunction[\"CountEmpty\"] = \"count_empty\";\r\n    CountAggregateFunction[\"CountNotEmpty\"] = \"count_not_empty\";\r\n})(CountAggregateFunction = exports.CountAggregateFunction || (exports.CountAggregateFunction = {}));\r\nvar PercentAggregateFunction;\r\n(function (PercentAggregateFunction) {\r\n    PercentAggregateFunction[\"PercentEmpty\"] = \"percent_empty\";\r\n    PercentAggregateFunction[\"PercentNotEmpty\"] = \"percent_not_empty\";\r\n})(PercentAggregateFunction = exports.PercentAggregateFunction || (exports.PercentAggregateFunction = {}));\r\nvar CheckboxAggregateFunction;\r\n(function (CheckboxAggregateFunction) {\r\n    CheckboxAggregateFunction[\"CountAll\"] = \"count_all\";\r\n    CheckboxAggregateFunction[\"CountChecked\"] = \"count_checked\";\r\n    CheckboxAggregateFunction[\"CountUnchecked\"] = \"count_unchecked\";\r\n})(CheckboxAggregateFunction = exports.CheckboxAggregateFunction || (exports.CheckboxAggregateFunction = {}));\r\nvar CheckPercentAggregateFunction;\r\n(function (CheckPercentAggregateFunction) {\r\n    CheckPercentAggregateFunction[\"PercentChecked\"] = \"percent_checked\";\r\n    CheckPercentAggregateFunction[\"PercentNotChecked\"] = \"percent_not_checked\";\r\n})(CheckPercentAggregateFunction = exports.CheckPercentAggregateFunction || (exports.CheckPercentAggregateFunction = {}));\r\nvar DateAggregateFunction;\r\n(function (DateAggregateFunction) {\r\n    DateAggregateFunction[\"EarliestDate\"] = \"earliest_date\";\r\n    DateAggregateFunction[\"LatestDate\"] = \"latest_date\";\r\n    DateAggregateFunction[\"DateRange\"] = \"date_range\";\r\n})(DateAggregateFunction = exports.DateAggregateFunction || (exports.DateAggregateFunction = {}));\r\nvar NumberAggregateFunction;\r\n(function (NumberAggregateFunction) {\r\n    NumberAggregateFunction[\"Sum\"] = \"sum\";\r\n    NumberAggregateFunction[\"Min\"] = \"min\";\r\n    NumberAggregateFunction[\"Max\"] = \"max\";\r\n    NumberAggregateFunction[\"Average\"] = \"average\";\r\n    NumberAggregateFunction[\"Range\"] = \"range\";\r\n})(NumberAggregateFunction = exports.NumberAggregateFunction || (exports.NumberAggregateFunction = {}));\r\nconst resolveAggregate = (values, aggregateFunction) => {\r\n    return (0, db_1.resolveColumnValuesAggregation)(values, aggregateFunction);\r\n};\r\nexports.resolveAggregate = resolveAggregate;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/methods/number.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/methods/object.js":
/*!****************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/methods/object.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.flattenObject = exports.omit = exports.inclusivePick = exports.countSubstituteVars = exports.substituteVarsInObjects = exports.substituteTagInObjects = exports.substituteVars = exports.getSubstituteVarValue = void 0;\r\n/**\r\n * Recursively retrieve a nested value from an object given an array of keys.\r\n */\r\nconst getNestedValue = (obj, keys) => {\r\n    if (!obj || keys.length === 0)\r\n        return obj;\r\n    const [first, ...rest] = keys;\r\n    // Use case-insensitive lookup: convert keys to lowercase\r\n    const foundKey = Object.keys(obj).find(key => key.toLowerCase() === first.toLowerCase());\r\n    if (foundKey === undefined)\r\n        return undefined;\r\n    return getNestedValue(obj[foundKey], rest);\r\n};\r\nconst getSubstituteVarValue = (vars, key) => {\r\n    // 1. Exact match\r\n    if (key in vars)\r\n        return vars[key];\r\n    // 2. Case-insensitive flat match\r\n    const flatKeyMatch = Object.keys(vars).find(k => k.toLowerCase() === key.toLowerCase());\r\n    if (flatKeyMatch)\r\n        return vars[flatKeyMatch];\r\n    // 3. Nested fallback (e.g. @secret.apikey → [\"@secret\", \"apikey\"])\r\n    const keys = key.split('.').map(s => s.trim());\r\n    return getNestedValue(vars, keys);\r\n};\r\nexports.getSubstituteVarValue = getSubstituteVarValue;\r\nconst substituteVars = (text, rawVars, transform, bracketPattern = 'square') => {\r\n    if (!text)\r\n        return '';\r\n    // Normalize rawVars keys to lower-case for the first level.\r\n    const vars = {};\r\n    for (const key in rawVars) {\r\n        if (rawVars.hasOwnProperty(key)) {\r\n            vars[key.toLowerCase().trim()] = rawVars[key];\r\n        }\r\n    }\r\n    // Pattern: any text enclosed in brackets\r\n    const pattern = bracketPattern === 'curly' ? /(\\{\\{.*?\\}\\})/gi : /(\\[.*?\\])/gi;\r\n    let matches = text.match(pattern) || [];\r\n    // Remove duplicates if any\r\n    matches = Array.from(new Set(matches));\r\n    for (const tag of matches) {\r\n        // Remove the brackets based on the chosen pattern.\r\n        const tagContent = bracketPattern === 'curly' ? tag.slice(2, -2).trim() : tag.slice(1, -1).trim();\r\n        // Check for --skip flag (case-insensitive)\r\n        if (tagContent.toLowerCase().endsWith('--skip')) {\r\n            const tagWithoutSkipContent = tagContent.slice(0, -6).trim();\r\n            const tagWithoutSkip = bracketPattern === 'curly'\r\n                ? `{{${tagWithoutSkipContent}}}`\r\n                : `[${tagWithoutSkipContent}]`;\r\n            text = text.replace(new RegExp(escapeRegExp(tag), 'g'), tagWithoutSkip);\r\n            continue;\r\n        }\r\n        // Split by slash to support default values, then dot for nested keys.\r\n        let [rawPathPart, defaultVal] = tagContent.split('/');\r\n        // normalize the path part to lower-case\r\n        let pathPart = rawPathPart.toLowerCase().trim();\r\n        let value = (0, exports.getSubstituteVarValue)(vars, pathPart);\r\n        // If value is falsy (except 0) OR it's 0 and a default is provided, then use the default.\r\n        if ((!value && value !== 0) || (value === 0 && defaultVal)) {\r\n            value = defaultVal !== undefined ? defaultVal : '';\r\n        }\r\n        // Apply transform if provided.\r\n        if (transform) {\r\n            value = transform(value, rawPathPart);\r\n        }\r\n        // Convert the final value to string.\r\n        let finalReplacement;\r\n        if (typeof value === 'object' && value !== null) {\r\n            // Convert object/array to JSON string\r\n            finalReplacement = JSON.stringify(value);\r\n        }\r\n        else {\r\n            // Fallback for primitives, strings, etc.\r\n            finalReplacement = String(value);\r\n        }\r\n        // Replace all instances of the tag in the text.\r\n        text = text.replace(new RegExp(escapeRegExp(tag), 'g'), finalReplacement);\r\n    }\r\n    return text;\r\n};\r\nexports.substituteVars = substituteVars;\r\nconst substituteTagInObjects = (config, vars, bracketPattern = 'square') => {\r\n    /**\r\n     * Checks if `str` is exactly one placeholder with no extra text,\r\n     * using the chosen bracket pattern.\r\n     * e.g. if bracketPattern = 'curly', we expect ^{{(.*?)}}$.\r\n     *      if bracketPattern = 'square', we expect ^\\[(.*?)\\]$.\r\n     */\r\n    function parseSinglePlaceholder(str, pattern) {\r\n        if (!str)\r\n            return null;\r\n        let regex;\r\n        if (pattern === 'square') {\r\n            regex = /^\\[([\\s\\S]*?)\\]$/; // e.g. \"[someKey]\"\r\n        }\r\n        else {\r\n            regex = /^{{([\\s\\S]*?)}}$/; // e.g. \"{{someKey}}\"\r\n        }\r\n        const match = str.match(regex);\r\n        return match ? match[1].trim() : null;\r\n    }\r\n    /**\r\n     * If a property is exactly one placeholder (e.g. `{{myObj}}` or `[myObj]`),\r\n     * attempt to retrieve an object from `vars`. Otherwise, do a normal string\r\n     * substitution using your existing `substituteVars`.\r\n     */\r\n    function handleStringValue(str) {\r\n        // 1. Check if it’s exactly one placeholder\r\n        const placeholderContent = parseSinglePlaceholder(str, bracketPattern);\r\n        if (!placeholderContent) {\r\n            // Check if the string is a JSON object\r\n            try {\r\n                const parsedObject = JSON.parse(str);\r\n                // Walk and substitute placeholders inside the object\r\n                const substituted = (0, exports.substituteVarsInObjects)(parsedObject, vars, bracketPattern);\r\n                // Return the re-stringified JSON\r\n                return JSON.stringify(substituted);\r\n            }\r\n            catch (_a) {\r\n                // Fallback: not a JSON object, just substitute directly\r\n                return (0, exports.substituteVars)(str, vars, undefined, bracketPattern);\r\n            }\r\n        }\r\n        // 2. If single placeholder ends with `--skip`, keep it literally (minus skip)\r\n        if (placeholderContent.toLowerCase().endsWith('--skip')) {\r\n            const stripped = placeholderContent.slice(0, -6).trim();\r\n            return bracketPattern === 'square'\r\n                ? `[${stripped}]`\r\n                : `{{${stripped}}}`;\r\n        }\r\n        // 3. Attempt to fetch the corresponding value as an object/array\r\n        //    (including default handling if user typed \"key/default\" in placeholder)\r\n        let [rawPath, defaultVal] = placeholderContent.split('/');\r\n        rawPath = (rawPath || '').trim();\r\n        let value = (0, exports.getSubstituteVarValue)(vars, rawPath); // case-insensitive nested lookup\r\n        if ((value === undefined || value === null) && defaultVal !== undefined) {\r\n            value = defaultVal;\r\n        }\r\n        if (!value && value !== 0) {\r\n            value = '';\r\n        }\r\n        // If the final value is an object or array, preserve it\r\n        if (value && typeof value === 'object') {\r\n            return value;\r\n        }\r\n        // Otherwise coerce to string\r\n        return String(value);\r\n    }\r\n    /**\r\n     * Recursively traverse the config and apply the above logic to each string property.\r\n     */\r\n    function recurse(val) {\r\n        if (Array.isArray(val)) {\r\n            return val.map(recurse);\r\n        }\r\n        if (val && typeof val === 'object') {\r\n            return Object.fromEntries(Object.entries(val).map(([k, v]) => [k, recurse(v)]));\r\n        }\r\n        if (typeof val === 'string') {\r\n            return handleStringValue(val);\r\n        }\r\n        // Booleans, numbers, null, etc. remain untouched\r\n        return val;\r\n    }\r\n    return recurse(config);\r\n};\r\nexports.substituteTagInObjects = substituteTagInObjects;\r\nexports.substituteVarsInObjects = exports.substituteTagInObjects;\r\n/**\r\n * Helper function to escape RegExp special characters in a string.\r\n */\r\nfunction escapeRegExp(str) {\r\n    return str.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\r\n}\r\nconst countSubstituteVars = (text, bracketPattern = 'square') => {\r\n    if (!text)\r\n        return 0;\r\n    const pattern = bracketPattern === 'curly' ? /(\\{\\{.*?\\}\\})/gi : /(\\[.*?\\])/gi;\r\n    let match = text.match(pattern);\r\n    return match ? match.length : 0;\r\n};\r\nexports.countSubstituteVars = countSubstituteVars;\r\nconst inclusivePick = (obj, keys) => {\r\n    if (!Array.isArray(keys) || keys.length === 0)\r\n        return {};\r\n    const obj2 = {};\r\n    for (const key of keys) {\r\n        obj2[key] = obj[key];\r\n    }\r\n    return obj2;\r\n};\r\nexports.inclusivePick = inclusivePick;\r\nconst omit = (obj, keys) => {\r\n    const obj2 = Object.assign({}, obj);\r\n    if (!Array.isArray(keys) || keys.length === 0)\r\n        return obj2;\r\n    for (const key of keys) {\r\n        delete obj2[key];\r\n    }\r\n    return obj2;\r\n};\r\nexports.omit = omit;\r\nconst flattenObject = (obj, keyMap = {}) => {\r\n    const result = {};\r\n    const stack = [{ obj, key: '' }];\r\n    const visited = new Set();\r\n    while (stack.length > 0) {\r\n        const current = stack.pop();\r\n        const value = current.obj;\r\n        const parentKey = current.key;\r\n        if ((typeof value === 'object' || Array.isArray(value)) && value !== null) {\r\n            if (visited.has(value)) {\r\n                continue;\r\n            }\r\n            else {\r\n                visited.add(value);\r\n            }\r\n            for (const key in value) {\r\n                if (value.hasOwnProperty(key)) {\r\n                    const childValue = value[key];\r\n                    const newKey = parentKey ? `${parentKey}.${key}` : key;\r\n                    if ((typeof childValue === 'object' || Array.isArray(childValue)) && childValue !== null) {\r\n                        stack.push({ obj: childValue, key: newKey });\r\n                    }\r\n                    else {\r\n                        result[newKey] = childValue;\r\n                        keyMap[newKey] = parentKey ? `${parentKey} ${key}`.replace(/\\./g, ' ') : key;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            result[parentKey] = value;\r\n            keyMap[parentKey] = parentKey;\r\n        }\r\n    }\r\n    return [result, keyMap];\r\n};\r\nexports.flattenObject = flattenObject;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/methods/object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/methods/string.js":
/*!****************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/methods/string.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.splitTextByCapitalization = exports.hexToRGB = exports.isEmpty = exports.isString = exports.contains = exports.endsWith = exports.startsWith = exports.generateUUID = exports.stripNonNumeric = exports.trimStringToLength = exports.intToOrdinalNumberString = exports.capitalizeFirstLetter = exports.formatByteSize = exports.getExtension = exports.generateRandomString = exports.strReplaceAll = exports.trimString = void 0;\r\nconst uuid_1 = __webpack_require__(/*! uuid */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/index.js\");\r\nconst trimString = (text, charToRemove) => {\r\n    if (!text)\r\n        return '';\r\n    while (text.charAt(0) === charToRemove) {\r\n        text = text.substring(1);\r\n    }\r\n    while (text.charAt(text.length - 1) === charToRemove) {\r\n        text = text.substring(0, text.length - 1);\r\n    }\r\n    return text;\r\n};\r\nexports.trimString = trimString;\r\nconst strReplaceAll = (needle, haystack, replacement) => {\r\n    return haystack.split(needle).join(replacement);\r\n};\r\nexports.strReplaceAll = strReplaceAll;\r\nconst generateRandomString = (length, caseSensitive = false) => {\r\n    let result = '';\r\n    const characters = caseSensitive\r\n        ? 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'\r\n        : 'abcdefghijklmnopqrstuvwxyz0123456789';\r\n    const charactersLength = characters.length;\r\n    for (let i = 0; i < length; i++) {\r\n        result += characters.charAt(Math.floor(Math.random() * charactersLength));\r\n    }\r\n    return result;\r\n};\r\nexports.generateRandomString = generateRandomString;\r\nfunction getExtension(filename) {\r\n    return filename.split(\".\").pop();\r\n}\r\nexports.getExtension = getExtension;\r\nfunction formatByteSize(bytes) {\r\n    if (bytes === 0)\r\n        return '0 Bytes';\r\n    const k = 1024;\r\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];\r\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\r\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\r\n}\r\nexports.formatByteSize = formatByteSize;\r\nconst capitalizeFirstLetter = (word) => (word ? word.charAt(0).toUpperCase() + word.slice(1) : '');\r\nexports.capitalizeFirstLetter = capitalizeFirstLetter;\r\n/**\r\n * intToOrdinalNumberString converts an integer to an ordinal\r\n * number string. For example: 1 => \"1st\", 12 => \"12th\", 23 => \"23rd\"\r\n */\r\nconst intToOrdinalNumberString = (num) => {\r\n    num = Math.round(num);\r\n    const numString = num.toString();\r\n    // If the ten's place is 1, the suffix is always \"th\"\r\n    // (10th, 11th, 12th, 13th, 14th, 111th, 112th, etc.)\r\n    if (Math.floor(num / 10) % 10 === 1) {\r\n        return numString + 'th';\r\n    }\r\n    // Otherwise, the suffix depends on the one's place as follows\r\n    // (1st, 2nd, 3rd, 4th, 21st, 22nd, etc.)\r\n    switch (num % 10) {\r\n        case 1:\r\n            return numString + 'st';\r\n        case 2:\r\n            return numString + 'nd';\r\n        case 3:\r\n            return numString + 'rd';\r\n        default:\r\n            return numString + 'th';\r\n    }\r\n};\r\nexports.intToOrdinalNumberString = intToOrdinalNumberString;\r\nlet trimStringToLength = (text, length) => {\r\n    if (!text)\r\n        return '';\r\n    return text.length > length ? text.substring(0, length) + '...' : text;\r\n};\r\nexports.trimStringToLength = trimStringToLength;\r\nconst stripNonNumeric = (str) => (str ? str.replace(/[^\\d]/g, '') : '');\r\nexports.stripNonNumeric = stripNonNumeric;\r\nconst generateUUID = () => {\r\n    return (0, uuid_1.v4)();\r\n};\r\nexports.generateUUID = generateUUID;\r\nconst startsWith = (str1, str2, ignoreCase = false) => {\r\n    str1 = (0, exports.isString)(str1) ? str1 : '';\r\n    str2 = (0, exports.isString)(str2) ? str2 : '';\r\n    if (ignoreCase) {\r\n        str1 = str1.toLowerCase();\r\n        str2 = str2.toLowerCase();\r\n    }\r\n    return str1.startsWith(str2);\r\n};\r\nexports.startsWith = startsWith;\r\nconst endsWith = (str1, str2, ignoreCase = false) => {\r\n    str1 = (0, exports.isString)(str1) ? str1 : '';\r\n    str2 = (0, exports.isString)(str2) ? str2 : '';\r\n    if (ignoreCase) {\r\n        str1 = str1.toLowerCase();\r\n        str2 = str2.toLowerCase();\r\n    }\r\n    return str1.endsWith(str2);\r\n};\r\nexports.endsWith = endsWith;\r\nconst contains = (str1, str2, ignoreCase = false) => {\r\n    str1 = (0, exports.isString)(str1) ? str1 : '';\r\n    str2 = (0, exports.isString)(str2) ? str2 : '';\r\n    if (ignoreCase) {\r\n        str1 = str1.toLowerCase();\r\n        str2 = str2.toLowerCase();\r\n    }\r\n    return str1.includes(str2);\r\n};\r\nexports.contains = contains;\r\nconst isString = (str) => typeof str === 'string';\r\nexports.isString = isString;\r\nconst isEmpty = (str) => !str || str.length === 0;\r\nexports.isEmpty = isEmpty;\r\nfunction hexToRGB(hex) {\r\n    const hexWithoutHash = hex.replace(/^#/, '');\r\n    if (hexWithoutHash.length === 3) {\r\n        const r = parseInt(hexWithoutHash[0] + hexWithoutHash[0], 16);\r\n        const g = parseInt(hexWithoutHash[1] + hexWithoutHash[1], 16);\r\n        const b = parseInt(hexWithoutHash[2] + hexWithoutHash[2], 16);\r\n        return { red: r, green: g, blue: b };\r\n    }\r\n    else if (hexWithoutHash.length === 6) {\r\n        const r = parseInt(hexWithoutHash.slice(0, 2), 16);\r\n        const g = parseInt(hexWithoutHash.slice(2, 4), 16);\r\n        const b = parseInt(hexWithoutHash.slice(4, 6), 16);\r\n        return { red: r, green: g, blue: b };\r\n    }\r\n    return null;\r\n}\r\nexports.hexToRGB = hexToRGB;\r\nfunction splitTextByCapitalization(text) {\r\n    return text.split(/(?=[A-Z])/).join(' ');\r\n}\r\nexports.splitTextByCapitalization = splitTextByCapitalization;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/methods/string.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/methods/suggest.js":
/*!*****************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/methods/suggest.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.suggest = void 0;\r\nconst suggest = (name, count = 50, isUsernameOrSlug = false) => {\r\n    const delimiter = '-';\r\n    const suggestions = new Set();\r\n    const tmpName = name.trim().toLowerCase();\r\n    // Generate additional suggestions based on the original name\r\n    const sanitizedOriginalName = isUsernameOrSlug ? tmpName.replace(/[^a-zA-Z0-9-]/g, '') : tmpName;\r\n    suggestions.add(sanitizedOriginalName);\r\n    suggestions.add(sanitizedOriginalName.replace(/[^a-zA-Z0-9]/g, '')); // suggestion without delimiter\r\n    // Split name by non-alphanumeric characters\r\n    const words = tmpName.split(/[^a-zA-Z0-9]/).filter(word => word.trim() !== '');\r\n    // Generate suggestions using two-pointer algorithm\r\n    for (let i = 0; i < words.length; i++) {\r\n        let suggestion = words[i];\r\n        for (let j = i + 1; j < words.length; j++) {\r\n            suggestion += delimiter + words[j];\r\n            suggestions.add(suggestion);\r\n        }\r\n    }\r\n    const generateCount = count || (suggestions.size < 3 ? 30 : 10);\r\n    while (suggestions.size < generateCount) {\r\n        suggestions.add(sanitizedOriginalName + delimiter + Math.floor(Math.random() * 1000));\r\n    }\r\n    return Array.from(suggestions);\r\n};\r\nexports.suggest = suggest;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9saWIvbWV0aG9kcy9zdWdnZXN0LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlFQUF5RTtBQUN6RTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0Isa0JBQWtCO0FBQ3RDO0FBQ0EsNEJBQTRCLGtCQUFrQjtBQUM5QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL29wZW5kYi1hcHAtZGItdXRpbHMvbGliL21ldGhvZHMvc3VnZ2VzdC5qcz9jODZjIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xyXG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XHJcbmV4cG9ydHMuc3VnZ2VzdCA9IHZvaWQgMDtcclxuY29uc3Qgc3VnZ2VzdCA9IChuYW1lLCBjb3VudCA9IDUwLCBpc1VzZXJuYW1lT3JTbHVnID0gZmFsc2UpID0+IHtcclxuICAgIGNvbnN0IGRlbGltaXRlciA9ICctJztcclxuICAgIGNvbnN0IHN1Z2dlc3Rpb25zID0gbmV3IFNldCgpO1xyXG4gICAgY29uc3QgdG1wTmFtZSA9IG5hbWUudHJpbSgpLnRvTG93ZXJDYXNlKCk7XHJcbiAgICAvLyBHZW5lcmF0ZSBhZGRpdGlvbmFsIHN1Z2dlc3Rpb25zIGJhc2VkIG9uIHRoZSBvcmlnaW5hbCBuYW1lXHJcbiAgICBjb25zdCBzYW5pdGl6ZWRPcmlnaW5hbE5hbWUgPSBpc1VzZXJuYW1lT3JTbHVnID8gdG1wTmFtZS5yZXBsYWNlKC9bXmEtekEtWjAtOS1dL2csICcnKSA6IHRtcE5hbWU7XHJcbiAgICBzdWdnZXN0aW9ucy5hZGQoc2FuaXRpemVkT3JpZ2luYWxOYW1lKTtcclxuICAgIHN1Z2dlc3Rpb25zLmFkZChzYW5pdGl6ZWRPcmlnaW5hbE5hbWUucmVwbGFjZSgvW15hLXpBLVowLTldL2csICcnKSk7IC8vIHN1Z2dlc3Rpb24gd2l0aG91dCBkZWxpbWl0ZXJcclxuICAgIC8vIFNwbGl0IG5hbWUgYnkgbm9uLWFscGhhbnVtZXJpYyBjaGFyYWN0ZXJzXHJcbiAgICBjb25zdCB3b3JkcyA9IHRtcE5hbWUuc3BsaXQoL1teYS16QS1aMC05XS8pLmZpbHRlcih3b3JkID0+IHdvcmQudHJpbSgpICE9PSAnJyk7XHJcbiAgICAvLyBHZW5lcmF0ZSBzdWdnZXN0aW9ucyB1c2luZyB0d28tcG9pbnRlciBhbGdvcml0aG1cclxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgd29yZHMubGVuZ3RoOyBpKyspIHtcclxuICAgICAgICBsZXQgc3VnZ2VzdGlvbiA9IHdvcmRzW2ldO1xyXG4gICAgICAgIGZvciAobGV0IGogPSBpICsgMTsgaiA8IHdvcmRzLmxlbmd0aDsgaisrKSB7XHJcbiAgICAgICAgICAgIHN1Z2dlc3Rpb24gKz0gZGVsaW1pdGVyICsgd29yZHNbal07XHJcbiAgICAgICAgICAgIHN1Z2dlc3Rpb25zLmFkZChzdWdnZXN0aW9uKTtcclxuICAgICAgICB9XHJcbiAgICB9XHJcbiAgICBjb25zdCBnZW5lcmF0ZUNvdW50ID0gY291bnQgfHwgKHN1Z2dlc3Rpb25zLnNpemUgPCAzID8gMzAgOiAxMCk7XHJcbiAgICB3aGlsZSAoc3VnZ2VzdGlvbnMuc2l6ZSA8IGdlbmVyYXRlQ291bnQpIHtcclxuICAgICAgICBzdWdnZXN0aW9ucy5hZGQoc2FuaXRpemVkT3JpZ2luYWxOYW1lICsgZGVsaW1pdGVyICsgTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpICogMTAwMCkpO1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIEFycmF5LmZyb20oc3VnZ2VzdGlvbnMpO1xyXG59O1xyXG5leHBvcnRzLnN1Z2dlc3QgPSBzdWdnZXN0O1xyXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/methods/suggest.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/methods/tag.js":
/*!*************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/methods/tag.js ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.stripHtmlTags = exports.taggedHtmlToTaggedText = exports.taggedTextToTaggedHtml = exports.generateTaggedHTML = exports.escapeHtml = void 0;\r\nconst string_1 = __webpack_require__(/*! ./string */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/string.js\");\r\nconst sanitize_html_1 = __importDefault(__webpack_require__(/*! sanitize-html */ \"(ssr)/./node_modules/sanitize-html/index.js\"));\r\nfunction escapeHtml(str, exclude = []) {\r\n    if (!str)\r\n        return '';\r\n    return str.replace(/[&<>\"'/]/g, (match) => {\r\n        if (exclude.includes(match)) {\r\n            return match;\r\n        }\r\n        switch (match) {\r\n            case '&':\r\n                return '&amp;';\r\n            case '<':\r\n                return '&lt;';\r\n            case '>':\r\n                return '&gt;';\r\n            case '\"':\r\n                return '&quot;';\r\n            case \"'\":\r\n                return '&#39;';\r\n            //            case '/':\r\n            //                return '&#x2F;';\r\n            default:\r\n                return match;\r\n        }\r\n    });\r\n}\r\nexports.escapeHtml = escapeHtml;\r\nconst generateTaggedHTML = (params, bracketPattern = 'square', excludeCharFromEscape = []) => {\r\n    const escapedTag = escapeHtml(params.tag, excludeCharFromEscape);\r\n    let strippedTag = '';\r\n    if (bracketPattern === 'curly') {\r\n        // Remove leading \"{{\" and trailing \"}}\"\r\n        strippedTag = escapedTag.slice(2, -2);\r\n    }\r\n    else {\r\n        // Remove leading \"[\" and trailing \"]\"\r\n        strippedTag = escapedTag.slice(1, -1);\r\n    }\r\n    const [label, defaultValue] = strippedTag.split('/');\r\n    const escapedLabel = (0, string_1.capitalizeFirstLetter)(escapeHtml(params.label || label, excludeCharFromEscape));\r\n    const escapedDefault = escapeHtml(defaultValue, excludeCharFromEscape);\r\n    return `<span data-tag=\"${escapedTag}\" data-tag-label=\"${escapedLabel}\" contenteditable=\"false\">${escapedLabel}${escapedDefault ? ' / ' + escapedDefault : ''}</span>`;\r\n};\r\nexports.generateTaggedHTML = generateTaggedHTML;\r\nconst taggedTextToTaggedHtml = (str, preserveNewlines = true, tagLabels = {}, bracketPattern = 'square', excludeCharFromEscape = []) => {\r\n    // Use appropriate regex based on the bracket pattern.\r\n    const regex = bracketPattern === 'curly'\r\n        ? /\\{\\{(.*?)(?:\\/(.*?))?\\}\\}/g\r\n        : /\\[(.*?)(?:\\/(.*?))?\\]/g;\r\n    let html = str.replace(regex, (_, tag = '', defaultValue = '') => {\r\n        const originalTag = bracketPattern === 'curly' ? `{{${tag}}}` : `[${tag}]`;\r\n        const label = tagLabels[originalTag] || tag;\r\n        const tagParams = {\r\n            tag: defaultValue\r\n                ? bracketPattern === 'curly'\r\n                    ? `{{${tag}/${defaultValue}}}`\r\n                    : `[${tag}/${defaultValue}]`\r\n                : originalTag,\r\n            label,\r\n        };\r\n        return (0, exports.generateTaggedHTML)(tagParams, bracketPattern, excludeCharFromEscape);\r\n    });\r\n    if (preserveNewlines) {\r\n        html = html.replace(/\\n/g, '<br/>');\r\n    }\r\n    return html;\r\n};\r\nexports.taggedTextToTaggedHtml = taggedTextToTaggedHtml;\r\nfunction taggedHtmlToTaggedText(html, bracketPattern = 'square') {\r\n    // Use a regex that matches the appropriate data-tag pattern.\r\n    const tagRegex = bracketPattern === 'curly'\r\n        ? /<(\\w+)[^>]*?\\sdata-tag\\s*=\\s*(['\"])\\{\\{([^\\}]+)\\}\\}\\2.*?>(.*?)<\\/\\1>/gi\r\n        : /<(\\w+)[^>]*?\\sdata-tag\\s*=\\s*(['\"])\\[([^\\]]+)\\]\\2.*?>(.*?)<\\/\\1>/gi;\r\n    return html.replace(tagRegex, (_, tag, quote, tagParams, value) => bracketPattern === 'curly' ? `{{${tagParams}}}` : `[${tagParams}]`);\r\n}\r\nexports.taggedHtmlToTaggedText = taggedHtmlToTaggedText;\r\nfunction stripHtmlTags(html, preserveNewLines = false) {\r\n    if (!html)\r\n        return '';\r\n    const options = {\r\n        allowedTags: [],\r\n        allowedAttributes: {},\r\n    };\r\n    const stripped = (0, sanitize_html_1.default)(html, options);\r\n    if (!preserveNewLines) {\r\n        return stripped.replace(/\\n/g, '');\r\n    }\r\n    return stripped;\r\n}\r\nexports.stripHtmlTags = stripHtmlTags;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/methods/tag.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/typings/color.js":
/*!***************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/typings/color.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\n/**\r\n * Credit: https://coolors.co/\r\n */\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.ColorNames = exports.ColorCodes = void 0;\r\nvar ColorCodes;\r\n(function (ColorCodes) {\r\n    ColorCodes[\"Black\"] = \"#000000\";\r\n    ColorCodes[\"Brown\"] = \"#B87D4B\";\r\n    ColorCodes[\"Yellow\"] = \"#FFC857\";\r\n    ColorCodes[\"DarkSlateGray\"] = \"#35524A\";\r\n    ColorCodes[\"SlateGray\"] = \"#627C85\";\r\n    ColorCodes[\"Emerald\"] = \"#32DE8A\";\r\n    ColorCodes[\"Caledon\"] = \"#A1CDA8\";\r\n    ColorCodes[\"Beige\"] = \"#F2F3D9\";\r\n    ColorCodes[\"SpaceBlue\"] = \"#151E3F\";\r\n    ColorCodes[\"OxfordBlue\"] = \"#030027\";\r\n    ColorCodes[\"OldRose\"] = \"#C16E70\";\r\n    ColorCodes[\"EnglishViolet\"] = \"#4B3F72\";\r\n    ColorCodes[\"Turquoise\"] = \"#47EBD5\";\r\n    ColorCodes[\"Red\"] = \"#F83A53\";\r\n    ColorCodes[\"LightGray\"] = \"#FDFFFC\";\r\n    ColorCodes[\"LightGreen\"] = \"#7CEA9C\";\r\n    ColorCodes[\"Denim\"] = \"#2E5EAA\";\r\n    ColorCodes[\"UltraViolet\"] = \"#5B4E77\";\r\n    ColorCodes[\"Jet\"] = \"#323031\";\r\n    ColorCodes[\"Teal\"] = \"#177E89\";\r\n    ColorCodes[\"MidnightGreen\"] = \"#084C61\";\r\n    ColorCodes[\"IndianRed\"] = \"#E05263\";\r\n    ColorCodes[\"LapisLazuli\"] = \"#381D2A\";\r\n    ColorCodes[\"Vanilla\"] = \"#E9E3B4\";\r\n    ColorCodes[\"AtomicTangerine\"] = \"#F39B6D\";\r\n})(ColorCodes = exports.ColorCodes || (exports.ColorCodes = {}));\r\nconst colorKeys = Object.keys(ColorCodes);\r\nconst colorKeyValue = {};\r\nfor (const colorKey of colorKeys) {\r\n    colorKeyValue[colorKey] = colorKey;\r\n}\r\nexports.ColorNames = colorKeyValue;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/typings/color.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/typings/common.js":
/*!****************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/typings/common.js ***!
  \****************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.ObjectType = void 0;\r\nvar ObjectType;\r\n(function (ObjectType) {\r\n    ObjectType[\"Image\"] = \"image\";\r\n    ObjectType[\"Icon\"] = \"icon\";\r\n    ObjectType[\"Emoji\"] = \"emoji\";\r\n    ObjectType[\"Option\"] = \"option\";\r\n    ObjectType[\"Person\"] = \"person\";\r\n    ObjectType[\"Page\"] = \"page\";\r\n})(ObjectType = exports.ObjectType || (exports.ObjectType = {}));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9saWIvdHlwaW5ncy9jb21tb24uanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYiw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDLHNDQUFzQyxrQkFBa0IsS0FBSyIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9saWIvdHlwaW5ncy9jb21tb24uanM/MTcxMCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcclxuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xyXG5leHBvcnRzLk9iamVjdFR5cGUgPSB2b2lkIDA7XHJcbnZhciBPYmplY3RUeXBlO1xyXG4oZnVuY3Rpb24gKE9iamVjdFR5cGUpIHtcclxuICAgIE9iamVjdFR5cGVbXCJJbWFnZVwiXSA9IFwiaW1hZ2VcIjtcclxuICAgIE9iamVjdFR5cGVbXCJJY29uXCJdID0gXCJpY29uXCI7XHJcbiAgICBPYmplY3RUeXBlW1wiRW1vamlcIl0gPSBcImVtb2ppXCI7XHJcbiAgICBPYmplY3RUeXBlW1wiT3B0aW9uXCJdID0gXCJvcHRpb25cIjtcclxuICAgIE9iamVjdFR5cGVbXCJQZXJzb25cIl0gPSBcInBlcnNvblwiO1xyXG4gICAgT2JqZWN0VHlwZVtcIlBhZ2VcIl0gPSBcInBhZ2VcIjtcclxufSkoT2JqZWN0VHlwZSA9IGV4cG9ydHMuT2JqZWN0VHlwZSB8fCAoZXhwb3J0cy5PYmplY3RUeXBlID0ge30pKTtcclxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/typings/common.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/typings/db.js":
/*!************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/typings/db.js ***!
  \************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.CurrentObject = exports.CurrentPerson = exports.Sort = exports.Match = exports.MagicColumn = exports.DateColumnFormat = exports.ScannableCodeFormat = exports.NumberColumnFormat = exports.TextColumnFormat = exports.ReadOnlyColumns = exports.CanBeUniqueColumnTypes = exports.AutoGeneratedColumnTypes = exports.DatabaseFieldDataType = void 0;\r\nvar DatabaseFieldDataType;\r\n(function (DatabaseFieldDataType) {\r\n    DatabaseFieldDataType[\"Text\"] = \"text\";\r\n    DatabaseFieldDataType[\"AI\"] = \"ai\";\r\n    DatabaseFieldDataType[\"UUID\"] = \"uuid\";\r\n    DatabaseFieldDataType[\"Number\"] = \"number\";\r\n    DatabaseFieldDataType[\"Checkbox\"] = \"checkbox\";\r\n    DatabaseFieldDataType[\"Select\"] = \"select\";\r\n    DatabaseFieldDataType[\"Person\"] = \"person\";\r\n    DatabaseFieldDataType[\"CreatedBy\"] = \"created-by\";\r\n    DatabaseFieldDataType[\"UpdatedBy\"] = \"updated-by\";\r\n    DatabaseFieldDataType[\"Linked\"] = \"linked\";\r\n    DatabaseFieldDataType[\"Summarize\"] = \"summarize\";\r\n    DatabaseFieldDataType[\"Files\"] = \"files\";\r\n    DatabaseFieldDataType[\"Date\"] = \"date\";\r\n    DatabaseFieldDataType[\"CreatedAt\"] = \"created-at\";\r\n    DatabaseFieldDataType[\"UpdatedAt\"] = \"updated-at\";\r\n    DatabaseFieldDataType[\"Derived\"] = \"derived\";\r\n    DatabaseFieldDataType[\"ScannableCode\"] = \"scannable-code\";\r\n    DatabaseFieldDataType[\"ButtonGroup\"] = \"button-group\";\r\n})(DatabaseFieldDataType = exports.DatabaseFieldDataType || (exports.DatabaseFieldDataType = {}));\r\nexports.AutoGeneratedColumnTypes = Object.freeze([\r\n    DatabaseFieldDataType.Summarize,\r\n    DatabaseFieldDataType.Derived,\r\n    DatabaseFieldDataType.UUID,\r\n    DatabaseFieldDataType.CreatedAt,\r\n    DatabaseFieldDataType.UpdatedAt,\r\n    DatabaseFieldDataType.CreatedBy,\r\n    DatabaseFieldDataType.UpdatedBy,\r\n]);\r\nexports.CanBeUniqueColumnTypes = Object.freeze([\r\n    DatabaseFieldDataType.Date,\r\n    DatabaseFieldDataType.Number,\r\n    DatabaseFieldDataType.Text,\r\n]);\r\nexports.ReadOnlyColumns = Object.freeze([\r\n    ...exports.AutoGeneratedColumnTypes,\r\n    DatabaseFieldDataType.AI,\r\n    DatabaseFieldDataType.ButtonGroup\r\n]);\r\nvar TextColumnFormat;\r\n(function (TextColumnFormat) {\r\n    TextColumnFormat[\"Text\"] = \"text\";\r\n    TextColumnFormat[\"Email\"] = \"email\";\r\n    TextColumnFormat[\"Phone\"] = \"phone\";\r\n    TextColumnFormat[\"Url\"] = \"url\";\r\n    TextColumnFormat[\"Location\"] = \"location\";\r\n})(TextColumnFormat = exports.TextColumnFormat || (exports.TextColumnFormat = {}));\r\nvar NumberColumnFormat;\r\n(function (NumberColumnFormat) {\r\n    NumberColumnFormat[\"Number\"] = \"Number\";\r\n    NumberColumnFormat[\"Percentage\"] = \"percentage\";\r\n    NumberColumnFormat[\"Currency\"] = \"currency\";\r\n})(NumberColumnFormat = exports.NumberColumnFormat || (exports.NumberColumnFormat = {}));\r\nvar ScannableCodeFormat;\r\n(function (ScannableCodeFormat) {\r\n    ScannableCodeFormat[\"QR\"] = \"qr\";\r\n    ScannableCodeFormat[\"Barcode\"] = \"barcode\";\r\n})(ScannableCodeFormat = exports.ScannableCodeFormat || (exports.ScannableCodeFormat = {}));\r\nvar DateColumnFormat;\r\n(function (DateColumnFormat) {\r\n    DateColumnFormat[\"Relative\"] = \"relative\";\r\n    DateColumnFormat[\"Absolute\"] = \"absolute\";\r\n})(DateColumnFormat = exports.DateColumnFormat || (exports.DateColumnFormat = {}));\r\nvar MagicColumn;\r\n(function (MagicColumn) {\r\n    MagicColumn[\"UUID\"] = \"uuid\";\r\n    MagicColumn[\"CreatedAt\"] = \"created-at\";\r\n    MagicColumn[\"UpdatedAt\"] = \"updated-at\";\r\n    MagicColumn[\"CreatedBy\"] = \"created-by\";\r\n    MagicColumn[\"UpdatedBy\"] = \"updated-by\";\r\n})(MagicColumn = exports.MagicColumn || (exports.MagicColumn = {}));\r\nvar Match;\r\n(function (Match) {\r\n    Match[\"All\"] = \"all\";\r\n    Match[\"Any\"] = \"any\";\r\n})(Match = exports.Match || (exports.Match = {}));\r\nvar Sort;\r\n(function (Sort) {\r\n    Sort[\"Asc\"] = \"asc\";\r\n    Sort[\"Desc\"] = \"desc\";\r\n})(Sort = exports.Sort || (exports.Sort = {}));\r\nexports.CurrentPerson = Object.freeze({\r\n    image: undefined,\r\n    firstName: 'Current',\r\n    lastName: 'User',\r\n    id: 'current_user',\r\n    title: 'Current User'\r\n});\r\nexports.CurrentObject = \"current_object\";\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/typings/db.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/typings/view.js":
/*!**************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/typings/view.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.DashboardElementType = exports.Position = exports.ViewType = void 0;\r\nvar ViewType;\r\n(function (ViewType) {\r\n    ViewType[\"Table\"] = \"table\";\r\n    ViewType[\"Board\"] = \"board\";\r\n    ViewType[\"Form\"] = \"form\";\r\n    ViewType[\"Document\"] = \"document\";\r\n    ViewType[\"Dashboard\"] = \"dashboard\";\r\n    ViewType[\"SummaryTable\"] = \"summary-table\";\r\n    ViewType[\"ListView\"] = \"list-view\";\r\n    ViewType[\"Calendar\"] = \"calendar\";\r\n})(ViewType = exports.ViewType || (exports.ViewType = {}));\r\nvar Position;\r\n(function (Position) {\r\n    Position[\"Left\"] = \"left\";\r\n    Position[\"Right\"] = \"right\";\r\n})(Position = exports.Position || (exports.Position = {}));\r\nvar DashboardElementType;\r\n(function (DashboardElementType) {\r\n    DashboardElementType[\"Infobox\"] = \"infobox\";\r\n    DashboardElementType[\"LineChart\"] = \"lineChart\";\r\n    DashboardElementType[\"BarChart\"] = \"barChart\";\r\n    DashboardElementType[\"PieChart\"] = \"pieChart\";\r\n    DashboardElementType[\"FunnelChart\"] = \"funnelChart\";\r\n    DashboardElementType[\"Embed\"] = \"embed\";\r\n    DashboardElementType[\"Image\"] = \"image\";\r\n    DashboardElementType[\"Text\"] = \"text\";\r\n})(DashboardElementType = exports.DashboardElementType || (exports.DashboardElementType = {}));\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/typings/view.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/utils/db.js":
/*!**********************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/utils/db.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.resolveColumnValuesAggregation = exports.FilterableRecord = exports.filterRecords = exports.sortRecords = exports.recordValueToText = exports.generateUnknownSelectOption = exports.generateUnknownPerson = exports.getRecordTitle = exports.transformRawRecords = exports.generatePseudoDbRecords = exports.generatePseudoPerson = exports.constructDerivedColumn = exports.constructDb = void 0;\r\nconst db_1 = __webpack_require__(/*! ../typings/db */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\r\nconst string_1 = __webpack_require__(/*! ../methods/string */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/string.js\");\r\nconst color_1 = __webpack_require__(/*! ../typings/color */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/typings/color.js\");\r\nconst common_1 = __webpack_require__(/*! ../typings/common */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/typings/common.js\");\r\nconst object_1 = __webpack_require__(/*! ../methods/object */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/object.js\");\r\nconst date_1 = __webpack_require__(/*! ../methods/date */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/date.js\");\r\nconst compare_1 = __webpack_require__(/*! ../methods/compare */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/compare.js\");\r\nconst array_1 = __webpack_require__(/*! ../methods/array */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/array.js\");\r\nconst number_1 = __webpack_require__(/*! ../methods/number */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/number.js\");\r\nconst onboarding_1 = __webpack_require__(/*! ./onboarding */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/utils/onboarding.js\");\r\nconst derived_1 = __webpack_require__(/*! ./derived */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/utils/derived.js\");\r\nconst constructDb = () => {\r\n    const textColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Text',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n    };\r\n    const numberColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Number',\r\n        type: db_1.DatabaseFieldDataType.Number,\r\n    };\r\n    const option1 = {\r\n        color: color_1.ColorNames.Caledon,\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Option 1',\r\n    };\r\n    const option2 = {\r\n        color: color_1.ColorNames.Brown,\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Option 2',\r\n    };\r\n    const optionsId = [option1.id, option2.id];\r\n    const optionsMap = {};\r\n    optionsMap[option1.id] = option1;\r\n    optionsMap[option2.id] = option2;\r\n    const selectColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        optionIds: optionsId,\r\n        optionsMap: optionsMap,\r\n        title: 'Select',\r\n        type: db_1.DatabaseFieldDataType.Select,\r\n    };\r\n    const multiSelectColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        optionIds: optionsId,\r\n        optionsMap: optionsMap,\r\n        title: 'Multi select',\r\n        type: db_1.DatabaseFieldDataType.Select,\r\n        isMulti: true\r\n    };\r\n    const phoneNumberColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Phone',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Phone\r\n    };\r\n    const emailColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Email',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Email\r\n    };\r\n    const aiColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'AI Intro',\r\n        type: db_1.DatabaseFieldDataType.AI,\r\n        prompt: \"\"\r\n    };\r\n    const checkboxColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Checkbox',\r\n        type: db_1.DatabaseFieldDataType.Checkbox,\r\n    };\r\n    const dateColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Date',\r\n        type: db_1.DatabaseFieldDataType.Date,\r\n    };\r\n    const createdAtColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'CreatedAt',\r\n        type: db_1.DatabaseFieldDataType.CreatedAt,\r\n    };\r\n    const updatedAtColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Updated At',\r\n        type: db_1.DatabaseFieldDataType.UpdatedAt,\r\n    };\r\n    const createdByColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Created By',\r\n        type: db_1.DatabaseFieldDataType.CreatedBy,\r\n    };\r\n    const updatedByColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Updated By',\r\n        type: db_1.DatabaseFieldDataType.UpdatedBy,\r\n    };\r\n    const uuidColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'UUID',\r\n        type: db_1.DatabaseFieldDataType.UUID,\r\n    };\r\n    const personColumn = {\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Person',\r\n        type: db_1.DatabaseFieldDataType.Person,\r\n    };\r\n    const derivedColumn = {\r\n        description: 'Column',\r\n        // icon,\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Derived',\r\n        type: db_1.DatabaseFieldDataType.Derived,\r\n        derivation: ` {{${numberColumn.id}}} + {{${textColumn.id}}}`,\r\n        // returnType: DatabaseFieldDataType.Text,\r\n    };\r\n    const columns = [\r\n        uuidColumn,\r\n        textColumn,\r\n        numberColumn,\r\n        selectColumn,\r\n        multiSelectColumn,\r\n        phoneNumberColumn,\r\n        emailColumn,\r\n        aiColumn,\r\n        derivedColumn,\r\n        checkboxColumn,\r\n        dateColumn,\r\n        createdAtColumn,\r\n        updatedAtColumn,\r\n        createdByColumn,\r\n        updatedByColumn,\r\n        personColumn,\r\n    ];\r\n    const dbDefinition = {\r\n        columnIds: [],\r\n        columnsMap: {},\r\n        uniqueColumnId: emailColumn.id,\r\n        titleFormat: `{{${textColumn.id}}} - {{${numberColumn.id}}}`,\r\n    };\r\n    for (const column of columns) {\r\n        dbDefinition.columnsMap[column.id] = column;\r\n        dbDefinition.columnIds.push(column.id);\r\n    }\r\n    return dbDefinition;\r\n};\r\nexports.constructDb = constructDb;\r\nconst constructDerivedColumn = (derivation) => {\r\n    const derivedColumn = {\r\n        description: 'Column',\r\n        // icon,\r\n        id: (0, string_1.generateUUID)(),\r\n        title: 'Derived',\r\n        type: db_1.DatabaseFieldDataType.Derived,\r\n        derivation: derivation,\r\n        // returnType: DatabaseFieldDataType.Text,\r\n    };\r\n    return derivedColumn;\r\n};\r\nexports.constructDerivedColumn = constructDerivedColumn;\r\nconst generatePseudoPerson = (count) => {\r\n    const firstNames = [\r\n        'Alice',\r\n        'Bob',\r\n        'Charlie',\r\n        'David',\r\n        'Eve',\r\n        'Frank',\r\n        'Grace',\r\n        'Henry',\r\n        'Ivy',\r\n        'Jack',\r\n        'Karen',\r\n        'Liam',\r\n        'Mia',\r\n        'Nora',\r\n        'Oliver',\r\n        'Penelope',\r\n        'Quinn',\r\n        'Riley',\r\n        'Samantha',\r\n        'Thomas',\r\n        'Ursula',\r\n        'Victoria',\r\n        'William',\r\n        'Xavier',\r\n        'Yara',\r\n        'Zachary',\r\n    ];\r\n    const lastNames = [\r\n        'Adams',\r\n        'Brown',\r\n        'Clark',\r\n        'Davis',\r\n        'Edwards',\r\n        'Franklin',\r\n        'Garcia',\r\n        'Hill',\r\n        'Ibrahim',\r\n        'Johnson',\r\n        'Kim',\r\n        'Lee',\r\n        'Martinez',\r\n        'Nguyen',\r\n        \"O'Brien\",\r\n        'Patel',\r\n        'Quinn',\r\n        'Ramirez',\r\n        'Singh',\r\n        'Taylor',\r\n        'Upton',\r\n        'Valdez',\r\n        'Williams',\r\n        'Xu',\r\n        'Yilmaz',\r\n        'Zhang',\r\n    ];\r\n    const persons = [];\r\n    for (let i = 0; i < count; i++) {\r\n        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];\r\n        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];\r\n        const person = {\r\n            firstName,\r\n            lastName,\r\n            id: (0, string_1.generateUUID)(),\r\n            image: {\r\n                type: common_1.ObjectType.Image,\r\n                url: 'https://placehold.co/600x400/EEE/31343C',\r\n            },\r\n            title: `${firstName} ${lastName}`,\r\n        };\r\n        persons.push(person);\r\n    }\r\n    return persons;\r\n};\r\nexports.generatePseudoPerson = generatePseudoPerson;\r\nconst generatePseudoDbRecords = (dbDefinition, count = 10, persons = (0, exports.generatePseudoPerson)(5)) => {\r\n    const records = [];\r\n    for (let i = 0; i < count; i++) {\r\n        const id = (0, string_1.generateUUID)();\r\n        const createdAt = new Date();\r\n        const updatedAt = new Date();\r\n        const createdBy = persons[Math.floor(Math.random() * persons.length)];\r\n        const updatedBy = persons[Math.floor(Math.random() * persons.length)];\r\n        const uniqueValue = `Unique value ${i}`;\r\n        const dataMap = {};\r\n        for (const columnId of dbDefinition.columnIds) {\r\n            const column = dbDefinition.columnsMap[columnId];\r\n            let value;\r\n            const type = column.type;\r\n            if (db_1.AutoGeneratedColumnTypes.includes(type))\r\n                continue;\r\n            switch (type) {\r\n                case db_1.DatabaseFieldDataType.Text:\r\n                    value = `${column.title} ${i}`;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Number:\r\n                    value = i;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Select:\r\n                    const selCol = column;\r\n                    value = selCol.isMulti ? selCol.optionIds : selCol.optionIds[0];\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Checkbox:\r\n                    value = i % 2 === 0;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Date:\r\n                    value = new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString();\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.AI:\r\n                    const val = \"This is the AI value\";\r\n                    value = val;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Linked:\r\n                    value = [\"1\", \"2\", \"3\"];\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.CreatedAt:\r\n                    value = (0, date_1.dateToMySQL)(createdAt);\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.UpdatedAt:\r\n                    value = (0, date_1.dateToMySQL)(updatedAt);\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.CreatedBy:\r\n                    value = createdBy.id;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.UpdatedBy:\r\n                    value = updatedBy.id;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Files:\r\n                    const files = [\r\n                        { id: (0, string_1.generateUUID)(), name: 'file1', link: 'https://placehold.co/600x400/EEE/31343C', type: 'image/jpeg' },\r\n                        { id: (0, string_1.generateUUID)(), name: 'file2', link: 'https://placehold.co/600x400/EEE/31343C', type: 'image/jpeg' },\r\n                        { id: (0, string_1.generateUUID)(), name: 'file3', link: 'https://placehold.co/600x400/EEE/31343C', type: 'image/jpeg' },\r\n                    ];\r\n                    value = files;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Derived:\r\n                    value = column.derivation === 'i * 2' ? i * 2 : i.toString();\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.UUID:\r\n                    value = id;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Person:\r\n                    value = [\r\n                        persons[Math.floor(Math.random() * persons.length)].id,\r\n                        persons[Math.floor(Math.random() * persons.length)].id,\r\n                        'personId',\r\n                    ];\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.ButtonGroup:\r\n                    value = null;\r\n                    break;\r\n                default:\r\n            }\r\n            dataMap[columnId] = value;\r\n        }\r\n        const record = {\r\n            id,\r\n            createdAt: createdAt,\r\n            updatedAt: updatedAt,\r\n            createdById: createdBy.id,\r\n            updatedById: updatedBy.id,\r\n            uniqueValue,\r\n            recordValues: dataMap,\r\n        };\r\n        records.push(record);\r\n    }\r\n    return records;\r\n};\r\nexports.generatePseudoDbRecords = generatePseudoDbRecords;\r\n// interface\r\nconst companyDef = (0, onboarding_1.getCompanyDbDefinition)();\r\nconst contactsDef = (0, onboarding_1.getCustomerDbDefinition)('');\r\nconst companySrcPackageName = getDatabasePackageName(companyDef);\r\nconst contactsSrcPackageName = getDatabasePackageName(contactsDef);\r\nconst transformRawRecords = (dbDefinition, records, persons, linkedDatabases = {}, isContacts = false, defaultTitle = false) => {\r\n    const { columnIds, columnsMap, titleFormat } = dbDefinition;\r\n    const titleColId = dbDefinition.titleColumnId;\r\n    const linkedDbsTitleColMap = {};\r\n    for (const linkedDb of Object.values(linkedDatabases)) {\r\n        let defaultTitle = false;\r\n        let isContacts = false;\r\n        let titleColId = linkedDb.definition.titleColumnId || '';\r\n        const srcPackageName = linkedDb.srcPackageName;\r\n        isContacts = srcPackageName === contactsSrcPackageName;\r\n        if (srcPackageName === companySrcPackageName && !titleColId) {\r\n            titleColId = 'name';\r\n        }\r\n        else if (isContacts && !titleColId) {\r\n            titleColId = 'firstName';\r\n            defaultTitle = true;\r\n        }\r\n        if (!titleColId) {\r\n            for (const column of Object.values(linkedDb.definition.columnsMap)) {\r\n                if (column.type === db_1.DatabaseFieldDataType.Text) {\r\n                    titleColId = column.id;\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        linkedDbsTitleColMap[linkedDb.id] = {\r\n            titleColId,\r\n            defaultTitle,\r\n            isContacts\r\n        };\r\n    }\r\n    return records.map((record) => {\r\n        const createdAt = new Date(record.createdAt);\r\n        const updatedAt = new Date(record.updatedAt);\r\n        const createdBy = persons.find((person) => person.id === record.createdById) || (0, exports.generateUnknownPerson)(record.createdById);\r\n        const updatedBy = persons.find((person) => person.id === record.updatedById) || (0, exports.generateUnknownPerson)(record.updatedById);\r\n        const processedRecord = {\r\n            createdAt,\r\n            createdBy,\r\n            processedRecordValues: {},\r\n            recordValues: record.recordValues,\r\n            uniqueValue: record.uniqueValue,\r\n            updatedAt,\r\n            updatedBy,\r\n            id: record.id,\r\n            valuesText: '',\r\n            title: (0, exports.getRecordTitle)(record, titleColId, defaultTitle, isContacts),\r\n        };\r\n        const textValues = [];\r\n        for (const columnId of columnIds) {\r\n            const column = columnsMap[columnId];\r\n            let rawValue = record.recordValues[columnId];\r\n            let processedValue;\r\n            switch (column.type) {\r\n                case db_1.DatabaseFieldDataType.Text:\r\n                case db_1.DatabaseFieldDataType.AI:\r\n                    processedValue = rawValue;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Linked:\r\n                    const linkedIds = (Array.isArray(rawValue)\r\n                        && rawValue.length > 0\r\n                        && typeof rawValue[0] === 'string' ? rawValue : []);\r\n                    const linked = [];\r\n                    if (column.databaseId && linkedDatabases[column.databaseId]) {\r\n                        for (const linkedId of linkedIds) {\r\n                            if (linkedDatabases[column.databaseId].recordsMap[linkedId]) {\r\n                                const { isContacts, defaultTitle, titleColId } = linkedDbsTitleColMap[column.databaseId];\r\n                                const linkedRecord = linkedDatabases[column.databaseId].recordsMap[linkedId];\r\n                                // const title = linkedRecord.title ? linkedRecord.title :\r\n                                //               isContacts && defaultTitle ? `${String(linkedRecord.recordValues['lastName'] || '')} ${String(linkedRecord.recordValues['firstName'] || '')}`.trim() :\r\n                                //               linkedRecord.recordValues[titleColId] && typeof linkedRecord.recordValues[titleColId] === 'string' ? String(linkedRecord.recordValues[titleColId]) :\r\n                                //               'Untitled'\r\n                                const title = (0, exports.getRecordTitle)(linkedRecord, titleColId, defaultTitle, isContacts);\r\n                                linked.push(({\r\n                                    id: linkedId,\r\n                                    title\r\n                                }));\r\n                            }\r\n                        }\r\n                    }\r\n                    processedValue = linked;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Summarize:\r\n                    const { targetColumnId, linkedDisplayColumnId } = column;\r\n                    // @todo: Work on this later\r\n                    processedValue = '';\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.UUID:\r\n                    processedValue = record.id;\r\n                    rawValue = record.id;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Select:\r\n                    const selectedIds = (Array.isArray(rawValue)\r\n                        && rawValue.length > 0\r\n                        && typeof rawValue[0] === 'string' ? rawValue : []);\r\n                    const selected = [];\r\n                    for (const selectedId1 of selectedIds) {\r\n                        const option = selectedId1\r\n                            ? column.optionsMap[selectedId1] || (0, exports.generateUnknownSelectOption)(selectedId1)\r\n                            : null;\r\n                        if (option)\r\n                            selected.push(option);\r\n                    }\r\n                    processedValue = selected;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Files:\r\n                    const selectedFiles = (Array.isArray(rawValue)\r\n                        && rawValue.length > 0\r\n                        && typeof rawValue[0] === 'object' ?\r\n                        rawValue : []);\r\n                    processedValue = selectedFiles;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Number:\r\n                    const numVal = parseFloat(rawValue);\r\n                    processedValue = isNaN(numVal) ? '' : numVal;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Derived:\r\n                    processedValue = rawValue;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Checkbox:\r\n                    processedValue = !!rawValue;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Date:\r\n                    try {\r\n                        const date = new Date(rawValue);\r\n                        processedValue = (0, date_1.isDateObjValid)(date) ? date.toISOString() : null;\r\n                    }\r\n                    catch (e) {\r\n                        processedValue = null;\r\n                    }\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.CreatedAt:\r\n                    processedValue = createdAt;\r\n                    rawValue = createdAt.toISOString();\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.UpdatedAt:\r\n                    processedValue = updatedAt;\r\n                    rawValue = updatedAt.toISOString();\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.CreatedBy:\r\n                    processedValue = createdBy;\r\n                    rawValue = createdBy.id;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.UpdatedBy:\r\n                    processedValue = updatedBy;\r\n                    rawValue = updatedBy.id;\r\n                    break;\r\n                case db_1.DatabaseFieldDataType.Person:\r\n                    const personIds = (Array.isArray(rawValue)\r\n                        && rawValue.length > 0\r\n                        && typeof rawValue[0] === 'string' ? rawValue : []);\r\n                    const ref = [];\r\n                    for (const id of personIds) {\r\n                        const person1 = persons.find((person) => person.id === id) || (0, exports.generateUnknownPerson)(id);\r\n                        if (person1)\r\n                            ref.push(person1);\r\n                    }\r\n                    processedValue = ref;\r\n                    break;\r\n                default:\r\n                    // We should never get here, but just in case\r\n                    processedValue = null;\r\n            }\r\n            processedRecord.processedRecordValues[columnId] = processedValue;\r\n            processedRecord.recordValues[columnId] = rawValue;\r\n            textValues.push(String((0, exports.recordValueToText)(processedValue)));\r\n        }\r\n        const vars = {};\r\n        for (const key of Object.keys(processedRecord.processedRecordValues)) {\r\n            const val = processedRecord.processedRecordValues[key];\r\n            vars[key] = (0, exports.recordValueToText)(val);\r\n        }\r\n        for (const columnId of columnIds) {\r\n            const column = columnsMap[columnId];\r\n            switch (column.type) {\r\n                case db_1.DatabaseFieldDataType.Derived:\r\n                    processedRecord.processedRecordValues[columnId] = (0, derived_1.evaluateDerived)(column.derivation || '', vars);\r\n                    vars[columnId] = processedRecord.processedRecordValues[columnId];\r\n                    textValues.push(String((0, exports.recordValueToText)(processedRecord.processedRecordValues[columnId])));\r\n                    break;\r\n                default:\r\n            }\r\n        }\r\n        for (const columnId of columnIds) {\r\n            const column = columnsMap[columnId];\r\n            switch (column.type) {\r\n                case db_1.DatabaseFieldDataType.ScannableCode:\r\n                    const value = (0, object_1.substituteVars)(column.derivation || '', vars, undefined, \"curly\");\r\n                    processedRecord.processedRecordValues[columnId] = value;\r\n                    vars[columnId] = processedRecord.processedRecordValues[columnId];\r\n                    textValues.push(String((0, exports.recordValueToText)(processedRecord.processedRecordValues[columnId])));\r\n                    break;\r\n                default:\r\n            }\r\n        }\r\n        if (!processedRecord.title) {\r\n            processedRecord.title = titleFormat ? (0, object_1.substituteVars)(titleFormat, vars, undefined, \"curly\") || 'Untitled' : 'Untitled';\r\n        }\r\n        processedRecord.valuesText = textValues.join(',');\r\n        return processedRecord;\r\n    });\r\n};\r\nexports.transformRawRecords = transformRawRecords;\r\nconst getRecordTitle = (record, titleColId, defaultTitle, isContacts) => {\r\n    return record.title ? record.title :\r\n        isContacts && defaultTitle ? `${String(record.recordValues['lastName'] || '')} ${String(record.recordValues['firstName'] || '')}`.trim() :\r\n            record.recordValues[titleColId] && typeof record.recordValues[titleColId] === 'string' ? String(record.recordValues[titleColId]) :\r\n                '';\r\n};\r\nexports.getRecordTitle = getRecordTitle;\r\nconst generateUnknownPerson = (id) => {\r\n    return {\r\n        firstName: 'Unknown',\r\n        id,\r\n        image: {\r\n            type: common_1.ObjectType.Image,\r\n            url: '',\r\n        },\r\n        lastName: 'Person',\r\n        title: 'Unknown Person'\r\n    };\r\n};\r\nexports.generateUnknownPerson = generateUnknownPerson;\r\nconst generateUnknownSelectOption = (id) => {\r\n    return {\r\n        color: 'Beige',\r\n        id,\r\n        title: 'Unknown'\r\n    };\r\n};\r\nexports.generateUnknownSelectOption = generateUnknownSelectOption;\r\nconst recordValueToText = (value) => {\r\n    if (value === null || value === undefined)\r\n        return '';\r\n    if (value instanceof Date)\r\n        return (0, date_1.dateToMySQL)(value);\r\n    if (Array.isArray(value)) {\r\n        return value.map((v) => (0, exports.recordValueToText)(v)).join(', ');\r\n    }\r\n    const type = typeof value;\r\n    if (type === 'string' || type === 'number' || type === 'boolean')\r\n        return value;\r\n    if (type === 'object')\r\n        return value.title || value.name || '';\r\n    return '';\r\n};\r\nexports.recordValueToText = recordValueToText;\r\nconst sortRecords = (records, processedRecords, sorts, definition) => {\r\n    const indexArray = records.map((_, index) => index);\r\n    indexArray.sort((a, b) => {\r\n        const recordA = processedRecords[a];\r\n        const recordB = processedRecords[b];\r\n        for (const sort of sorts) {\r\n            const { columnId, order } = sort;\r\n            const column = definition.columnsMap[columnId];\r\n            let colType = column === null || column === void 0 ? void 0 : column.type;\r\n            let aValue = (0, exports.recordValueToText)(recordA.processedRecordValues[columnId] || '');\r\n            let bValue = (0, exports.recordValueToText)(recordB.processedRecordValues[columnId] || '');\r\n            if (!colType && Object.values(db_1.MagicColumn).includes(columnId)) {\r\n                switch (columnId) {\r\n                    case db_1.MagicColumn.UUID:\r\n                        aValue = recordA.id;\r\n                        bValue = recordB.id;\r\n                        break;\r\n                    case db_1.MagicColumn.CreatedAt:\r\n                        aValue = new Date(recordA.createdAt).getTime();\r\n                        bValue = new Date(recordB.createdAt).getTime();\r\n                        break;\r\n                    case db_1.MagicColumn.UpdatedAt:\r\n                        aValue = new Date(recordA.updatedAt).getTime();\r\n                        bValue = new Date(recordB.updatedAt).getTime();\r\n                        break;\r\n                    case db_1.MagicColumn.CreatedBy:\r\n                        aValue = (0, exports.recordValueToText)(recordA.createdBy);\r\n                        bValue = (0, exports.recordValueToText)(recordB.createdBy);\r\n                        break;\r\n                    case db_1.MagicColumn.UpdatedBy:\r\n                        aValue = (0, exports.recordValueToText)(recordA.updatedBy);\r\n                        bValue = (0, exports.recordValueToText)(recordB.updatedBy);\r\n                        break;\r\n                }\r\n            }\r\n            else if (colType) {\r\n                switch (colType) {\r\n                    case db_1.DatabaseFieldDataType.Number:\r\n                        aValue = Number(aValue);\r\n                        bValue = Number(bValue);\r\n                        break;\r\n                    case db_1.DatabaseFieldDataType.Checkbox:\r\n                        aValue = !!aValue;\r\n                        bValue = !!bValue;\r\n                        break;\r\n                    case db_1.DatabaseFieldDataType.Date:\r\n                    case db_1.DatabaseFieldDataType.UpdatedAt:\r\n                    case db_1.DatabaseFieldDataType.CreatedAt:\r\n                        aValue = new Date(recordA.processedRecordValues[columnId]).getTime();\r\n                        bValue = new Date(recordB.processedRecordValues[columnId]).getTime();\r\n                        break;\r\n                    default:\r\n                }\r\n            }\r\n            else\r\n                continue;\r\n            if (aValue < bValue) {\r\n                return order === db_1.Sort.Asc ? -1 : 1;\r\n            }\r\n            else if (aValue > bValue) {\r\n                return order === db_1.Sort.Asc ? 1 : -1;\r\n            }\r\n        }\r\n        return 0;\r\n    });\r\n    const sortedRecords = [];\r\n    const sortedProcessedRecords = [];\r\n    for (let index of indexArray) {\r\n        sortedRecords.push(records[index]);\r\n        sortedProcessedRecords.push(processedRecords[index]);\r\n    }\r\n    return {\r\n        records: sortedRecords,\r\n        processedRecords: sortedProcessedRecords\r\n    };\r\n};\r\nexports.sortRecords = sortRecords;\r\nconst filterRecords = (records, processedRecords, filter, definition, currentUserId = '', currentObjectId = '') => {\r\n    const filteredRecords = [];\r\n    const filteredProcessedRecords = [];\r\n    for (let i = 0; i < records.length; i++) {\r\n        const record = records[i];\r\n        const processedRecord = processedRecords[i];\r\n        if (filter.conditions.length === 0) {\r\n            filteredRecords.push(record);\r\n            filteredProcessedRecords.push(processedRecord);\r\n            continue;\r\n        }\r\n        const match = filter.match;\r\n        let isConditionMatched = match === db_1.Match.All;\r\n        for (const condition of filter.conditions) {\r\n            const isMatched = runFilter(condition, record, processedRecord, definition, currentUserId, currentObjectId);\r\n            if (isMatched && match === db_1.Match.Any) {\r\n                isConditionMatched = true;\r\n                break;\r\n            }\r\n            if (!isMatched && match === db_1.Match.All) {\r\n                isConditionMatched = false;\r\n                break;\r\n            }\r\n        }\r\n        if (isConditionMatched) {\r\n            filteredRecords.push(record);\r\n            filteredProcessedRecords.push(processedRecord);\r\n        }\r\n    }\r\n    return {\r\n        records: filteredRecords,\r\n        processedRecords: filteredProcessedRecords\r\n    };\r\n};\r\nexports.filterRecords = filterRecords;\r\nclass FilterableRecord {\r\n}\r\nexports.FilterableRecord = FilterableRecord;\r\nconst runFilter = (condition, record, processedRecord, definition, currentUserId, currentObjectId) => {\r\n    let val1 = '';\r\n    let val2 = '';\r\n    const op = condition.op;\r\n    const defaultVal = true;\r\n    if (!condition.columnId)\r\n        return defaultVal;\r\n    if (!compare_1.SingleOperandOperators.includes(op) && (typeof condition.value !== 'number' && !condition.value)) {\r\n        return true;\r\n    }\r\n    let colValue = processedRecord.recordValues[condition.columnId];\r\n    let condValue = condition.value;\r\n    const column = definition.columnsMap[condition.columnId];\r\n    let colType = column === null || column === void 0 ? void 0 : column.type;\r\n    if (!colType && Object.values(db_1.MagicColumn).includes(condition.columnId)) {\r\n        colType = condition.columnId;\r\n        switch (condition.columnId) {\r\n            case db_1.MagicColumn.UUID:\r\n                colValue = record.id;\r\n                break;\r\n            case db_1.MagicColumn.CreatedAt:\r\n                colValue = new Date(record.createdAt).toISOString();\r\n                break;\r\n            case db_1.MagicColumn.UpdatedAt:\r\n                colValue = new Date(record.updatedAt).toISOString();\r\n                break;\r\n            case db_1.MagicColumn.CreatedBy:\r\n                colValue = record.createdById;\r\n                break;\r\n            case db_1.MagicColumn.UpdatedBy:\r\n                colValue = record.updatedById;\r\n                break;\r\n        }\r\n    }\r\n    if (!colType)\r\n        return defaultVal;\r\n    switch (colType) {\r\n        case db_1.DatabaseFieldDataType.AI:\r\n        case db_1.DatabaseFieldDataType.Text:\r\n        case db_1.DatabaseFieldDataType.Derived:\r\n            val1 = String(processedRecord.processedRecordValues[condition.columnId] || '');\r\n            val2 = String(condValue);\r\n            break;\r\n        case db_1.DatabaseFieldDataType.UUID:\r\n            val1 = String(colValue || '');\r\n            val2 = String(condValue);\r\n            break;\r\n        case db_1.DatabaseFieldDataType.Person:\r\n        case db_1.DatabaseFieldDataType.CreatedBy:\r\n        case db_1.DatabaseFieldDataType.UpdatedBy:\r\n            val1 = valToArray(colValue);\r\n            val2 = valToArray(condValue).map(v => {\r\n                if (v === db_1.CurrentPerson.id)\r\n                    return currentUserId;\r\n                return v;\r\n            });\r\n            break;\r\n        case db_1.DatabaseFieldDataType.Linked:\r\n            val1 = valToArray(colValue);\r\n            val2 = valToArray(condValue).map(v => {\r\n                if (v === db_1.CurrentObject)\r\n                    return currentObjectId;\r\n                return v;\r\n            });\r\n            break;\r\n        // val1 = recordValueToText(colValue as FileItem[])\r\n        // val2 = condValue\r\n        // break;\r\n        // case DatabaseFieldDataType.Summarize:\r\n        case db_1.DatabaseFieldDataType.Select:\r\n            val1 = valToArray(colValue);\r\n            val2 = valToArray(condValue);\r\n            break;\r\n        case db_1.DatabaseFieldDataType.Number:\r\n            val1 = Number(colValue);\r\n            val2 = Number(condValue);\r\n            break;\r\n        case db_1.DatabaseFieldDataType.Checkbox:\r\n            val1 = !!colValue;\r\n            val2 = !!condValue;\r\n            break;\r\n        case db_1.DatabaseFieldDataType.CreatedAt:\r\n        case db_1.DatabaseFieldDataType.UpdatedAt:\r\n        case db_1.DatabaseFieldDataType.Date:\r\n            val1 = typeof colValue === 'string' ? colValue : (0, date_1.isDateValid)(String(colValue)) ? new Date(colValue).toISOString() : '';\r\n            try {\r\n                val2 = Object.values(date_1.RelativeDate).includes(condValue) ? (0, date_1.resolveRelativeDate)(condValue).toISOString() : new Date(condValue).toISOString();\r\n            }\r\n            catch (e) {\r\n                val2 = new Date(0).toISOString();\r\n            }\r\n            break;\r\n        default:\r\n            return true;\r\n    }\r\n    return matchFilterCondition(colType, val1, op, val2);\r\n};\r\n/*\r\nconst textOptions: SelectData[] = [\r\n    CompareOperator.Contains,\r\n    CompareOperator.DoesNotContain,\r\n    CompareOperator.Equals,\r\n    CompareOperator.NotEquals,\r\n    CompareOperator.StartsWith,\r\n    CompareOperator.IsEmpty,\r\n    CompareOperator.IsNotEmpty,\r\n  ].map((o) => valueToSelect(o));\r\n\r\n  const numberOptions: SelectData[] = [\r\n    CompareOperator.Equals,\r\n    CompareOperator.NotEquals,\r\n    CompareOperator.GreaterThan,\r\n    CompareOperator.LessThan,\r\n    CompareOperator.IsEmpty,\r\n    CompareOperator.IsNotEmpty,\r\n  ].map((o) => valueToSelect(o));\r\n\r\n  const dateOptions: SelectData[] = [\r\n    CompareOperator.IsSameDayAs,\r\n    CompareOperator.IsEarlierThan,\r\n    CompareOperator.IsLaterThan,\r\n    CompareOperator.IsEmpty,\r\n    CompareOperator.IsNotEmpty,\r\n  ].map((o) => valueToSelect(o));\r\n\r\n  const tagsOptions: SelectData[] = [\r\n    CompareOperator.Equals,\r\n    CompareOperator.NotEquals,\r\n    CompareOperator.Contains,\r\n    CompareOperator.DoesNotContain,\r\n    CompareOperator.IsEmpty,\r\n    CompareOperator.IsNotEmpty,\r\n  ].map((o) => valueToSelect(o));\r\n\r\n  const checkboxOptions: SelectData[] = [\r\n    CompareOperator.IsChecked,\r\n    CompareOperator.IsNotChecked,\r\n  ].map((o) => valueToSelect(o));\r\n\r\n  */\r\nconst matchFilterCondition = (colType, col1, op, col2) => {\r\n    switch (colType) {\r\n        case db_1.DatabaseFieldDataType.Checkbox:\r\n            return (0, compare_1.compareFields)(col1 ? col1 : '', op, '');\r\n        case db_1.DatabaseFieldDataType.Text:\r\n        case db_1.DatabaseFieldDataType.Derived:\r\n        case db_1.DatabaseFieldDataType.UUID:\r\n        case db_1.DatabaseFieldDataType.Summarize:\r\n        case db_1.DatabaseFieldDataType.AI:\r\n            return (0, compare_1.compareFields)(col1, op, col2);\r\n        case db_1.DatabaseFieldDataType.CreatedAt:\r\n        case db_1.DatabaseFieldDataType.UpdatedAt:\r\n        case db_1.DatabaseFieldDataType.Date:\r\n            return (0, compare_1.compareFields)(col1, op, col2);\r\n        case db_1.DatabaseFieldDataType.Number:\r\n            return (0, compare_1.compareFields)(col1, op, col2);\r\n        case db_1.DatabaseFieldDataType.Select:\r\n        case db_1.DatabaseFieldDataType.Person:\r\n        case db_1.DatabaseFieldDataType.CreatedBy:\r\n        case db_1.DatabaseFieldDataType.UpdatedBy:\r\n        case db_1.DatabaseFieldDataType.Linked:\r\n            switch (op) {\r\n                case compare_1.CompareOperator.Equals:\r\n                    return (0, array_1.arraysHaveSameElements)(valToArray(col1), valToArray(col2));\r\n                case compare_1.CompareOperator.NotEquals:\r\n                    return !(0, array_1.arraysHaveSameElements)(valToArray(col1), valToArray(col2));\r\n                case compare_1.CompareOperator.Contains:\r\n                    return (0, array_1.arrayContains)(valToArray(col1), valToArray(col2));\r\n                case compare_1.CompareOperator.DoesNotContain:\r\n                    return !(0, array_1.arrayContains)(valToArray(col1), valToArray(col2));\r\n                case compare_1.CompareOperator.IsEmpty:\r\n                    return valToArray(col1).length === 0;\r\n                case compare_1.CompareOperator.IsNotEmpty:\r\n                    return valToArray(col1).length > 0;\r\n                case compare_1.CompareOperator.IsAnyOf:\r\n                    return (0, array_1.arrayContainsAnyOf)(valToArray(col1), valToArray(col2));\r\n                case compare_1.CompareOperator.IsNoneOf:\r\n                    return (0, array_1.arrayContainsNoneOf)(valToArray(col1), valToArray(col2));\r\n                default:\r\n                    return false;\r\n            }\r\n    }\r\n};\r\nconst valToArray = (val) => {\r\n    if (Array.isArray(val)) {\r\n        return val.map(v => String(v));\r\n    }\r\n    return val ? [String(val)] : [];\r\n};\r\nconst resolveColumnValuesAggregation = (values, aggregateFunction) => {\r\n    let isArray = false;\r\n    const tagValues = [];\r\n    let emptyCount = 0;\r\n    for (const value of values) {\r\n        const isValArray = Array.isArray(value);\r\n        isArray = isArray || isValArray;\r\n        if (isValArray) {\r\n            tagValues.push(...value);\r\n        }\r\n        else\r\n            tagValues.push(value);\r\n        if (value === undefined || value === null || value === false || !String(value).trim()) {\r\n            emptyCount += 1;\r\n        }\r\n        else if (isValArray && Array.isArray(value) && value.length === 0) {\r\n            emptyCount += 1;\r\n        }\r\n    }\r\n    const numericValues = values.map((value) => Number(value)).filter((value) => !isNaN(value));\r\n    const uniqueValues = isArray ? (0, array_1.arrayDeDuplicate)(tagValues) : (0, array_1.arrayDeDuplicate)(values);\r\n    switch (aggregateFunction) {\r\n        case number_1.ShowValuesAggregateFunction.ShowOriginal:\r\n            return values;\r\n        case number_1.ShowValuesAggregateFunction.ShowUnique:\r\n            return uniqueValues;\r\n        case number_1.CountAggregateFunction.CountAll:\r\n            return values.length;\r\n        case number_1.CountAggregateFunction.CountValues:\r\n            return isArray ? tagValues.length : values.length;\r\n        case number_1.CountAggregateFunction.CountUnique:\r\n            return uniqueValues.length;\r\n        case number_1.CountAggregateFunction.CountEmpty:\r\n            return emptyCount;\r\n        case number_1.CountAggregateFunction.CountNotEmpty:\r\n            return values.length - emptyCount;\r\n        case number_1.PercentAggregateFunction.PercentEmpty:\r\n            return emptyCount / values.length * 100;\r\n        case number_1.PercentAggregateFunction.PercentNotEmpty:\r\n            return (values.length - emptyCount) / values.length * 100;\r\n        case number_1.CheckboxAggregateFunction.CountChecked:\r\n            return values.length - emptyCount;\r\n        case number_1.CheckboxAggregateFunction.CountUnchecked:\r\n            return emptyCount;\r\n        case number_1.CheckPercentAggregateFunction.PercentChecked:\r\n            return (values.length - emptyCount) / values.length * 100;\r\n        case number_1.CheckPercentAggregateFunction.PercentNotChecked:\r\n            return emptyCount / values.length * 100;\r\n        case number_1.DateAggregateFunction.DateRange:\r\n        case number_1.DateAggregateFunction.EarliestDate:\r\n        case number_1.DateAggregateFunction.LatestDate:\r\n            // const dates: Date[] = []\r\n            let earliestDate;\r\n            let latestDate;\r\n            for (const value of values) {\r\n                if (!value)\r\n                    continue;\r\n                const date = new Date(String(value));\r\n                if ((0, date_1.isDateObjValid)(date)) {\r\n                    // dates.push(date)\r\n                    if (!earliestDate || date < earliestDate)\r\n                        earliestDate = date;\r\n                    if (!latestDate || date > latestDate)\r\n                        latestDate = date;\r\n                }\r\n            }\r\n            if (aggregateFunction === number_1.DateAggregateFunction.DateRange && earliestDate && latestDate)\r\n                return [earliestDate, latestDate];\r\n            if (aggregateFunction === number_1.DateAggregateFunction.EarliestDate && earliestDate)\r\n                return [earliestDate];\r\n            if (aggregateFunction === number_1.DateAggregateFunction.LatestDate && latestDate)\r\n                return [latestDate];\r\n            return null;\r\n        case number_1.NumberAggregateFunction.Sum:\r\n            return numericValues.reduce((sum, value) => sum + value, 0);\r\n        case number_1.NumberAggregateFunction.Min:\r\n            return Math.min(...numericValues);\r\n        case number_1.NumberAggregateFunction.Max:\r\n            return Math.max(...numericValues);\r\n        case number_1.NumberAggregateFunction.Average:\r\n            if (numericValues.length === 0) {\r\n                return 0; // Handle division by zero\r\n            }\r\n            const sum = numericValues.reduce((acc, value) => acc + value, 0);\r\n            return sum / numericValues.length;\r\n        case number_1.NumberAggregateFunction.Range:\r\n            if (numericValues.length === 0)\r\n                return 0;\r\n            const min = Math.min(...numericValues);\r\n            const max = Math.max(...numericValues);\r\n            return max - min;\r\n        default:\r\n            throw new Error(`Unsupported aggregate function: ${aggregateFunction}`);\r\n    }\r\n};\r\nexports.resolveColumnValuesAggregation = resolveColumnValuesAggregation;\r\nfunction getDatabasePackageName(versioned) {\r\n    const { domain, versionName, templateName, versionNumber } = versioned;\r\n    return `${domain}.${templateName}`;\r\n}\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/utils/db.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/utils/derived.js":
/*!***************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/utils/derived.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\r\nvar __importDefault = (this && this.__importDefault) || function (mod) {\r\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\r\n};\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.evaluateUnaryExpression = exports.unaryExpressionMap = exports.callExpressionFunction = exports.expressionFunctionMap = exports.binaryExpressionMap = exports.evaluateBinaryExpression = exports.evaluateExpression = exports.prepareDerived = exports.evaluateDerived = void 0;\r\nconst object_1 = __webpack_require__(/*! ../methods/object */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/object.js\");\r\nconst jsep_1 = __importDefault(__webpack_require__(/*! jsep */ \"(ssr)/./node_modules/jsep/dist/cjs/jsep.cjs.js\"));\r\nconst date_1 = __webpack_require__(/*! ../methods/date */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/methods/date.js\");\r\nconst evaluateDerived = (derivation, vars, bubbleException = false) => {\r\n    let finalValue = \"\";\r\n    const prepared = (0, exports.prepareDerived)(derivation, vars);\r\n    try {\r\n        const expression = (0, jsep_1.default)(prepared.derivation);\r\n        finalValue = (0, exports.evaluateExpression)(expression, prepared.vars);\r\n    }\r\n    catch (err) {\r\n        if (bubbleException) {\r\n            throw new Error(`Error evaluating formula: ${err.message}`);\r\n        }\r\n    }\r\n    return finalValue;\r\n};\r\nexports.evaluateDerived = evaluateDerived;\r\nconst prepareDerived = (derivation, vars) => {\r\n    // Here we leave vars intact.\r\n    const newVars = Object.assign({}, vars);\r\n    // Replace every placeholder with ___global_var___['originalKey']\r\n    const newDerivation = derivation.replace(/{{([^}]*)}}/g, (_, placeholder) => {\r\n        const trimmed = placeholder.trim();\r\n        return `___global_var___['${trimmed}']`;\r\n    });\r\n    return { derivation: newDerivation, vars: newVars };\r\n};\r\nexports.prepareDerived = prepareDerived;\r\nconst evaluateExpression = (expression, vars) => {\r\n    // export type ExpressionType =\r\n    //     'Compound'\r\n    //     | 'SequenceExpression'\r\n    //     | 'Identifier'\r\n    //     | 'MemberExpression'\r\n    //     | 'Literal'\r\n    //     | 'ThisExpression'\r\n    //     | 'CallExpression'\r\n    //     | 'UnaryExpression'\r\n    //     | 'BinaryExpression'\r\n    //     | 'ConditionalExpression'\r\n    //     | 'ArrayExpression';\r\n    var _a;\r\n    const name = expression.name;\r\n    const type = expression.type;\r\n    switch (type) {\r\n        case \"Literal\":\r\n            return expression.value;\r\n        case \"Identifier\": {\r\n            if (expression.name === 'undefined')\r\n                return undefined;\r\n            // return recordValueToText((vars[name] ?? '') as DatabaseColumnReturnValue);\r\n            return (_a = vars[name]) !== null && _a !== void 0 ? _a : '';\r\n        }\r\n        case \"BinaryExpression\": {\r\n            const binaryExpression = expression;\r\n            const left = (0, exports.evaluateExpression)(binaryExpression.left, vars);\r\n            const right = (0, exports.evaluateExpression)(binaryExpression.right, vars);\r\n            return (0, exports.evaluateBinaryExpression)(left, right, binaryExpression.operator);\r\n        }\r\n        case \"CallExpression\": {\r\n            const callExpression = expression;\r\n            return callExpressionFunction(callExpression.callee.name, ...callExpression.arguments.map((arg) => (0, exports.evaluateExpression)(arg, vars)));\r\n        }\r\n        case \"ArrayExpression\": {\r\n            const arrayExpression = expression;\r\n            return arrayExpression.elements.map((element) => (0, exports.evaluateExpression)(element, vars));\r\n        }\r\n        case \"MemberExpression\": {\r\n            const memberExpression = expression;\r\n            return resolveMemberExpression(memberExpression, vars);\r\n        }\r\n        case \"UnaryExpression\": {\r\n            const unaryExpression = expression;\r\n            if (!unaryExpression.argument) {\r\n                throw new Error(`Missing unaryOp argument at character ${unaryExpression.start || 'unknown'}`);\r\n            }\r\n            const arg = (0, exports.evaluateExpression)(unaryExpression.argument, vars);\r\n            return (0, exports.evaluateUnaryExpression)(unaryExpression.operator, arg);\r\n        }\r\n        default:\r\n            throw new Error(`Unsupported expression type: ${expression.type}`);\r\n    }\r\n};\r\nexports.evaluateExpression = evaluateExpression;\r\nfunction resolveMemberExpression(node, vars) {\r\n    var _a;\r\n    // Evaluate the left-hand side of the member expression.\r\n    const obj = (0, exports.evaluateExpression)(node.object, vars);\r\n    console.log(\"Resolve member expression\", { obj, node, vars });\r\n    // If the object is our special global marker, use the original key from vars.\r\n    if (node.object.type === \"Identifier\" && node.object.name === \"___global_var___\") {\r\n        const key = String((_a = (node.property.type === \"Literal\" ? node.property.value : node.property.name)) !== null && _a !== void 0 ? _a : '');\r\n        return (0, object_1.getSubstituteVarValue)(vars, key);\r\n    }\r\n    // Otherwise, handle the member expression normally.\r\n    if (node.property.type === \"Identifier\") {\r\n        // @ts-ignore\r\n        return obj[node.property.name]; // Resolve property access like myObj.nested\r\n    }\r\n    else if (node.property.type === \"Literal\") {\r\n        // @ts-ignore\r\n        return obj[node.property.value]; // Handle cases like obj[\"key\"]\r\n    }\r\n    else {\r\n        throw new Error(`Unsupported property type in MemberExpression`);\r\n    }\r\n}\r\nconst evaluateBinaryExpression = (left, right, operator) => {\r\n    const binaryExpr = exports.binaryExpressionMap[operator];\r\n    if (!binaryExpr) {\r\n        throw new Error(`Unsupported operator: ${operator}`);\r\n    }\r\n    return binaryExpr.callback(left, right);\r\n};\r\nexports.evaluateBinaryExpression = evaluateBinaryExpression;\r\nconst binaryExpressions = [\r\n    {\r\n        key: \"+\",\r\n        callback: (left, right) => left + right,\r\n        description: \"Adds two numbers or concatenates two strings.\",\r\n        example: `5 + 3 = 8; \"hello\" + \"world\" = \"helloworld\"`,\r\n    },\r\n    {\r\n        key: \"-\",\r\n        callback: (left, right) => left - right,\r\n        description: \"Subtracts the right number from the left number.\",\r\n        example: `10 - 4 = 6`,\r\n    },\r\n    {\r\n        key: \"*\",\r\n        callback: (left, right) => left * right,\r\n        description: \"Multiplies two numbers.\",\r\n        example: `6 * 2 = 12`,\r\n    },\r\n    {\r\n        key: \"/\",\r\n        callback: (left, right) => {\r\n            if (right === 0)\r\n                throw new Error(\"Division by zero\");\r\n            return left / right;\r\n        },\r\n        description: \"Divides the left number by the right number.\",\r\n        example: `15 / 3 = 5`,\r\n    },\r\n    {\r\n        key: \"%\",\r\n        callback: (left, right) => left % right,\r\n        description: \"Returns the remainder of dividing the left number by the right number.\",\r\n        example: `10 % 3 = 1`,\r\n    },\r\n    {\r\n        key: \"==\",\r\n        callback: (left, right) => left == right,\r\n        description: \"Checks if the left value is equal to the right value (loose equality).\",\r\n        example: `5 == \"5\" = true`,\r\n    },\r\n    {\r\n        key: \"!=\",\r\n        callback: (left, right) => left != right,\r\n        description: \"Checks if the left value is not equal to the right value (loose inequality).\",\r\n        example: `5 != \"6\" = true`,\r\n    },\r\n    {\r\n        key: \">\",\r\n        callback: (left, right) => left > right,\r\n        description: \"Checks if the left value is greater than the right value.\",\r\n        example: `7 > 4 = true`,\r\n    },\r\n    {\r\n        key: \"<\",\r\n        callback: (left, right) => left < right,\r\n        description: \"Checks if the left value is less than the right value.\",\r\n        example: `3 < 8 = true`,\r\n    },\r\n    {\r\n        key: \">=\",\r\n        callback: (left, right) => left >= right,\r\n        description: \"Checks if the left value is greater than or equal to the right value.\",\r\n        example: `5 >= 5 = true`,\r\n    },\r\n    {\r\n        key: \"<=\",\r\n        callback: (left, right) => left <= right,\r\n        description: \"Checks if the left value is less than or equal to the right value.\",\r\n        example: `4 <= 6 = true`,\r\n    },\r\n    {\r\n        key: \"&&\",\r\n        callback: (left, right) => left && right,\r\n        description: \"Returns true if both the left and right values are truthy.\",\r\n        example: `true && true = true`,\r\n    },\r\n    {\r\n        key: \"||\",\r\n        callback: (left, right) => left || right,\r\n        description: \"Returns true if either the left or right value is truthy.\",\r\n        example: `false || true = true`,\r\n    },\r\n];\r\n// Create a registry (map) for quick lookup by operator\r\nexports.binaryExpressionMap = {};\r\nbinaryExpressions.forEach((expr) => {\r\n    exports.binaryExpressionMap[expr.key] = expr;\r\n});\r\n// Define all functions as an array of FunctionalExpression.\r\nconst expressions = [\r\n    // Logical / Conditional Functions\r\n    {\r\n        key: \"if\",\r\n        callback: (condition, trueValue, falseValue) => condition ? trueValue : falseValue,\r\n        description: \"Evaluates a condition and returns one of two values based on the condition.\",\r\n        example: `if(true, \"yes\", \"no\") = \"yes\"`,\r\n    },\r\n    {\r\n        key: \"and\",\r\n        callback: (...args) => args.every(Boolean),\r\n        description: \"Returns true if all arguments are truthy.\",\r\n        example: `and(true, true) = true`,\r\n    },\r\n    {\r\n        key: \"or\",\r\n        callback: (...args) => args.some(Boolean),\r\n        description: \"Returns true if at least one argument is truthy.\",\r\n        example: `or(false, true) = true`,\r\n    },\r\n    {\r\n        key: \"not\",\r\n        callback: (value) => !value,\r\n        description: \"Returns the logical negation of the given boolean value.\",\r\n        example: `not(true) = false`,\r\n    },\r\n    // Math Functions\r\n    {\r\n        key: \"abs\",\r\n        callback: Math.abs,\r\n        description: \"Returns the absolute value of a number.\",\r\n        example: `abs(-5) = 5`,\r\n    },\r\n    {\r\n        key: \"round\",\r\n        callback: Math.round,\r\n        description: \"Rounds a number to the nearest integer.\",\r\n        example: `round(4.5) = 5`,\r\n    },\r\n    {\r\n        key: \"floor\",\r\n        callback: Math.floor,\r\n        description: \"Rounds a number down to the nearest integer.\",\r\n        example: `floor(4.9) = 4`,\r\n    },\r\n    {\r\n        key: \"ceil\",\r\n        callback: Math.ceil,\r\n        description: \"Rounds a number up to the nearest integer.\",\r\n        example: `ceil(4.1) = 5`,\r\n    },\r\n    {\r\n        key: \"sum\",\r\n        callback: (...args) => args.reduce((a, b) => a + b, 0),\r\n        description: \"Calculates the sum of the provided numbers.\",\r\n        example: `sum(1, 2, 3) = 6`,\r\n    },\r\n    {\r\n        key: \"avg\",\r\n        callback: (...args) => args.length ? args.reduce((a, b) => a + b, 0) / args.length : 0,\r\n        description: \"Calculates the average of the provided numbers.\",\r\n        example: `avg(2, 4, 6) = 4`,\r\n    },\r\n    {\r\n        key: \"min\",\r\n        callback: Math.min,\r\n        description: \"Returns the smallest number among the provided arguments.\",\r\n        example: `min(3, 1, 2) = 1`,\r\n    },\r\n    {\r\n        key: \"max\",\r\n        callback: Math.max,\r\n        description: \"Returns the largest number among the provided arguments.\",\r\n        example: `max(3, 1, 2) = 3`,\r\n    },\r\n    {\r\n        key: \"pow\",\r\n        callback: Math.pow,\r\n        description: \"Calculates the power of a number (base raised to exponent).\",\r\n        example: `pow(2, 3) = 8`,\r\n    },\r\n    {\r\n        key: \"mod\",\r\n        callback: (a, b) => a % b,\r\n        description: \"Returns the remainder of division of two numbers.\",\r\n        example: `mod(10, 3) = 1`,\r\n    },\r\n    {\r\n        key: \"sqrt\",\r\n        callback: Math.sqrt,\r\n        description: \"Returns the square root of a number.\",\r\n        example: `sqrt(16) = 4`,\r\n    },\r\n    // String / Collection Functions\r\n    {\r\n        key: \"concat\",\r\n        callback: (...args) => args.join(\"\"),\r\n        description: \"Concatenates multiple strings together.\",\r\n        example: `concat(\"Hello\", \" \", \"World\") = \"Hello World\"`,\r\n    },\r\n    {\r\n        key: \"toUpperCase\",\r\n        callback: (str) => str.toUpperCase(),\r\n        description: \"Converts a string to upper case.\",\r\n        example: `toUpperCase(\"hello\") = \"HELLO\"`,\r\n    },\r\n    {\r\n        key: \"toLowerCase\",\r\n        callback: (str) => str.toLowerCase(),\r\n        description: \"Converts a string to lower case.\",\r\n        example: `toLowerCase(\"HELLO\") = \"hello\"`,\r\n    },\r\n    {\r\n        key: \"trim\",\r\n        callback: (str) => str.trim(),\r\n        description: \"Trims whitespace from both ends of a string.\",\r\n        example: `trim(\"  hello  \") = \"hello\"`,\r\n    },\r\n    {\r\n        key: \"length\",\r\n        callback: (input) => {\r\n            if (input == null)\r\n                return 0;\r\n            const inputType = typeof input;\r\n            if (inputType === \"string\" || inputType === \"number\") {\r\n                return String(input).trim().length;\r\n            }\r\n            if (Array.isArray(input)) {\r\n                return input.length;\r\n            }\r\n            if (inputType === \"object\") {\r\n                return Object.keys(input).length;\r\n            }\r\n            return 0;\r\n        },\r\n        description: \"Returns the length of a string (trimmed), a number (as a string), an array, or an object's keys.\",\r\n        example: `length(\" hello \") = 5`,\r\n    },\r\n    {\r\n        key: \"first\",\r\n        callback: (arr) => arr[0],\r\n        description: \"Returns the first element of an array.\",\r\n        example: `first([1, 2, 3]) = 1`,\r\n    },\r\n    {\r\n        key: \"last\",\r\n        callback: (arr) => arr[arr.length - 1],\r\n        description: \"Returns the last element of an array.\",\r\n        example: `last([1, 2, 3]) = 3`,\r\n    },\r\n    {\r\n        key: \"reverse\",\r\n        callback: (arr) => [...arr].reverse(),\r\n        description: \"Returns a new array with the elements in reverse order.\",\r\n        example: `reverse([1, 2, 3]) = [3, 2, 1]`,\r\n    },\r\n    // Date Functions – input is a date string, output is in ISO format.\r\n    {\r\n        key: \"datePlusDays\",\r\n        callback: (dateStr, days) => {\r\n            const date = new Date(dateStr);\r\n            date.setDate(date.getDate() + days);\r\n            return date.toISOString();\r\n        },\r\n        description: \"Adds a specified number of days to the given date string and returns the result in ISO format.\",\r\n        example: `datePlusDays(\"2021-01-01\", 5) = \"2021-01-06\"`,\r\n    },\r\n    {\r\n        key: \"datePlusMinutes\",\r\n        callback: (dateStr, minutes) => {\r\n            const date = new Date(dateStr);\r\n            date.setMinutes(date.getMinutes() + minutes);\r\n            return date.toISOString();\r\n        },\r\n        description: \"Adds a specified number of minutes to the given date string and returns the result in ISO format.\",\r\n        example: `datePlusMinutes(\"2021-01-01\", 30) = \"2021-01-01T00:30:00.000Z\"`,\r\n    },\r\n    {\r\n        key: \"datePlusSeconds\",\r\n        callback: (dateStr, seconds) => {\r\n            const date = new Date(dateStr);\r\n            date.setSeconds(date.getSeconds() + seconds);\r\n            return date.toISOString();\r\n        },\r\n        description: \"Adds a specified number of seconds to the given date string and returns the result in ISO format.\",\r\n        example: `datePlusSeconds(\"2021-01-01\", 45) = \"2021-01-01T00:00:45.000Z\"`,\r\n    },\r\n    {\r\n        key: \"datePlusHours\",\r\n        callback: (dateStr, hours) => {\r\n            const date = new Date(dateStr);\r\n            date.setHours(date.getHours() + hours);\r\n            return date.toISOString();\r\n        },\r\n        description: \"Adds a specified number of hours to the given date string and returns the result in ISO format.\",\r\n        example: `datePlusHours(\"2021-01-01\", 3) = \"2021-01-01T03:00:00.000Z\"`,\r\n    },\r\n    {\r\n        key: \"datePlusMonths\",\r\n        callback: (dateStr, months) => {\r\n            const date = new Date(dateStr);\r\n            date.setUTCMonth(date.getUTCMonth() + months);\r\n            date.setUTCHours(0, 0, 0, 0); // Reset time to 00:00:00.000 UTC\r\n            return date.toISOString();\r\n        },\r\n        description: \"Adds a specified number of months to the given date string and returns the result in ISO format.\",\r\n        example: `datePlusMonths(\"2021-01-01\", 2) = \"2021-03-01\"`,\r\n    },\r\n    {\r\n        key: \"datePlusWeeks\",\r\n        callback: (dateStr, weeks) => {\r\n            const date = new Date(dateStr);\r\n            date.setDate(date.getDate() + weeks * 7);\r\n            return date.toISOString();\r\n        },\r\n        description: \"Adds a specified number of weeks to the given date string and returns the result in ISO format.\",\r\n        example: `datePlusWeeks(\"2021-01-01\", 1) = \"2021-01-08\"`,\r\n    },\r\n    {\r\n        key: \"isBefore\",\r\n        callback: (dateStr1, dateStr2) => {\r\n            const d1 = new Date(dateStr1);\r\n            const d2 = new Date(dateStr2);\r\n            return d1.getTime() < d2.getTime();\r\n        },\r\n        description: \"Returns true if the first date is before the second date.\",\r\n        example: `isBefore(\"2021-01-01\", \"2021-01-02\") = true`,\r\n    },\r\n    {\r\n        key: \"isAfter\",\r\n        callback: (dateStr1, dateStr2) => {\r\n            const d1 = new Date(dateStr1);\r\n            const d2 = new Date(dateStr2);\r\n            return d1.getTime() > d2.getTime();\r\n        },\r\n        description: \"Returns true if the first date is after the second date.\",\r\n        example: `isAfter(\"2021-01-02\", \"2021-01-01\") = true`,\r\n    },\r\n    {\r\n        key: \"isSameDayAs\",\r\n        callback: (dateStr1, dateStr2) => {\r\n            const d1 = new Date(dateStr1);\r\n            const d2 = new Date(dateStr2);\r\n            return (0, date_1.isSameDay)(d1, d2);\r\n        },\r\n        description: \"Returns true if the two dates fall on the same calendar day.\",\r\n        example: `isSameDayAs(\"2021-01-01\", \"2021-01-01T23:59:59.000Z\") = true`,\r\n    },\r\n    {\r\n        key: \"dayOfWeek\",\r\n        callback: (dateStr) => {\r\n            const date = new Date(dateStr);\r\n            return date.getUTCDay();\r\n        },\r\n        description: \"Returns the day of the week for the given date string (0 for Sunday, 6 for Saturday).\",\r\n        example: `dayOfWeek(\"2021-01-01\") = 5`,\r\n    },\r\n    {\r\n        key: \"dateOfMonth\",\r\n        callback: (dateStr) => {\r\n            const date = new Date(dateStr);\r\n            console.log(\"date from dateOfMonth\", date);\r\n            return date.getUTCDate();\r\n        },\r\n        description: \"Returns the day of the month for the given date string.\",\r\n        example: `dateOfMonth(\"2021-01-01\") = 1`,\r\n    },\r\n    // Array and Object Specific Functions\r\n    {\r\n        key: \"itemAt\",\r\n        callback: (collection, indexOrKey) => {\r\n            if (Array.isArray(collection)) {\r\n                return collection[indexOrKey];\r\n            }\r\n            else if (typeof collection === \"object\" && collection !== null) {\r\n                return collection[indexOrKey];\r\n            }\r\n            return undefined;\r\n        },\r\n        description: \"Returns the element at a given index if the input is an array, or the value for the given key if the input is an object.\",\r\n        example: `itemAt([10,20,30], 1) = 20; itemAt({ a: 100, b: 200 }, \"b\") = 200`,\r\n    },\r\n    {\r\n        key: \"keys\",\r\n        callback: (obj) => {\r\n            if (typeof obj === \"object\" && !Array.isArray(obj) && obj !== null) {\r\n                return Object.keys(obj);\r\n            }\r\n            return [];\r\n        },\r\n        description: \"Returns an array of keys from the given object.\",\r\n        example: `keys({ a: 1, b: 2 }) = [\"a\", \"b\"]`,\r\n    },\r\n    {\r\n        key: \"values\",\r\n        callback: (obj) => {\r\n            if (typeof obj === \"object\" && !Array.isArray(obj) && obj !== null) {\r\n                return Object.values(obj);\r\n            }\r\n            return [];\r\n        },\r\n        description: \"Returns an array of values from the given object.\",\r\n        example: `values({ a: 1, b: 2 }) = [1, 2]`,\r\n    },\r\n    {\r\n        key: \"contains\",\r\n        callback: (collection, item) => {\r\n            if (Array.isArray(collection)) {\r\n                return collection.includes(item);\r\n            }\r\n            else if (typeof collection === \"object\" && collection !== null) {\r\n                return Object.values(collection).includes(item);\r\n            }\r\n            else if (typeof collection === \"string\" && typeof item === \"string\") {\r\n                return collection.includes(item);\r\n            }\r\n            return false;\r\n        },\r\n        description: \"Returns true if the array contains the item, the object has the item as one of its values, or the string contains the substring.\",\r\n        example: `contains([1, 2, 3], 2) = true; contains({ a: 10, b: 20 }, 10) = true; contains(\"hello world\", \"world\") = true`,\r\n    },\r\n    {\r\n        key: \"pluck\",\r\n        callback: (arr, key) => {\r\n            if (!Array.isArray(arr))\r\n                return [];\r\n            return arr.map(item => item && typeof item === \"object\" ? item[key] : undefined);\r\n        },\r\n        description: \"Extracts the values of the specified property from an array of objects.\",\r\n        example: `pluck([{a:1, b:2}, {a:3, b:4}], \"a\") = [1, 3]`,\r\n    },\r\n    {\r\n        key: \"merge\",\r\n        callback: (first, second) => {\r\n            if (Array.isArray(first) && Array.isArray(second)) {\r\n                return [...first, ...second];\r\n            }\r\n            else if (first !== null &&\r\n                second !== null &&\r\n                typeof first === \"object\" &&\r\n                typeof second === \"object\") {\r\n                return Object.assign(Object.assign({}, first), second);\r\n            }\r\n            else {\r\n                throw new Error(\"Unsupported types for merge. Provide two arrays or two objects.\");\r\n            }\r\n        },\r\n        description: \"Merges two arrays or two objects into one. For arrays, concatenates them; for objects, combines their key-value pairs.\",\r\n        example: `merge([1,2], [3,4]) = [1,2,3,4]; merge({a:1}, {b:2}) = {a:1, b:2}`,\r\n    },\r\n    {\r\n        key: \"findIndex\",\r\n        callback: (arr, item) => {\r\n            if (!Array.isArray(arr))\r\n                return -1;\r\n            return arr.indexOf(item);\r\n        },\r\n        description: \"Returns the index of the specified item in an array, or -1 if not found.\",\r\n        example: `findIndex([10, 20, 30], 20) = 1`,\r\n    },\r\n    {\r\n        key: \"createList\",\r\n        callback: (length, value) => {\r\n            if (typeof length !== \"number\" || length < 0) {\r\n                throw new Error(\"Length must be a non-negative number\");\r\n            }\r\n            return Array(length).fill(value);\r\n        },\r\n        description: \"Creates a new list of the specified length, with each element set to the provided value.\",\r\n        example: `createList(5, 2) = [2,2,2,2,2]`,\r\n    },\r\n    {\r\n        key: \"parseJSON\",\r\n        callback: (input) => {\r\n            return JSON.parse(input);\r\n        },\r\n        description: \"Parses a JSON-formatted string and returns the corresponding JavaScript value.\",\r\n        example: `parseJSON('{\"a\":1}') = { a: 1 }`,\r\n    },\r\n    {\r\n        key: \"stringifyJSON\",\r\n        callback: (value) => {\r\n            return JSON.stringify(value);\r\n        },\r\n        description: \"Converts a JavaScript value into a JSON-formatted string.\",\r\n        example: `stringifyJSON({ a: 1 }) = '{\"a\":1}'`,\r\n    },\r\n    {\r\n        key: \"isDateAnniversary\",\r\n        callback: date_1.isDateAnniversary,\r\n        description: \"Checks if a given date falls on the same month and day as a reference date (default is today), ignoring the year.\",\r\n        example: `isDateAnniversary(\"2025-08-22\", \"2028-08-22\") = true`,\r\n    },\r\n    {\r\n        key: \"daysUntilDate\",\r\n        callback: (dateStr, referenceDateStr) => {\r\n            const targetDate = new Date(dateStr);\r\n            const referenceDate = referenceDateStr ? new Date(referenceDateStr) : new Date();\r\n            const diffTime = targetDate.getTime() - referenceDate.getTime();\r\n            return Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n        },\r\n        description: \"Returns the number of days until the given date from the reference date (defaults to today).\",\r\n        example: `daysUntilDate(\"2025-01-01\", \"2024-12-30\") = 2`,\r\n    },\r\n    {\r\n        key: \"isWeekend\",\r\n        callback: (dateStr) => {\r\n            const date = new Date(dateStr);\r\n            const day = date.getUTCDay();\r\n            return day === 0 || day === 6;\r\n        },\r\n        description: \"Checks if the given date falls on a weekend (Saturday or Sunday).\",\r\n        example: `isWeekend(\"2024-03-09\") = true`,\r\n    },\r\n    {\r\n        key: \"isLeapYear\",\r\n        callback: date_1.isLeapYear,\r\n        description: \"Determines if the given year or date represents a leap year. If omitted, the current year is used. \" +\r\n            \"Examples: isLeapYear(2024) = true, isLeapYear('2024-02-29') = true, \",\r\n        example: `isLeapYear(2024) = true\\nisLeapYear(\"2024-02-29\") = true`,\r\n    },\r\n    {\r\n        key: \"formatDate\",\r\n        callback: date_1.formatDate,\r\n        description: \"Formats a date according to a given format string. Supported tokens include:\\n\\n\" +\r\n            \"- YYYY: 4-digit year (e.g., 2025)\\n\" +\r\n            \"- MMMM: Full month name (e.g., January)\\n\" +\r\n            \"- MMM: Abbreviated month name (e.g., Jan)\\n\" +\r\n            \"- MM: Month number with leading zero (e.g., 01)\\n\" +\r\n            \"- M: Month number without leading zero (e.g., 1)\\n\" +\r\n            \"- dddd: Full weekday name (e.g., Thursday)\\n\" +\r\n            \"- ddd: Abbreviated weekday name (e.g., Thu)\\n\" +\r\n            \"- DD: Day of month with leading zero (e.g., 02)\\n\" +\r\n            \"- D: Day of month without padding (e.g., 2)\\n\" +\r\n            \"- Do: Day of month with ordinal suffix (e.g., 2nd)\\n\\n\" +\r\n            \"If the date is provided in the 'YYYY-MM-DD' format, it is treated as UTC.\",\r\n        example: `formatDate(\"2025-01-02\", \"ddd, Do MMM YYYY\") = \"Thu, 2nd Jan 2025\"`,\r\n    },\r\n    {\r\n        key: \"timeSince\",\r\n        callback: (dateInput) => {\r\n            const past = new Date(dateInput);\r\n            const now = new Date();\r\n            const seconds = Math.floor((now.getTime() - past.getTime()) / 1000);\r\n            if (seconds < 60)\r\n                return `${seconds} seconds ago`;\r\n            const minutes = Math.floor(seconds / 60);\r\n            if (minutes < 60)\r\n                return `${minutes} minutes ago`;\r\n            const hours = Math.floor(minutes / 60);\r\n            if (hours < 24)\r\n                return `${hours} hours ago`;\r\n            const days = Math.floor(hours / 24);\r\n            return `${days} days ago`;\r\n        },\r\n        description: \"Returns a human-readable string representing the time elapsed since the given date.\",\r\n        example: `timeSince(\"2024-03-01\") = \"X days ago\"`,\r\n    },\r\n    {\r\n        key: \"timeDifferenceInUnits\",\r\n        callback: date_1.timeDifferenceInUnits,\r\n        description: \"Calculates the difference between two dates in the specified unit ('seconds', 'minutes', 'hours', 'days', 'weeks', 'months', 'years'). The second date is an optional reference date, defaulting to the current date if not provided.\",\r\n        example: `timeDifferenceInUnits(\"2024-02-01\", \"days\", \"2024-02-10\") = 9`,\r\n    },\r\n    {\r\n        key: \"isBetweenDates\",\r\n        callback: (dateStr, startDateStr, endDateStr) => {\r\n            const date = new Date(dateStr);\r\n            const start = new Date(startDateStr);\r\n            const end = new Date(endDateStr);\r\n            return date >= start && date <= end;\r\n        },\r\n        description: \"Checks if a given date falls between two dates (inclusive).\",\r\n        example: `isBetweenDates(\"2024-01-15\", \"2024-01-01\", \"2024-01-31\") = true`,\r\n    },\r\n    {\r\n        key: \"nextWeekDay\",\r\n        callback: (dateStr, weekday) => {\r\n            const date = new Date(dateStr);\r\n            const weekdays = {\r\n                sunday: 0,\r\n                monday: 1,\r\n                tuesday: 2,\r\n                wednesday: 3,\r\n                thursday: 4,\r\n                friday: 5,\r\n                saturday: 6,\r\n            };\r\n            const targetDay = weekdays[weekday.toLowerCase()];\r\n            if (targetDay === undefined) {\r\n                throw new Error(`Invalid weekday: ${weekday}`);\r\n            }\r\n            // Increment until the date's day matches the target\r\n            while (date.getDay() !== targetDay) {\r\n                date.setDate(date.getDate() + 1);\r\n            }\r\n            return date.toISOString();\r\n        },\r\n        description: \"Returns the ISO string of the next occurrence of the specified weekday after the given date. \\n\" +\r\n            \"Supported weekdays: Sunday, Monday, Tuesday, Wednesday, Thursday, Friday, Saturday.\",\r\n        example: `nextWeekDay(\"2024-03-06\", \"Monday\") = \"2024-03-11T...Z\"`,\r\n    },\r\n    {\r\n        key: \"isSameWeek\",\r\n        callback: (dateStr1, dateStr2) => {\r\n            const d1 = new Date(dateStr1);\r\n            const d2 = new Date(dateStr2);\r\n            // Normalize both dates to midnight UTC\r\n            d1.setUTCHours(0, 0, 0, 0);\r\n            d2.setUTCHours(0, 0, 0, 0);\r\n            // Calculate the start of the week (Sunday) in UTC for both dates\r\n            const weekStart1 = new Date(d1);\r\n            weekStart1.setUTCDate(d1.getUTCDate() - d1.getUTCDay());\r\n            const weekStart2 = new Date(d2);\r\n            weekStart2.setUTCDate(d2.getUTCDate() - d2.getUTCDay());\r\n            return weekStart1.getTime() === weekStart2.getTime();\r\n        },\r\n        description: \"Checks if two dates fall within the same week (week starts on Sunday) using UTC normalization.\",\r\n        example: `isSameWeek(\"2024-03-03\", \"2024-03-08\") = true`,\r\n    },\r\n    {\r\n        key: \"businessDaysBetween\",\r\n        callback: (startDateStr, endDateStr) => {\r\n            const start = new Date(startDateStr);\r\n            const end = new Date(endDateStr);\r\n            let count = 0;\r\n            for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {\r\n                const day = d.getDay();\r\n                if (day !== 0 && day !== 6) {\r\n                    count++;\r\n                }\r\n            }\r\n            return count;\r\n        },\r\n        description: \"Returns the number of business days (Monday to Friday) between two dates, inclusive.\",\r\n        example: `businessDaysBetween(\"2024-03-04\", \"2024-03-10\") = 5`,\r\n    },\r\n    {\r\n        key: \"getQuarter\",\r\n        callback: (dateInput) => {\r\n            const date = new Date(dateInput);\r\n            return Math.floor(date.getMonth() / 3) + 1;\r\n        },\r\n        description: \"Returns the quarter (1-4) for the given date.\",\r\n        example: `getQuarter(\"2024-05-15\") = 2`,\r\n    },\r\n    {\r\n        key: \"split\",\r\n        callback: (str, delimiter) => {\r\n            return String(str || '').split(delimiter);\r\n        },\r\n        description: \"Splits a string into an array of substrings based on the specified delimiter.\",\r\n        example: `split(\"apple,banana,cherry\", \",\") = [\"apple\", \"banana\", \"cherry\"]`,\r\n    },\r\n    {\r\n        key: \"join\",\r\n        callback: (arr, delimiter) => {\r\n            if (Array.isArray(arr)) {\r\n                return arr.join(delimiter);\r\n            }\r\n            else if (typeof arr === \"object\" && arr !== null) {\r\n                return JSON.stringify(arr);\r\n            }\r\n            else {\r\n                return String(arr);\r\n            }\r\n        },\r\n        description: \"Joins an array of strings into a single string using the specified delimiter.\",\r\n        example: `join([\"apple\", \"banana\", \"cherry\"], \", \") = \"apple, banana, cherry\"`,\r\n    },\r\n    {\r\n        key: \"extractAndJoin\",\r\n        callback: (arr, key, delimiter) => {\r\n            const delim = delimiter !== null && delimiter !== void 0 ? delimiter : \",\";\r\n            // If not an array:\r\n            // - primitives are stringified\r\n            // - objects are JSON.stringify’d\r\n            if (!Array.isArray(arr)) {\r\n                if (arr == null)\r\n                    return \"\";\r\n                return typeof arr === \"object\"\r\n                    ? JSON.stringify(arr)\r\n                    : String(arr);\r\n            }\r\n            // For arrays:\r\n            // If no key, join items directly\r\n            if (key == null) {\r\n                return arr.map(item => String(item !== null && item !== void 0 ? item : \"\")).join(delim);\r\n            }\r\n            // With key, pluck and join\r\n            return arr\r\n                .map(item => item != null && typeof item === \"object\" && key in item\r\n                ? String(item[key])\r\n                : \"\")\r\n                .join(delim);\r\n        },\r\n        description: \"Extracts `key` from each object in `arr` and joins the results with `delimiter`. \" +\r\n            \"If `key` is omitted, does a straightforward join of the array values. \" +\r\n            \"Delimiter is also optional, defaults to ','.\",\r\n        example: `extractAndJoin([{name:\"Alice\"},{name:\"Bob\"}], \"name\")       // \"Alice,Bob\"\\n` +\r\n            `extractAndJoin([{a:1},{a:2}], \"a\", \";\")                    // \"1;2\"\\n` +\r\n            `extractAndJoin([\"x\",\"y\",\"z\"])                              // \"x,y,z\"\\n` +\r\n            `extractAndJoin([1,2,3], undefined, \"-\")                    // \"1-2-3\"`\r\n    }\r\n];\r\n// Create a registry (map) for quick lookup by key.\r\nexports.expressionFunctionMap = {};\r\nexpressions.sort((a, b) => a.key.localeCompare(b.key))\r\n    .forEach((expr) => {\r\n    exports.expressionFunctionMap[expr.key] = expr;\r\n});\r\n/**\r\n * Calls the expression function registered under the provided key with the given arguments.\r\n * Throws an error if the function is not registered.\r\n *\r\n * @param key - The key of the registered function.\r\n * @param args - Arguments to pass to the function callback.\r\n * @returns The result of the function call.\r\n */\r\nfunction callExpressionFunction(key, ...args) {\r\n    const funcExpr = exports.expressionFunctionMap[key];\r\n    if (!funcExpr) {\r\n        throw new Error(`Function '${key}' is not registered.`);\r\n    }\r\n    console.log(\"funcExpr from callExpressionFunction\", { funcExpr, key, args });\r\n    const result = funcExpr.callback(...args);\r\n    if (![\"parseJSON\", \"stringifyJSON\", \"split\", \"pluck\"].includes(key) && result && typeof result === 'object') {\r\n        return JSON.stringify(result);\r\n    }\r\n    return result;\r\n}\r\nexports.callExpressionFunction = callExpressionFunction;\r\nconst unaryExpressions = [\r\n    {\r\n        key: \"-\",\r\n        callback: (arg) => -arg,\r\n        description: \"Negates the numeric value of the operand.\",\r\n        example: `-5 = -5; -(-3) = 3`,\r\n    },\r\n    {\r\n        key: \"+\",\r\n        callback: (arg) => +arg,\r\n        description: \"Converts the operand to a number, or returns the number if already numeric.\",\r\n        example: `+\"5\" = 5; +3 = 3`,\r\n    },\r\n    {\r\n        key: \"!\",\r\n        callback: (arg) => !arg,\r\n        description: \"Returns the logical negation of the operand's truthiness.\",\r\n        example: `!true = false; !0 = true`,\r\n    },\r\n];\r\n// Create a registry (map) for quick lookup by operator\r\nexports.unaryExpressionMap = {};\r\nunaryExpressions.forEach((expr) => {\r\n    exports.unaryExpressionMap[expr.key] = expr;\r\n});\r\nconst evaluateUnaryExpression = (operator, argument) => {\r\n    const unaryExpr = exports.unaryExpressionMap[operator];\r\n    if (!unaryExpr) {\r\n        throw new Error(`Unsupported unary operator: ${operator}`);\r\n    }\r\n    return unaryExpr.callback(argument);\r\n};\r\nexports.evaluateUnaryExpression = evaluateUnaryExpression;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/utils/derived.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/lib/utils/onboarding.js":
/*!******************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/lib/utils/onboarding.js ***!
  \******************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\r\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\r\nexports.getDatabasePackageName = exports.getCustomerDbDefinition = exports.getContactDbDefinition = exports.getCompanyDbDefinition = void 0;\r\nconst db_1 = __webpack_require__(/*! ../typings/db */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\r\nconst color_1 = __webpack_require__(/*! ../typings/color */ \"(ssr)/./node_modules/opendb-app-db-utils/lib/typings/color.js\");\r\nvar DbConstants;\r\n(function (DbConstants) {\r\n    DbConstants[\"OpenDbDomain\"] = \"opendb\";\r\n    DbConstants[\"OpenDbContactsDbName\"] = \"contacts\";\r\n    DbConstants[\"OpenDbContactableDbName\"] = \"contactable\";\r\n    DbConstants[\"OpenDbCompaniesDbName\"] = \"companies\";\r\n})(DbConstants || (DbConstants = {}));\r\nconst getCompanyDbDefinition = () => {\r\n    const name = {\r\n        id: 'name',\r\n        title: 'Name',\r\n        type: db_1.DatabaseFieldDataType.Text\r\n    };\r\n    const email = {\r\n        id: 'email',\r\n        title: 'Email',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Email\r\n    };\r\n    const phone = {\r\n        id: 'phone',\r\n        title: 'Phone number',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Phone\r\n    };\r\n    const url = {\r\n        id: 'url',\r\n        title: 'URL',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Url\r\n    };\r\n    const address = {\r\n        id: 'address',\r\n        title: 'Address',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Location\r\n    };\r\n    const description = {\r\n        id: 'description',\r\n        title: 'Description',\r\n        type: db_1.DatabaseFieldDataType.Text\r\n    };\r\n    const category = {\r\n        id: \"category\",\r\n        optionIds: [],\r\n        optionsMap: {},\r\n        title: 'Category',\r\n        type: db_1.DatabaseFieldDataType.Select,\r\n        isMulti: true\r\n    };\r\n    {\r\n        const optionsId = [];\r\n        const optionsMap = {};\r\n        const partner = {\r\n            color: color_1.ColorNames.Brown,\r\n            id: 'partner',\r\n            title: 'Partner',\r\n        };\r\n        const investor = {\r\n            color: color_1.ColorNames.Red,\r\n            id: 'investor',\r\n            title: 'Investor',\r\n        };\r\n        const customer = {\r\n            color: color_1.ColorNames.SlateGray,\r\n            id: 'customer',\r\n            title: 'Customer',\r\n        };\r\n        for (let o of [partner, investor, customer]) {\r\n            optionsId.push(o.id);\r\n            optionsMap[o.id] = o;\r\n        }\r\n        category.optionsMap = optionsMap;\r\n        category.optionIds = optionsId;\r\n    }\r\n    const linkedIn = {\r\n        id: 'linkedIn',\r\n        title: 'LinkedIn',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Url\r\n    };\r\n    const twitter = {\r\n        id: 'twitter',\r\n        title: 'Twitter',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Url\r\n    };\r\n    const assignedTo = {\r\n        id: 'assigned-to',\r\n        title: 'Assigned To',\r\n        type: db_1.DatabaseFieldDataType.Person,\r\n        isMulti: true\r\n    };\r\n    const unsubscribed = {\r\n        id: 'unsubscribed',\r\n        title: 'Unsubscribed',\r\n        type: db_1.DatabaseFieldDataType.Checkbox\r\n    };\r\n    const definition = {\r\n        uniqueColumnId: '',\r\n        columnsMap: {},\r\n        columnIds: [],\r\n        titleColumnId: name.id\r\n    };\r\n    for (const col of [name, email, phone, url, address, description, category, linkedIn, twitter, assignedTo, unsubscribed]) {\r\n        definition.columnsMap[col.id] = col;\r\n        definition.columnIds.push(col.id);\r\n    }\r\n    return {\r\n        definition,\r\n        versionName: \"0.0.1\",\r\n        versionNumber: 1,\r\n        domain: DbConstants.OpenDbDomain,\r\n        templateName: DbConstants.OpenDbCompaniesDbName\r\n    };\r\n};\r\nexports.getCompanyDbDefinition = getCompanyDbDefinition;\r\nconst getContactDbDefinition = () => {\r\n    const firstName = {\r\n        id: 'firstName',\r\n        title: 'First Name',\r\n        type: db_1.DatabaseFieldDataType.Text\r\n    };\r\n    const lastName = {\r\n        id: 'lastName',\r\n        title: 'Last Name',\r\n        type: db_1.DatabaseFieldDataType.Text\r\n    };\r\n    const email = {\r\n        id: 'email',\r\n        title: 'Email',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Email\r\n    };\r\n    const phone = {\r\n        id: 'phone',\r\n        title: 'Phone number',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Phone\r\n    };\r\n    const unsubscribed = {\r\n        id: 'unsubscribed',\r\n        title: 'Unsubscribed',\r\n        type: db_1.DatabaseFieldDataType.Checkbox\r\n    };\r\n    const bounced = {\r\n        id: 'bounced',\r\n        title: 'Bounced',\r\n        type: db_1.DatabaseFieldDataType.Checkbox\r\n    };\r\n    const bounceReason = {\r\n        id: 'bounce_reason',\r\n        title: 'Bounce Reason',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        isLong: true\r\n    };\r\n    const definition = {\r\n        uniqueColumnId: '',\r\n        columnsMap: {},\r\n        columnIds: [],\r\n        titleFormat: `{{firstName}} {{lastName}}`\r\n    };\r\n    for (const col of [firstName, lastName, email, phone, unsubscribed, bounced, bounceReason]) {\r\n        definition.columnsMap[col.id] = col;\r\n        definition.columnIds.push(col.id);\r\n    }\r\n    return {\r\n        definition,\r\n        versionName: \"0.0.1\",\r\n        versionNumber: 1,\r\n        domain: DbConstants.OpenDbDomain,\r\n        templateName: DbConstants.OpenDbContactableDbName\r\n    };\r\n};\r\nexports.getContactDbDefinition = getContactDbDefinition;\r\nconst getCustomerDbDefinition = (companyDbId) => {\r\n    const firstName = {\r\n        id: 'firstName',\r\n        title: 'First Name',\r\n        type: db_1.DatabaseFieldDataType.Text\r\n    };\r\n    const lastName = {\r\n        id: 'lastName',\r\n        title: 'Last Name',\r\n        type: db_1.DatabaseFieldDataType.Text\r\n    };\r\n    const description = {\r\n        id: 'description',\r\n        title: 'Description',\r\n        type: db_1.DatabaseFieldDataType.Text\r\n    };\r\n    const email = {\r\n        id: 'email',\r\n        title: 'Email',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Email\r\n    };\r\n    const phone = {\r\n        id: 'phone',\r\n        title: 'Phone number',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Phone\r\n    };\r\n    const gender = {\r\n        id: \"gender\",\r\n        optionIds: [],\r\n        optionsMap: {},\r\n        title: 'Gender',\r\n        type: db_1.DatabaseFieldDataType.Select,\r\n    };\r\n    {\r\n        const optionsId = [];\r\n        const optionsMap = {};\r\n        const male = {\r\n            color: color_1.ColorNames.Brown,\r\n            id: 'male',\r\n            title: 'Male',\r\n        };\r\n        const female = {\r\n            color: color_1.ColorNames.Red,\r\n            id: 'female',\r\n            title: 'Female',\r\n        };\r\n        const other = {\r\n            color: color_1.ColorNames.SlateGray,\r\n            id: 'other',\r\n            title: 'Other',\r\n        };\r\n        const unknown = {\r\n            color: color_1.ColorNames.Teal,\r\n            id: 'unknown',\r\n            title: 'Unknown',\r\n        };\r\n        for (let o of [male, female, other, unknown]) {\r\n            optionsId.push(o.id);\r\n            optionsMap[o.id] = o;\r\n        }\r\n        gender.optionsMap = optionsMap;\r\n        gender.optionIds = optionsId;\r\n    }\r\n    const birthday = {\r\n        id: 'birthday',\r\n        title: 'Birthday',\r\n        type: db_1.DatabaseFieldDataType.Date,\r\n        withTime: false\r\n    };\r\n    const address = {\r\n        id: 'address',\r\n        title: 'Address',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Location\r\n    };\r\n    const companies = {\r\n        title: 'Companies',\r\n        id: 'companies',\r\n        type: db_1.DatabaseFieldDataType.Linked,\r\n        isMulti: true,\r\n        databaseId: companyDbId\r\n    };\r\n    const jobTitle = {\r\n        id: 'job-title',\r\n        title: 'Job Title',\r\n        type: db_1.DatabaseFieldDataType.Text\r\n    };\r\n    const linkedIn = {\r\n        id: 'linkedIn',\r\n        title: 'LinkedIn',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Url\r\n    };\r\n    const twitter = {\r\n        id: 'twitter',\r\n        title: 'Twitter',\r\n        type: db_1.DatabaseFieldDataType.Text,\r\n        format: db_1.TextColumnFormat.Url\r\n    };\r\n    const unsubscribed = {\r\n        id: 'unsubscribed',\r\n        title: 'Unsubscribed',\r\n        type: db_1.DatabaseFieldDataType.Checkbox\r\n    };\r\n    const definition = {\r\n        uniqueColumnId: '',\r\n        columnsMap: {},\r\n        columnIds: [],\r\n    };\r\n    for (const col of [firstName, lastName, description, email, phone, gender, birthday, address, companies, jobTitle, linkedIn, twitter, unsubscribed]) {\r\n        definition.columnsMap[col.id] = col;\r\n        definition.columnIds.push(col.id);\r\n    }\r\n    return {\r\n        definition,\r\n        versionName: \"0.0.1\",\r\n        versionNumber: 1,\r\n        domain: DbConstants.OpenDbDomain,\r\n        templateName: DbConstants.OpenDbContactsDbName\r\n    };\r\n};\r\nexports.getCustomerDbDefinition = getCustomerDbDefinition;\r\nconst getDatabasePackageName = (versioned) => {\r\n    const { domain, versionName, templateName, versionNumber } = versioned;\r\n    return `${domain}.${templateName}`;\r\n};\r\nexports.getDatabasePackageName = getDatabasePackageName;\r\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/lib/utils/onboarding.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/index.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NIL: () => (/* reexport safe */ _nil_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   parse: () => (/* reexport safe */ _parse_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   stringify: () => (/* reexport safe */ _stringify_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   v1: () => (/* reexport safe */ _v1_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   v3: () => (/* reexport safe */ _v3_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   v4: () => (/* reexport safe */ _v4_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   v5: () => (/* reexport safe */ _v5_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   validate: () => (/* reexport safe */ _validate_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   version: () => (/* reexport safe */ _version_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _v1_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./v1.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v1.js\");\n/* harmony import */ var _v3_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./v3.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v3.js\");\n/* harmony import */ var _v4_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./v4.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v4.js\");\n/* harmony import */ var _v5_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./v5.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v5.js\");\n/* harmony import */ var _nil_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./nil.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/nil.js\");\n/* harmony import */ var _version_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./version.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/version.js\");\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./validate.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/validate.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/stringify.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./parse.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/parse.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3QztBQUNBO0FBQ0E7QUFDQTtBQUNFO0FBQ1E7QUFDRTtBQUNFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvaW5kZXguanM/MTU0NCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBkZWZhdWx0IGFzIHYxIH0gZnJvbSAnLi92MS5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHYzIH0gZnJvbSAnLi92My5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHY0IH0gZnJvbSAnLi92NC5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHY1IH0gZnJvbSAnLi92NS5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIE5JTCB9IGZyb20gJy4vbmlsLmpzJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgdmVyc2lvbiB9IGZyb20gJy4vdmVyc2lvbi5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHZhbGlkYXRlIH0gZnJvbSAnLi92YWxpZGF0ZS5qcyc7XG5leHBvcnQgeyBkZWZhdWx0IGFzIHN0cmluZ2lmeSB9IGZyb20gJy4vc3RyaW5naWZ5LmpzJztcbmV4cG9ydCB7IGRlZmF1bHQgYXMgcGFyc2UgfSBmcm9tICcuL3BhcnNlLmpzJzsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/md5.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/md5.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction md5(bytes) {\n  if (Array.isArray(bytes)) {\n    bytes = Buffer.from(bytes);\n  } else if (typeof bytes === 'string') {\n    bytes = Buffer.from(bytes, 'utf8');\n  }\n\n  return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('md5').update(bytes).digest();\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (md5);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL21kNS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEI7O0FBRTVCO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBOztBQUVBLFNBQVMsd0RBQWlCO0FBQzFCOztBQUVBLGlFQUFlLEdBQUciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL29wZW5kYi1hcHAtZGItdXRpbHMvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS9tZDUuanM/YTJiYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5cbmZ1bmN0aW9uIG1kNShieXRlcykge1xuICBpZiAoQXJyYXkuaXNBcnJheShieXRlcykpIHtcbiAgICBieXRlcyA9IEJ1ZmZlci5mcm9tKGJ5dGVzKTtcbiAgfSBlbHNlIGlmICh0eXBlb2YgYnl0ZXMgPT09ICdzdHJpbmcnKSB7XG4gICAgYnl0ZXMgPSBCdWZmZXIuZnJvbShieXRlcywgJ3V0ZjgnKTtcbiAgfVxuXG4gIHJldHVybiBjcnlwdG8uY3JlYXRlSGFzaCgnbWQ1JykudXBkYXRlKGJ5dGVzKS5kaWdlc3QoKTtcbn1cblxuZXhwb3J0IGRlZmF1bHQgbWQ1OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/md5.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/native.js":
/*!************************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/native.js ***!
  \************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ({\n  randomUUID: (crypto__WEBPACK_IMPORTED_MODULE_0___default().randomUUID)\n});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL25hdGl2ZS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEI7QUFDNUIsaUVBQWU7QUFDZixjQUFjLDBEQUFpQjtBQUMvQixDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvbmF0aXZlLmpzP2RjZGMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyeXB0byBmcm9tICdjcnlwdG8nO1xuZXhwb3J0IGRlZmF1bHQge1xuICByYW5kb21VVUlEOiBjcnlwdG8ucmFuZG9tVVVJRFxufTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/native.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/nil.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/nil.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ('00000000-0000-0000-0000-000000000000');//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL25pbC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUEsaUVBQWUsc0NBQXNDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvbmlsLmpzPzZlMGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgJzAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCc7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/nil.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/parse.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/parse.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/validate.js\");\n\n\nfunction parse(uuid) {\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  let v;\n  const arr = new Uint8Array(16); // Parse ########-....-....-....-............\n\n  arr[0] = (v = parseInt(uuid.slice(0, 8), 16)) >>> 24;\n  arr[1] = v >>> 16 & 0xff;\n  arr[2] = v >>> 8 & 0xff;\n  arr[3] = v & 0xff; // Parse ........-####-....-....-............\n\n  arr[4] = (v = parseInt(uuid.slice(9, 13), 16)) >>> 8;\n  arr[5] = v & 0xff; // Parse ........-....-####-....-............\n\n  arr[6] = (v = parseInt(uuid.slice(14, 18), 16)) >>> 8;\n  arr[7] = v & 0xff; // Parse ........-....-....-####-............\n\n  arr[8] = (v = parseInt(uuid.slice(19, 23), 16)) >>> 8;\n  arr[9] = v & 0xff; // Parse ........-....-....-....-############\n  // (Use \"/\" to avoid 32-bit truncation when bit-shifting high-order bytes)\n\n  arr[10] = (v = parseInt(uuid.slice(24, 36), 16)) / 0x10000000000 & 0xff;\n  arr[11] = v / 0x100000000 & 0xff;\n  arr[12] = v >>> 24 & 0xff;\n  arr[13] = v >>> 16 & 0xff;\n  arr[14] = v >>> 8 & 0xff;\n  arr[15] = v & 0xff;\n  return arr;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (parse);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/regex.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/regex.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL3JlZ2V4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjLEVBQUUsVUFBVSxFQUFFLGVBQWUsRUFBRSxnQkFBZ0IsRUFBRSxVQUFVLEdBQUcseUNBQXlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvcmVnZXguanM/YmY5NCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCAvXig/OlswLTlhLWZdezh9LVswLTlhLWZdezR9LVsxLTVdWzAtOWEtZl17M30tWzg5YWJdWzAtOWEtZl17M30tWzAtOWEtZl17MTJ9fDAwMDAwMDAwLTAwMDAtMDAwMC0wMDAwLTAwMDAwMDAwMDAwMCkkL2k7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/regex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/rng.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/rng.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rng)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nconst rnds8Pool = new Uint8Array(256); // # of random values to pre-allocate\n\nlet poolPtr = rnds8Pool.length;\nfunction rng() {\n  if (poolPtr > rnds8Pool.length - 16) {\n    crypto__WEBPACK_IMPORTED_MODULE_0___default().randomFillSync(rnds8Pool);\n    poolPtr = 0;\n  }\n\n  return rnds8Pool.slice(poolPtr, poolPtr += 16);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL3JuZy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBNEI7QUFDNUIsdUNBQXVDOztBQUV2QztBQUNlO0FBQ2Y7QUFDQSxJQUFJLDREQUFxQjtBQUN6QjtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL29wZW5kYi1hcHAtZGItdXRpbHMvbm9kZV9tb2R1bGVzL3V1aWQvZGlzdC9lc20tbm9kZS9ybmcuanM/NDFlZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5jb25zdCBybmRzOFBvb2wgPSBuZXcgVWludDhBcnJheSgyNTYpOyAvLyAjIG9mIHJhbmRvbSB2YWx1ZXMgdG8gcHJlLWFsbG9jYXRlXG5cbmxldCBwb29sUHRyID0gcm5kczhQb29sLmxlbmd0aDtcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIHJuZygpIHtcbiAgaWYgKHBvb2xQdHIgPiBybmRzOFBvb2wubGVuZ3RoIC0gMTYpIHtcbiAgICBjcnlwdG8ucmFuZG9tRmlsbFN5bmMocm5kczhQb29sKTtcbiAgICBwb29sUHRyID0gMDtcbiAgfVxuXG4gIHJldHVybiBybmRzOFBvb2wuc2xpY2UocG9vbFB0ciwgcG9vbFB0ciArPSAxNik7XG59Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/rng.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/sha1.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/sha1.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction sha1(bytes) {\n  if (Array.isArray(bytes)) {\n    bytes = Buffer.from(bytes);\n  } else if (typeof bytes === 'string') {\n    bytes = Buffer.from(bytes, 'utf8');\n  }\n\n  return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha1').update(bytes).digest();\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sha1);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL3NoYTEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRCOztBQUU1QjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQSxTQUFTLHdEQUFpQjtBQUMxQjs7QUFFQSxpRUFBZSxJQUFJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvc2hhMS5qcz82Mzg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcnlwdG8gZnJvbSAnY3J5cHRvJztcblxuZnVuY3Rpb24gc2hhMShieXRlcykge1xuICBpZiAoQXJyYXkuaXNBcnJheShieXRlcykpIHtcbiAgICBieXRlcyA9IEJ1ZmZlci5mcm9tKGJ5dGVzKTtcbiAgfSBlbHNlIGlmICh0eXBlb2YgYnl0ZXMgPT09ICdzdHJpbmcnKSB7XG4gICAgYnl0ZXMgPSBCdWZmZXIuZnJvbShieXRlcywgJ3V0ZjgnKTtcbiAgfVxuXG4gIHJldHVybiBjcnlwdG8uY3JlYXRlSGFzaCgnc2hhMScpLnVwZGF0ZShieXRlcykuZGlnZXN0KCk7XG59XG5cbmV4cG9ydCBkZWZhdWx0IHNoYTE7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/sha1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/stringify.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/stringify.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   unsafeStringify: () => (/* binding */ unsafeStringify)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/validate.js\");\n\n/**\n * Convert array of 16 byte values to UUID string format of the form:\n * XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX\n */\n\nconst byteToHex = [];\n\nfor (let i = 0; i < 256; ++i) {\n  byteToHex.push((i + 0x100).toString(16).slice(1));\n}\n\nfunction unsafeStringify(arr, offset = 0) {\n  // Note: Be careful editing this code!  It's been tuned for performance\n  // and works in ways you may not expect. See https://github.com/uuidjs/uuid/pull/434\n  return byteToHex[arr[offset + 0]] + byteToHex[arr[offset + 1]] + byteToHex[arr[offset + 2]] + byteToHex[arr[offset + 3]] + '-' + byteToHex[arr[offset + 4]] + byteToHex[arr[offset + 5]] + '-' + byteToHex[arr[offset + 6]] + byteToHex[arr[offset + 7]] + '-' + byteToHex[arr[offset + 8]] + byteToHex[arr[offset + 9]] + '-' + byteToHex[arr[offset + 10]] + byteToHex[arr[offset + 11]] + byteToHex[arr[offset + 12]] + byteToHex[arr[offset + 13]] + byteToHex[arr[offset + 14]] + byteToHex[arr[offset + 15]];\n}\n\nfunction stringify(arr, offset = 0) {\n  const uuid = unsafeStringify(arr, offset); // Consistency check for valid UUID.  If this throws, it's likely due to one\n  // of the following:\n  // - One or more input array values don't map to a hex octet (leading to\n  // \"undefined\" in the uuid)\n  // - Invalid input values for the RFC `version` or `variant` fields\n\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Stringified UUID is invalid');\n  }\n\n  return uuid;\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (stringify);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/stringify.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v1.js":
/*!********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v1.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./rng.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/stringify.js\");\n\n // **`v1()` - Generate time-based UUID**\n//\n// Inspired by https://github.com/LiosK/UUID.js\n// and http://docs.python.org/library/uuid.html\n\nlet _nodeId;\n\nlet _clockseq; // Previous uuid creation time\n\n\nlet _lastMSecs = 0;\nlet _lastNSecs = 0; // See https://github.com/uuidjs/uuid for API details\n\nfunction v1(options, buf, offset) {\n  let i = buf && offset || 0;\n  const b = buf || new Array(16);\n  options = options || {};\n  let node = options.node || _nodeId;\n  let clockseq = options.clockseq !== undefined ? options.clockseq : _clockseq; // node and clockseq need to be initialized to random values if they're not\n  // specified.  We do this lazily to minimize issues related to insufficient\n  // system entropy.  See #189\n\n  if (node == null || clockseq == null) {\n    const seedBytes = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])();\n\n    if (node == null) {\n      // Per 4.5, create and 48-bit node id, (47 random bits + multicast bit = 1)\n      node = _nodeId = [seedBytes[0] | 0x01, seedBytes[1], seedBytes[2], seedBytes[3], seedBytes[4], seedBytes[5]];\n    }\n\n    if (clockseq == null) {\n      // Per 4.2.2, randomize (14 bit) clockseq\n      clockseq = _clockseq = (seedBytes[6] << 8 | seedBytes[7]) & 0x3fff;\n    }\n  } // UUID timestamps are 100 nano-second units since the Gregorian epoch,\n  // (1582-10-15 00:00).  JSNumbers aren't precise enough for this, so\n  // time is handled internally as 'msecs' (integer milliseconds) and 'nsecs'\n  // (100-nanoseconds offset from msecs) since unix epoch, 1970-01-01 00:00.\n\n\n  let msecs = options.msecs !== undefined ? options.msecs : Date.now(); // Per 4.2.1.2, use count of uuid's generated during the current clock\n  // cycle to simulate higher resolution clock\n\n  let nsecs = options.nsecs !== undefined ? options.nsecs : _lastNSecs + 1; // Time since last uuid creation (in msecs)\n\n  const dt = msecs - _lastMSecs + (nsecs - _lastNSecs) / 10000; // Per 4.2.1.2, Bump clockseq on clock regression\n\n  if (dt < 0 && options.clockseq === undefined) {\n    clockseq = clockseq + 1 & 0x3fff;\n  } // Reset nsecs if clock regresses (new clockseq) or we've moved onto a new\n  // time interval\n\n\n  if ((dt < 0 || msecs > _lastMSecs) && options.nsecs === undefined) {\n    nsecs = 0;\n  } // Per 4.2.1.2 Throw error if too many uuids are requested\n\n\n  if (nsecs >= 10000) {\n    throw new Error(\"uuid.v1(): Can't create more than 10M uuids/sec\");\n  }\n\n  _lastMSecs = msecs;\n  _lastNSecs = nsecs;\n  _clockseq = clockseq; // Per 4.1.4 - Convert from unix epoch to Gregorian epoch\n\n  msecs += 12219292800000; // `time_low`\n\n  const tl = ((msecs & 0xfffffff) * 10000 + nsecs) % 0x100000000;\n  b[i++] = tl >>> 24 & 0xff;\n  b[i++] = tl >>> 16 & 0xff;\n  b[i++] = tl >>> 8 & 0xff;\n  b[i++] = tl & 0xff; // `time_mid`\n\n  const tmh = msecs / 0x100000000 * 10000 & 0xfffffff;\n  b[i++] = tmh >>> 8 & 0xff;\n  b[i++] = tmh & 0xff; // `time_high_and_version`\n\n  b[i++] = tmh >>> 24 & 0xf | 0x10; // include version\n\n  b[i++] = tmh >>> 16 & 0xff; // `clock_seq_hi_and_reserved` (Per 4.2.2 - include variant)\n\n  b[i++] = clockseq >>> 8 | 0x80; // `clock_seq_low`\n\n  b[i++] = clockseq & 0xff; // `node`\n\n  for (let n = 0; n < 6; ++n) {\n    b[i + n] = node[n];\n  }\n\n  return buf || (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.unsafeStringify)(b);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v1);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v1.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v3.js":
/*!********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v3.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _v35_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./v35.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v35.js\");\n/* harmony import */ var _md5_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./md5.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/md5.js\");\n\n\nconst v3 = (0,_v35_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('v3', 0x30, _md5_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v3);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL3YzLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQjtBQUNBO0FBQzNCLFdBQVcsbURBQUcsYUFBYSwrQ0FBRztBQUM5QixpRUFBZSxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdjMuanM/NTQ3OSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdjM1IGZyb20gJy4vdjM1LmpzJztcbmltcG9ydCBtZDUgZnJvbSAnLi9tZDUuanMnO1xuY29uc3QgdjMgPSB2MzUoJ3YzJywgMHgzMCwgbWQ1KTtcbmV4cG9ydCBkZWZhdWx0IHYzOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v3.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v35.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v35.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DNS: () => (/* binding */ DNS),\n/* harmony export */   URL: () => (/* binding */ URL),\n/* harmony export */   \"default\": () => (/* binding */ v35)\n/* harmony export */ });\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/stringify.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/parse.js\");\n\n\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  const bytes = [];\n\n  for (let i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nconst DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nconst URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nfunction v35(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    var _namespace;\n\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = (0,_parse_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(namespace);\n    }\n\n    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    let bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (let i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_1__.unsafeStringify)(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v35.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v4.js":
/*!********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v4.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _native_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./native.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/native.js\");\n/* harmony import */ var _rng_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./rng.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/rng.js\");\n/* harmony import */ var _stringify_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./stringify.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/stringify.js\");\n\n\n\n\nfunction v4(options, buf, offset) {\n  if (_native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID && !buf && !options) {\n    return _native_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].randomUUID();\n  }\n\n  options = options || {};\n  const rnds = options.random || (options.rng || _rng_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(); // Per 4.4, set bits for version and `clock_seq_hi_and_reserved`\n\n  rnds[6] = rnds[6] & 0x0f | 0x40;\n  rnds[8] = rnds[8] & 0x3f | 0x80; // Copy bytes to buffer, if provided\n\n  if (buf) {\n    offset = offset || 0;\n\n    for (let i = 0; i < 16; ++i) {\n      buf[offset + i] = rnds[i];\n    }\n\n    return buf;\n  }\n\n  return (0,_stringify_js__WEBPACK_IMPORTED_MODULE_2__.unsafeStringify)(rnds);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v4);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL3Y0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBaUM7QUFDTjtBQUNzQjs7QUFFakQ7QUFDQSxNQUFNLGtEQUFNO0FBQ1osV0FBVyxrREFBTTtBQUNqQjs7QUFFQTtBQUNBLGlEQUFpRCwrQ0FBRyxLQUFLOztBQUV6RDtBQUNBLG1DQUFtQzs7QUFFbkM7QUFDQTs7QUFFQSxvQkFBb0IsUUFBUTtBQUM1QjtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsU0FBUyw4REFBZTtBQUN4Qjs7QUFFQSxpRUFBZSxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdjQuanM/ZDVmZiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgbmF0aXZlIGZyb20gJy4vbmF0aXZlLmpzJztcbmltcG9ydCBybmcgZnJvbSAnLi9ybmcuanMnO1xuaW1wb3J0IHsgdW5zYWZlU3RyaW5naWZ5IH0gZnJvbSAnLi9zdHJpbmdpZnkuanMnO1xuXG5mdW5jdGlvbiB2NChvcHRpb25zLCBidWYsIG9mZnNldCkge1xuICBpZiAobmF0aXZlLnJhbmRvbVVVSUQgJiYgIWJ1ZiAmJiAhb3B0aW9ucykge1xuICAgIHJldHVybiBuYXRpdmUucmFuZG9tVVVJRCgpO1xuICB9XG5cbiAgb3B0aW9ucyA9IG9wdGlvbnMgfHwge307XG4gIGNvbnN0IHJuZHMgPSBvcHRpb25zLnJhbmRvbSB8fCAob3B0aW9ucy5ybmcgfHwgcm5nKSgpOyAvLyBQZXIgNC40LCBzZXQgYml0cyBmb3IgdmVyc2lvbiBhbmQgYGNsb2NrX3NlcV9oaV9hbmRfcmVzZXJ2ZWRgXG5cbiAgcm5kc1s2XSA9IHJuZHNbNl0gJiAweDBmIHwgMHg0MDtcbiAgcm5kc1s4XSA9IHJuZHNbOF0gJiAweDNmIHwgMHg4MDsgLy8gQ29weSBieXRlcyB0byBidWZmZXIsIGlmIHByb3ZpZGVkXG5cbiAgaWYgKGJ1Zikge1xuICAgIG9mZnNldCA9IG9mZnNldCB8fCAwO1xuXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCAxNjsgKytpKSB7XG4gICAgICBidWZbb2Zmc2V0ICsgaV0gPSBybmRzW2ldO1xuICAgIH1cblxuICAgIHJldHVybiBidWY7XG4gIH1cblxuICByZXR1cm4gdW5zYWZlU3RyaW5naWZ5KHJuZHMpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2NDsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v4.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v5.js":
/*!********************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v5.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _v35_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./v35.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v35.js\");\n/* harmony import */ var _sha1_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sha1.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/sha1.js\");\n\n\nconst v5 = (0,_v35_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])('v5', 0x50, _sha1_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (v5);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL3Y1LmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUEyQjtBQUNFO0FBQzdCLFdBQVcsbURBQUcsYUFBYSxnREFBSTtBQUMvQixpRUFBZSxFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdjUuanM/YzEwOSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdjM1IGZyb20gJy4vdjM1LmpzJztcbmltcG9ydCBzaGExIGZyb20gJy4vc2hhMS5qcyc7XG5jb25zdCB2NSA9IHYzNSgndjUnLCAweDUwLCBzaGExKTtcbmV4cG9ydCBkZWZhdWx0IHY1OyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/v5.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/validate.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/validate.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _regex_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./regex.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/regex.js\");\n\n\nfunction validate(uuid) {\n  return typeof uuid === 'string' && _regex_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"].test(uuid);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (validate);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL3ZhbGlkYXRlLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQStCOztBQUUvQjtBQUNBLHFDQUFxQyxpREFBSztBQUMxQzs7QUFFQSxpRUFBZSxRQUFRIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdmFsaWRhdGUuanM/MmE1ZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgUkVHRVggZnJvbSAnLi9yZWdleC5qcyc7XG5cbmZ1bmN0aW9uIHZhbGlkYXRlKHV1aWQpIHtcbiAgcmV0dXJuIHR5cGVvZiB1dWlkID09PSAnc3RyaW5nJyAmJiBSRUdFWC50ZXN0KHV1aWQpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2YWxpZGF0ZTsiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/validate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/version.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/version.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./validate.js */ \"(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/validate.js\");\n\n\nfunction version(uuid) {\n  if (!(0,_validate_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(uuid)) {\n    throw TypeError('Invalid UUID');\n  }\n\n  return parseInt(uuid.slice(14, 15), 16);\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (version);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvb3BlbmRiLWFwcC1kYi11dGlscy9ub2RlX21vZHVsZXMvdXVpZC9kaXN0L2VzbS1ub2RlL3ZlcnNpb24uanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBcUM7O0FBRXJDO0FBQ0EsT0FBTyx3REFBUTtBQUNmO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQSxpRUFBZSxPQUFPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9vcGVuZGItYXBwLWRiLXV0aWxzL25vZGVfbW9kdWxlcy91dWlkL2Rpc3QvZXNtLW5vZGUvdmVyc2lvbi5qcz9hMjA5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB2YWxpZGF0ZSBmcm9tICcuL3ZhbGlkYXRlLmpzJztcblxuZnVuY3Rpb24gdmVyc2lvbih1dWlkKSB7XG4gIGlmICghdmFsaWRhdGUodXVpZCkpIHtcbiAgICB0aHJvdyBUeXBlRXJyb3IoJ0ludmFsaWQgVVVJRCcpO1xuICB9XG5cbiAgcmV0dXJuIHBhcnNlSW50KHV1aWQuc2xpY2UoMTQsIDE1KSwgMTYpO1xufVxuXG5leHBvcnQgZGVmYXVsdCB2ZXJzaW9uOyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/opendb-app-db-utils/node_modules/uuid/dist/esm-node/version.js\n");

/***/ })

};
;