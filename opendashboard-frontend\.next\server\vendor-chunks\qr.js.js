/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/qr.js";
exports.ids = ["vendor-chunks/qr.js"];
exports.modules = {

/***/ "(ssr)/./node_modules/qr.js/lib/8BitByte.js":
/*!********************************************!*\
  !*** ./node_modules/qr.js/lib/8BitByte.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qr.js/lib/mode.js\");\n\nfunction QR8bitByte(data) {\n\tthis.mode = mode.MODE_8BIT_BYTE;\n\tthis.data = data;\n}\n\nQR8bitByte.prototype = {\n\n\tgetLength : function(buffer) {\n\t\treturn this.data.length;\n\t},\n\t\n\twrite : function(buffer) {\n\t\tfor (var i = 0; i < this.data.length; i++) {\n\t\t\t// not JIS ...\n\t\t\tbuffer.put(this.data.charCodeAt(i), 8);\n\t\t}\n\t}\n};\n\nmodule.exports = QR8bitByte;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXIuanMvbGliLzhCaXRCeXRlLmpzIiwibWFwcGluZ3MiOiJBQUFBLFdBQVcsbUJBQU8sQ0FBQyxzREFBUTs7QUFFM0I7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBLEVBQUU7QUFDRjtBQUNBO0FBQ0Esa0JBQWtCLHNCQUFzQjtBQUN4QztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9xci5qcy9saWIvOEJpdEJ5dGUuanM/ZWRhOCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgbW9kZSA9IHJlcXVpcmUoJy4vbW9kZScpO1xuXG5mdW5jdGlvbiBRUjhiaXRCeXRlKGRhdGEpIHtcblx0dGhpcy5tb2RlID0gbW9kZS5NT0RFXzhCSVRfQllURTtcblx0dGhpcy5kYXRhID0gZGF0YTtcbn1cblxuUVI4Yml0Qnl0ZS5wcm90b3R5cGUgPSB7XG5cblx0Z2V0TGVuZ3RoIDogZnVuY3Rpb24oYnVmZmVyKSB7XG5cdFx0cmV0dXJuIHRoaXMuZGF0YS5sZW5ndGg7XG5cdH0sXG5cdFxuXHR3cml0ZSA6IGZ1bmN0aW9uKGJ1ZmZlcikge1xuXHRcdGZvciAodmFyIGkgPSAwOyBpIDwgdGhpcy5kYXRhLmxlbmd0aDsgaSsrKSB7XG5cdFx0XHQvLyBub3QgSklTIC4uLlxuXHRcdFx0YnVmZmVyLnB1dCh0aGlzLmRhdGEuY2hhckNvZGVBdChpKSwgOCk7XG5cdFx0fVxuXHR9XG59O1xuXG5tb2R1bGUuZXhwb3J0cyA9IFFSOGJpdEJ5dGU7XG5cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/8BitByte.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qr.js/lib/BitBuffer.js":
/*!*********************************************!*\
  !*** ./node_modules/qr.js/lib/BitBuffer.js ***!
  \*********************************************/
/***/ ((module) => {

eval("function QRBitBuffer() {\n\tthis.buffer = new Array();\n\tthis.length = 0;\n}\n\nQRBitBuffer.prototype = {\n\n\tget : function(index) {\n\t\tvar bufIndex = Math.floor(index / 8);\n\t\treturn ( (this.buffer[bufIndex] >>> (7 - index % 8) ) & 1) == 1;\n\t},\n\t\n\tput : function(num, length) {\n\t\tfor (var i = 0; i < length; i++) {\n\t\t\tthis.putBit( ( (num >>> (length - i - 1) ) & 1) == 1);\n\t\t}\n\t},\n\t\n\tgetLengthInBits : function() {\n\t\treturn this.length;\n\t},\n\t\n\tputBit : function(bit) {\n\t\n\t\tvar bufIndex = Math.floor(this.length / 8);\n\t\tif (this.buffer.length <= bufIndex) {\n\t\t\tthis.buffer.push(0);\n\t\t}\n\t\n\t\tif (bit) {\n\t\t\tthis.buffer[bufIndex] |= (0x80 >>> (this.length % 8) );\n\t\t}\n\t\n\t\tthis.length++;\n\t}\n};\n\nmodule.exports = QRBitBuffer;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXIuanMvbGliL0JpdEJ1ZmZlci5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBLGtCQUFrQixZQUFZO0FBQzlCO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcXIuanMvbGliL0JpdEJ1ZmZlci5qcz9mMTY1Il0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIFFSQml0QnVmZmVyKCkge1xuXHR0aGlzLmJ1ZmZlciA9IG5ldyBBcnJheSgpO1xuXHR0aGlzLmxlbmd0aCA9IDA7XG59XG5cblFSQml0QnVmZmVyLnByb3RvdHlwZSA9IHtcblxuXHRnZXQgOiBmdW5jdGlvbihpbmRleCkge1xuXHRcdHZhciBidWZJbmRleCA9IE1hdGguZmxvb3IoaW5kZXggLyA4KTtcblx0XHRyZXR1cm4gKCAodGhpcy5idWZmZXJbYnVmSW5kZXhdID4+PiAoNyAtIGluZGV4ICUgOCkgKSAmIDEpID09IDE7XG5cdH0sXG5cdFxuXHRwdXQgOiBmdW5jdGlvbihudW0sIGxlbmd0aCkge1xuXHRcdGZvciAodmFyIGkgPSAwOyBpIDwgbGVuZ3RoOyBpKyspIHtcblx0XHRcdHRoaXMucHV0Qml0KCAoIChudW0gPj4+IChsZW5ndGggLSBpIC0gMSkgKSAmIDEpID09IDEpO1xuXHRcdH1cblx0fSxcblx0XG5cdGdldExlbmd0aEluQml0cyA6IGZ1bmN0aW9uKCkge1xuXHRcdHJldHVybiB0aGlzLmxlbmd0aDtcblx0fSxcblx0XG5cdHB1dEJpdCA6IGZ1bmN0aW9uKGJpdCkge1xuXHRcblx0XHR2YXIgYnVmSW5kZXggPSBNYXRoLmZsb29yKHRoaXMubGVuZ3RoIC8gOCk7XG5cdFx0aWYgKHRoaXMuYnVmZmVyLmxlbmd0aCA8PSBidWZJbmRleCkge1xuXHRcdFx0dGhpcy5idWZmZXIucHVzaCgwKTtcblx0XHR9XG5cdFxuXHRcdGlmIChiaXQpIHtcblx0XHRcdHRoaXMuYnVmZmVyW2J1ZkluZGV4XSB8PSAoMHg4MCA+Pj4gKHRoaXMubGVuZ3RoICUgOCkgKTtcblx0XHR9XG5cdFxuXHRcdHRoaXMubGVuZ3RoKys7XG5cdH1cbn07XG5cbm1vZHVsZS5leHBvcnRzID0gUVJCaXRCdWZmZXI7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/BitBuffer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qr.js/lib/ErrorCorrectLevel.js":
/*!*****************************************************!*\
  !*** ./node_modules/qr.js/lib/ErrorCorrectLevel.js ***!
  \*****************************************************/
/***/ ((module) => {

eval("module.exports = {\n\tL : 1,\n\tM : 0,\n\tQ : 3,\n\tH : 2\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXIuanMvbGliL0Vycm9yQ29ycmVjdExldmVsLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL29wZW5kYXNoYm9hcmQtZnJvbnRlbmQvLi9ub2RlX21vZHVsZXMvcXIuanMvbGliL0Vycm9yQ29ycmVjdExldmVsLmpzPzA2NGQiXSwic291cmNlc0NvbnRlbnQiOlsibW9kdWxlLmV4cG9ydHMgPSB7XG5cdEwgOiAxLFxuXHRNIDogMCxcblx0USA6IDMsXG5cdEggOiAyXG59O1xuXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/ErrorCorrectLevel.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qr.js/lib/Polynomial.js":
/*!**********************************************!*\
  !*** ./node_modules/qr.js/lib/Polynomial.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var math = __webpack_require__(/*! ./math */ \"(ssr)/./node_modules/qr.js/lib/math.js\");\n\nfunction QRPolynomial(num, shift) {\n\n\tif (num.length == undefined) {\n\t\tthrow new Error(num.length + \"/\" + shift);\n\t}\n\n\tvar offset = 0;\n\n\twhile (offset < num.length && num[offset] == 0) {\n\t\toffset++;\n\t}\n\n\tthis.num = new Array(num.length - offset + shift);\n\tfor (var i = 0; i < num.length - offset; i++) {\n\t\tthis.num[i] = num[i + offset];\n\t}\n}\n\nQRPolynomial.prototype = {\n\n\tget : function(index) {\n\t\treturn this.num[index];\n\t},\n\t\n\tgetLength : function() {\n\t\treturn this.num.length;\n\t},\n\t\n\tmultiply : function(e) {\n\t\n\t\tvar num = new Array(this.getLength() + e.getLength() - 1);\n\t\n\t\tfor (var i = 0; i < this.getLength(); i++) {\n\t\t\tfor (var j = 0; j < e.getLength(); j++) {\n\t\t\t\tnum[i + j] ^= math.gexp(math.glog(this.get(i) ) + math.glog(e.get(j) ) );\n\t\t\t}\n\t\t}\n\t\n\t\treturn new QRPolynomial(num, 0);\n\t},\n\t\n\tmod : function(e) {\n\t\n\t\tif (this.getLength() - e.getLength() < 0) {\n\t\t\treturn this;\n\t\t}\n\t\n\t\tvar ratio = math.glog(this.get(0) ) - math.glog(e.get(0) );\n\t\n\t\tvar num = new Array(this.getLength() );\n\t\t\n\t\tfor (var i = 0; i < this.getLength(); i++) {\n\t\t\tnum[i] = this.get(i);\n\t\t}\n\t\t\n\t\tfor (var i = 0; i < e.getLength(); i++) {\n\t\t\tnum[i] ^= math.gexp(math.glog(e.get(i) ) + ratio);\n\t\t}\n\t\n\t\t// recursive call\n\t\treturn new QRPolynomial(num, 0).mod(e);\n\t}\n};\n\nmodule.exports = QRPolynomial;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/Polynomial.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qr.js/lib/QRCode.js":
/*!******************************************!*\
  !*** ./node_modules/qr.js/lib/QRCode.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var BitByte = __webpack_require__(/*! ./8BitByte */ \"(ssr)/./node_modules/qr.js/lib/8BitByte.js\");\nvar RSBlock = __webpack_require__(/*! ./RSBlock */ \"(ssr)/./node_modules/qr.js/lib/RSBlock.js\");\nvar BitBuffer = __webpack_require__(/*! ./BitBuffer */ \"(ssr)/./node_modules/qr.js/lib/BitBuffer.js\");\nvar util = __webpack_require__(/*! ./util */ \"(ssr)/./node_modules/qr.js/lib/util.js\");\nvar Polynomial = __webpack_require__(/*! ./Polynomial */ \"(ssr)/./node_modules/qr.js/lib/Polynomial.js\");\n\nfunction QRCode(typeNumber, errorCorrectLevel) {\n\tthis.typeNumber = typeNumber;\n\tthis.errorCorrectLevel = errorCorrectLevel;\n\tthis.modules = null;\n\tthis.moduleCount = 0;\n\tthis.dataCache = null;\n\tthis.dataList = [];\n}\n\n// for client side minification\nvar proto = QRCode.prototype;\n\nproto.addData = function(data) {\n\tvar newData = new BitByte(data);\n\tthis.dataList.push(newData);\n\tthis.dataCache = null;\n};\n\nproto.isDark = function(row, col) {\n\tif (row < 0 || this.moduleCount <= row || col < 0 || this.moduleCount <= col) {\n\t\tthrow new Error(row + \",\" + col);\n\t}\n\treturn this.modules[row][col];\n};\n\nproto.getModuleCount = function() {\n\treturn this.moduleCount;\n};\n\nproto.make = function() {\n\t// Calculate automatically typeNumber if provided is < 1\n\tif (this.typeNumber < 1 ){\n\t\tvar typeNumber = 1;\n\t\tfor (typeNumber = 1; typeNumber < 40; typeNumber++) {\n\t\t\tvar rsBlocks = RSBlock.getRSBlocks(typeNumber, this.errorCorrectLevel);\n\n\t\t\tvar buffer = new BitBuffer();\n\t\t\tvar totalDataCount = 0;\n\t\t\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\t\t\ttotalDataCount += rsBlocks[i].dataCount;\n\t\t\t}\n\n\t\t\tfor (var i = 0; i < this.dataList.length; i++) {\n\t\t\t\tvar data = this.dataList[i];\n\t\t\t\tbuffer.put(data.mode, 4);\n\t\t\t\tbuffer.put(data.getLength(), util.getLengthInBits(data.mode, typeNumber) );\n\t\t\t\tdata.write(buffer);\n\t\t\t}\n\t\t\tif (buffer.getLengthInBits() <= totalDataCount * 8)\n\t\t\t\tbreak;\n\t\t}\n\t\tthis.typeNumber = typeNumber;\n\t}\n\tthis.makeImpl(false, this.getBestMaskPattern() );\n};\n\nproto.makeImpl = function(test, maskPattern) {\n\t\n\tthis.moduleCount = this.typeNumber * 4 + 17;\n\tthis.modules = new Array(this.moduleCount);\n\t\n\tfor (var row = 0; row < this.moduleCount; row++) {\n\t\t\n\t\tthis.modules[row] = new Array(this.moduleCount);\n\t\t\n\t\tfor (var col = 0; col < this.moduleCount; col++) {\n\t\t\tthis.modules[row][col] = null;//(col + row) % 3;\n\t\t}\n\t}\n\n\tthis.setupPositionProbePattern(0, 0);\n\tthis.setupPositionProbePattern(this.moduleCount - 7, 0);\n\tthis.setupPositionProbePattern(0, this.moduleCount - 7);\n\tthis.setupPositionAdjustPattern();\n\tthis.setupTimingPattern();\n\tthis.setupTypeInfo(test, maskPattern);\n\t\n\tif (this.typeNumber >= 7) {\n\t\tthis.setupTypeNumber(test);\n\t}\n\n\tif (this.dataCache == null) {\n\t\tthis.dataCache = QRCode.createData(this.typeNumber, this.errorCorrectLevel, this.dataList);\n\t}\n\n\tthis.mapData(this.dataCache, maskPattern);\n};\n\nproto.setupPositionProbePattern = function(row, col)  {\n\t\n\tfor (var r = -1; r <= 7; r++) {\n\t\t\n\t\tif (row + r <= -1 || this.moduleCount <= row + r) continue;\n\t\t\n\t\tfor (var c = -1; c <= 7; c++) {\n\t\t\t\n\t\t\tif (col + c <= -1 || this.moduleCount <= col + c) continue;\n\t\t\t\n\t\t\tif ( (0 <= r && r <= 6 && (c == 0 || c == 6) )\n\t\t\t\t\t|| (0 <= c && c <= 6 && (r == 0 || r == 6) )\n\t\t\t\t\t|| (2 <= r && r <= 4 && 2 <= c && c <= 4) ) {\n\t\t\t\tthis.modules[row + r][col + c] = true;\n\t\t\t} else {\n\t\t\t\tthis.modules[row + r][col + c] = false;\n\t\t\t}\n\t\t}\t\t\n\t}\t\t\n};\n\nproto.getBestMaskPattern = function() {\n\n\tvar minLostPoint = 0;\n\tvar pattern = 0;\n\n\tfor (var i = 0; i < 8; i++) {\n\t\t\n\t\tthis.makeImpl(true, i);\n\n\t\tvar lostPoint = util.getLostPoint(this);\n\n\t\tif (i == 0 || minLostPoint >  lostPoint) {\n\t\t\tminLostPoint = lostPoint;\n\t\t\tpattern = i;\n\t\t}\n\t}\n\n\treturn pattern;\n};\n\nproto.createMovieClip = function(target_mc, instance_name, depth) {\n\n\tvar qr_mc = target_mc.createEmptyMovieClip(instance_name, depth);\n\tvar cs = 1;\n\n\tthis.make();\n\n\tfor (var row = 0; row < this.modules.length; row++) {\n\t\t\n\t\tvar y = row * cs;\n\t\t\n\t\tfor (var col = 0; col < this.modules[row].length; col++) {\n\n\t\t\tvar x = col * cs;\n\t\t\tvar dark = this.modules[row][col];\n\t\t\n\t\t\tif (dark) {\n\t\t\t\tqr_mc.beginFill(0, 100);\n\t\t\t\tqr_mc.moveTo(x, y);\n\t\t\t\tqr_mc.lineTo(x + cs, y);\n\t\t\t\tqr_mc.lineTo(x + cs, y + cs);\n\t\t\t\tqr_mc.lineTo(x, y + cs);\n\t\t\t\tqr_mc.endFill();\n\t\t\t}\n\t\t}\n\t}\n\t\n\treturn qr_mc;\n};\n\nproto.setupTimingPattern = function() {\n\t\n\tfor (var r = 8; r < this.moduleCount - 8; r++) {\n\t\tif (this.modules[r][6] != null) {\n\t\t\tcontinue;\n\t\t}\n\t\tthis.modules[r][6] = (r % 2 == 0);\n\t}\n\n\tfor (var c = 8; c < this.moduleCount - 8; c++) {\n\t\tif (this.modules[6][c] != null) {\n\t\t\tcontinue;\n\t\t}\n\t\tthis.modules[6][c] = (c % 2 == 0);\n\t}\n};\n\nproto.setupPositionAdjustPattern = function() {\n\n\tvar pos = util.getPatternPosition(this.typeNumber);\n\t\n\tfor (var i = 0; i < pos.length; i++) {\n\t\n\t\tfor (var j = 0; j < pos.length; j++) {\n\t\t\n\t\t\tvar row = pos[i];\n\t\t\tvar col = pos[j];\n\t\t\t\n\t\t\tif (this.modules[row][col] != null) {\n\t\t\t\tcontinue;\n\t\t\t}\n\t\t\t\n\t\t\tfor (var r = -2; r <= 2; r++) {\n\t\t\t\n\t\t\t\tfor (var c = -2; c <= 2; c++) {\n\t\t\t\t\n\t\t\t\t\tif (r == -2 || r == 2 || c == -2 || c == 2\n\t\t\t\t\t\t\t|| (r == 0 && c == 0) ) {\n\t\t\t\t\t\tthis.modules[row + r][col + c] = true;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.modules[row + r][col + c] = false;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n};\n\nproto.setupTypeNumber = function(test) {\n\n\tvar bits = util.getBCHTypeNumber(this.typeNumber);\n\n\tfor (var i = 0; i < 18; i++) {\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\tthis.modules[Math.floor(i / 3)][i % 3 + this.moduleCount - 8 - 3] = mod;\n\t}\n\n\tfor (var i = 0; i < 18; i++) {\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\tthis.modules[i % 3 + this.moduleCount - 8 - 3][Math.floor(i / 3)] = mod;\n\t}\n};\n\nproto.setupTypeInfo = function(test, maskPattern) {\n\n\tvar data = (this.errorCorrectLevel << 3) | maskPattern;\n\tvar bits = util.getBCHTypeInfo(data);\n\n\t// vertical\t\t\n\tfor (var i = 0; i < 15; i++) {\n\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\n\t\tif (i < 6) {\n\t\t\tthis.modules[i][8] = mod;\n\t\t} else if (i < 8) {\n\t\t\tthis.modules[i + 1][8] = mod;\n\t\t} else {\n\t\t\tthis.modules[this.moduleCount - 15 + i][8] = mod;\n\t\t}\n\t}\n\n\t// horizontal\n\tfor (var i = 0; i < 15; i++) {\n\n\t\tvar mod = (!test && ( (bits >> i) & 1) == 1);\n\t\t\n\t\tif (i < 8) {\n\t\t\tthis.modules[8][this.moduleCount - i - 1] = mod;\n\t\t} else if (i < 9) {\n\t\t\tthis.modules[8][15 - i - 1 + 1] = mod;\n\t\t} else {\n\t\t\tthis.modules[8][15 - i - 1] = mod;\n\t\t}\n\t}\n\n\t// fixed module\n\tthis.modules[this.moduleCount - 8][8] = (!test);\n};\n\nproto.mapData = function(data, maskPattern) {\n\t\n\tvar inc = -1;\n\tvar row = this.moduleCount - 1;\n\tvar bitIndex = 7;\n\tvar byteIndex = 0;\n\t\n\tfor (var col = this.moduleCount - 1; col > 0; col -= 2) {\n\n\t\tif (col == 6) col--;\n\n\t\twhile (true) {\n\n\t\t\tfor (var c = 0; c < 2; c++) {\n\t\t\t\t\n\t\t\t\tif (this.modules[row][col - c] == null) {\n\t\t\t\t\t\n\t\t\t\t\tvar dark = false;\n\n\t\t\t\t\tif (byteIndex < data.length) {\n\t\t\t\t\t\tdark = ( ( (data[byteIndex] >>> bitIndex) & 1) == 1);\n\t\t\t\t\t}\n\n\t\t\t\t\tvar mask = util.getMask(maskPattern, row, col - c);\n\n\t\t\t\t\tif (mask) {\n\t\t\t\t\t\tdark = !dark;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.modules[row][col - c] = dark;\n\t\t\t\t\tbitIndex--;\n\n\t\t\t\t\tif (bitIndex == -1) {\n\t\t\t\t\t\tbyteIndex++;\n\t\t\t\t\t\tbitIndex = 7;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\t\t\t\t\n\t\t\trow += inc;\n\n\t\t\tif (row < 0 || this.moduleCount <= row) {\n\t\t\t\trow -= inc;\n\t\t\t\tinc = -inc;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n};\n\nQRCode.PAD0 = 0xEC;\nQRCode.PAD1 = 0x11;\n\nQRCode.createData = function(typeNumber, errorCorrectLevel, dataList) {\n\t\n\tvar rsBlocks = RSBlock.getRSBlocks(typeNumber, errorCorrectLevel);\n\t\n\tvar buffer = new BitBuffer();\n\t\n\tfor (var i = 0; i < dataList.length; i++) {\n\t\tvar data = dataList[i];\n\t\tbuffer.put(data.mode, 4);\n\t\tbuffer.put(data.getLength(), util.getLengthInBits(data.mode, typeNumber) );\n\t\tdata.write(buffer);\n\t}\n\n\t// calc num max data.\n\tvar totalDataCount = 0;\n\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\ttotalDataCount += rsBlocks[i].dataCount;\n\t}\n\n\tif (buffer.getLengthInBits() > totalDataCount * 8) {\n\t\tthrow new Error(\"code length overflow. (\"\n\t\t\t+ buffer.getLengthInBits()\n\t\t\t+ \">\"\n\t\t\t+  totalDataCount * 8\n\t\t\t+ \")\");\n\t}\n\n\t// end code\n\tif (buffer.getLengthInBits() + 4 <= totalDataCount * 8) {\n\t\tbuffer.put(0, 4);\n\t}\n\n\t// padding\n\twhile (buffer.getLengthInBits() % 8 != 0) {\n\t\tbuffer.putBit(false);\n\t}\n\n\t// padding\n\twhile (true) {\n\t\t\n\t\tif (buffer.getLengthInBits() >= totalDataCount * 8) {\n\t\t\tbreak;\n\t\t}\n\t\tbuffer.put(QRCode.PAD0, 8);\n\t\t\n\t\tif (buffer.getLengthInBits() >= totalDataCount * 8) {\n\t\t\tbreak;\n\t\t}\n\t\tbuffer.put(QRCode.PAD1, 8);\n\t}\n\n\treturn QRCode.createBytes(buffer, rsBlocks);\n};\n\nQRCode.createBytes = function(buffer, rsBlocks) {\n\n\tvar offset = 0;\n\t\n\tvar maxDcCount = 0;\n\tvar maxEcCount = 0;\n\t\n\tvar dcdata = new Array(rsBlocks.length);\n\tvar ecdata = new Array(rsBlocks.length);\n\t\n\tfor (var r = 0; r < rsBlocks.length; r++) {\n\n\t\tvar dcCount = rsBlocks[r].dataCount;\n\t\tvar ecCount = rsBlocks[r].totalCount - dcCount;\n\n\t\tmaxDcCount = Math.max(maxDcCount, dcCount);\n\t\tmaxEcCount = Math.max(maxEcCount, ecCount);\n\t\t\n\t\tdcdata[r] = new Array(dcCount);\n\t\t\n\t\tfor (var i = 0; i < dcdata[r].length; i++) {\n\t\t\tdcdata[r][i] = 0xff & buffer.buffer[i + offset];\n\t\t}\n\t\toffset += dcCount;\n\t\t\n\t\tvar rsPoly = util.getErrorCorrectPolynomial(ecCount);\n\t\tvar rawPoly = new Polynomial(dcdata[r], rsPoly.getLength() - 1);\n\n\t\tvar modPoly = rawPoly.mod(rsPoly);\n\t\tecdata[r] = new Array(rsPoly.getLength() - 1);\n\t\tfor (var i = 0; i < ecdata[r].length; i++) {\n            var modIndex = i + modPoly.getLength() - ecdata[r].length;\n\t\t\tecdata[r][i] = (modIndex >= 0)? modPoly.get(modIndex) : 0;\n\t\t}\n\n\t}\n\t\n\tvar totalCodeCount = 0;\n\tfor (var i = 0; i < rsBlocks.length; i++) {\n\t\ttotalCodeCount += rsBlocks[i].totalCount;\n\t}\n\n\tvar data = new Array(totalCodeCount);\n\tvar index = 0;\n\n\tfor (var i = 0; i < maxDcCount; i++) {\n\t\tfor (var r = 0; r < rsBlocks.length; r++) {\n\t\t\tif (i < dcdata[r].length) {\n\t\t\t\tdata[index++] = dcdata[r][i];\n\t\t\t}\n\t\t}\n\t}\n\n\tfor (var i = 0; i < maxEcCount; i++) {\n\t\tfor (var r = 0; r < rsBlocks.length; r++) {\n\t\t\tif (i < ecdata[r].length) {\n\t\t\t\tdata[index++] = ecdata[r][i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn data;\n};\n\nmodule.exports = QRCode;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/QRCode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qr.js/lib/RSBlock.js":
/*!*******************************************!*\
  !*** ./node_modules/qr.js/lib/RSBlock.js ***!
  \*******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// ErrorCorrectLevel\nvar ECL = __webpack_require__(/*! ./ErrorCorrectLevel */ \"(ssr)/./node_modules/qr.js/lib/ErrorCorrectLevel.js\");\n\nfunction QRRSBlock(totalCount, dataCount) {\n\tthis.totalCount = totalCount;\n\tthis.dataCount  = dataCount;\n}\n\nQRRSBlock.RS_BLOCK_TABLE = [\n\n\t// L\n\t// M\n\t// Q\n\t// H\n\n\t// 1\n\t[1, 26, 19],\n\t[1, 26, 16],\n\t[1, 26, 13],\n\t[1, 26, 9],\n\t\n\t// 2\n\t[1, 44, 34],\n\t[1, 44, 28],\n\t[1, 44, 22],\n\t[1, 44, 16],\n\n\t// 3\n\t[1, 70, 55],\n\t[1, 70, 44],\n\t[2, 35, 17],\n\t[2, 35, 13],\n\n\t// 4\t\t\n\t[1, 100, 80],\n\t[2, 50, 32],\n\t[2, 50, 24],\n\t[4, 25, 9],\n\t\n\t// 5\n\t[1, 134, 108],\n\t[2, 67, 43],\n\t[2, 33, 15, 2, 34, 16],\n\t[2, 33, 11, 2, 34, 12],\n\t\n\t// 6\n\t[2, 86, 68],\n\t[4, 43, 27],\n\t[4, 43, 19],\n\t[4, 43, 15],\n\t\n\t// 7\t\t\n\t[2, 98, 78],\n\t[4, 49, 31],\n\t[2, 32, 14, 4, 33, 15],\n\t[4, 39, 13, 1, 40, 14],\n\t\n\t// 8\n\t[2, 121, 97],\n\t[2, 60, 38, 2, 61, 39],\n\t[4, 40, 18, 2, 41, 19],\n\t[4, 40, 14, 2, 41, 15],\n\t\n\t// 9\n\t[2, 146, 116],\n\t[3, 58, 36, 2, 59, 37],\n\t[4, 36, 16, 4, 37, 17],\n\t[4, 36, 12, 4, 37, 13],\n\t\n\t// 10\t\t\n\t[2, 86, 68, 2, 87, 69],\n\t[4, 69, 43, 1, 70, 44],\n\t[6, 43, 19, 2, 44, 20],\n\t[6, 43, 15, 2, 44, 16],\n\n\t// 11\n\t[4, 101, 81],\n\t[1, 80, 50, 4, 81, 51],\n\t[4, 50, 22, 4, 51, 23],\n\t[3, 36, 12, 8, 37, 13],\n\n\t// 12\n\t[2, 116, 92, 2, 117, 93],\n\t[6, 58, 36, 2, 59, 37],\n\t[4, 46, 20, 6, 47, 21],\n\t[7, 42, 14, 4, 43, 15],\n\n\t// 13\n\t[4, 133, 107],\n\t[8, 59, 37, 1, 60, 38],\n\t[8, 44, 20, 4, 45, 21],\n\t[12, 33, 11, 4, 34, 12],\n\n\t// 14\n\t[3, 145, 115, 1, 146, 116],\n\t[4, 64, 40, 5, 65, 41],\n\t[11, 36, 16, 5, 37, 17],\n\t[11, 36, 12, 5, 37, 13],\n\n\t// 15\n\t[5, 109, 87, 1, 110, 88],\n\t[5, 65, 41, 5, 66, 42],\n\t[5, 54, 24, 7, 55, 25],\n\t[11, 36, 12],\n\n\t// 16\n\t[5, 122, 98, 1, 123, 99],\n\t[7, 73, 45, 3, 74, 46],\n\t[15, 43, 19, 2, 44, 20],\n\t[3, 45, 15, 13, 46, 16],\n\n\t// 17\n\t[1, 135, 107, 5, 136, 108],\n\t[10, 74, 46, 1, 75, 47],\n\t[1, 50, 22, 15, 51, 23],\n\t[2, 42, 14, 17, 43, 15],\n\n\t// 18\n\t[5, 150, 120, 1, 151, 121],\n\t[9, 69, 43, 4, 70, 44],\n\t[17, 50, 22, 1, 51, 23],\n\t[2, 42, 14, 19, 43, 15],\n\n\t// 19\n\t[3, 141, 113, 4, 142, 114],\n\t[3, 70, 44, 11, 71, 45],\n\t[17, 47, 21, 4, 48, 22],\n\t[9, 39, 13, 16, 40, 14],\n\n\t// 20\n\t[3, 135, 107, 5, 136, 108],\n\t[3, 67, 41, 13, 68, 42],\n\t[15, 54, 24, 5, 55, 25],\n\t[15, 43, 15, 10, 44, 16],\n\n\t// 21\n\t[4, 144, 116, 4, 145, 117],\n\t[17, 68, 42],\n\t[17, 50, 22, 6, 51, 23],\n\t[19, 46, 16, 6, 47, 17],\n\n\t// 22\n\t[2, 139, 111, 7, 140, 112],\n\t[17, 74, 46],\n\t[7, 54, 24, 16, 55, 25],\n\t[34, 37, 13],\n\n\t// 23\n\t[4, 151, 121, 5, 152, 122],\n\t[4, 75, 47, 14, 76, 48],\n\t[11, 54, 24, 14, 55, 25],\n\t[16, 45, 15, 14, 46, 16],\n\n\t// 24\n\t[6, 147, 117, 4, 148, 118],\n\t[6, 73, 45, 14, 74, 46],\n\t[11, 54, 24, 16, 55, 25],\n\t[30, 46, 16, 2, 47, 17],\n\n\t// 25\n\t[8, 132, 106, 4, 133, 107],\n\t[8, 75, 47, 13, 76, 48],\n\t[7, 54, 24, 22, 55, 25],\n\t[22, 45, 15, 13, 46, 16],\n\n\t// 26\n\t[10, 142, 114, 2, 143, 115],\n\t[19, 74, 46, 4, 75, 47],\n\t[28, 50, 22, 6, 51, 23],\n\t[33, 46, 16, 4, 47, 17],\n\n\t// 27\n\t[8, 152, 122, 4, 153, 123],\n\t[22, 73, 45, 3, 74, 46],\n\t[8, 53, 23, 26, 54, 24],\n\t[12, 45, 15, 28, 46, 16],\n\n\t// 28\n\t[3, 147, 117, 10, 148, 118],\n\t[3, 73, 45, 23, 74, 46],\n\t[4, 54, 24, 31, 55, 25],\n\t[11, 45, 15, 31, 46, 16],\n\n\t// 29\n\t[7, 146, 116, 7, 147, 117],\n\t[21, 73, 45, 7, 74, 46],\n\t[1, 53, 23, 37, 54, 24],\n\t[19, 45, 15, 26, 46, 16],\n\n\t// 30\n\t[5, 145, 115, 10, 146, 116],\n\t[19, 75, 47, 10, 76, 48],\n\t[15, 54, 24, 25, 55, 25],\n\t[23, 45, 15, 25, 46, 16],\n\n\t// 31\n\t[13, 145, 115, 3, 146, 116],\n\t[2, 74, 46, 29, 75, 47],\n\t[42, 54, 24, 1, 55, 25],\n\t[23, 45, 15, 28, 46, 16],\n\n\t// 32\n\t[17, 145, 115],\n\t[10, 74, 46, 23, 75, 47],\n\t[10, 54, 24, 35, 55, 25],\n\t[19, 45, 15, 35, 46, 16],\n\n\t// 33\n\t[17, 145, 115, 1, 146, 116],\n\t[14, 74, 46, 21, 75, 47],\n\t[29, 54, 24, 19, 55, 25],\n\t[11, 45, 15, 46, 46, 16],\n\n\t// 34\n\t[13, 145, 115, 6, 146, 116],\n\t[14, 74, 46, 23, 75, 47],\n\t[44, 54, 24, 7, 55, 25],\n\t[59, 46, 16, 1, 47, 17],\n\n\t// 35\n\t[12, 151, 121, 7, 152, 122],\n\t[12, 75, 47, 26, 76, 48],\n\t[39, 54, 24, 14, 55, 25],\n\t[22, 45, 15, 41, 46, 16],\n\n\t// 36\n\t[6, 151, 121, 14, 152, 122],\n\t[6, 75, 47, 34, 76, 48],\n\t[46, 54, 24, 10, 55, 25],\n\t[2, 45, 15, 64, 46, 16],\n\n\t// 37\n\t[17, 152, 122, 4, 153, 123],\n\t[29, 74, 46, 14, 75, 47],\n\t[49, 54, 24, 10, 55, 25],\n\t[24, 45, 15, 46, 46, 16],\n\n\t// 38\n\t[4, 152, 122, 18, 153, 123],\n\t[13, 74, 46, 32, 75, 47],\n\t[48, 54, 24, 14, 55, 25],\n\t[42, 45, 15, 32, 46, 16],\n\n\t// 39\n\t[20, 147, 117, 4, 148, 118],\n\t[40, 75, 47, 7, 76, 48],\n\t[43, 54, 24, 22, 55, 25],\n\t[10, 45, 15, 67, 46, 16],\n\n\t// 40\n\t[19, 148, 118, 6, 149, 119],\n\t[18, 75, 47, 31, 76, 48],\n\t[34, 54, 24, 34, 55, 25],\n\t[20, 45, 15, 61, 46, 16]\n];\n\nQRRSBlock.getRSBlocks = function(typeNumber, errorCorrectLevel) {\n\t\n\tvar rsBlock = QRRSBlock.getRsBlockTable(typeNumber, errorCorrectLevel);\n\t\n\tif (rsBlock == undefined) {\n\t\tthrow new Error(\"bad rs block @ typeNumber:\" + typeNumber + \"/errorCorrectLevel:\" + errorCorrectLevel);\n\t}\n\n\tvar length = rsBlock.length / 3;\n\t\n\tvar list = new Array();\n\t\n\tfor (var i = 0; i < length; i++) {\n\n\t\tvar count = rsBlock[i * 3 + 0];\n\t\tvar totalCount = rsBlock[i * 3 + 1];\n\t\tvar dataCount  = rsBlock[i * 3 + 2];\n\n\t\tfor (var j = 0; j < count; j++) {\n\t\t\tlist.push(new QRRSBlock(totalCount, dataCount) );\t\n\t\t}\n\t}\n\t\n\treturn list;\n}\n\nQRRSBlock.getRsBlockTable = function(typeNumber, errorCorrectLevel) {\n\n\tswitch(errorCorrectLevel) {\n\tcase ECL.L :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 0];\n\tcase ECL.M :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 1];\n\tcase ECL.Q :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 2];\n\tcase ECL.H :\n\t\treturn QRRSBlock.RS_BLOCK_TABLE[(typeNumber - 1) * 4 + 3];\n\tdefault :\n\t\treturn undefined;\n\t}\n}\n\nmodule.exports = QRRSBlock;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/RSBlock.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qr.js/lib/math.js":
/*!****************************************!*\
  !*** ./node_modules/qr.js/lib/math.js ***!
  \****************************************/
/***/ ((module) => {

eval("var QRMath = {\n\n\tglog : function(n) {\n\t\n\t\tif (n < 1) {\n\t\t\tthrow new Error(\"glog(\" + n + \")\");\n\t\t}\n\t\t\n\t\treturn QRMath.LOG_TABLE[n];\n\t},\n\t\n\tgexp : function(n) {\n\t\n\t\twhile (n < 0) {\n\t\t\tn += 255;\n\t\t}\n\t\n\t\twhile (n >= 256) {\n\t\t\tn -= 255;\n\t\t}\n\t\n\t\treturn QRMath.EXP_TABLE[n];\n\t},\n\t\n\tEXP_TABLE : new Array(256),\n\t\n\tLOG_TABLE : new Array(256)\n\n};\n\t\nfor (var i = 0; i < 8; i++) {\n\tQRMath.EXP_TABLE[i] = 1 << i;\n}\nfor (var i = 8; i < 256; i++) {\n\tQRMath.EXP_TABLE[i] = QRMath.EXP_TABLE[i - 4]\n\t\t^ QRMath.EXP_TABLE[i - 5]\n\t\t^ QRMath.EXP_TABLE[i - 6]\n\t\t^ QRMath.EXP_TABLE[i - 8];\n}\nfor (var i = 0; i < 255; i++) {\n\tQRMath.LOG_TABLE[QRMath.EXP_TABLE[i] ] = i;\n}\n\nmodule.exports = QRMath;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXIuanMvbGliL21hdGguanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxFQUFFO0FBQ0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRTtBQUNGO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxnQkFBZ0IsT0FBTztBQUN2QjtBQUNBO0FBQ0EsZ0JBQWdCLFNBQVM7QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdCQUFnQixTQUFTO0FBQ3pCO0FBQ0E7O0FBRUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9vcGVuZGFzaGJvYXJkLWZyb250ZW5kLy4vbm9kZV9tb2R1bGVzL3FyLmpzL2xpYi9tYXRoLmpzP2ZlOGMiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIFFSTWF0aCA9IHtcblxuXHRnbG9nIDogZnVuY3Rpb24obikge1xuXHRcblx0XHRpZiAobiA8IDEpIHtcblx0XHRcdHRocm93IG5ldyBFcnJvcihcImdsb2coXCIgKyBuICsgXCIpXCIpO1xuXHRcdH1cblx0XHRcblx0XHRyZXR1cm4gUVJNYXRoLkxPR19UQUJMRVtuXTtcblx0fSxcblx0XG5cdGdleHAgOiBmdW5jdGlvbihuKSB7XG5cdFxuXHRcdHdoaWxlIChuIDwgMCkge1xuXHRcdFx0biArPSAyNTU7XG5cdFx0fVxuXHRcblx0XHR3aGlsZSAobiA+PSAyNTYpIHtcblx0XHRcdG4gLT0gMjU1O1xuXHRcdH1cblx0XG5cdFx0cmV0dXJuIFFSTWF0aC5FWFBfVEFCTEVbbl07XG5cdH0sXG5cdFxuXHRFWFBfVEFCTEUgOiBuZXcgQXJyYXkoMjU2KSxcblx0XG5cdExPR19UQUJMRSA6IG5ldyBBcnJheSgyNTYpXG5cbn07XG5cdFxuZm9yICh2YXIgaSA9IDA7IGkgPCA4OyBpKyspIHtcblx0UVJNYXRoLkVYUF9UQUJMRVtpXSA9IDEgPDwgaTtcbn1cbmZvciAodmFyIGkgPSA4OyBpIDwgMjU2OyBpKyspIHtcblx0UVJNYXRoLkVYUF9UQUJMRVtpXSA9IFFSTWF0aC5FWFBfVEFCTEVbaSAtIDRdXG5cdFx0XiBRUk1hdGguRVhQX1RBQkxFW2kgLSA1XVxuXHRcdF4gUVJNYXRoLkVYUF9UQUJMRVtpIC0gNl1cblx0XHReIFFSTWF0aC5FWFBfVEFCTEVbaSAtIDhdO1xufVxuZm9yICh2YXIgaSA9IDA7IGkgPCAyNTU7IGkrKykge1xuXHRRUk1hdGguTE9HX1RBQkxFW1FSTWF0aC5FWFBfVEFCTEVbaV0gXSA9IGk7XG59XG5cbm1vZHVsZS5leHBvcnRzID0gUVJNYXRoO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/math.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qr.js/lib/mode.js":
/*!****************************************!*\
  !*** ./node_modules/qr.js/lib/mode.js ***!
  \****************************************/
/***/ ((module) => {

eval("module.exports = {\n\tMODE_NUMBER :\t\t1 << 0,\n\tMODE_ALPHA_NUM : \t1 << 1,\n\tMODE_8BIT_BYTE : \t1 << 2,\n\tMODE_KANJI :\t\t1 << 3\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcXIuanMvbGliL21vZGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9xci5qcy9saWIvbW9kZS5qcz81OGNjIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0ge1xuXHRNT0RFX05VTUJFUiA6XHRcdDEgPDwgMCxcblx0TU9ERV9BTFBIQV9OVU0gOiBcdDEgPDwgMSxcblx0TU9ERV84QklUX0JZVEUgOiBcdDEgPDwgMixcblx0TU9ERV9LQU5KSSA6XHRcdDEgPDwgM1xufTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/mode.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/qr.js/lib/util.js":
/*!****************************************!*\
  !*** ./node_modules/qr.js/lib/util.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("var Mode = __webpack_require__(/*! ./mode */ \"(ssr)/./node_modules/qr.js/lib/mode.js\");\nvar Polynomial = __webpack_require__(/*! ./Polynomial */ \"(ssr)/./node_modules/qr.js/lib/Polynomial.js\");\nvar math = __webpack_require__(/*! ./math */ \"(ssr)/./node_modules/qr.js/lib/math.js\");\n\nvar QRMaskPattern = {\n\tPATTERN000 : 0,\n\tPATTERN001 : 1,\n\tPATTERN010 : 2,\n\tPATTERN011 : 3,\n\tPATTERN100 : 4,\n\tPATTERN101 : 5,\n\tPATTERN110 : 6,\n\tPATTERN111 : 7\n};\n\nvar QRUtil = {\n\n    PATTERN_POSITION_TABLE : [\n\t    [],\n\t    [6, 18],\n\t    [6, 22],\n\t    [6, 26],\n\t    [6, 30],\n\t    [6, 34],\n\t    [6, 22, 38],\n\t    [6, 24, 42],\n\t    [6, 26, 46],\n\t    [6, 28, 50],\n\t    [6, 30, 54],\t\t\n\t    [6, 32, 58],\n\t    [6, 34, 62],\n\t    [6, 26, 46, 66],\n\t    [6, 26, 48, 70],\n\t    [6, 26, 50, 74],\n\t    [6, 30, 54, 78],\n\t    [6, 30, 56, 82],\n\t    [6, 30, 58, 86],\n\t    [6, 34, 62, 90],\n\t    [6, 28, 50, 72, 94],\n\t    [6, 26, 50, 74, 98],\n\t    [6, 30, 54, 78, 102],\n\t    [6, 28, 54, 80, 106],\n\t    [6, 32, 58, 84, 110],\n\t    [6, 30, 58, 86, 114],\n\t    [6, 34, 62, 90, 118],\n\t    [6, 26, 50, 74, 98, 122],\n\t    [6, 30, 54, 78, 102, 126],\n\t    [6, 26, 52, 78, 104, 130],\n\t    [6, 30, 56, 82, 108, 134],\n\t    [6, 34, 60, 86, 112, 138],\n\t    [6, 30, 58, 86, 114, 142],\n\t    [6, 34, 62, 90, 118, 146],\n\t    [6, 30, 54, 78, 102, 126, 150],\n\t    [6, 24, 50, 76, 102, 128, 154],\n\t    [6, 28, 54, 80, 106, 132, 158],\n\t    [6, 32, 58, 84, 110, 136, 162],\n\t    [6, 26, 54, 82, 110, 138, 166],\n\t    [6, 30, 58, 86, 114, 142, 170]\n    ],\n\n    G15 : (1 << 10) | (1 << 8) | (1 << 5) | (1 << 4) | (1 << 2) | (1 << 1) | (1 << 0),\n    G18 : (1 << 12) | (1 << 11) | (1 << 10) | (1 << 9) | (1 << 8) | (1 << 5) | (1 << 2) | (1 << 0),\n    G15_MASK : (1 << 14) | (1 << 12) | (1 << 10)\t| (1 << 4) | (1 << 1),\n\n    getBCHTypeInfo : function(data) {\n\t    var d = data << 10;\n\t    while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) >= 0) {\n\t\t    d ^= (QRUtil.G15 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G15) ) ); \t\n\t    }\n\t    return ( (data << 10) | d) ^ QRUtil.G15_MASK;\n    },\n\n    getBCHTypeNumber : function(data) {\n\t    var d = data << 12;\n\t    while (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) >= 0) {\n\t\t    d ^= (QRUtil.G18 << (QRUtil.getBCHDigit(d) - QRUtil.getBCHDigit(QRUtil.G18) ) ); \t\n\t    }\n\t    return (data << 12) | d;\n    },\n\n    getBCHDigit : function(data) {\n\n\t    var digit = 0;\n\n\t    while (data != 0) {\n\t\t    digit++;\n\t\t    data >>>= 1;\n\t    }\n\n\t    return digit;\n    },\n\n    getPatternPosition : function(typeNumber) {\n\t    return QRUtil.PATTERN_POSITION_TABLE[typeNumber - 1];\n    },\n\n    getMask : function(maskPattern, i, j) {\n\t    \n\t    switch (maskPattern) {\n\t\t    \n\t    case QRMaskPattern.PATTERN000 : return (i + j) % 2 == 0;\n\t    case QRMaskPattern.PATTERN001 : return i % 2 == 0;\n\t    case QRMaskPattern.PATTERN010 : return j % 3 == 0;\n\t    case QRMaskPattern.PATTERN011 : return (i + j) % 3 == 0;\n\t    case QRMaskPattern.PATTERN100 : return (Math.floor(i / 2) + Math.floor(j / 3) ) % 2 == 0;\n\t    case QRMaskPattern.PATTERN101 : return (i * j) % 2 + (i * j) % 3 == 0;\n\t    case QRMaskPattern.PATTERN110 : return ( (i * j) % 2 + (i * j) % 3) % 2 == 0;\n\t    case QRMaskPattern.PATTERN111 : return ( (i * j) % 3 + (i + j) % 2) % 2 == 0;\n\n\t    default :\n\t\t    throw new Error(\"bad maskPattern:\" + maskPattern);\n\t    }\n    },\n\n    getErrorCorrectPolynomial : function(errorCorrectLength) {\n\n\t    var a = new Polynomial([1], 0);\n\n\t    for (var i = 0; i < errorCorrectLength; i++) {\n\t\t    a = a.multiply(new Polynomial([1, math.gexp(i)], 0) );\n\t    }\n\n\t    return a;\n    },\n\n    getLengthInBits : function(mode, type) {\n\n\t    if (1 <= type && type < 10) {\n\n\t\t    // 1 - 9\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 10;\n\t\t    case Mode.MODE_ALPHA_NUM \t: return 9;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 8;\n\t\t    case Mode.MODE_KANJI  \t: return 8;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else if (type < 27) {\n\n\t\t    // 10 - 26\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 12;\n\t\t    case Mode.MODE_ALPHA_NUM \t: return 11;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 16;\n\t\t    case Mode.MODE_KANJI  \t: return 10;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else if (type < 41) {\n\n\t\t    // 27 - 40\n\n\t\t    switch(mode) {\n\t\t    case Mode.MODE_NUMBER \t: return 14;\n\t\t    case Mode.MODE_ALPHA_NUM\t: return 13;\n\t\t    case Mode.MODE_8BIT_BYTE\t: return 16;\n\t\t    case Mode.MODE_KANJI  \t: return 12;\n\t\t    default :\n\t\t\t    throw new Error(\"mode:\" + mode);\n\t\t    }\n\n\t    } else {\n\t\t    throw new Error(\"type:\" + type);\n\t    }\n    },\n\n    getLostPoint : function(qrCode) {\n\t    \n\t    var moduleCount = qrCode.getModuleCount();\n\t    \n\t    var lostPoint = 0;\n\t    \n\t    // LEVEL1\n\t    \n\t    for (var row = 0; row < moduleCount; row++) {\n\n\t\t    for (var col = 0; col < moduleCount; col++) {\n\n\t\t\t    var sameCount = 0;\n\t\t\t    var dark = qrCode.isDark(row, col);\n\n\t\t\t\tfor (var r = -1; r <= 1; r++) {\n\n\t\t\t\t    if (row + r < 0 || moduleCount <= row + r) {\n\t\t\t\t\t    continue;\n\t\t\t\t    }\n\n\t\t\t\t    for (var c = -1; c <= 1; c++) {\n\n\t\t\t\t\t    if (col + c < 0 || moduleCount <= col + c) {\n\t\t\t\t\t\t    continue;\n\t\t\t\t\t    }\n\n\t\t\t\t\t    if (r == 0 && c == 0) {\n\t\t\t\t\t\t    continue;\n\t\t\t\t\t    }\n\n\t\t\t\t\t    if (dark == qrCode.isDark(row + r, col + c) ) {\n\t\t\t\t\t\t    sameCount++;\n\t\t\t\t\t    }\n\t\t\t\t    }\n\t\t\t    }\n\n\t\t\t    if (sameCount > 5) {\n\t\t\t\t    lostPoint += (3 + sameCount - 5);\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL2\n\n\t    for (var row = 0; row < moduleCount - 1; row++) {\n\t\t    for (var col = 0; col < moduleCount - 1; col++) {\n\t\t\t    var count = 0;\n\t\t\t    if (qrCode.isDark(row,     col    ) ) count++;\n\t\t\t    if (qrCode.isDark(row + 1, col    ) ) count++;\n\t\t\t    if (qrCode.isDark(row,     col + 1) ) count++;\n\t\t\t    if (qrCode.isDark(row + 1, col + 1) ) count++;\n\t\t\t    if (count == 0 || count == 4) {\n\t\t\t\t    lostPoint += 3;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL3\n\n\t    for (var row = 0; row < moduleCount; row++) {\n\t\t    for (var col = 0; col < moduleCount - 6; col++) {\n\t\t\t    if (qrCode.isDark(row, col)\n\t\t\t\t\t    && !qrCode.isDark(row, col + 1)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 2)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 3)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 4)\n\t\t\t\t\t    && !qrCode.isDark(row, col + 5)\n\t\t\t\t\t    &&  qrCode.isDark(row, col + 6) ) {\n\t\t\t\t    lostPoint += 40;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    for (var col = 0; col < moduleCount; col++) {\n\t\t    for (var row = 0; row < moduleCount - 6; row++) {\n\t\t\t    if (qrCode.isDark(row, col)\n\t\t\t\t\t    && !qrCode.isDark(row + 1, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 2, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 3, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 4, col)\n\t\t\t\t\t    && !qrCode.isDark(row + 5, col)\n\t\t\t\t\t    &&  qrCode.isDark(row + 6, col) ) {\n\t\t\t\t    lostPoint += 40;\n\t\t\t    }\n\t\t    }\n\t    }\n\n\t    // LEVEL4\n\t    \n\t    var darkCount = 0;\n\n\t    for (var col = 0; col < moduleCount; col++) {\n\t\t    for (var row = 0; row < moduleCount; row++) {\n\t\t\t    if (qrCode.isDark(row, col) ) {\n\t\t\t\t    darkCount++;\n\t\t\t    }\n\t\t    }\n\t    }\n\t    \n\t    var ratio = Math.abs(100 * darkCount / moduleCount / moduleCount - 50) / 5;\n\t    lostPoint += ratio * 10;\n\n\t    return lostPoint;\t\t\n    }\n};\n\nmodule.exports = QRUtil;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/qr.js/lib/util.js\n");

/***/ })

};
;