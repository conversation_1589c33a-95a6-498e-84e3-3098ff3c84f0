"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-data-grid";
exports.ids = ["vendor-chunks/react-data-grid"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-data-grid/lib/styles.css":
/*!*****************************************************!*\
  !*** ./node_modules/react-data-grid/lib/styles.css ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"6bde5746ff7e\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhY3QtZGF0YS1ncmlkL2xpYi9zdHlsZXMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vb3BlbmRhc2hib2FyZC1mcm9udGVuZC8uL25vZGVfbW9kdWxlcy9yZWFjdC1kYXRhLWdyaWQvbGliL3N0eWxlcy5jc3M/NDVhYyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjZiZGU1NzQ2ZmY3ZVwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-data-grid/lib/styles.css\n");

/***/ }),

/***/ "(ssr)/./node_modules/react-data-grid/lib/bundle.js":
/*!****************************************************!*\
  !*** ./node_modules/react-data-grid/lib/bundle.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DataGridDefaultRenderersProvider: () => (/* binding */ DataGridDefaultRenderersProvider),\n/* harmony export */   Row: () => (/* binding */ RowComponent$1),\n/* harmony export */   SELECT_COLUMN_KEY: () => (/* binding */ SELECT_COLUMN_KEY),\n/* harmony export */   SelectCellFormatter: () => (/* binding */ SelectCellFormatter),\n/* harmony export */   SelectColumn: () => (/* binding */ SelectColumn),\n/* harmony export */   ToggleGroup: () => (/* binding */ ToggleGroup),\n/* harmony export */   TreeDataGrid: () => (/* binding */ TreeDataGrid$1),\n/* harmony export */   \"default\": () => (/* binding */ DataGrid$1),\n/* harmony export */   renderCheckbox: () => (/* binding */ renderCheckbox),\n/* harmony export */   renderHeaderCell: () => (/* binding */ renderHeaderCell),\n/* harmony export */   renderSortIcon: () => (/* binding */ renderSortIcon),\n/* harmony export */   renderSortPriority: () => (/* binding */ renderSortPriority),\n/* harmony export */   renderToggleGroup: () => (/* binding */ renderToggleGroup),\n/* harmony export */   renderValue: () => (/* binding */ renderValue),\n/* harmony export */   textEditor: () => (/* binding */ textEditor),\n/* harmony export */   useRowSelection: () => (/* binding */ useRowSelection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n\n\n\n\n\nfunction getColSpan(column, lastFrozenColumnIndex, args) {\n  const colSpan = typeof column.colSpan === 'function' ? column.colSpan(args) : 1;\n  if (Number.isInteger(colSpan) && colSpan > 1 && (!column.frozen || column.idx + colSpan - 1 <= lastFrozenColumnIndex)) {\n    return colSpan;\n  }\n  return undefined;\n}\n\nfunction stopPropagation(event) {\n  event.stopPropagation();\n}\nfunction scrollIntoView(element) {\n  element?.scrollIntoView({\n    inline: 'nearest',\n    block: 'nearest'\n  });\n}\n\nfunction createCellEvent(event) {\n  let defaultPrevented = false;\n  const cellEvent = {\n    ...event,\n    preventGridDefault() {\n      defaultPrevented = true;\n    },\n    isGridDefaultPrevented() {\n      return defaultPrevented;\n    }\n  };\n  Object.setPrototypeOf(cellEvent, Object.getPrototypeOf(event));\n  return cellEvent;\n}\n\nconst nonInputKeys = new Set(['Unidentified', 'Alt', 'AltGraph', 'CapsLock', 'Control', 'Fn', 'FnLock', 'Meta', 'NumLock', 'ScrollLock', 'Shift', 'Tab', 'ArrowDown', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'End', 'Home', 'PageDown', 'PageUp', 'Insert', 'ContextMenu', 'Escape', 'Pause', 'Play', 'PrintScreen', 'F1', 'F3', 'F4', 'F5', 'F6', 'F7', 'F8', 'F9', 'F10', 'F11', 'F12']);\nfunction isCtrlKeyHeldDown(e) {\n  return (e.ctrlKey || e.metaKey) && e.key !== 'Control';\n}\nfunction isDefaultCellInput(event) {\n  return !nonInputKeys.has(event.key);\n}\nfunction onEditorNavigation({\n  key,\n  target\n}) {\n  if (key === 'Tab' && (target instanceof HTMLInputElement || target instanceof HTMLTextAreaElement || target instanceof HTMLSelectElement)) {\n    return target.closest('.rdg-editor-container')?.querySelectorAll('input, textarea, select').length === 1;\n  }\n  return false;\n}\n\nconst measuringCellClassname = \"mlln6zg7-0-0-beta-41\";\nfunction renderMeasuringCells(viewportColumns) {\n  return viewportColumns.map(({\n    key,\n    idx,\n    minWidth,\n    maxWidth\n  }) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    className: measuringCellClassname,\n    style: {\n      gridColumnStart: idx + 1,\n      minWidth,\n      maxWidth\n    },\n    \"data-measuring-cell-key\": key\n  }, key));\n}\n\nfunction isSelectedCellEditable({\n  selectedPosition,\n  columns,\n  rows\n}) {\n  const column = columns[selectedPosition.idx];\n  const row = rows[selectedPosition.rowIdx];\n  return isCellEditableUtil(column, row);\n}\nfunction isCellEditableUtil(column, row) {\n  return column.renderEditCell != null && (typeof column.editable === 'function' ? column.editable(row) : column.editable) !== false;\n}\nfunction getSelectedCellColSpan({\n  rows,\n  topSummaryRows,\n  bottomSummaryRows,\n  rowIdx,\n  mainHeaderRowIdx,\n  lastFrozenColumnIndex,\n  column\n}) {\n  const topSummaryRowsCount = topSummaryRows?.length ?? 0;\n  if (rowIdx === mainHeaderRowIdx) {\n    return getColSpan(column, lastFrozenColumnIndex, {\n      type: 'HEADER'\n    });\n  }\n  if (topSummaryRows && rowIdx > mainHeaderRowIdx && rowIdx <= topSummaryRowsCount + mainHeaderRowIdx) {\n    return getColSpan(column, lastFrozenColumnIndex, {\n      type: 'SUMMARY',\n      row: topSummaryRows[rowIdx + topSummaryRowsCount]\n    });\n  }\n  if (rowIdx >= 0 && rowIdx < rows.length) {\n    const row = rows[rowIdx];\n    return getColSpan(column, lastFrozenColumnIndex, {\n      type: 'ROW',\n      row\n    });\n  }\n  if (bottomSummaryRows) {\n    return getColSpan(column, lastFrozenColumnIndex, {\n      type: 'SUMMARY',\n      row: bottomSummaryRows[rowIdx - rows.length]\n    });\n  }\n  return undefined;\n}\nfunction getNextSelectedCellPosition({\n  moveUp,\n  moveNext,\n  cellNavigationMode,\n  columns,\n  colSpanColumns,\n  rows,\n  topSummaryRows,\n  bottomSummaryRows,\n  minRowIdx,\n  mainHeaderRowIdx,\n  maxRowIdx,\n  currentPosition: {\n    idx: currentIdx,\n    rowIdx: currentRowIdx\n  },\n  nextPosition,\n  lastFrozenColumnIndex,\n  isCellWithinBounds\n}) {\n  let {\n    idx: nextIdx,\n    rowIdx: nextRowIdx\n  } = nextPosition;\n  const columnsCount = columns.length;\n  const setColSpan = moveNext => {\n    for (const column of colSpanColumns) {\n      const colIdx = column.idx;\n      if (colIdx > nextIdx) break;\n      const colSpan = getSelectedCellColSpan({\n        rows,\n        topSummaryRows,\n        bottomSummaryRows,\n        rowIdx: nextRowIdx,\n        mainHeaderRowIdx,\n        lastFrozenColumnIndex,\n        column\n      });\n      if (colSpan && nextIdx > colIdx && nextIdx < colSpan + colIdx) {\n        nextIdx = colIdx + (moveNext ? colSpan : 0);\n        break;\n      }\n    }\n  };\n  const getParentRowIdx = parent => {\n    return parent.level + mainHeaderRowIdx;\n  };\n  const setHeaderGroupColAndRowSpan = () => {\n    if (moveNext) {\n      const nextColumn = columns[nextIdx];\n      let parent = nextColumn.parent;\n      while (parent !== undefined) {\n        const parentRowIdx = getParentRowIdx(parent);\n        if (nextRowIdx === parentRowIdx) {\n          nextIdx = parent.idx + parent.colSpan;\n          break;\n        }\n        parent = parent.parent;\n      }\n    } else if (moveUp) {\n      const nextColumn = columns[nextIdx];\n      let parent = nextColumn.parent;\n      let found = false;\n      while (parent !== undefined) {\n        const parentRowIdx = getParentRowIdx(parent);\n        if (nextRowIdx >= parentRowIdx) {\n          nextIdx = parent.idx;\n          nextRowIdx = parentRowIdx;\n          found = true;\n          break;\n        }\n        parent = parent.parent;\n      }\n      if (!found) {\n        nextIdx = currentIdx;\n        nextRowIdx = currentRowIdx;\n      }\n    }\n  };\n  if (isCellWithinBounds(nextPosition)) {\n    setColSpan(moveNext);\n    if (nextRowIdx < mainHeaderRowIdx) {\n      setHeaderGroupColAndRowSpan();\n    }\n  }\n  if (cellNavigationMode === 'CHANGE_ROW') {\n    const isAfterLastColumn = nextIdx === columnsCount;\n    const isBeforeFirstColumn = nextIdx === -1;\n    if (isAfterLastColumn) {\n      const isLastRow = nextRowIdx === maxRowIdx;\n      if (!isLastRow) {\n        nextIdx = 0;\n        nextRowIdx += 1;\n      }\n    } else if (isBeforeFirstColumn) {\n      const isFirstRow = nextRowIdx === minRowIdx;\n      if (!isFirstRow) {\n        nextRowIdx -= 1;\n        nextIdx = columnsCount - 1;\n      }\n      setColSpan(false);\n    }\n  }\n  if (nextRowIdx < mainHeaderRowIdx) {\n    const nextColumn = columns[nextIdx];\n    let parent = nextColumn.parent;\n    const nextParentRowIdx = nextRowIdx;\n    nextRowIdx = mainHeaderRowIdx;\n    while (parent !== undefined) {\n      const parentRowIdx = getParentRowIdx(parent);\n      if (parentRowIdx >= nextParentRowIdx) {\n        nextRowIdx = parentRowIdx;\n        nextIdx = parent.idx;\n      }\n      parent = parent.parent;\n    }\n  }\n  return {\n    idx: nextIdx,\n    rowIdx: nextRowIdx\n  };\n}\nfunction canExitGrid({\n  maxColIdx,\n  minRowIdx,\n  maxRowIdx,\n  selectedPosition: {\n    rowIdx,\n    idx\n  },\n  shiftKey\n}) {\n  const atLastCellInRow = idx === maxColIdx;\n  const atFirstCellInRow = idx === 0;\n  const atLastRow = rowIdx === maxRowIdx;\n  const atFirstRow = rowIdx === minRowIdx;\n  return shiftKey ? atFirstCellInRow && atFirstRow : atLastCellInRow && atLastRow;\n}\n\nconst cell = \"cj343x07-0-0-beta-41\";\nconst cellClassname = `rdg-cell ${cell}`;\nconst cellFrozen = \"csofj7r7-0-0-beta-41\";\nconst cellFrozenClassname = `rdg-cell-frozen ${cellFrozen}`;\nconst cellFrozenLast = \"ch2wcw87-0-0-beta-41\";\nconst cellFrozenLastClassname = `rdg-cell-frozen-last ${cellFrozenLast}`;\n\nfunction getRowStyle(rowIdx, height) {\n  if (height !== undefined) {\n    return {\n      '--rdg-grid-row-start': rowIdx,\n      '--rdg-row-height': `${height}px`\n    };\n  }\n  return {\n    '--rdg-grid-row-start': rowIdx\n  };\n}\nfunction getHeaderCellStyle(column, rowIdx, rowSpan) {\n  const gridRowEnd = rowIdx + 1;\n  const paddingBlockStart = `calc(${rowSpan - 1} * var(--rdg-header-row-height))`;\n  if (column.parent === undefined) {\n    return {\n      insetBlockStart: 0,\n      gridRowStart: 1,\n      gridRowEnd,\n      paddingBlockStart\n    };\n  }\n  return {\n    insetBlockStart: `calc(${rowIdx - rowSpan} * var(--rdg-header-row-height))`,\n    gridRowStart: gridRowEnd - rowSpan,\n    gridRowEnd,\n    paddingBlockStart\n  };\n}\nfunction getCellStyle(column, colSpan = 1) {\n  const index = column.idx + 1;\n  return {\n    gridColumnStart: index,\n    gridColumnEnd: index + colSpan,\n    insetInlineStart: column.frozen ? `var(--rdg-frozen-left-${column.idx})` : undefined\n  };\n}\nfunction getCellClassname(column, ...extraClasses) {\n  return (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cellClassname, ...extraClasses, column.frozen && cellFrozenClassname, column.isLastFrozenColumn && cellFrozenLastClassname);\n}\n\nconst {\n  min,\n  max,\n  floor,\n  sign,\n  abs\n} = Math;\nfunction assertIsValidKeyGetter(keyGetter) {\n  if (typeof keyGetter !== 'function') {\n    throw new Error('Please specify the rowKeyGetter prop to use selection');\n  }\n}\nfunction clampColumnWidth(width, {\n  minWidth,\n  maxWidth\n}) {\n  width = max(width, minWidth);\n  if (typeof maxWidth === 'number' && maxWidth >= minWidth) {\n    return min(width, maxWidth);\n  }\n  return width;\n}\nfunction getHeaderCellRowSpan(column, rowIdx) {\n  return column.parent === undefined ? rowIdx : column.level - column.parent.level;\n}\n\nconst checkboxLabel = \"c1bn88vv7-0-0-beta-41\";\nconst checkboxLabelClassname = `rdg-checkbox-label ${checkboxLabel}`;\nconst checkboxInput = \"c1qt073l7-0-0-beta-41\";\nconst checkboxInputClassname = `rdg-checkbox-input ${checkboxInput}`;\nconst checkbox = \"cf71kmq7-0-0-beta-41\";\nconst checkboxClassname = `rdg-checkbox ${checkbox}`;\nconst checkboxLabelDisabled = \"c1lwve4p7-0-0-beta-41\";\nconst checkboxLabelDisabledClassname = `rdg-checkbox-label-disabled ${checkboxLabelDisabled}`;\nfunction renderCheckbox({\n  onChange,\n  ...props\n}) {\n  function handleChange(e) {\n    onChange(e.target.checked, e.nativeEvent.shiftKey);\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"label\", {\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(checkboxLabelClassname, props.disabled && checkboxLabelDisabledClassname),\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"input\", {\n      type: \"checkbox\",\n      ...props,\n      className: checkboxInputClassname,\n      onChange: handleChange\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n      className: checkboxClassname\n    })]\n  });\n}\n\nconst groupCellContent = \"g1s9ylgp7-0-0-beta-41\";\nconst groupCellContentClassname = `rdg-group-cell-content ${groupCellContent}`;\nconst caret = \"cz54e4y7-0-0-beta-41\";\nconst caretClassname = `rdg-caret ${caret}`;\nfunction renderToggleGroup(props) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ToggleGroup, {\n    ...props\n  });\n}\nfunction ToggleGroup({\n  groupKey,\n  isExpanded,\n  tabIndex,\n  toggleGroup\n}) {\n  function handleKeyDown({\n    key\n  }) {\n    if (key === 'Enter') {\n      toggleGroup();\n    }\n  }\n  const d = isExpanded ? 'M1 1 L 7 7 L 13 1' : 'M1 7 L 7 1 L 13 7';\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"span\", {\n    className: groupCellContentClassname,\n    tabIndex: tabIndex,\n    onKeyDown: handleKeyDown,\n    children: [groupKey, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"svg\", {\n      viewBox: \"0 0 14 8\",\n      width: \"14\",\n      height: \"8\",\n      className: caretClassname,\n      \"aria-hidden\": true,\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"path\", {\n        d: d\n      })\n    })]\n  });\n}\n\nfunction renderValue(props) {\n  try {\n    return props.row[props.column.key];\n  } catch {\n    return null;\n  }\n}\n\nconst DataGridDefaultRenderersContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nconst DataGridDefaultRenderersProvider = DataGridDefaultRenderersContext.Provider;\nfunction useDefaultRenderers() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(DataGridDefaultRenderersContext);\n}\n\nfunction SelectCellFormatter({\n  value,\n  tabIndex,\n  disabled,\n  onChange,\n  'aria-label': ariaLabel,\n  'aria-labelledby': ariaLabelledBy\n}) {\n  const renderCheckbox = useDefaultRenderers().renderCheckbox;\n  return renderCheckbox({\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    tabIndex,\n    disabled,\n    checked: value,\n    onChange\n  });\n}\n\nconst RowSelectionContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nconst RowSelectionProvider = RowSelectionContext.Provider;\nconst RowSelectionChangeContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\nconst RowSelectionChangeProvider = RowSelectionChangeContext.Provider;\nfunction useRowSelection() {\n  const rowSelectionContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(RowSelectionContext);\n  const rowSelectionChangeContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(RowSelectionChangeContext);\n  if (rowSelectionContext === undefined || rowSelectionChangeContext === undefined) {\n    throw new Error('useRowSelection must be used within DataGrid cells');\n  }\n  return [rowSelectionContext, rowSelectionChangeContext];\n}\n\nconst SELECT_COLUMN_KEY = 'select-row';\nfunction HeaderRenderer(props) {\n  const [isRowSelected, onRowSelectionChange] = useRowSelection();\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SelectCellFormatter, {\n    \"aria-label\": \"Select All\",\n    tabIndex: props.tabIndex,\n    value: isRowSelected,\n    onChange: checked => {\n      onRowSelectionChange({\n        type: 'HEADER',\n        checked\n      });\n    }\n  });\n}\nfunction SelectFormatter(props) {\n  const [isRowSelected, onRowSelectionChange] = useRowSelection();\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SelectCellFormatter, {\n    \"aria-label\": \"Select\",\n    tabIndex: props.tabIndex,\n    value: isRowSelected,\n    onChange: (checked, isShiftClick) => {\n      onRowSelectionChange({\n        type: 'ROW',\n        row: props.row,\n        checked,\n        isShiftClick\n      });\n    }\n  });\n}\nfunction SelectGroupFormatter(props) {\n  const [isRowSelected, onRowSelectionChange] = useRowSelection();\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SelectCellFormatter, {\n    \"aria-label\": \"Select Group\",\n    tabIndex: props.tabIndex,\n    value: isRowSelected,\n    onChange: checked => {\n      onRowSelectionChange({\n        type: 'ROW',\n        row: props.row,\n        checked,\n        isShiftClick: false\n      });\n    }\n  });\n}\nconst SelectColumn = {\n  key: SELECT_COLUMN_KEY,\n  name: '',\n  width: 35,\n  minWidth: 35,\n  maxWidth: 35,\n  resizable: false,\n  sortable: false,\n  frozen: true,\n  renderHeaderCell(props) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(HeaderRenderer, {\n      ...props\n    });\n  },\n  renderCell(props) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SelectFormatter, {\n      ...props\n    });\n  },\n  renderGroupCell(props) {\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SelectGroupFormatter, {\n      ...props\n    });\n  }\n};\n\nconst DEFAULT_COLUMN_WIDTH = 'auto';\nconst DEFAULT_COLUMN_MIN_WIDTH = 50;\nfunction useCalculatedColumns({\n  rawColumns,\n  defaultColumnOptions,\n  getColumnWidth,\n  viewportWidth,\n  scrollLeft,\n  enableVirtualization\n}) {\n  const defaultWidth = defaultColumnOptions?.width ?? DEFAULT_COLUMN_WIDTH;\n  const defaultMinWidth = defaultColumnOptions?.minWidth ?? DEFAULT_COLUMN_MIN_WIDTH;\n  const defaultMaxWidth = defaultColumnOptions?.maxWidth ?? undefined;\n  const defaultCellRenderer = defaultColumnOptions?.renderCell ?? renderValue;\n  const defaultSortable = defaultColumnOptions?.sortable ?? false;\n  const defaultResizable = defaultColumnOptions?.resizable ?? false;\n  const defaultDraggable = defaultColumnOptions?.draggable ?? false;\n  const {\n    columns,\n    colSpanColumns,\n    lastFrozenColumnIndex,\n    headerRowsCount\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    let lastFrozenColumnIndex = -1;\n    let headerRowsCount = 1;\n    const columns = [];\n    collectColumns(rawColumns, 1);\n    function collectColumns(rawColumns, level, parent) {\n      for (const rawColumn of rawColumns) {\n        if ('children' in rawColumn) {\n          const calculatedColumnParent = {\n            name: rawColumn.name,\n            parent,\n            idx: -1,\n            colSpan: 0,\n            level: 0,\n            headerCellClass: rawColumn.headerCellClass\n          };\n          collectColumns(rawColumn.children, level + 1, calculatedColumnParent);\n          continue;\n        }\n        const frozen = rawColumn.frozen ?? false;\n        const column = {\n          ...rawColumn,\n          parent,\n          idx: 0,\n          level: 0,\n          frozen,\n          isLastFrozenColumn: false,\n          width: rawColumn.width ?? defaultWidth,\n          minWidth: rawColumn.minWidth ?? defaultMinWidth,\n          maxWidth: rawColumn.maxWidth ?? defaultMaxWidth,\n          sortable: rawColumn.sortable ?? defaultSortable,\n          resizable: rawColumn.resizable ?? defaultResizable,\n          draggable: rawColumn.draggable ?? defaultDraggable,\n          renderCell: rawColumn.renderCell ?? defaultCellRenderer\n        };\n        columns.push(column);\n        if (frozen) {\n          lastFrozenColumnIndex++;\n        }\n        if (level > headerRowsCount) {\n          headerRowsCount = level;\n        }\n      }\n    }\n    columns.sort(({\n      key: aKey,\n      frozen: frozenA\n    }, {\n      key: bKey,\n      frozen: frozenB\n    }) => {\n      if (aKey === SELECT_COLUMN_KEY) return -1;\n      if (bKey === SELECT_COLUMN_KEY) return 1;\n      if (frozenA) {\n        if (frozenB) return 0;\n        return -1;\n      }\n      if (frozenB) return 1;\n      return 0;\n    });\n    const colSpanColumns = [];\n    columns.forEach((column, idx) => {\n      column.idx = idx;\n      updateColumnParent(column, idx, 0);\n      if (column.colSpan != null) {\n        colSpanColumns.push(column);\n      }\n    });\n    if (lastFrozenColumnIndex !== -1) {\n      columns[lastFrozenColumnIndex].isLastFrozenColumn = true;\n    }\n    return {\n      columns,\n      colSpanColumns,\n      lastFrozenColumnIndex,\n      headerRowsCount\n    };\n  }, [rawColumns, defaultWidth, defaultMinWidth, defaultMaxWidth, defaultCellRenderer, defaultResizable, defaultSortable, defaultDraggable]);\n  const {\n    templateColumns,\n    layoutCssVars,\n    totalFrozenColumnWidth,\n    columnMetrics\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const columnMetrics = new Map();\n    let left = 0;\n    let totalFrozenColumnWidth = 0;\n    const templateColumns = [];\n    for (const column of columns) {\n      let width = getColumnWidth(column);\n      if (typeof width === 'number') {\n        width = clampColumnWidth(width, column);\n      } else {\n        width = column.minWidth;\n      }\n      templateColumns.push(`${width}px`);\n      columnMetrics.set(column, {\n        width,\n        left\n      });\n      left += width;\n    }\n    if (lastFrozenColumnIndex !== -1) {\n      const columnMetric = columnMetrics.get(columns[lastFrozenColumnIndex]);\n      totalFrozenColumnWidth = columnMetric.left + columnMetric.width;\n    }\n    const layoutCssVars = {};\n    for (let i = 0; i <= lastFrozenColumnIndex; i++) {\n      const column = columns[i];\n      layoutCssVars[`--rdg-frozen-left-${column.idx}`] = `${columnMetrics.get(column).left}px`;\n    }\n    return {\n      templateColumns,\n      layoutCssVars,\n      totalFrozenColumnWidth,\n      columnMetrics\n    };\n  }, [getColumnWidth, columns, lastFrozenColumnIndex]);\n  const [colOverscanStartIdx, colOverscanEndIdx] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (!enableVirtualization) {\n      return [0, columns.length - 1];\n    }\n    const viewportLeft = scrollLeft + totalFrozenColumnWidth;\n    const viewportRight = scrollLeft + viewportWidth;\n    const lastColIdx = columns.length - 1;\n    const firstUnfrozenColumnIdx = min(lastFrozenColumnIndex + 1, lastColIdx);\n    if (viewportLeft >= viewportRight) {\n      return [firstUnfrozenColumnIdx, firstUnfrozenColumnIdx];\n    }\n    let colVisibleStartIdx = firstUnfrozenColumnIdx;\n    while (colVisibleStartIdx < lastColIdx) {\n      const {\n        left,\n        width\n      } = columnMetrics.get(columns[colVisibleStartIdx]);\n      if (left + width > viewportLeft) {\n        break;\n      }\n      colVisibleStartIdx++;\n    }\n    let colVisibleEndIdx = colVisibleStartIdx;\n    while (colVisibleEndIdx < lastColIdx) {\n      const {\n        left,\n        width\n      } = columnMetrics.get(columns[colVisibleEndIdx]);\n      if (left + width >= viewportRight) {\n        break;\n      }\n      colVisibleEndIdx++;\n    }\n    const colOverscanStartIdx = max(firstUnfrozenColumnIdx, colVisibleStartIdx - 1);\n    const colOverscanEndIdx = min(lastColIdx, colVisibleEndIdx + 1);\n    return [colOverscanStartIdx, colOverscanEndIdx];\n  }, [columnMetrics, columns, lastFrozenColumnIndex, scrollLeft, totalFrozenColumnWidth, viewportWidth, enableVirtualization]);\n  return {\n    columns,\n    colSpanColumns,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    templateColumns,\n    layoutCssVars,\n    headerRowsCount,\n    lastFrozenColumnIndex,\n    totalFrozenColumnWidth\n  };\n}\nfunction updateColumnParent(column, index, level) {\n  if (level < column.level) {\n    column.level = level;\n  }\n  if (column.parent !== undefined) {\n    const {\n      parent\n    } = column;\n    if (parent.idx === -1) {\n      parent.idx = index;\n    }\n    parent.colSpan += 1;\n    updateColumnParent(parent, index, level - 1);\n  }\n}\n\nconst useLayoutEffect = typeof window === 'undefined' ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n\nfunction useColumnWidths(columns, viewportColumns, templateColumns, gridRef, gridWidth, resizedColumnWidths, measuredColumnWidths, setResizedColumnWidths, setMeasuredColumnWidths, onColumnResize) {\n  const prevGridWidthRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(gridWidth);\n  const columnsCanFlex = columns.length === viewportColumns.length;\n  const ignorePreviouslyMeasuredColumns = columnsCanFlex && gridWidth !== prevGridWidthRef.current;\n  const newTemplateColumns = [...templateColumns];\n  const columnsToMeasure = [];\n  for (const {\n    key,\n    idx,\n    width\n  } of viewportColumns) {\n    if (typeof width === 'string' && (ignorePreviouslyMeasuredColumns || !measuredColumnWidths.has(key)) && !resizedColumnWidths.has(key)) {\n      newTemplateColumns[idx] = width;\n      columnsToMeasure.push(key);\n    }\n  }\n  const gridTemplateColumns = newTemplateColumns.join(' ');\n  useLayoutEffect(() => {\n    prevGridWidthRef.current = gridWidth;\n    updateMeasuredWidths(columnsToMeasure);\n  });\n  function updateMeasuredWidths(columnsToMeasure) {\n    if (columnsToMeasure.length === 0) return;\n    setMeasuredColumnWidths(measuredColumnWidths => {\n      const newMeasuredColumnWidths = new Map(measuredColumnWidths);\n      let hasChanges = false;\n      for (const key of columnsToMeasure) {\n        const measuredWidth = measureColumnWidth(gridRef, key);\n        hasChanges ||= measuredWidth !== measuredColumnWidths.get(key);\n        if (measuredWidth === undefined) {\n          newMeasuredColumnWidths.delete(key);\n        } else {\n          newMeasuredColumnWidths.set(key, measuredWidth);\n        }\n      }\n      return hasChanges ? newMeasuredColumnWidths : measuredColumnWidths;\n    });\n  }\n  function handleColumnResize(column, nextWidth) {\n    const {\n      key: resizingKey\n    } = column;\n    const newTemplateColumns = [...templateColumns];\n    const columnsToMeasure = [];\n    for (const {\n      key,\n      idx,\n      width\n    } of viewportColumns) {\n      if (resizingKey === key) {\n        const width = typeof nextWidth === 'number' ? `${nextWidth}px` : nextWidth;\n        newTemplateColumns[idx] = width;\n      } else if (columnsCanFlex && typeof width === 'string' && !resizedColumnWidths.has(key)) {\n        newTemplateColumns[idx] = width;\n        columnsToMeasure.push(key);\n      }\n    }\n    gridRef.current.style.gridTemplateColumns = newTemplateColumns.join(' ');\n    const measuredWidth = typeof nextWidth === 'number' ? nextWidth : measureColumnWidth(gridRef, resizingKey);\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n      setResizedColumnWidths(resizedColumnWidths => {\n        const newResizedColumnWidths = new Map(resizedColumnWidths);\n        newResizedColumnWidths.set(resizingKey, measuredWidth);\n        return newResizedColumnWidths;\n      });\n      updateMeasuredWidths(columnsToMeasure);\n    });\n    onColumnResize?.(column.idx, measuredWidth);\n  }\n  return {\n    gridTemplateColumns,\n    handleColumnResize\n  };\n}\nfunction measureColumnWidth(gridRef, key) {\n  const selector = `[data-measuring-cell-key=\"${CSS.escape(key)}\"]`;\n  const measuringCell = gridRef.current.querySelector(selector);\n  return measuringCell?.getBoundingClientRect().width;\n}\n\nfunction useGridDimensions() {\n  const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [inlineSize, setInlineSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  const [blockSize, setBlockSize] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n  useLayoutEffect(() => {\n    const {\n      ResizeObserver\n    } = window;\n    if (ResizeObserver == null) return;\n    const {\n      clientWidth,\n      clientHeight,\n      offsetWidth,\n      offsetHeight\n    } = gridRef.current;\n    const {\n      width,\n      height\n    } = gridRef.current.getBoundingClientRect();\n    const initialWidth = width - offsetWidth + clientWidth;\n    const initialHeight = height - offsetHeight + clientHeight;\n    setInlineSize(initialWidth);\n    setBlockSize(initialHeight);\n    const resizeObserver = new ResizeObserver(entries => {\n      const size = entries[0].contentBoxSize[0];\n      (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n        setInlineSize(size.inlineSize);\n        setBlockSize(size.blockSize);\n      });\n    });\n    resizeObserver.observe(gridRef.current);\n    return () => {\n      resizeObserver.disconnect();\n    };\n  }, []);\n  return [gridRef, inlineSize, blockSize];\n}\n\nfunction useLatestFunc(fn) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fn);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = fn;\n  });\n  const callbackFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args) => {\n    ref.current(...args);\n  }, []);\n  return fn ? callbackFn : fn;\n}\n\nfunction useRovingTabIndex(isSelected) {\n  const [isChildFocused, setIsChildFocused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  if (isChildFocused && !isSelected) {\n    setIsChildFocused(false);\n  }\n  function onFocus(event) {\n    if (event.target !== event.currentTarget) {\n      setIsChildFocused(true);\n    }\n  }\n  const isFocusable = isSelected && !isChildFocused;\n  return {\n    tabIndex: isFocusable ? 0 : -1,\n    childTabIndex: isSelected ? 0 : -1,\n    onFocus: isSelected ? onFocus : undefined\n  };\n}\n\nfunction useViewportColumns({\n  columns,\n  colSpanColumns,\n  rows,\n  topSummaryRows,\n  bottomSummaryRows,\n  colOverscanStartIdx,\n  colOverscanEndIdx,\n  lastFrozenColumnIndex,\n  rowOverscanStartIdx,\n  rowOverscanEndIdx\n}) {\n  const startIdx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (colOverscanStartIdx === 0) return 0;\n    let startIdx = colOverscanStartIdx;\n    const updateStartIdx = (colIdx, colSpan) => {\n      if (colSpan !== undefined && colIdx + colSpan > colOverscanStartIdx) {\n        startIdx = colIdx;\n        return true;\n      }\n      return false;\n    };\n    for (const column of colSpanColumns) {\n      const colIdx = column.idx;\n      if (colIdx >= startIdx) break;\n      if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, {\n        type: 'HEADER'\n      }))) {\n        break;\n      }\n      for (let rowIdx = rowOverscanStartIdx; rowIdx <= rowOverscanEndIdx; rowIdx++) {\n        const row = rows[rowIdx];\n        if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, {\n          type: 'ROW',\n          row\n        }))) {\n          break;\n        }\n      }\n      if (topSummaryRows != null) {\n        for (const row of topSummaryRows) {\n          if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, {\n            type: 'SUMMARY',\n            row\n          }))) {\n            break;\n          }\n        }\n      }\n      if (bottomSummaryRows != null) {\n        for (const row of bottomSummaryRows) {\n          if (updateStartIdx(colIdx, getColSpan(column, lastFrozenColumnIndex, {\n            type: 'SUMMARY',\n            row\n          }))) {\n            break;\n          }\n        }\n      }\n    }\n    return startIdx;\n  }, [rowOverscanStartIdx, rowOverscanEndIdx, rows, topSummaryRows, bottomSummaryRows, colOverscanStartIdx, lastFrozenColumnIndex, colSpanColumns]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const viewportColumns = [];\n    for (let colIdx = 0; colIdx <= colOverscanEndIdx; colIdx++) {\n      const column = columns[colIdx];\n      if (colIdx < startIdx && !column.frozen) continue;\n      viewportColumns.push(column);\n    }\n    return viewportColumns;\n  }, [startIdx, colOverscanEndIdx, columns]);\n}\n\nfunction useViewportRows({\n  rows,\n  rowHeight,\n  clientHeight,\n  scrollTop,\n  enableVirtualization\n}) {\n  const {\n    totalRowHeight,\n    gridTemplateRows,\n    getRowTop,\n    getRowHeight,\n    findRowIdx\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (typeof rowHeight === 'number') {\n      return {\n        totalRowHeight: rowHeight * rows.length,\n        gridTemplateRows: ` repeat(${rows.length}, ${rowHeight}px)`,\n        getRowTop: rowIdx => rowIdx * rowHeight,\n        getRowHeight: () => rowHeight,\n        findRowIdx: offset => floor(offset / rowHeight)\n      };\n    }\n    let totalRowHeight = 0;\n    let gridTemplateRows = ' ';\n    const rowPositions = rows.map(row => {\n      const currentRowHeight = rowHeight(row);\n      const position = {\n        top: totalRowHeight,\n        height: currentRowHeight\n      };\n      gridTemplateRows += `${currentRowHeight}px `;\n      totalRowHeight += currentRowHeight;\n      return position;\n    });\n    const validateRowIdx = rowIdx => {\n      return max(0, min(rows.length - 1, rowIdx));\n    };\n    return {\n      totalRowHeight,\n      gridTemplateRows,\n      getRowTop: rowIdx => rowPositions[validateRowIdx(rowIdx)].top,\n      getRowHeight: rowIdx => rowPositions[validateRowIdx(rowIdx)].height,\n      findRowIdx(offset) {\n        let start = 0;\n        let end = rowPositions.length - 1;\n        while (start <= end) {\n          const middle = start + floor((end - start) / 2);\n          const currentOffset = rowPositions[middle].top;\n          if (currentOffset === offset) return middle;\n          if (currentOffset < offset) {\n            start = middle + 1;\n          } else if (currentOffset > offset) {\n            end = middle - 1;\n          }\n          if (start > end) return end;\n        }\n        return 0;\n      }\n    };\n  }, [rowHeight, rows]);\n  let rowOverscanStartIdx = 0;\n  let rowOverscanEndIdx = rows.length - 1;\n  if (enableVirtualization) {\n    const overscanThreshold = 4;\n    const rowVisibleStartIdx = findRowIdx(scrollTop);\n    const rowVisibleEndIdx = findRowIdx(scrollTop + clientHeight);\n    rowOverscanStartIdx = max(0, rowVisibleStartIdx - overscanThreshold);\n    rowOverscanEndIdx = min(rows.length - 1, rowVisibleEndIdx + overscanThreshold);\n  }\n  return {\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    totalRowHeight,\n    gridTemplateRows,\n    getRowTop,\n    getRowHeight,\n    findRowIdx\n  };\n}\n\nconst cellDragHandle = \"c1w9bbhr7-0-0-beta-41\";\nconst cellDragHandleFrozenClassname = \"c1creorc7-0-0-beta-41\";\nconst cellDragHandleClassname = `rdg-cell-drag-handle ${cellDragHandle}`;\nfunction DragHandle({\n  gridRowStart,\n  rows,\n  column,\n  columnWidth,\n  maxColIdx,\n  isLastRow,\n  selectedPosition,\n  latestDraggedOverRowIdx,\n  isCellEditable,\n  onRowsChange,\n  onFill,\n  onClick,\n  setDragging,\n  setDraggedOverRowIdx\n}) {\n  const {\n    idx,\n    rowIdx\n  } = selectedPosition;\n  function handleMouseDown(event) {\n    event.preventDefault();\n    if (event.buttons !== 1) return;\n    setDragging(true);\n    window.addEventListener('mouseover', onMouseOver);\n    window.addEventListener('mouseup', onMouseUp);\n    function onMouseOver(event) {\n      if (event.buttons !== 1) onMouseUp();\n    }\n    function onMouseUp() {\n      window.removeEventListener('mouseover', onMouseOver);\n      window.removeEventListener('mouseup', onMouseUp);\n      setDragging(false);\n      handleDragEnd();\n    }\n  }\n  function handleDragEnd() {\n    const overRowIdx = latestDraggedOverRowIdx.current;\n    if (overRowIdx === undefined) return;\n    const startRowIndex = rowIdx < overRowIdx ? rowIdx + 1 : overRowIdx;\n    const endRowIndex = rowIdx < overRowIdx ? overRowIdx + 1 : rowIdx;\n    updateRows(startRowIndex, endRowIndex);\n    setDraggedOverRowIdx(undefined);\n  }\n  function handleDoubleClick(event) {\n    event.stopPropagation();\n    updateRows(rowIdx + 1, rows.length);\n  }\n  function updateRows(startRowIdx, endRowIdx) {\n    const sourceRow = rows[rowIdx];\n    const updatedRows = [...rows];\n    const indexes = [];\n    for (let i = startRowIdx; i < endRowIdx; i++) {\n      if (isCellEditable({\n        rowIdx: i,\n        idx\n      })) {\n        const updatedRow = onFill({\n          columnKey: column.key,\n          sourceRow,\n          targetRow: rows[i]\n        });\n        if (updatedRow !== rows[i]) {\n          updatedRows[i] = updatedRow;\n          indexes.push(i);\n        }\n      }\n    }\n    if (indexes.length > 0) {\n      onRowsChange?.(updatedRows, {\n        indexes,\n        column\n      });\n    }\n  }\n  function getStyle() {\n    const colSpan = column.colSpan?.({\n      type: 'ROW',\n      row: rows[rowIdx]\n    }) ?? 1;\n    const {\n      insetInlineStart,\n      ...style\n    } = getCellStyle(column, colSpan);\n    const marginEnd = 'calc(var(--rdg-drag-handle-size) * -0.5 + 1px)';\n    const isLastColumn = column.idx + colSpan - 1 === maxColIdx;\n    return {\n      ...style,\n      gridRowStart,\n      marginInlineEnd: isLastColumn ? undefined : marginEnd,\n      marginBlockEnd: isLastRow ? undefined : marginEnd,\n      insetInlineStart: insetInlineStart ? `calc(${insetInlineStart} + ${columnWidth}px + var(--rdg-drag-handle-size) * -0.5 - 1px)` : undefined\n    };\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    style: getStyle(),\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cellDragHandleClassname, column.frozen && cellDragHandleFrozenClassname),\n    onClick: onClick,\n    onMouseDown: handleMouseDown,\n    onDoubleClick: handleDoubleClick\n  });\n}\n\nconst cellEditing = \"cis5rrm7-0-0-beta-41\";\nfunction EditCell({\n  column,\n  colSpan,\n  row,\n  rowIdx,\n  onRowChange,\n  closeEditor,\n  onKeyDown,\n  navigate\n}) {\n  const frameRequestRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const commitOnOutsideClick = column.editorOptions?.commitOnOutsideClick !== false;\n  const commitOnOutsideMouseDown = useLatestFunc(() => {\n    onClose(true, false);\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!commitOnOutsideClick) return;\n    function onWindowCaptureMouseDown() {\n      frameRequestRef.current = requestAnimationFrame(commitOnOutsideMouseDown);\n    }\n    addEventListener('mousedown', onWindowCaptureMouseDown, {\n      capture: true\n    });\n    return () => {\n      removeEventListener('mousedown', onWindowCaptureMouseDown, {\n        capture: true\n      });\n      cancelFrameRequest();\n    };\n  }, [commitOnOutsideClick, commitOnOutsideMouseDown]);\n  function cancelFrameRequest() {\n    cancelAnimationFrame(frameRequestRef.current);\n  }\n  function handleKeyDown(event) {\n    if (onKeyDown) {\n      const cellEvent = createCellEvent(event);\n      onKeyDown({\n        mode: 'EDIT',\n        row,\n        column,\n        rowIdx,\n        navigate() {\n          navigate(event);\n        },\n        onClose\n      }, cellEvent);\n      if (cellEvent.isGridDefaultPrevented()) return;\n    }\n    if (event.key === 'Escape') {\n      onClose();\n    } else if (event.key === 'Enter') {\n      onClose(true);\n    } else if (onEditorNavigation(event)) {\n      navigate(event);\n    }\n  }\n  function onClose(commitChanges = false, shouldFocusCell = true) {\n    if (commitChanges) {\n      onRowChange(row, true, shouldFocusCell);\n    } else {\n      closeEditor(shouldFocusCell);\n    }\n  }\n  function onEditorRowChange(row, commitChangesAndFocus = false) {\n    onRowChange(row, commitChangesAndFocus, commitChangesAndFocus);\n  }\n  const {\n    cellClass\n  } = column;\n  const className = getCellClassname(column, 'rdg-editor-container', typeof cellClass === 'function' ? cellClass(row) : cellClass, !column.editorOptions?.displayCellContent && cellEditing);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    role: \"gridcell\",\n    \"aria-colindex\": column.idx + 1,\n    \"aria-colspan\": colSpan,\n    \"aria-selected\": true,\n    className: className,\n    style: getCellStyle(column, colSpan),\n    onKeyDown: handleKeyDown,\n    onMouseDownCapture: cancelFrameRequest,\n    children: column.renderEditCell != null && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.Fragment, {\n      children: [column.renderEditCell({\n        column,\n        row,\n        onRowChange: onEditorRowChange,\n        onClose\n      }), column.editorOptions?.displayCellContent && column.renderCell({\n        column,\n        row,\n        rowIdx,\n        isCellEditable: true,\n        tabIndex: -1,\n        onRowChange: onEditorRowChange\n      })]\n    })\n  });\n}\n\nfunction GroupedColumnHeaderCell({\n  column,\n  rowIdx,\n  isCellSelected,\n  selectCell\n}) {\n  const {\n    tabIndex,\n    onFocus\n  } = useRovingTabIndex(isCellSelected);\n  const {\n    colSpan\n  } = column;\n  const rowSpan = getHeaderCellRowSpan(column, rowIdx);\n  const index = column.idx + 1;\n  function onClick() {\n    selectCell({\n      idx: column.idx,\n      rowIdx\n    });\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    role: \"columnheader\",\n    \"aria-colindex\": index,\n    \"aria-colspan\": colSpan,\n    \"aria-rowspan\": rowSpan,\n    \"aria-selected\": isCellSelected,\n    tabIndex: tabIndex,\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(cellClassname, column.headerCellClass),\n    style: {\n      ...getHeaderCellStyle(column, rowIdx, rowSpan),\n      gridColumnStart: index,\n      gridColumnEnd: index + colSpan\n    },\n    onFocus: onFocus,\n    onClick: onClick,\n    children: column.name\n  });\n}\n\nconst headerSortCellClassname = \"h44jtk67-0-0-beta-41\";\nconst headerSortName = \"hcgkhxz7-0-0-beta-41\";\nconst headerSortNameClassname = `rdg-header-sort-name ${headerSortName}`;\nfunction renderHeaderCell({\n  column,\n  sortDirection,\n  priority\n}) {\n  if (!column.sortable) return column.name;\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SortableHeaderCell, {\n    sortDirection: sortDirection,\n    priority: priority,\n    children: column.name\n  });\n}\nfunction SortableHeaderCell({\n  sortDirection,\n  priority,\n  children\n}) {\n  const renderSortStatus = useDefaultRenderers().renderSortStatus;\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"span\", {\n    className: headerSortCellClassname,\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"span\", {\n      className: headerSortNameClassname,\n      children: children\n    }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"span\", {\n      children: renderSortStatus({\n        sortDirection,\n        priority\n      })\n    })]\n  });\n}\n\nconst cellSortableClassname = \"c6l2wv17-0-0-beta-41\";\nconst cellResizable = \"c1kqdw7y7-0-0-beta-41\";\nconst cellResizableClassname = `rdg-cell-resizable ${cellResizable}`;\nconst resizeHandleClassname = \"r1y6ywlx7-0-0-beta-41\";\nconst cellDraggableClassname = 'rdg-cell-draggable';\nconst cellDragging = \"c1bezg5o7-0-0-beta-41\";\nconst cellDraggingClassname = `rdg-cell-dragging ${cellDragging}`;\nconst cellOver = \"c1vc96037-0-0-beta-41\";\nconst cellOverClassname = `rdg-cell-drag-over ${cellOver}`;\nfunction HeaderCell({\n  column,\n  colSpan,\n  rowIdx,\n  isCellSelected,\n  onColumnResize,\n  onColumnsReorder,\n  sortColumns,\n  onSortColumnsChange,\n  selectCell,\n  shouldFocusGrid,\n  direction,\n  dragDropKey\n}) {\n  const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [isOver, setIsOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const isRtl = direction === 'rtl';\n  const rowSpan = getHeaderCellRowSpan(column, rowIdx);\n  const {\n    tabIndex,\n    childTabIndex,\n    onFocus\n  } = useRovingTabIndex(isCellSelected);\n  const sortIndex = sortColumns?.findIndex(sort => sort.columnKey === column.key);\n  const sortColumn = sortIndex !== undefined && sortIndex > -1 ? sortColumns[sortIndex] : undefined;\n  const sortDirection = sortColumn?.direction;\n  const priority = sortColumn !== undefined && sortColumns.length > 1 ? sortIndex + 1 : undefined;\n  const ariaSort = sortDirection && !priority ? sortDirection === 'ASC' ? 'ascending' : 'descending' : undefined;\n  const {\n    sortable,\n    resizable,\n    draggable\n  } = column;\n  const className = getCellClassname(column, column.headerCellClass, sortable && cellSortableClassname, resizable && cellResizableClassname, draggable && cellDraggableClassname, isDragging && cellDraggingClassname, isOver && cellOverClassname);\n  const renderHeaderCell$1 = column.renderHeaderCell ?? renderHeaderCell;\n  function onPointerDown(event) {\n    if (event.pointerType === 'mouse' && event.buttons !== 1) {\n      return;\n    }\n    event.preventDefault();\n    const {\n      currentTarget,\n      pointerId\n    } = event;\n    const headerCell = currentTarget.parentElement;\n    const {\n      right,\n      left\n    } = headerCell.getBoundingClientRect();\n    const offset = isRtl ? event.clientX - left : right - event.clientX;\n    function onPointerMove(event) {\n      const {\n        right,\n        left\n      } = headerCell.getBoundingClientRect();\n      const width = isRtl ? right + offset - event.clientX : event.clientX + offset - left;\n      if (width > 0) {\n        onColumnResize(column, clampColumnWidth(width, column));\n      }\n    }\n    function onLostPointerCapture() {\n      currentTarget.removeEventListener('pointermove', onPointerMove);\n      currentTarget.removeEventListener('lostpointercapture', onLostPointerCapture);\n    }\n    currentTarget.setPointerCapture(pointerId);\n    currentTarget.addEventListener('pointermove', onPointerMove);\n    currentTarget.addEventListener('lostpointercapture', onLostPointerCapture);\n  }\n  function onSort(ctrlClick) {\n    if (onSortColumnsChange == null) return;\n    const {\n      sortDescendingFirst\n    } = column;\n    if (sortColumn === undefined) {\n      const nextSort = {\n        columnKey: column.key,\n        direction: sortDescendingFirst ? 'DESC' : 'ASC'\n      };\n      onSortColumnsChange(sortColumns && ctrlClick ? [...sortColumns, nextSort] : [nextSort]);\n    } else {\n      let nextSortColumn;\n      if (sortDescendingFirst === true && sortDirection === 'DESC' || sortDescendingFirst !== true && sortDirection === 'ASC') {\n        nextSortColumn = {\n          columnKey: column.key,\n          direction: sortDirection === 'ASC' ? 'DESC' : 'ASC'\n        };\n      }\n      if (ctrlClick) {\n        const nextSortColumns = [...sortColumns];\n        if (nextSortColumn) {\n          nextSortColumns[sortIndex] = nextSortColumn;\n        } else {\n          nextSortColumns.splice(sortIndex, 1);\n        }\n        onSortColumnsChange(nextSortColumns);\n      } else {\n        onSortColumnsChange(nextSortColumn ? [nextSortColumn] : []);\n      }\n    }\n  }\n  function onClick(event) {\n    selectCell({\n      idx: column.idx,\n      rowIdx\n    });\n    if (sortable) {\n      onSort(event.ctrlKey || event.metaKey);\n    }\n  }\n  function onDoubleClick() {\n    onColumnResize(column, 'max-content');\n  }\n  function handleFocus(event) {\n    onFocus?.(event);\n    if (shouldFocusGrid) {\n      selectCell({\n        idx: 0,\n        rowIdx\n      });\n    }\n  }\n  function onKeyDown(event) {\n    if (event.key === ' ' || event.key === 'Enter') {\n      event.preventDefault();\n      onSort(event.ctrlKey || event.metaKey);\n    }\n  }\n  function onDragStart(event) {\n    event.dataTransfer.setData(dragDropKey, column.key);\n    event.dataTransfer.dropEffect = 'move';\n    setIsDragging(true);\n  }\n  function onDragEnd() {\n    setIsDragging(false);\n  }\n  function onDragOver(event) {\n    event.preventDefault();\n    event.dataTransfer.dropEffect = 'move';\n  }\n  function onDrop(event) {\n    setIsOver(false);\n    if (event.dataTransfer.types.includes(dragDropKey)) {\n      const sourceKey = event.dataTransfer.getData(dragDropKey);\n      if (sourceKey !== column.key) {\n        event.preventDefault();\n        onColumnsReorder?.(sourceKey, column.key);\n      }\n    }\n  }\n  function onDragEnter(event) {\n    if (isEventPertinent(event)) {\n      setIsOver(true);\n    }\n  }\n  function onDragLeave(event) {\n    if (isEventPertinent(event)) {\n      setIsOver(false);\n    }\n  }\n  let draggableProps;\n  if (draggable) {\n    draggableProps = {\n      draggable: true,\n      onDragStart,\n      onDragEnd,\n      onDragOver,\n      onDragEnter,\n      onDragLeave,\n      onDrop\n    };\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n    role: \"columnheader\",\n    \"aria-colindex\": column.idx + 1,\n    \"aria-colspan\": colSpan,\n    \"aria-rowspan\": rowSpan,\n    \"aria-selected\": isCellSelected,\n    \"aria-sort\": ariaSort,\n    tabIndex: shouldFocusGrid ? 0 : tabIndex,\n    className: className,\n    style: {\n      ...getHeaderCellStyle(column, rowIdx, rowSpan),\n      ...getCellStyle(column, colSpan)\n    },\n    onFocus: handleFocus,\n    onClick: onClick,\n    onKeyDown: sortable ? onKeyDown : undefined,\n    ...draggableProps,\n    children: [renderHeaderCell$1({\n      column,\n      sortDirection,\n      priority,\n      tabIndex: childTabIndex\n    }), resizable && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n      className: resizeHandleClassname,\n      onClick: stopPropagation,\n      onDoubleClick: onDoubleClick,\n      onPointerDown: onPointerDown\n    })]\n  });\n}\nfunction isEventPertinent(event) {\n  const relatedTarget = event.relatedTarget;\n  return !event.currentTarget.contains(relatedTarget);\n}\n\nconst row = \"r1upfr807-0-0-beta-41\";\nconst rowClassname = `rdg-row ${row}`;\nconst rowSelected = \"r190mhd37-0-0-beta-41\";\nconst rowSelectedClassname = 'rdg-row-selected';\nconst rowSelectedWithFrozenCell = \"r139qu9m7-0-0-beta-41\";\n\nconst headerRow = \"h10tskcx7-0-0-beta-41\";\nconst headerRowClassname = `rdg-header-row ${headerRow}`;\nfunction HeaderRow({\n  rowIdx,\n  columns,\n  onColumnResize,\n  onColumnsReorder,\n  sortColumns,\n  onSortColumnsChange,\n  lastFrozenColumnIndex,\n  selectedCellIdx,\n  selectCell,\n  shouldFocusGrid,\n  direction\n}) {\n  const dragDropKey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const cells = [];\n  for (let index = 0; index < columns.length; index++) {\n    const column = columns[index];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, {\n      type: 'HEADER'\n    });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n    cells.push( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(HeaderCell, {\n      column: column,\n      colSpan: colSpan,\n      rowIdx: rowIdx,\n      isCellSelected: selectedCellIdx === column.idx,\n      onColumnResize: onColumnResize,\n      onColumnsReorder: onColumnsReorder,\n      onSortColumnsChange: onSortColumnsChange,\n      sortColumns: sortColumns,\n      selectCell: selectCell,\n      shouldFocusGrid: shouldFocusGrid && index === 0,\n      direction: direction,\n      dragDropKey: dragDropKey\n    }, column.key));\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    role: \"row\",\n    \"aria-rowindex\": rowIdx,\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(headerRowClassname, selectedCellIdx === -1 && rowSelectedClassname),\n    children: cells\n  });\n}\nconst HeaderRow$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(HeaderRow);\n\nfunction GroupedColumnHeaderRow({\n  rowIdx,\n  level,\n  columns,\n  selectedCellIdx,\n  selectCell\n}) {\n  const cells = [];\n  const renderedParents = new Set();\n  for (const column of columns) {\n    let {\n      parent\n    } = column;\n    if (parent === undefined) continue;\n    while (parent.level > level) {\n      if (parent.parent === undefined) break;\n      parent = parent.parent;\n    }\n    if (parent.level === level && !renderedParents.has(parent)) {\n      renderedParents.add(parent);\n      const {\n        idx\n      } = parent;\n      cells.push( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(GroupedColumnHeaderCell, {\n        column: parent,\n        rowIdx: rowIdx,\n        isCellSelected: selectedCellIdx === idx,\n        selectCell: selectCell\n      }, idx));\n    }\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    role: \"row\",\n    \"aria-rowindex\": rowIdx,\n    className: headerRowClassname,\n    children: cells\n  });\n}\nconst GroupedColumnHeaderRow$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GroupedColumnHeaderRow);\n\nconst cellCopied = \"c6ra8a37-0-0-beta-41\";\nconst cellCopiedClassname = `rdg-cell-copied ${cellCopied}`;\nconst cellDraggedOver = \"cq910m07-0-0-beta-41\";\nconst cellDraggedOverClassname = `rdg-cell-dragged-over ${cellDraggedOver}`;\nfunction Cell({\n  column,\n  colSpan,\n  isCellSelected,\n  isCopied,\n  isDraggedOver,\n  row,\n  rowIdx,\n  onClick,\n  onDoubleClick,\n  onContextMenu,\n  onRowChange,\n  selectCell,\n  ...props\n}) {\n  const {\n    tabIndex,\n    childTabIndex,\n    onFocus\n  } = useRovingTabIndex(isCellSelected);\n  const {\n    cellClass\n  } = column;\n  const className = getCellClassname(column, typeof cellClass === 'function' ? cellClass(row) : cellClass, isCopied && cellCopiedClassname, isDraggedOver && cellDraggedOverClassname);\n  const isEditable = isCellEditableUtil(column, row);\n  function selectCellWrapper(openEditor) {\n    selectCell({\n      rowIdx,\n      idx: column.idx\n    }, openEditor);\n  }\n  function handleClick(event) {\n    if (onClick) {\n      const cellEvent = createCellEvent(event);\n      onClick({\n        row,\n        column,\n        selectCell: selectCellWrapper\n      }, cellEvent);\n      if (cellEvent.isGridDefaultPrevented()) return;\n    }\n    selectCellWrapper();\n  }\n  function handleContextMenu(event) {\n    if (onContextMenu) {\n      const cellEvent = createCellEvent(event);\n      onContextMenu({\n        row,\n        column,\n        selectCell: selectCellWrapper\n      }, cellEvent);\n      if (cellEvent.isGridDefaultPrevented()) return;\n    }\n    selectCellWrapper();\n  }\n  function handleDoubleClick(event) {\n    if (onDoubleClick) {\n      const cellEvent = createCellEvent(event);\n      onDoubleClick({\n        row,\n        column,\n        selectCell: selectCellWrapper\n      }, cellEvent);\n      if (cellEvent.isGridDefaultPrevented()) return;\n    }\n    selectCellWrapper(true);\n  }\n  function handleRowChange(newRow) {\n    onRowChange(column, newRow);\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    role: \"gridcell\",\n    \"aria-colindex\": column.idx + 1,\n    \"aria-colspan\": colSpan,\n    \"aria-selected\": isCellSelected,\n    \"aria-readonly\": !isEditable || undefined,\n    tabIndex: tabIndex,\n    className: className,\n    style: getCellStyle(column, colSpan),\n    onClick: handleClick,\n    onDoubleClick: handleDoubleClick,\n    onContextMenu: handleContextMenu,\n    onFocus: onFocus,\n    ...props,\n    children: column.renderCell({\n      column,\n      row,\n      rowIdx,\n      isCellEditable: isEditable,\n      tabIndex: childTabIndex,\n      onRowChange: handleRowChange\n    })\n  });\n}\nconst Cell$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(Cell);\n\nfunction Row({\n  className,\n  rowIdx,\n  gridRowStart,\n  height,\n  selectedCellIdx,\n  isRowSelected,\n  copiedCellIdx,\n  draggedOverCellIdx,\n  lastFrozenColumnIndex,\n  row,\n  viewportColumns,\n  selectedCellEditor,\n  onCellClick,\n  onCellDoubleClick,\n  onCellContextMenu,\n  rowClass,\n  setDraggedOverRowIdx,\n  onMouseEnter,\n  onRowChange,\n  selectCell,\n  ...props\n}, ref) {\n  const handleRowChange = useLatestFunc((column, newRow) => {\n    onRowChange(column, rowIdx, newRow);\n  });\n  function handleDragEnter(event) {\n    setDraggedOverRowIdx?.(rowIdx);\n    onMouseEnter?.(event);\n  }\n  className = (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(rowClassname, `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`, rowClass?.(row, rowIdx), className, selectedCellIdx === -1 && rowSelectedClassname);\n  const cells = [];\n  for (let index = 0; index < viewportColumns.length; index++) {\n    const column = viewportColumns[index];\n    const {\n      idx\n    } = column;\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, {\n      type: 'ROW',\n      row\n    });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n    const isCellSelected = selectedCellIdx === idx;\n    if (isCellSelected && selectedCellEditor) {\n      cells.push(selectedCellEditor);\n    } else {\n      cells.push( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(Cell$1, {\n        column: column,\n        colSpan: colSpan,\n        row: row,\n        rowIdx: rowIdx,\n        isCopied: copiedCellIdx === idx,\n        isDraggedOver: draggedOverCellIdx === idx,\n        isCellSelected: isCellSelected,\n        onClick: onCellClick,\n        onDoubleClick: onCellDoubleClick,\n        onContextMenu: onCellContextMenu,\n        onRowChange: handleRowChange,\n        selectCell: selectCell\n      }, column.key));\n    }\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(RowSelectionProvider, {\n    value: isRowSelected,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n      role: \"row\",\n      ref: ref,\n      className: className,\n      onMouseEnter: handleDragEnter,\n      style: getRowStyle(gridRowStart, height),\n      ...props,\n      children: cells\n    })\n  });\n}\nconst RowComponent = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)( /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(Row));\nconst RowComponent$1 = RowComponent;\nfunction defaultRenderRow(key, props) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(RowComponent, {\n    ...props\n  }, key);\n}\n\nfunction ScrollToCell({\n  scrollToPosition: {\n    idx,\n    rowIdx\n  },\n  gridElement,\n  setScrollToCellPosition\n}) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  useLayoutEffect(() => {\n    scrollIntoView(ref.current);\n  });\n  useLayoutEffect(() => {\n    function removeScrollToCell() {\n      setScrollToCellPosition(null);\n    }\n    const observer = new IntersectionObserver(removeScrollToCell, {\n      root: gridElement,\n      threshold: 1.0\n    });\n    observer.observe(ref.current);\n    return () => {\n      observer.disconnect();\n    };\n  }, [gridElement, setScrollToCellPosition]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    ref: ref,\n    style: {\n      gridColumn: idx === undefined ? '1/-1' : idx + 1,\n      gridRow: rowIdx === undefined ? '1/-1' : rowIdx + 2\n    }\n  });\n}\n\nconst arrow = \"a3ejtar7-0-0-beta-41\";\nconst arrowClassname = `rdg-sort-arrow ${arrow}`;\nfunction renderSortStatus({\n  sortDirection,\n  priority\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.Fragment, {\n    children: [renderSortIcon({\n      sortDirection\n    }), renderSortPriority({\n      priority\n    })]\n  });\n}\nfunction renderSortIcon({\n  sortDirection\n}) {\n  if (sortDirection === undefined) return null;\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"svg\", {\n    viewBox: \"0 0 12 8\",\n    width: \"12\",\n    height: \"8\",\n    className: arrowClassname,\n    \"aria-hidden\": true,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"path\", {\n      d: sortDirection === 'ASC' ? 'M0 8 6 0 12 8' : 'M0 0 6 8 12 0'\n    })\n  });\n}\nfunction renderSortPriority({\n  priority\n}) {\n  return priority;\n}\n\nconst root = \"rnvodz57-0-0-beta-41\";\nconst rootClassname = `rdg ${root}`;\nconst viewportDragging = \"vlqv91k7-0-0-beta-41\";\nconst viewportDraggingClassname = `rdg-viewport-dragging ${viewportDragging}`;\nconst focusSinkClassname = \"f1lsfrzw7-0-0-beta-41\";\nconst focusSinkHeaderAndSummaryClassname = \"f1cte0lg7-0-0-beta-41\";\n\nconst summaryCellClassname = \"s8wc6fl7-0-0-beta-41\";\nfunction SummaryCell({\n  column,\n  colSpan,\n  row,\n  rowIdx,\n  isCellSelected,\n  selectCell\n}) {\n  const {\n    tabIndex,\n    childTabIndex,\n    onFocus\n  } = useRovingTabIndex(isCellSelected);\n  const {\n    summaryCellClass\n  } = column;\n  const className = getCellClassname(column, summaryCellClassname, typeof summaryCellClass === 'function' ? summaryCellClass(row) : summaryCellClass);\n  function onClick() {\n    selectCell({\n      rowIdx,\n      idx: column.idx\n    });\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    role: \"gridcell\",\n    \"aria-colindex\": column.idx + 1,\n    \"aria-colspan\": colSpan,\n    \"aria-selected\": isCellSelected,\n    tabIndex: tabIndex,\n    className: className,\n    style: getCellStyle(column, colSpan),\n    onClick: onClick,\n    onFocus: onFocus,\n    children: column.renderSummaryCell?.({\n      column,\n      row,\n      tabIndex: childTabIndex\n    })\n  });\n}\nconst SummaryCell$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(SummaryCell);\n\nconst summaryRow = \"skuhp557-0-0-beta-41\";\nconst topSummaryRow = \"tf8l5ub7-0-0-beta-41\";\nconst topSummaryRowBorderClassname = \"tb9ughf7-0-0-beta-41\";\nconst bottomSummaryRowBorderClassname = \"b1yssfnt7-0-0-beta-41\";\nconst summaryRowClassname = `rdg-summary-row ${summaryRow}`;\nconst topSummaryRowClassname = `rdg-top-summary-row ${topSummaryRow}`;\nfunction SummaryRow({\n  rowIdx,\n  gridRowStart,\n  row,\n  viewportColumns,\n  top,\n  bottom,\n  lastFrozenColumnIndex,\n  selectedCellIdx,\n  isTop,\n  showBorder,\n  selectCell,\n  'aria-rowindex': ariaRowIndex\n}) {\n  const cells = [];\n  for (let index = 0; index < viewportColumns.length; index++) {\n    const column = viewportColumns[index];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, {\n      type: 'SUMMARY',\n      row\n    });\n    if (colSpan !== undefined) {\n      index += colSpan - 1;\n    }\n    const isCellSelected = selectedCellIdx === column.idx;\n    cells.push( /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SummaryCell$1, {\n      column: column,\n      colSpan: colSpan,\n      row: row,\n      rowIdx: rowIdx,\n      isCellSelected: isCellSelected,\n      selectCell: selectCell\n    }, column.key));\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    role: \"row\",\n    \"aria-rowindex\": ariaRowIndex,\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(rowClassname, `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`, summaryRowClassname, isTop ? [topSummaryRowClassname, showBorder && topSummaryRowBorderClassname] : ['rdg-bottom-summary-row', showBorder && bottomSummaryRowBorderClassname], selectedCellIdx === -1 && rowSelectedClassname),\n    style: {\n      ...getRowStyle(gridRowStart),\n      '--rdg-summary-row-top': top !== undefined ? `${top}px` : undefined,\n      '--rdg-summary-row-bottom': bottom !== undefined ? `${bottom}px` : undefined\n    },\n    children: cells\n  });\n}\nconst SummaryRow$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(SummaryRow);\n\nfunction DataGrid(props, ref) {\n  const {\n    columns: rawColumns,\n    rows,\n    topSummaryRows,\n    bottomSummaryRows,\n    rowKeyGetter,\n    onRowsChange,\n    rowHeight: rawRowHeight,\n    headerRowHeight: rawHeaderRowHeight,\n    summaryRowHeight: rawSummaryRowHeight,\n    selectedRows,\n    onSelectedRowsChange,\n    sortColumns,\n    onSortColumnsChange,\n    defaultColumnOptions,\n    onCellClick,\n    onCellDoubleClick,\n    onCellContextMenu,\n    onCellKeyDown,\n    onSelectedCellChange,\n    onScroll,\n    onColumnResize,\n    onColumnsReorder,\n    onFill,\n    onCopy,\n    onPaste,\n    enableVirtualization: rawEnableVirtualization,\n    renderers,\n    className,\n    style,\n    rowClass,\n    direction: rawDirection,\n    role: rawRole,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledBy,\n    'aria-describedby': ariaDescribedBy,\n    'aria-rowcount': rawAriaRowCount,\n    'data-testid': testId\n  } = props;\n  const defaultRenderers = useDefaultRenderers();\n  const role = rawRole ?? 'grid';\n  const rowHeight = rawRowHeight ?? 35;\n  const headerRowHeight = rawHeaderRowHeight ?? (typeof rowHeight === 'number' ? rowHeight : 35);\n  const summaryRowHeight = rawSummaryRowHeight ?? (typeof rowHeight === 'number' ? rowHeight : 35);\n  const renderRow = renderers?.renderRow ?? defaultRenderers?.renderRow ?? defaultRenderRow;\n  const renderSortStatus$1 = renderers?.renderSortStatus ?? defaultRenderers?.renderSortStatus ?? renderSortStatus;\n  const renderCheckbox$1 = renderers?.renderCheckbox ?? defaultRenderers?.renderCheckbox ?? renderCheckbox;\n  const noRowsFallback = renderers?.noRowsFallback ?? defaultRenderers?.noRowsFallback;\n  const enableVirtualization = rawEnableVirtualization ?? true;\n  const direction = rawDirection ?? 'ltr';\n  const [scrollTop, setScrollTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [scrollLeft, setScrollLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  const [resizedColumnWidths, setResizedColumnWidths] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new Map());\n  const [measuredColumnWidths, setMeasuredColumnWidths] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => new Map());\n  const [copiedCell, setCopiedCell] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [isDragging, setDragging] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [draggedOverRowIdx, setOverRowIdx] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(undefined);\n  const [scrollToPosition, setScrollToPosition] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const getColumnWidth = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(column => {\n    return resizedColumnWidths.get(column.key) ?? measuredColumnWidths.get(column.key) ?? column.width;\n  }, [measuredColumnWidths, resizedColumnWidths]);\n  const [gridRef, gridWidth, gridHeight] = useGridDimensions();\n  const {\n    columns,\n    colSpanColumns,\n    lastFrozenColumnIndex,\n    headerRowsCount,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    templateColumns,\n    layoutCssVars,\n    totalFrozenColumnWidth\n  } = useCalculatedColumns({\n    rawColumns,\n    defaultColumnOptions,\n    getColumnWidth,\n    scrollLeft,\n    viewportWidth: gridWidth,\n    enableVirtualization\n  });\n  const topSummaryRowsCount = topSummaryRows?.length ?? 0;\n  const bottomSummaryRowsCount = bottomSummaryRows?.length ?? 0;\n  const summaryRowsCount = topSummaryRowsCount + bottomSummaryRowsCount;\n  const headerAndTopSummaryRowsCount = headerRowsCount + topSummaryRowsCount;\n  const groupedColumnHeaderRowsCount = headerRowsCount - 1;\n  const minRowIdx = -headerAndTopSummaryRowsCount;\n  const mainHeaderRowIdx = minRowIdx + groupedColumnHeaderRowsCount;\n  const maxRowIdx = rows.length + bottomSummaryRowsCount - 1;\n  const [selectedPosition, setSelectedPosition] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => ({\n    idx: -1,\n    rowIdx: minRowIdx - 1,\n    mode: 'SELECT'\n  }));\n  const prevSelectedPosition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(selectedPosition);\n  const latestDraggedOverRowIdx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(draggedOverRowIdx);\n  const lastSelectedRowIdx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(-1);\n  const focusSinkRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const shouldFocusCellRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const isTreeGrid = role === 'treegrid';\n  const headerRowsHeight = headerRowsCount * headerRowHeight;\n  const clientHeight = gridHeight - headerRowsHeight - summaryRowsCount * summaryRowHeight;\n  const isSelectable = selectedRows != null && onSelectedRowsChange != null;\n  const isRtl = direction === 'rtl';\n  const leftKey = isRtl ? 'ArrowRight' : 'ArrowLeft';\n  const rightKey = isRtl ? 'ArrowLeft' : 'ArrowRight';\n  const ariaRowCount = rawAriaRowCount ?? headerRowsCount + rows.length + summaryRowsCount;\n  const defaultGridComponents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    renderCheckbox: renderCheckbox$1,\n    renderSortStatus: renderSortStatus$1\n  }), [renderCheckbox$1, renderSortStatus$1]);\n  const allRowsSelected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const {\n      length\n    } = rows;\n    return length !== 0 && selectedRows != null && rowKeyGetter != null && selectedRows.size >= length && rows.every(row => selectedRows.has(rowKeyGetter(row)));\n  }, [rows, selectedRows, rowKeyGetter]);\n  const {\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    totalRowHeight,\n    gridTemplateRows,\n    getRowTop,\n    getRowHeight,\n    findRowIdx\n  } = useViewportRows({\n    rows,\n    rowHeight,\n    clientHeight,\n    scrollTop,\n    enableVirtualization\n  });\n  const viewportColumns = useViewportColumns({\n    columns,\n    colSpanColumns,\n    colOverscanStartIdx,\n    colOverscanEndIdx,\n    lastFrozenColumnIndex,\n    rowOverscanStartIdx,\n    rowOverscanEndIdx,\n    rows,\n    topSummaryRows,\n    bottomSummaryRows\n  });\n  const {\n    gridTemplateColumns,\n    handleColumnResize\n  } = useColumnWidths(columns, viewportColumns, templateColumns, gridRef, gridWidth, resizedColumnWidths, measuredColumnWidths, setResizedColumnWidths, setMeasuredColumnWidths, onColumnResize);\n  const minColIdx = isTreeGrid ? -1 : 0;\n  const maxColIdx = columns.length - 1;\n  const selectedCellIsWithinSelectionBounds = isCellWithinSelectionBounds(selectedPosition);\n  const selectedCellIsWithinViewportBounds = isCellWithinViewportBounds(selectedPosition);\n  const handleColumnResizeLatest = useLatestFunc(handleColumnResize);\n  const onColumnsReorderLastest = useLatestFunc(onColumnsReorder);\n  const onSortColumnsChangeLatest = useLatestFunc(onSortColumnsChange);\n  const onCellClickLatest = useLatestFunc(onCellClick);\n  const onCellDoubleClickLatest = useLatestFunc(onCellDoubleClick);\n  const onCellContextMenuLatest = useLatestFunc(onCellContextMenu);\n  const selectRowLatest = useLatestFunc(selectRow);\n  const handleFormatterRowChangeLatest = useLatestFunc(updateRow);\n  const selectCellLatest = useLatestFunc(selectCell);\n  const selectHeaderCellLatest = useLatestFunc(({\n    idx,\n    rowIdx\n  }) => {\n    selectCell({\n      rowIdx: minRowIdx + rowIdx - 1,\n      idx\n    });\n  });\n  useLayoutEffect(() => {\n    if (!selectedCellIsWithinSelectionBounds || isSamePosition(selectedPosition, prevSelectedPosition.current)) {\n      prevSelectedPosition.current = selectedPosition;\n      return;\n    }\n    prevSelectedPosition.current = selectedPosition;\n    if (selectedPosition.idx === -1) {\n      focusSinkRef.current.focus({\n        preventScroll: true\n      });\n      scrollIntoView(focusSinkRef.current);\n    }\n  });\n  useLayoutEffect(() => {\n    if (!shouldFocusCellRef.current) return;\n    shouldFocusCellRef.current = false;\n    focusCellOrCellContent();\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(ref, () => ({\n    element: gridRef.current,\n    scrollToCell({\n      idx,\n      rowIdx\n    }) {\n      const scrollToIdx = idx !== undefined && idx > lastFrozenColumnIndex && idx < columns.length ? idx : undefined;\n      const scrollToRowIdx = rowIdx !== undefined && isRowIdxWithinViewportBounds(rowIdx) ? rowIdx : undefined;\n      if (scrollToIdx !== undefined || scrollToRowIdx !== undefined) {\n        setScrollToPosition({\n          idx: scrollToIdx,\n          rowIdx: scrollToRowIdx\n        });\n      }\n    },\n    selectCell\n  }));\n  const setDraggedOverRowIdx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rowIdx => {\n    setOverRowIdx(rowIdx);\n    latestDraggedOverRowIdx.current = rowIdx;\n  }, []);\n  function selectRow(args) {\n    if (!onSelectedRowsChange) return;\n    assertIsValidKeyGetter(rowKeyGetter);\n    if (args.type === 'HEADER') {\n      const newSelectedRows = new Set(selectedRows);\n      for (const row of rows) {\n        const rowKey = rowKeyGetter(row);\n        if (args.checked) {\n          newSelectedRows.add(rowKey);\n        } else {\n          newSelectedRows.delete(rowKey);\n        }\n      }\n      onSelectedRowsChange(newSelectedRows);\n      return;\n    }\n    const {\n      row,\n      checked,\n      isShiftClick\n    } = args;\n    const newSelectedRows = new Set(selectedRows);\n    const rowKey = rowKeyGetter(row);\n    if (checked) {\n      newSelectedRows.add(rowKey);\n      const previousRowIdx = lastSelectedRowIdx.current;\n      const rowIdx = rows.indexOf(row);\n      lastSelectedRowIdx.current = rowIdx;\n      if (isShiftClick && previousRowIdx !== -1 && previousRowIdx !== rowIdx) {\n        const step = sign(rowIdx - previousRowIdx);\n        for (let i = previousRowIdx + step; i !== rowIdx; i += step) {\n          const row = rows[i];\n          newSelectedRows.add(rowKeyGetter(row));\n        }\n      }\n    } else {\n      newSelectedRows.delete(rowKey);\n      lastSelectedRowIdx.current = -1;\n    }\n    onSelectedRowsChange(newSelectedRows);\n  }\n  function handleKeyDown(event) {\n    const {\n      idx,\n      rowIdx,\n      mode\n    } = selectedPosition;\n    if (mode === 'EDIT') return;\n    if (onCellKeyDown && isRowIdxWithinViewportBounds(rowIdx)) {\n      const row = rows[rowIdx];\n      const cellEvent = createCellEvent(event);\n      onCellKeyDown({\n        mode: 'SELECT',\n        row,\n        column: columns[idx],\n        rowIdx,\n        selectCell\n      }, cellEvent);\n      if (cellEvent.isGridDefaultPrevented()) return;\n    }\n    if (!(event.target instanceof Element)) return;\n    const isCellEvent = event.target.closest('.rdg-cell') !== null;\n    const isRowEvent = isTreeGrid && event.target === focusSinkRef.current;\n    if (!isCellEvent && !isRowEvent) return;\n    const {\n      keyCode\n    } = event;\n    if (selectedCellIsWithinViewportBounds && (onPaste != null || onCopy != null) && isCtrlKeyHeldDown(event)) {\n      const cKey = 67;\n      const vKey = 86;\n      if (keyCode === cKey) {\n        handleCopy();\n        return;\n      }\n      if (keyCode === vKey) {\n        handlePaste();\n        return;\n      }\n    }\n    switch (event.key) {\n      case 'Escape':\n        setCopiedCell(null);\n        return;\n      case 'ArrowUp':\n      case 'ArrowDown':\n      case 'ArrowLeft':\n      case 'ArrowRight':\n      case 'Tab':\n      case 'Home':\n      case 'End':\n      case 'PageUp':\n      case 'PageDown':\n        navigate(event);\n        break;\n      default:\n        handleCellInput(event);\n        break;\n    }\n  }\n  function handleScroll(event) {\n    const {\n      scrollTop,\n      scrollLeft\n    } = event.currentTarget;\n    (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n      setScrollTop(scrollTop);\n      setScrollLeft(abs(scrollLeft));\n    });\n    onScroll?.(event);\n  }\n  function updateRow(column, rowIdx, row) {\n    if (typeof onRowsChange !== 'function') return;\n    if (row === rows[rowIdx]) return;\n    const updatedRows = [...rows];\n    updatedRows[rowIdx] = row;\n    onRowsChange(updatedRows, {\n      indexes: [rowIdx],\n      column\n    });\n  }\n  function commitEditorChanges() {\n    if (selectedPosition.mode !== 'EDIT') return;\n    updateRow(columns[selectedPosition.idx], selectedPosition.rowIdx, selectedPosition.row);\n  }\n  function handleCopy() {\n    const {\n      idx,\n      rowIdx\n    } = selectedPosition;\n    const sourceRow = rows[rowIdx];\n    const sourceColumnKey = columns[idx].key;\n    setCopiedCell({\n      row: sourceRow,\n      columnKey: sourceColumnKey\n    });\n    onCopy?.({\n      sourceRow,\n      sourceColumnKey\n    });\n  }\n  function handlePaste() {\n    if (!onPaste || !onRowsChange || copiedCell === null || !isCellEditable(selectedPosition)) {\n      return;\n    }\n    const {\n      idx,\n      rowIdx\n    } = selectedPosition;\n    const targetColumn = columns[idx];\n    const targetRow = rows[rowIdx];\n    const updatedTargetRow = onPaste({\n      sourceRow: copiedCell.row,\n      sourceColumnKey: copiedCell.columnKey,\n      targetRow,\n      targetColumnKey: targetColumn.key\n    });\n    updateRow(targetColumn, rowIdx, updatedTargetRow);\n  }\n  function handleCellInput(event) {\n    if (!selectedCellIsWithinViewportBounds) return;\n    const row = rows[selectedPosition.rowIdx];\n    const {\n      key,\n      shiftKey\n    } = event;\n    if (isSelectable && shiftKey && key === ' ') {\n      assertIsValidKeyGetter(rowKeyGetter);\n      const rowKey = rowKeyGetter(row);\n      selectRow({\n        type: 'ROW',\n        row,\n        checked: !selectedRows.has(rowKey),\n        isShiftClick: false\n      });\n      event.preventDefault();\n      return;\n    }\n    if (isCellEditable(selectedPosition) && isDefaultCellInput(event)) {\n      setSelectedPosition(({\n        idx,\n        rowIdx\n      }) => ({\n        idx,\n        rowIdx,\n        mode: 'EDIT',\n        row,\n        originalRow: row\n      }));\n    }\n  }\n  function isColIdxWithinSelectionBounds(idx) {\n    return idx >= minColIdx && idx <= maxColIdx;\n  }\n  function isRowIdxWithinViewportBounds(rowIdx) {\n    return rowIdx >= 0 && rowIdx < rows.length;\n  }\n  function isCellWithinSelectionBounds({\n    idx,\n    rowIdx\n  }) {\n    return rowIdx >= minRowIdx && rowIdx <= maxRowIdx && isColIdxWithinSelectionBounds(idx);\n  }\n  function isCellWithinEditBounds({\n    idx,\n    rowIdx\n  }) {\n    return isRowIdxWithinViewportBounds(rowIdx) && idx >= 0 && idx <= maxColIdx;\n  }\n  function isCellWithinViewportBounds({\n    idx,\n    rowIdx\n  }) {\n    return isRowIdxWithinViewportBounds(rowIdx) && isColIdxWithinSelectionBounds(idx);\n  }\n  function isCellEditable(position) {\n    return isCellWithinEditBounds(position) && isSelectedCellEditable({\n      columns,\n      rows,\n      selectedPosition: position\n    });\n  }\n  function selectCell(position, enableEditor) {\n    if (!isCellWithinSelectionBounds(position)) return;\n    commitEditorChanges();\n    const row = rows[position.rowIdx];\n    const samePosition = isSamePosition(selectedPosition, position);\n    if (enableEditor && isCellEditable(position)) {\n      setSelectedPosition({\n        ...position,\n        mode: 'EDIT',\n        row,\n        originalRow: row\n      });\n    } else if (samePosition) {\n      scrollIntoView(getCellToScroll(gridRef.current));\n    } else {\n      shouldFocusCellRef.current = true;\n      setSelectedPosition({\n        ...position,\n        mode: 'SELECT'\n      });\n    }\n    if (onSelectedCellChange && !samePosition) {\n      onSelectedCellChange({\n        rowIdx: position.rowIdx,\n        row,\n        column: columns[position.idx]\n      });\n    }\n  }\n  function getNextPosition(key, ctrlKey, shiftKey) {\n    const {\n      idx,\n      rowIdx\n    } = selectedPosition;\n    const isRowSelected = selectedCellIsWithinSelectionBounds && idx === -1;\n    switch (key) {\n      case 'ArrowUp':\n        return {\n          idx,\n          rowIdx: rowIdx - 1\n        };\n      case 'ArrowDown':\n        return {\n          idx,\n          rowIdx: rowIdx + 1\n        };\n      case leftKey:\n        return {\n          idx: idx - 1,\n          rowIdx\n        };\n      case rightKey:\n        return {\n          idx: idx + 1,\n          rowIdx\n        };\n      case 'Tab':\n        return {\n          idx: idx + (shiftKey ? -1 : 1),\n          rowIdx\n        };\n      case 'Home':\n        if (isRowSelected) return {\n          idx,\n          rowIdx: minRowIdx\n        };\n        return {\n          idx: 0,\n          rowIdx: ctrlKey ? minRowIdx : rowIdx\n        };\n      case 'End':\n        if (isRowSelected) return {\n          idx,\n          rowIdx: maxRowIdx\n        };\n        return {\n          idx: maxColIdx,\n          rowIdx: ctrlKey ? maxRowIdx : rowIdx\n        };\n      case 'PageUp':\n        {\n          if (selectedPosition.rowIdx === minRowIdx) return selectedPosition;\n          const nextRowY = getRowTop(rowIdx) + getRowHeight(rowIdx) - clientHeight;\n          return {\n            idx,\n            rowIdx: nextRowY > 0 ? findRowIdx(nextRowY) : 0\n          };\n        }\n      case 'PageDown':\n        {\n          if (selectedPosition.rowIdx >= rows.length) return selectedPosition;\n          const nextRowY = getRowTop(rowIdx) + clientHeight;\n          return {\n            idx,\n            rowIdx: nextRowY < totalRowHeight ? findRowIdx(nextRowY) : rows.length - 1\n          };\n        }\n      default:\n        return selectedPosition;\n    }\n  }\n  function navigate(event) {\n    const {\n      key,\n      shiftKey\n    } = event;\n    let cellNavigationMode = 'NONE';\n    if (key === 'Tab') {\n      if (canExitGrid({\n        shiftKey,\n        maxColIdx,\n        minRowIdx,\n        maxRowIdx,\n        selectedPosition\n      })) {\n        commitEditorChanges();\n        return;\n      }\n      cellNavigationMode = 'CHANGE_ROW';\n    }\n    event.preventDefault();\n    const ctrlKey = isCtrlKeyHeldDown(event);\n    const nextPosition = getNextPosition(key, ctrlKey, shiftKey);\n    if (isSamePosition(selectedPosition, nextPosition)) return;\n    const nextSelectedCellPosition = getNextSelectedCellPosition({\n      moveUp: key === 'ArrowUp',\n      moveNext: key === rightKey || key === 'Tab' && !shiftKey,\n      columns,\n      colSpanColumns,\n      rows,\n      topSummaryRows,\n      bottomSummaryRows,\n      minRowIdx,\n      mainHeaderRowIdx,\n      maxRowIdx,\n      lastFrozenColumnIndex,\n      cellNavigationMode,\n      currentPosition: selectedPosition,\n      nextPosition,\n      isCellWithinBounds: isCellWithinSelectionBounds\n    });\n    selectCell(nextSelectedCellPosition);\n  }\n  function getDraggedOverCellIdx(currentRowIdx) {\n    if (draggedOverRowIdx === undefined) return;\n    const {\n      rowIdx\n    } = selectedPosition;\n    const isDraggedOver = rowIdx < draggedOverRowIdx ? rowIdx < currentRowIdx && currentRowIdx <= draggedOverRowIdx : rowIdx > currentRowIdx && currentRowIdx >= draggedOverRowIdx;\n    return isDraggedOver ? selectedPosition.idx : undefined;\n  }\n  function focusCellOrCellContent() {\n    const cell = getCellToScroll(gridRef.current);\n    if (cell === null) return;\n    scrollIntoView(cell);\n    const elementToFocus = cell.querySelector('[tabindex=\"0\"]') ?? cell;\n    elementToFocus.focus({\n      preventScroll: true\n    });\n  }\n  function renderDragHandle() {\n    if (onFill == null || selectedPosition.mode === 'EDIT' || !isCellWithinViewportBounds(selectedPosition)) {\n      return;\n    }\n    const {\n      idx,\n      rowIdx\n    } = selectedPosition;\n    const column = columns[idx];\n    if (column.renderEditCell == null || column.editable === false) {\n      return;\n    }\n    const columnWidth = getColumnWidth(column);\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DragHandle, {\n      gridRowStart: headerAndTopSummaryRowsCount + rowIdx + 1,\n      rows: rows,\n      column: column,\n      columnWidth: columnWidth,\n      maxColIdx: maxColIdx,\n      isLastRow: rowIdx === maxRowIdx,\n      selectedPosition: selectedPosition,\n      isCellEditable: isCellEditable,\n      latestDraggedOverRowIdx: latestDraggedOverRowIdx,\n      onRowsChange: onRowsChange,\n      onClick: focusCellOrCellContent,\n      onFill: onFill,\n      setDragging: setDragging,\n      setDraggedOverRowIdx: setDraggedOverRowIdx\n    });\n  }\n  function getCellEditor(rowIdx) {\n    if (selectedPosition.rowIdx !== rowIdx || selectedPosition.mode === 'SELECT') return;\n    const {\n      idx,\n      row\n    } = selectedPosition;\n    const column = columns[idx];\n    const colSpan = getColSpan(column, lastFrozenColumnIndex, {\n      type: 'ROW',\n      row\n    });\n    const closeEditor = shouldFocusCell => {\n      shouldFocusCellRef.current = shouldFocusCell;\n      setSelectedPosition(({\n        idx,\n        rowIdx\n      }) => ({\n        idx,\n        rowIdx,\n        mode: 'SELECT'\n      }));\n    };\n    const onRowChange = (row, commitChanges, shouldFocusCell) => {\n      if (commitChanges) {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n          updateRow(column, selectedPosition.rowIdx, row);\n          closeEditor(shouldFocusCell);\n        });\n      } else {\n        setSelectedPosition(position => ({\n          ...position,\n          row\n        }));\n      }\n    };\n    if (rows[selectedPosition.rowIdx] !== selectedPosition.originalRow) {\n      closeEditor(false);\n    }\n    return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(EditCell, {\n      column: column,\n      colSpan: colSpan,\n      row: row,\n      rowIdx: rowIdx,\n      onRowChange: onRowChange,\n      closeEditor: closeEditor,\n      onKeyDown: onCellKeyDown,\n      navigate: navigate\n    }, column.key);\n  }\n  function getRowViewportColumns(rowIdx) {\n    const selectedColumn = selectedPosition.idx === -1 ? undefined : columns[selectedPosition.idx];\n    if (selectedColumn !== undefined && selectedPosition.rowIdx === rowIdx && !viewportColumns.includes(selectedColumn)) {\n      return selectedPosition.idx > colOverscanEndIdx ? [...viewportColumns, selectedColumn] : [...viewportColumns.slice(0, lastFrozenColumnIndex + 1), selectedColumn, ...viewportColumns.slice(lastFrozenColumnIndex + 1)];\n    }\n    return viewportColumns;\n  }\n  function getViewportRows() {\n    const rowElements = [];\n    const {\n      idx: selectedIdx,\n      rowIdx: selectedRowIdx\n    } = selectedPosition;\n    const startRowIdx = selectedCellIsWithinViewportBounds && selectedRowIdx < rowOverscanStartIdx ? rowOverscanStartIdx - 1 : rowOverscanStartIdx;\n    const endRowIdx = selectedCellIsWithinViewportBounds && selectedRowIdx > rowOverscanEndIdx ? rowOverscanEndIdx + 1 : rowOverscanEndIdx;\n    for (let viewportRowIdx = startRowIdx; viewportRowIdx <= endRowIdx; viewportRowIdx++) {\n      const isRowOutsideViewport = viewportRowIdx === rowOverscanStartIdx - 1 || viewportRowIdx === rowOverscanEndIdx + 1;\n      const rowIdx = isRowOutsideViewport ? selectedRowIdx : viewportRowIdx;\n      let rowColumns = viewportColumns;\n      const selectedColumn = selectedIdx === -1 ? undefined : columns[selectedIdx];\n      if (selectedColumn !== undefined) {\n        if (isRowOutsideViewport) {\n          rowColumns = [selectedColumn];\n        } else {\n          rowColumns = getRowViewportColumns(rowIdx);\n        }\n      }\n      const row = rows[rowIdx];\n      const gridRowStart = headerAndTopSummaryRowsCount + rowIdx + 1;\n      let key = rowIdx;\n      let isRowSelected = false;\n      if (typeof rowKeyGetter === 'function') {\n        key = rowKeyGetter(row);\n        isRowSelected = selectedRows?.has(key) ?? false;\n      }\n      rowElements.push(renderRow(key, {\n        'aria-rowindex': headerAndTopSummaryRowsCount + rowIdx + 1,\n        'aria-selected': isSelectable ? isRowSelected : undefined,\n        rowIdx,\n        row,\n        viewportColumns: rowColumns,\n        isRowSelected,\n        onCellClick: onCellClickLatest,\n        onCellDoubleClick: onCellDoubleClickLatest,\n        onCellContextMenu: onCellContextMenuLatest,\n        rowClass,\n        gridRowStart,\n        height: getRowHeight(rowIdx),\n        copiedCellIdx: copiedCell !== null && copiedCell.row === row ? columns.findIndex(c => c.key === copiedCell.columnKey) : undefined,\n        selectedCellIdx: selectedRowIdx === rowIdx ? selectedIdx : undefined,\n        draggedOverCellIdx: getDraggedOverCellIdx(rowIdx),\n        setDraggedOverRowIdx: isDragging ? setDraggedOverRowIdx : undefined,\n        lastFrozenColumnIndex,\n        onRowChange: handleFormatterRowChangeLatest,\n        selectCell: selectCellLatest,\n        selectedCellEditor: getCellEditor(rowIdx)\n      }));\n    }\n    return rowElements;\n  }\n  if (selectedPosition.idx > maxColIdx || selectedPosition.rowIdx > maxRowIdx) {\n    setSelectedPosition({\n      idx: -1,\n      rowIdx: minRowIdx - 1,\n      mode: 'SELECT'\n    });\n    setDraggedOverRowIdx(undefined);\n  }\n  let templateRows = `repeat(${headerRowsCount}, ${headerRowHeight}px)`;\n  if (topSummaryRowsCount > 0) {\n    templateRows += ` repeat(${topSummaryRowsCount}, ${summaryRowHeight}px)`;\n  }\n  if (rows.length > 0) {\n    templateRows += gridTemplateRows;\n  }\n  if (bottomSummaryRowsCount > 0) {\n    templateRows += ` repeat(${bottomSummaryRowsCount}, ${summaryRowHeight}px)`;\n  }\n  const isGroupRowFocused = selectedPosition.idx === -1 && selectedPosition.rowIdx !== minRowIdx - 1;\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(\"div\", {\n    role: role,\n    \"aria-label\": ariaLabel,\n    \"aria-labelledby\": ariaLabelledBy,\n    \"aria-describedby\": ariaDescribedBy,\n    \"aria-multiselectable\": isSelectable ? true : undefined,\n    \"aria-colcount\": columns.length,\n    \"aria-rowcount\": ariaRowCount,\n    className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(rootClassname, className, isDragging && viewportDraggingClassname),\n    style: {\n      ...style,\n      scrollPaddingInlineStart: selectedPosition.idx > lastFrozenColumnIndex || scrollToPosition?.idx !== undefined ? `${totalFrozenColumnWidth}px` : undefined,\n      scrollPaddingBlock: isRowIdxWithinViewportBounds(selectedPosition.rowIdx) || scrollToPosition?.rowIdx !== undefined ? `${headerRowsHeight + topSummaryRowsCount * summaryRowHeight}px ${bottomSummaryRowsCount * summaryRowHeight}px` : undefined,\n      gridTemplateColumns,\n      gridTemplateRows: templateRows,\n      '--rdg-header-row-height': `${headerRowHeight}px`,\n      '--rdg-summary-row-height': `${summaryRowHeight}px`,\n      '--rdg-sign': isRtl ? -1 : 1,\n      ...layoutCssVars\n    },\n    dir: direction,\n    ref: gridRef,\n    onScroll: handleScroll,\n    onKeyDown: handleKeyDown,\n    \"data-testid\": testId,\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DataGridDefaultRenderersProvider, {\n      value: defaultGridComponents,\n      children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(RowSelectionChangeProvider, {\n        value: selectRowLatest,\n        children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(RowSelectionProvider, {\n          value: allRowsSelected,\n          children: [Array.from({\n            length: groupedColumnHeaderRowsCount\n          }, (_, index) => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(GroupedColumnHeaderRow$1, {\n            rowIdx: index + 1,\n            level: -groupedColumnHeaderRowsCount + index,\n            columns: getRowViewportColumns(minRowIdx + index),\n            selectedCellIdx: selectedPosition.rowIdx === minRowIdx + index ? selectedPosition.idx : undefined,\n            selectCell: selectHeaderCellLatest\n          }, index)), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(HeaderRow$1, {\n            rowIdx: headerRowsCount,\n            columns: getRowViewportColumns(mainHeaderRowIdx),\n            onColumnResize: handleColumnResizeLatest,\n            onColumnsReorder: onColumnsReorderLastest,\n            sortColumns: sortColumns,\n            onSortColumnsChange: onSortColumnsChangeLatest,\n            lastFrozenColumnIndex: lastFrozenColumnIndex,\n            selectedCellIdx: selectedPosition.rowIdx === mainHeaderRowIdx ? selectedPosition.idx : undefined,\n            selectCell: selectHeaderCellLatest,\n            shouldFocusGrid: !selectedCellIsWithinSelectionBounds,\n            direction: direction\n          })]\n        }), rows.length === 0 && noRowsFallback ? noRowsFallback : /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.Fragment, {\n          children: [topSummaryRows?.map((row, rowIdx) => {\n            const gridRowStart = headerRowsCount + 1 + rowIdx;\n            const summaryRowIdx = mainHeaderRowIdx + 1 + rowIdx;\n            const isSummaryRowSelected = selectedPosition.rowIdx === summaryRowIdx;\n            const top = headerRowsHeight + summaryRowHeight * rowIdx;\n            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SummaryRow$1, {\n              \"aria-rowindex\": gridRowStart,\n              rowIdx: summaryRowIdx,\n              gridRowStart: gridRowStart,\n              row: row,\n              top: top,\n              bottom: undefined,\n              viewportColumns: getRowViewportColumns(summaryRowIdx),\n              lastFrozenColumnIndex: lastFrozenColumnIndex,\n              selectedCellIdx: isSummaryRowSelected ? selectedPosition.idx : undefined,\n              isTop: true,\n              showBorder: rowIdx === topSummaryRowsCount - 1,\n              selectCell: selectCellLatest\n            }, rowIdx);\n          }), getViewportRows(), bottomSummaryRows?.map((row, rowIdx) => {\n            const gridRowStart = headerAndTopSummaryRowsCount + rows.length + rowIdx + 1;\n            const summaryRowIdx = rows.length + rowIdx;\n            const isSummaryRowSelected = selectedPosition.rowIdx === summaryRowIdx;\n            const top = clientHeight > totalRowHeight ? gridHeight - summaryRowHeight * (bottomSummaryRows.length - rowIdx) : undefined;\n            const bottom = top === undefined ? summaryRowHeight * (bottomSummaryRows.length - 1 - rowIdx) : undefined;\n            return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(SummaryRow$1, {\n              \"aria-rowindex\": ariaRowCount - bottomSummaryRowsCount + rowIdx + 1,\n              rowIdx: summaryRowIdx,\n              gridRowStart: gridRowStart,\n              row: row,\n              top: top,\n              bottom: bottom,\n              viewportColumns: getRowViewportColumns(summaryRowIdx),\n              lastFrozenColumnIndex: lastFrozenColumnIndex,\n              selectedCellIdx: isSummaryRowSelected ? selectedPosition.idx : undefined,\n              isTop: false,\n              showBorder: rowIdx === 0,\n              selectCell: selectCellLatest\n            }, rowIdx);\n          })]\n        })]\n      })\n    }), renderDragHandle(), renderMeasuringCells(viewportColumns), isTreeGrid && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n      ref: focusSinkRef,\n      tabIndex: isGroupRowFocused ? 0 : -1,\n      className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(focusSinkClassname, isGroupRowFocused && [rowSelected, lastFrozenColumnIndex !== -1 && rowSelectedWithFrozenCell], !isRowIdxWithinViewportBounds(selectedPosition.rowIdx) && focusSinkHeaderAndSummaryClassname),\n      style: {\n        gridRowStart: selectedPosition.rowIdx + headerAndTopSummaryRowsCount + 1\n      }\n    }), scrollToPosition !== null && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(ScrollToCell, {\n      scrollToPosition: scrollToPosition,\n      setScrollToCellPosition: setScrollToPosition,\n      gridElement: gridRef.current\n    })]\n  });\n}\nfunction getCellToScroll(gridEl) {\n  return gridEl.querySelector(':scope > [role=\"row\"] > [tabindex=\"0\"]');\n}\nfunction isSamePosition(p1, p2) {\n  return p1.idx === p2.idx && p1.rowIdx === p2.rowIdx;\n}\nconst DataGrid$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(DataGrid);\n\nfunction GroupCell({\n  id,\n  groupKey,\n  childRows,\n  isExpanded,\n  isCellSelected,\n  column,\n  row,\n  groupColumnIndex,\n  isGroupByColumn,\n  toggleGroup: toggleGroupWrapper\n}) {\n  const {\n    tabIndex,\n    childTabIndex,\n    onFocus\n  } = useRovingTabIndex(isCellSelected);\n  function toggleGroup() {\n    toggleGroupWrapper(id);\n  }\n  const isLevelMatching = isGroupByColumn && groupColumnIndex === column.idx;\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n    role: \"gridcell\",\n    \"aria-colindex\": column.idx + 1,\n    \"aria-selected\": isCellSelected,\n    tabIndex: tabIndex,\n    className: getCellClassname(column),\n    style: {\n      ...getCellStyle(column),\n      cursor: isLevelMatching ? 'pointer' : 'default'\n    },\n    onClick: isLevelMatching ? toggleGroup : undefined,\n    onFocus: onFocus,\n    children: (!isGroupByColumn || isLevelMatching) && column.renderGroupCell?.({\n      groupKey,\n      childRows,\n      column,\n      row,\n      isExpanded,\n      tabIndex: childTabIndex,\n      toggleGroup\n    })\n  }, column.key);\n}\nconst GroupCell$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GroupCell);\n\nconst groupRow = \"g1yxluv37-0-0-beta-41\";\nconst groupRowClassname = `rdg-group-row ${groupRow}`;\nfunction GroupedRow({\n  className,\n  row,\n  rowIdx,\n  viewportColumns,\n  selectedCellIdx,\n  isRowSelected,\n  selectCell,\n  gridRowStart,\n  height,\n  groupBy,\n  toggleGroup,\n  ...props\n}) {\n  const idx = viewportColumns[0].key === SELECT_COLUMN_KEY ? row.level + 1 : row.level;\n  function handleSelectGroup() {\n    selectCell({\n      rowIdx,\n      idx: -1\n    });\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(RowSelectionProvider, {\n    value: isRowSelected,\n    children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"div\", {\n      role: \"row\",\n      \"aria-level\": row.level + 1,\n      \"aria-setsize\": row.setSize,\n      \"aria-posinset\": row.posInSet + 1,\n      \"aria-expanded\": row.isExpanded,\n      className: (0,clsx__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(rowClassname, groupRowClassname, `rdg-row-${rowIdx % 2 === 0 ? 'even' : 'odd'}`, className, selectedCellIdx === -1 && rowSelectedClassname),\n      onClick: handleSelectGroup,\n      style: getRowStyle(gridRowStart, height),\n      ...props,\n      children: viewportColumns.map(column => /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(GroupCell$1, {\n        id: row.id,\n        groupKey: row.groupKey,\n        childRows: row.childRows,\n        isExpanded: row.isExpanded,\n        isCellSelected: selectedCellIdx === column.idx,\n        column: column,\n        row: row,\n        groupColumnIndex: idx,\n        toggleGroup: toggleGroup,\n        isGroupByColumn: groupBy.includes(column.key)\n      }, column.key))\n    })\n  });\n}\nconst GroupedRow$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(GroupedRow);\n\nfunction TreeDataGrid({\n  columns: rawColumns,\n  rows: rawRows,\n  rowHeight: rawRowHeight,\n  rowKeyGetter: rawRowKeyGetter,\n  onCellKeyDown: rawOnCellKeyDown,\n  onRowsChange,\n  selectedRows: rawSelectedRows,\n  onSelectedRowsChange: rawOnSelectedRowsChange,\n  renderers,\n  groupBy: rawGroupBy,\n  rowGrouper,\n  expandedGroupIds,\n  onExpandedGroupIdsChange,\n  ...props\n}, ref) {\n  const defaultRenderers = useDefaultRenderers();\n  const rawRenderRow = renderers?.renderRow ?? defaultRenderers?.renderRow ?? defaultRenderRow;\n  const headerAndTopSummaryRowsCount = 1 + (props.topSummaryRows?.length ?? 0);\n  const isRtl = props.direction === 'rtl';\n  const leftKey = isRtl ? 'ArrowRight' : 'ArrowLeft';\n  const rightKey = isRtl ? 'ArrowLeft' : 'ArrowRight';\n  const toggleGroupLatest = useLatestFunc(toggleGroup);\n  const {\n    columns,\n    groupBy\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const columns = [...rawColumns].sort(({\n      key: aKey\n    }, {\n      key: bKey\n    }) => {\n      if (aKey === SELECT_COLUMN_KEY) return -1;\n      if (bKey === SELECT_COLUMN_KEY) return 1;\n      if (rawGroupBy.includes(aKey)) {\n        if (rawGroupBy.includes(bKey)) {\n          return rawGroupBy.indexOf(aKey) - rawGroupBy.indexOf(bKey);\n        }\n        return -1;\n      }\n      if (rawGroupBy.includes(bKey)) return 1;\n      return 0;\n    });\n    const groupBy = [];\n    for (const [index, column] of columns.entries()) {\n      if (rawGroupBy.includes(column.key)) {\n        groupBy.push(column.key);\n        columns[index] = {\n          ...column,\n          frozen: true,\n          renderCell: () => null,\n          renderGroupCell: column.renderGroupCell ?? renderToggleGroup,\n          editable: false\n        };\n      }\n    }\n    return {\n      columns,\n      groupBy\n    };\n  }, [rawColumns, rawGroupBy]);\n  const [groupedRows, rowsCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (groupBy.length === 0) return [undefined, rawRows.length];\n    const groupRows = (rows, [groupByKey, ...remainingGroupByKeys], startRowIndex) => {\n      let groupRowsCount = 0;\n      const groups = {};\n      for (const [key, childRows] of Object.entries(rowGrouper(rows, groupByKey))) {\n        const [childGroups, childRowsCount] = remainingGroupByKeys.length === 0 ? [childRows, childRows.length] : groupRows(childRows, remainingGroupByKeys, startRowIndex + groupRowsCount + 1);\n        groups[key] = {\n          childRows,\n          childGroups,\n          startRowIndex: startRowIndex + groupRowsCount\n        };\n        groupRowsCount += childRowsCount + 1;\n      }\n      return [groups, groupRowsCount];\n    };\n    return groupRows(rawRows, groupBy, 0);\n  }, [groupBy, rowGrouper, rawRows]);\n  const [rows, isGroupRow] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const allGroupRows = new Set();\n    if (!groupedRows) return [rawRows, isGroupRow];\n    const flattenedRows = [];\n    const expandGroup = (rows, parentId, level) => {\n      if (isReadonlyArray(rows)) {\n        flattenedRows.push(...rows);\n        return;\n      }\n      Object.keys(rows).forEach((groupKey, posInSet, keys) => {\n        const id = parentId !== undefined ? `${parentId}__${groupKey}` : groupKey;\n        const isExpanded = expandedGroupIds.has(id);\n        const {\n          childRows,\n          childGroups,\n          startRowIndex\n        } = rows[groupKey];\n        const groupRow = {\n          id,\n          parentId,\n          groupKey,\n          isExpanded,\n          childRows,\n          level,\n          posInSet,\n          startRowIndex,\n          setSize: keys.length\n        };\n        flattenedRows.push(groupRow);\n        allGroupRows.add(groupRow);\n        if (isExpanded) {\n          expandGroup(childGroups, id, level + 1);\n        }\n      });\n    };\n    expandGroup(groupedRows, undefined, 0);\n    return [flattenedRows, isGroupRow];\n    function isGroupRow(row) {\n      return allGroupRows.has(row);\n    }\n  }, [expandedGroupIds, groupedRows, rawRows]);\n  const rowHeight = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (typeof rawRowHeight === 'function') {\n      return row => {\n        if (isGroupRow(row)) {\n          return rawRowHeight({\n            type: 'GROUP',\n            row\n          });\n        }\n        return rawRowHeight({\n          type: 'ROW',\n          row\n        });\n      };\n    }\n    return rawRowHeight;\n  }, [isGroupRow, rawRowHeight]);\n  const getParentRowAndIndex = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(row => {\n    const rowIdx = rows.indexOf(row);\n    for (let i = rowIdx - 1; i >= 0; i--) {\n      const parentRow = rows[i];\n      if (isGroupRow(parentRow) && (!isGroupRow(row) || row.parentId === parentRow.id)) {\n        return [parentRow, i];\n      }\n    }\n    return undefined;\n  }, [isGroupRow, rows]);\n  const rowKeyGetter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(row => {\n    if (isGroupRow(row)) {\n      return row.id;\n    }\n    if (typeof rawRowKeyGetter === 'function') {\n      return rawRowKeyGetter(row);\n    }\n    const parentRowAndIndex = getParentRowAndIndex(row);\n    if (parentRowAndIndex !== undefined) {\n      const {\n        startRowIndex,\n        childRows\n      } = parentRowAndIndex[0];\n      const groupIndex = childRows.indexOf(row);\n      return startRowIndex + groupIndex + 1;\n    }\n    return rows.indexOf(row);\n  }, [getParentRowAndIndex, isGroupRow, rawRowKeyGetter, rows]);\n  const selectedRows = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (rawSelectedRows == null) return null;\n    assertIsValidKeyGetter(rawRowKeyGetter);\n    const selectedRows = new Set(rawSelectedRows);\n    for (const row of rows) {\n      if (isGroupRow(row)) {\n        const isGroupRowSelected = row.childRows.every(cr => rawSelectedRows.has(rawRowKeyGetter(cr)));\n        if (isGroupRowSelected) {\n          selectedRows.add(row.id);\n        }\n      }\n    }\n    return selectedRows;\n  }, [isGroupRow, rawRowKeyGetter, rawSelectedRows, rows]);\n  function onSelectedRowsChange(newSelectedRows) {\n    if (!rawOnSelectedRowsChange) return;\n    assertIsValidKeyGetter(rawRowKeyGetter);\n    const newRawSelectedRows = new Set(rawSelectedRows);\n    for (const row of rows) {\n      const key = rowKeyGetter(row);\n      if (selectedRows?.has(key) && !newSelectedRows.has(key)) {\n        if (isGroupRow(row)) {\n          for (const cr of row.childRows) {\n            newRawSelectedRows.delete(rawRowKeyGetter(cr));\n          }\n        } else {\n          newRawSelectedRows.delete(key);\n        }\n      } else if (!selectedRows?.has(key) && newSelectedRows.has(key)) {\n        if (isGroupRow(row)) {\n          for (const cr of row.childRows) {\n            newRawSelectedRows.add(rawRowKeyGetter(cr));\n          }\n        } else {\n          newRawSelectedRows.add(key);\n        }\n      }\n    }\n    rawOnSelectedRowsChange(newRawSelectedRows);\n  }\n  function handleKeyDown(args, event) {\n    rawOnCellKeyDown?.(args, event);\n    if (event.isGridDefaultPrevented()) return;\n    if (args.mode === 'EDIT') return;\n    const {\n      column,\n      rowIdx,\n      selectCell\n    } = args;\n    const idx = column?.idx ?? -1;\n    const row = rows[rowIdx];\n    if (!isGroupRow(row)) return;\n    if (idx === -1 && (event.key === leftKey && row.isExpanded || event.key === rightKey && !row.isExpanded)) {\n      event.preventDefault();\n      event.preventGridDefault();\n      toggleGroup(row.id);\n    }\n    if (idx === -1 && event.key === leftKey && !row.isExpanded && row.level !== 0) {\n      const parentRowAndIndex = getParentRowAndIndex(row);\n      if (parentRowAndIndex !== undefined) {\n        event.preventGridDefault();\n        selectCell({\n          idx,\n          rowIdx: parentRowAndIndex[1]\n        });\n      }\n    }\n    if (isCtrlKeyHeldDown(event) && (event.keyCode === 67 || event.keyCode === 86)) {\n      event.preventGridDefault();\n    }\n  }\n  function handleRowsChange(updatedRows, {\n    indexes,\n    column\n  }) {\n    if (!onRowsChange) return;\n    const updatedRawRows = [...rawRows];\n    const rawIndexes = [];\n    indexes.forEach(index => {\n      const rawIndex = rawRows.indexOf(rows[index]);\n      updatedRawRows[rawIndex] = updatedRows[index];\n      rawIndexes.push(rawIndex);\n    });\n    onRowsChange(updatedRawRows, {\n      indexes: rawIndexes,\n      column\n    });\n  }\n  function toggleGroup(groupId) {\n    const newExpandedGroupIds = new Set(expandedGroupIds);\n    if (newExpandedGroupIds.has(groupId)) {\n      newExpandedGroupIds.delete(groupId);\n    } else {\n      newExpandedGroupIds.add(groupId);\n    }\n    onExpandedGroupIdsChange(newExpandedGroupIds);\n  }\n  function renderRow(key, {\n    row,\n    rowClass,\n    onCellClick,\n    onCellDoubleClick,\n    onCellContextMenu,\n    onRowChange,\n    lastFrozenColumnIndex,\n    copiedCellIdx,\n    draggedOverCellIdx,\n    setDraggedOverRowIdx,\n    selectedCellEditor,\n    ...rowProps\n  }) {\n    if (isGroupRow(row)) {\n      const {\n        startRowIndex\n      } = row;\n      return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(GroupedRow$1, {\n        ...rowProps,\n        \"aria-rowindex\": headerAndTopSummaryRowsCount + startRowIndex + 1,\n        row: row,\n        groupBy: groupBy,\n        toggleGroup: toggleGroupLatest\n      }, key);\n    }\n    let ariaRowIndex = rowProps['aria-rowindex'];\n    const parentRowAndIndex = getParentRowAndIndex(row);\n    if (parentRowAndIndex !== undefined) {\n      const {\n        startRowIndex,\n        childRows\n      } = parentRowAndIndex[0];\n      const groupIndex = childRows.indexOf(row);\n      ariaRowIndex = startRowIndex + headerAndTopSummaryRowsCount + groupIndex + 2;\n    }\n    return rawRenderRow(key, {\n      ...rowProps,\n      'aria-rowindex': ariaRowIndex,\n      row,\n      rowClass,\n      onCellClick,\n      onCellDoubleClick,\n      onCellContextMenu,\n      onRowChange,\n      lastFrozenColumnIndex,\n      copiedCellIdx,\n      draggedOverCellIdx,\n      setDraggedOverRowIdx,\n      selectedCellEditor\n    });\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(DataGrid$1, {\n    ...props,\n    role: \"treegrid\",\n    \"aria-rowcount\": rowsCount + 1 + (props.topSummaryRows?.length ?? 0) + (props.bottomSummaryRows?.length ?? 0),\n    ref: ref,\n    columns: columns,\n    rows: rows,\n    rowHeight: rowHeight,\n    rowKeyGetter: rowKeyGetter,\n    onRowsChange: handleRowsChange,\n    selectedRows: selectedRows,\n    onSelectedRowsChange: onSelectedRowsChange,\n    onCellKeyDown: handleKeyDown,\n    renderers: {\n      ...renderers,\n      renderRow\n    }\n  });\n}\nfunction isReadonlyArray(arr) {\n  return Array.isArray(arr);\n}\nconst TreeDataGrid$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(TreeDataGrid);\n\nconst textEditorInternalClassname = \"t7vyx3i7-0-0-beta-41\";\nconst textEditorClassname = `rdg-text-editor ${textEditorInternalClassname}`;\nfunction autoFocusAndSelect(input) {\n  input?.focus();\n  input?.select();\n}\nfunction textEditor({\n  row,\n  column,\n  onRowChange,\n  onClose\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_3__.jsx)(\"input\", {\n    className: textEditorClassname,\n    ref: autoFocusAndSelect,\n    value: row[column.key],\n    onChange: event => onRowChange({\n      ...row,\n      [column.key]: event.target.value\n    }),\n    onBlur: () => onClose(true, false)\n  });\n}\n\n\n//# sourceMappingURL=bundle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-data-grid/lib/bundle.js\n");

/***/ })

};
;