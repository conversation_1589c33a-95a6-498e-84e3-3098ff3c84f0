"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/relative-time-format";
exports.ids = ["vendor-chunks/relative-time-format"];
exports.modules = {

/***/ "(ssr)/./node_modules/relative-time-format/modules/LocaleDataStore.js":
/*!**********************************************************************!*\
  !*** ./node_modules/relative-time-format/modules/LocaleDataStore.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addLocaleData: () => (/* binding */ addLocaleData),\n/* harmony export */   getDefaultLocale: () => (/* binding */ getDefaultLocale),\n/* harmony export */   getLocaleData: () => (/* binding */ getLocaleData),\n/* harmony export */   resolveLocale: () => (/* binding */ resolveLocale),\n/* harmony export */   setDefaultLocale: () => (/* binding */ setDefaultLocale)\n/* harmony export */ });\n// Fallback locale.\n// (when not a single one of the supplied \"preferred\" locales is available)\nvar defaultLocale = 'en'; // For all locales added\n// their relative time formatter messages will be stored here.\n\nvar localesData = {}; // According to the spec BCP 47 language tags are case-insensitive.\n// https://tools.ietf.org/html/rfc5646\n\nvar lowercaseLocaleLookup = {};\nfunction getDefaultLocale() {\n  return defaultLocale;\n}\nfunction setDefaultLocale(locale) {\n  defaultLocale = locale;\n}\n/**\r\n * Gets locale data previously added by `addLocaleData()`.\r\n * @return  {object} [localeData]\r\n */\n\nfunction getLocaleData(locale) {\n  return localesData[locale];\n}\n/**\r\n * Adds locale data.\r\n * Is called by `RelativeTimeFormat.addLocale(...)`.\r\n * @param  {object} localeData\r\n */\n\nfunction addLocaleData(localeData) {\n  if (!localeData) {\n    throw new Error('No locale data passed');\n  } // This locale data is stored in a global variable\n  // and later used when calling `.format(time)`.\n\n\n  localesData[localeData.locale] = localeData;\n  lowercaseLocaleLookup[localeData.locale.toLowerCase()] = localeData.locale;\n}\n/**\r\n * Returns a locale for which locale data has been added\r\n * via `RelativeTimeFormat.addLocale(...)`.\r\n * @param  {string} locale\r\n * @return {string} [locale]\r\n */\n\nfunction resolveLocale(locale) {\n  if (localesData[locale]) {\n    return locale;\n  }\n\n  if (lowercaseLocaleLookup[locale.toLowerCase()]) {\n    return lowercaseLocaleLookup[locale.toLowerCase()];\n  }\n}\n//# sourceMappingURL=LocaleDataStore.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/relative-time-format/modules/LocaleDataStore.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/relative-time-format/modules/PluralRuleFunctions.js":
/*!**************************************************************************!*\
  !*** ./node_modules/relative-time-format/modules/PluralRuleFunctions.js ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// (this file was autogenerated by `generate-locales`)\n// \"plural rules\" functions are not stored in locale JSON files because they're not strings.\n// This file isn't big — it's about 5 kilobytes in size (minified).\n// Alternatively, the pluralization rules for each locale could be stored\n// in their JSON files in a non-parsed form and later parsed via `make-plural` library.\n// But `make-plural` library itself is relatively big in size:\n// `make-plural.min.js` is about 6 kilobytes (https://unpkg.com/make-plural/).\n// So, it's more practical to bypass runtime `make-plural` pluralization rules compilation\n// and just include the already compiled pluarlization rules for all locales in the library code.\nvar $ = {\n  af: function af(n) {\n    return n == 1 ? 'one' : 'other';\n  },\n  am: function am(n) {\n    return n >= 0 && n <= 1 ? 'one' : 'other';\n  },\n  ar: function ar(n) {\n    var s = String(n).split('.'),\n        t0 = Number(s[0]) == n,\n        n100 = t0 && s[0].slice(-2);\n    return n == 0 ? 'zero' : n == 1 ? 'one' : n == 2 ? 'two' : n100 >= 3 && n100 <= 10 ? 'few' : n100 >= 11 && n100 <= 99 ? 'many' : 'other';\n  },\n  ast: function ast(n) {\n    var s = String(n).split('.'),\n        v0 = !s[1];\n    return n == 1 && v0 ? 'one' : 'other';\n  },\n  be: function be(n) {\n    var s = String(n).split('.'),\n        t0 = Number(s[0]) == n,\n        n10 = t0 && s[0].slice(-1),\n        n100 = t0 && s[0].slice(-2);\n    return n10 == 1 && n100 != 11 ? 'one' : n10 >= 2 && n10 <= 4 && (n100 < 12 || n100 > 14) ? 'few' : t0 && n10 == 0 || n10 >= 5 && n10 <= 9 || n100 >= 11 && n100 <= 14 ? 'many' : 'other';\n  },\n  br: function br(n) {\n    var s = String(n).split('.'),\n        t0 = Number(s[0]) == n,\n        n10 = t0 && s[0].slice(-1),\n        n100 = t0 && s[0].slice(-2),\n        n1000000 = t0 && s[0].slice(-6);\n    return n10 == 1 && n100 != 11 && n100 != 71 && n100 != 91 ? 'one' : n10 == 2 && n100 != 12 && n100 != 72 && n100 != 92 ? 'two' : (n10 == 3 || n10 == 4 || n10 == 9) && (n100 < 10 || n100 > 19) && (n100 < 70 || n100 > 79) && (n100 < 90 || n100 > 99) ? 'few' : n != 0 && t0 && n1000000 == 0 ? 'many' : 'other';\n  },\n  bs: function bs(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        f = s[1] || '',\n        v0 = !s[1],\n        i10 = i.slice(-1),\n        i100 = i.slice(-2),\n        f10 = f.slice(-1),\n        f100 = f.slice(-2);\n    return v0 && i10 == 1 && i100 != 11 || f10 == 1 && f100 != 11 ? 'one' : v0 && i10 >= 2 && i10 <= 4 && (i100 < 12 || i100 > 14) || f10 >= 2 && f10 <= 4 && (f100 < 12 || f100 > 14) ? 'few' : 'other';\n  },\n  ca: function ca(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1],\n        i1000000 = i.slice(-6);\n    return n == 1 && v0 ? 'one' : i != 0 && i1000000 == 0 && v0 ? 'many' : 'other';\n  },\n  ceb: function ceb(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        f = s[1] || '',\n        v0 = !s[1],\n        i10 = i.slice(-1),\n        f10 = f.slice(-1);\n    return v0 && (i == 1 || i == 2 || i == 3) || v0 && i10 != 4 && i10 != 6 && i10 != 9 || !v0 && f10 != 4 && f10 != 6 && f10 != 9 ? 'one' : 'other';\n  },\n  cs: function cs(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1];\n    return n == 1 && v0 ? 'one' : i >= 2 && i <= 4 && v0 ? 'few' : !v0 ? 'many' : 'other';\n  },\n  cy: function cy(n) {\n    return n == 0 ? 'zero' : n == 1 ? 'one' : n == 2 ? 'two' : n == 3 ? 'few' : n == 6 ? 'many' : 'other';\n  },\n  da: function da(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        t0 = Number(s[0]) == n;\n    return n == 1 || !t0 && (i == 0 || i == 1) ? 'one' : 'other';\n  },\n  dsb: function dsb(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        f = s[1] || '',\n        v0 = !s[1],\n        i100 = i.slice(-2),\n        f100 = f.slice(-2);\n    return v0 && i100 == 1 || f100 == 1 ? 'one' : v0 && i100 == 2 || f100 == 2 ? 'two' : v0 && (i100 == 3 || i100 == 4) || f100 == 3 || f100 == 4 ? 'few' : 'other';\n  },\n  dz: function dz(n) {\n    return 'other';\n  },\n  es: function es(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1],\n        i1000000 = i.slice(-6);\n    return n == 1 ? 'one' : i != 0 && i1000000 == 0 && v0 ? 'many' : 'other';\n  },\n  ff: function ff(n) {\n    return n >= 0 && n < 2 ? 'one' : 'other';\n  },\n  fr: function fr(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1],\n        i1000000 = i.slice(-6);\n    return n >= 0 && n < 2 ? 'one' : i != 0 && i1000000 == 0 && v0 ? 'many' : 'other';\n  },\n  ga: function ga(n) {\n    var s = String(n).split('.'),\n        t0 = Number(s[0]) == n;\n    return n == 1 ? 'one' : n == 2 ? 'two' : t0 && n >= 3 && n <= 6 ? 'few' : t0 && n >= 7 && n <= 10 ? 'many' : 'other';\n  },\n  gd: function gd(n) {\n    var s = String(n).split('.'),\n        t0 = Number(s[0]) == n;\n    return n == 1 || n == 11 ? 'one' : n == 2 || n == 12 ? 'two' : t0 && n >= 3 && n <= 10 || t0 && n >= 13 && n <= 19 ? 'few' : 'other';\n  },\n  he: function he(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1];\n    return i == 1 && v0 || i == 0 && !v0 ? 'one' : i == 2 && v0 ? 'two' : 'other';\n  },\n  is: function is(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        t = (s[1] || '').replace(/0+$/, ''),\n        t0 = Number(s[0]) == n,\n        i10 = i.slice(-1),\n        i100 = i.slice(-2);\n    return t0 && i10 == 1 && i100 != 11 || t % 10 == 1 && t % 100 != 11 ? 'one' : 'other';\n  },\n  ksh: function ksh(n) {\n    return n == 0 ? 'zero' : n == 1 ? 'one' : 'other';\n  },\n  lt: function lt(n) {\n    var s = String(n).split('.'),\n        f = s[1] || '',\n        t0 = Number(s[0]) == n,\n        n10 = t0 && s[0].slice(-1),\n        n100 = t0 && s[0].slice(-2);\n    return n10 == 1 && (n100 < 11 || n100 > 19) ? 'one' : n10 >= 2 && n10 <= 9 && (n100 < 11 || n100 > 19) ? 'few' : f != 0 ? 'many' : 'other';\n  },\n  lv: function lv(n) {\n    var s = String(n).split('.'),\n        f = s[1] || '',\n        v = f.length,\n        t0 = Number(s[0]) == n,\n        n10 = t0 && s[0].slice(-1),\n        n100 = t0 && s[0].slice(-2),\n        f100 = f.slice(-2),\n        f10 = f.slice(-1);\n    return t0 && n10 == 0 || n100 >= 11 && n100 <= 19 || v == 2 && f100 >= 11 && f100 <= 19 ? 'zero' : n10 == 1 && n100 != 11 || v == 2 && f10 == 1 && f100 != 11 || v != 2 && f10 == 1 ? 'one' : 'other';\n  },\n  mk: function mk(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        f = s[1] || '',\n        v0 = !s[1],\n        i10 = i.slice(-1),\n        i100 = i.slice(-2),\n        f10 = f.slice(-1),\n        f100 = f.slice(-2);\n    return v0 && i10 == 1 && i100 != 11 || f10 == 1 && f100 != 11 ? 'one' : 'other';\n  },\n  mt: function mt(n) {\n    var s = String(n).split('.'),\n        t0 = Number(s[0]) == n,\n        n100 = t0 && s[0].slice(-2);\n    return n == 1 ? 'one' : n == 2 ? 'two' : n == 0 || n100 >= 3 && n100 <= 10 ? 'few' : n100 >= 11 && n100 <= 19 ? 'many' : 'other';\n  },\n  pa: function pa(n) {\n    return n == 0 || n == 1 ? 'one' : 'other';\n  },\n  pl: function pl(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1],\n        i10 = i.slice(-1),\n        i100 = i.slice(-2);\n    return n == 1 && v0 ? 'one' : v0 && i10 >= 2 && i10 <= 4 && (i100 < 12 || i100 > 14) ? 'few' : v0 && i != 1 && (i10 == 0 || i10 == 1) || v0 && i10 >= 5 && i10 <= 9 || v0 && i100 >= 12 && i100 <= 14 ? 'many' : 'other';\n  },\n  pt: function pt(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1],\n        i1000000 = i.slice(-6);\n    return i == 0 || i == 1 ? 'one' : i != 0 && i1000000 == 0 && v0 ? 'many' : 'other';\n  },\n  ro: function ro(n) {\n    var s = String(n).split('.'),\n        v0 = !s[1],\n        t0 = Number(s[0]) == n,\n        n100 = t0 && s[0].slice(-2);\n    return n == 1 && v0 ? 'one' : !v0 || n == 0 || n != 1 && n100 >= 1 && n100 <= 19 ? 'few' : 'other';\n  },\n  ru: function ru(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1],\n        i10 = i.slice(-1),\n        i100 = i.slice(-2);\n    return v0 && i10 == 1 && i100 != 11 ? 'one' : v0 && i10 >= 2 && i10 <= 4 && (i100 < 12 || i100 > 14) ? 'few' : v0 && i10 == 0 || v0 && i10 >= 5 && i10 <= 9 || v0 && i100 >= 11 && i100 <= 14 ? 'many' : 'other';\n  },\n  se: function se(n) {\n    return n == 1 ? 'one' : n == 2 ? 'two' : 'other';\n  },\n  si: function si(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        f = s[1] || '';\n    return n == 0 || n == 1 || i == 0 && f == 1 ? 'one' : 'other';\n  },\n  sl: function sl(n) {\n    var s = String(n).split('.'),\n        i = s[0],\n        v0 = !s[1],\n        i100 = i.slice(-2);\n    return v0 && i100 == 1 ? 'one' : v0 && i100 == 2 ? 'two' : v0 && (i100 == 3 || i100 == 4) || !v0 ? 'few' : 'other';\n  }\n};\n$.as = $.am;\n$.az = $.af;\n$.bg = $.af;\n$.bn = $.am;\n$.brx = $.af;\n$.ce = $.af;\n$.chr = $.af;\n$.de = $.ast;\n$.ee = $.af;\n$.el = $.af;\n$.en = $.ast;\n$.et = $.ast;\n$.eu = $.af;\n$.fa = $.am;\n$.fi = $.ast;\n$.fil = $.ceb;\n$.fo = $.af;\n$.fur = $.af;\n$.fy = $.ast;\n$.gl = $.ast;\n$.gu = $.am;\n$.ha = $.af;\n$.hi = $.am;\n$.hr = $.bs;\n$.hsb = $.dsb;\n$.hu = $.af;\n$.hy = $.ff;\n$.ia = $.ast;\n$.id = $.dz;\n$.ig = $.dz;\n$.it = $.ca;\n$.ja = $.dz;\n$.jgo = $.af;\n$.jv = $.dz;\n$.ka = $.af;\n$.kea = $.dz;\n$.kk = $.af;\n$.kl = $.af;\n$.km = $.dz;\n$.kn = $.am;\n$.ko = $.dz;\n$.ks = $.af;\n$.ku = $.af;\n$.ky = $.af;\n$.lb = $.af;\n$.lkt = $.dz;\n$.lo = $.dz;\n$.ml = $.af;\n$.mn = $.af;\n$.mr = $.af;\n$.ms = $.dz;\n$.my = $.dz;\n$.nb = $.af;\n$.ne = $.af;\n$.nl = $.ast;\n$.nn = $.af;\n$.no = $.af;\n$.or = $.af;\n$.pcm = $.am;\n$.ps = $.af;\n$.rm = $.af;\n$.sah = $.dz;\n$.sc = $.ast;\n$.sd = $.af;\n$.sk = $.cs;\n$.so = $.af;\n$.sq = $.af;\n$.sr = $.bs;\n$.su = $.dz;\n$.sv = $.ast;\n$.sw = $.ast;\n$.ta = $.af;\n$.te = $.af;\n$.th = $.dz;\n$.ti = $.pa;\n$.tk = $.af;\n$.to = $.dz;\n$.tr = $.af;\n$.ug = $.af;\n$.uk = $.ru;\n$.ur = $.ast;\n$.uz = $.af;\n$.vi = $.dz;\n$.wae = $.af;\n$.wo = $.dz;\n$.xh = $.af;\n$.yi = $.ast;\n$.yo = $.dz;\n$.yue = $.dz;\n$.zh = $.dz;\n$.zu = $.am;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ($);\n//# sourceMappingURL=PluralRuleFunctions.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/relative-time-format/modules/PluralRuleFunctions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/relative-time-format/modules/PluralRules.js":
/*!******************************************************************!*\
  !*** ./node_modules/relative-time-format/modules/PluralRules.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PluralRules)\n/* harmony export */ });\n/* harmony import */ var _PluralRuleFunctions_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./PluralRuleFunctions.js */ \"(ssr)/./node_modules/relative-time-format/modules/PluralRuleFunctions.js\");\n/* harmony import */ var _getPluralRulesLocale_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./getPluralRulesLocale.js */ \"(ssr)/./node_modules/relative-time-format/modules/getPluralRulesLocale.js\");\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n// Importing `PluralRule` polyfill from a separate package\n// results in a bundle that is larger by 1kB for some reason.\n// export { default as default } from 'intl-plural-rules-polyfill/cardinal'\n\n\n/**\r\n * `Intl.PluralRules` polyfill.\r\n * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/PluralRules\r\n */\n\nvar PluralRules = /*#__PURE__*/function () {\n  function PluralRules(locale, options) {\n    _classCallCheck(this, PluralRules);\n\n    var locales = PluralRules.supportedLocalesOf(locale);\n\n    if (locales.length === 0) {\n      throw new RangeError(\"Unsupported locale: \" + locale);\n    }\n\n    if (options && options.type !== \"cardinal\") {\n      throw new RangeError(\"Only \\\"cardinal\\\" \\\"type\\\" is supported\");\n    }\n\n    this.$ = _PluralRuleFunctions_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"][(0,_getPluralRulesLocale_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locales[0])];\n  }\n\n  _createClass(PluralRules, [{\n    key: \"select\",\n    value: function select(number) {\n      return this.$(number);\n    }\n  }], [{\n    key: \"supportedLocalesOf\",\n    value: function supportedLocalesOf(locales) {\n      if (typeof locales === \"string\") {\n        locales = [locales];\n      }\n\n      return locales.filter(function (locale) {\n        return _PluralRuleFunctions_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"][(0,_getPluralRulesLocale_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(locale)];\n      });\n    }\n  }]);\n\n  return PluralRules;\n}();\n\n\n//# sourceMappingURL=PluralRules.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/relative-time-format/modules/PluralRules.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/relative-time-format/modules/RelativeTimeFormat.js":
/*!*************************************************************************!*\
  !*** ./node_modules/relative-time-format/modules/RelativeTimeFormat.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UNITS: () => (/* binding */ UNITS),\n/* harmony export */   \"default\": () => (/* binding */ RelativeTimeFormat)\n/* harmony export */ });\n/* harmony import */ var _LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./LocaleDataStore.js */ \"(ssr)/./node_modules/relative-time-format/modules/LocaleDataStore.js\");\n/* harmony import */ var _resolveLocale_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./resolveLocale.js */ \"(ssr)/./node_modules/relative-time-format/modules/resolveLocale.js\");\n/* harmony import */ var _PluralRules_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./PluralRules.js */ \"(ssr)/./node_modules/relative-time-format/modules/PluralRules.js\");\nfunction _typeof(obj) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }, _typeof(obj); }\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _defineProperty(obj, key, value) { if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\n\nfunction _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }\n\nfunction _nonIterableRest() { throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\n\nfunction _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === \"string\") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === \"Object\" && o.constructor) n = o.constructor.name; if (n === \"Map\" || n === \"Set\") return Array.from(o); if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }\n\nfunction _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) { arr2[i] = arr[i]; } return arr2; }\n\nfunction _iterableToArrayLimit(arr, i) { var _i = arr == null ? null : typeof Symbol !== \"undefined\" && arr[Symbol.iterator] || arr[\"@@iterator\"]; if (_i == null) return; var _arr = []; var _n = true; var _d = false; var _s, _e; try { for (_i = _i.call(arr); !(_n = (_s = _i.next()).done); _n = true) { _arr.push(_s.value); if (i && _arr.length === i) break; } } catch (err) { _d = true; _e = err; } finally { try { if (!_n && _i[\"return\"] != null) _i[\"return\"](); } finally { if (_d) throw _e; } } return _arr; }\n\nfunction _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }\n\nfunction _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); Object.defineProperty(Constructor, \"prototype\", { writable: false }); return Constructor; }\n\n\n\n // Importing `PluralRule` polyfill from a separate package\n// results in a bundle that is larger by 1kB for some reason.\n// import PluralRules from 'intl-plural-rules-polyfill/cardinal'\n// Valid time units.\n\nvar UNITS = [\"second\", \"minute\", \"hour\", \"day\", \"week\", \"month\", \"quarter\", \"year\"]; // Valid values for the `numeric` option.\n\nvar NUMERIC_VALUES = [\"auto\", \"always\"]; // Valid values for the `style` option.\n\nvar STYLE_VALUES = [\"long\", \"short\", \"narrow\"]; // Valid values for the `localeMatcher` option.\n\nvar LOCALE_MATCHER_VALUES = [\"lookup\", \"best fit\"];\n/**\r\n * Polyfill for `Intl.RelativeTimeFormat` proposal.\r\n * https://github.com/tc39/proposal-intl-relative-time\r\n * https://github.com/tc39/proposal-intl-relative-time/issues/55\r\n */\n\nvar RelativeTimeFormat = /*#__PURE__*/function () {\n  /**\r\n   * @param {(string|string[])} [locales] - Preferred locales (or locale).\r\n   * @param {Object} [options] - Formatting options.\r\n   * @param {string} [options.style=\"long\"] - One of: \"long\", \"short\", \"narrow\".\r\n   * @param {string} [options.numeric=\"always\"] - (Version >= 2) One of: \"always\", \"auto\".\r\n   * @param {string} [options.localeMatcher=\"lookup\"] - One of: \"lookup\", \"best fit\". Currently only \"lookup\" is supported.\r\n   */\n  function RelativeTimeFormat() {\n    var locales = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n    _classCallCheck(this, RelativeTimeFormat);\n\n    var numeric = options.numeric,\n        style = options.style,\n        localeMatcher = options.localeMatcher;\n    this.numeric = \"always\";\n    this.style = \"long\";\n    this.localeMatcher = \"lookup\"; // Set `numeric` option.\n\n    if (numeric !== undefined) {\n      if (NUMERIC_VALUES.indexOf(numeric) < 0) {\n        throw new RangeError(\"Invalid \\\"numeric\\\" option: \".concat(numeric));\n      }\n\n      this.numeric = numeric;\n    } // Set `style` option.\n\n\n    if (style !== undefined) {\n      if (STYLE_VALUES.indexOf(style) < 0) {\n        throw new RangeError(\"Invalid \\\"style\\\" option: \".concat(style));\n      }\n\n      this.style = style;\n    } // Set `localeMatcher` option.\n\n\n    if (localeMatcher !== undefined) {\n      if (LOCALE_MATCHER_VALUES.indexOf(localeMatcher) < 0) {\n        throw new RangeError(\"Invalid \\\"localeMatcher\\\" option: \".concat(localeMatcher));\n      }\n\n      this.localeMatcher = localeMatcher;\n    } // Set `locale`.\n    // Convert `locales` to an array.\n\n\n    if (typeof locales === 'string') {\n      locales = [locales];\n    } // Add default locale.\n\n\n    locales.push((0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultLocale)()); // Choose the most appropriate locale.\n\n    this.locale = RelativeTimeFormat.supportedLocalesOf(locales, {\n      localeMatcher: this.localeMatcher\n    })[0];\n\n    if (!this.locale) {\n      throw new Error(\"No supported locale was found\");\n    } // Construct an `Intl.PluralRules` instance (polyfill).\n\n\n    if (_PluralRules_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"].supportedLocalesOf(this.locale).length > 0) {\n      this.pluralRules = new _PluralRules_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"](this.locale);\n    } else {\n      console.warn(\"\\\"\".concat(this.locale, \"\\\" locale is not supported\"));\n    } // Use `Intl.NumberFormat` for formatting numbers (when available).\n\n\n    if (typeof Intl !== 'undefined' && Intl.NumberFormat) {\n      this.numberFormat = new Intl.NumberFormat(this.locale);\n      this.numberingSystem = this.numberFormat.resolvedOptions().numberingSystem;\n    } else {\n      this.numberingSystem = 'latn';\n    }\n\n    this.locale = (0,_resolveLocale_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(this.locale, {\n      localeMatcher: this.localeMatcher\n    });\n  }\n  /**\r\n   * Formats time `number` in `units` (either in past or in future).\r\n   * @param {number} number - Time interval value.\r\n   * @param {string} unit - Time interval measurement unit.\r\n   * @return {string}\r\n   * @throws {RangeError} If unit is not one of \"second\", \"minute\", \"hour\", \"day\", \"week\", \"month\", \"quarter\".\r\n   * @example\r\n   * // Returns \"2 days ago\"\r\n   * rtf.format(-2, \"day\")\r\n   * // Returns \"in 5 minutes\"\r\n   * rtf.format(5, \"minute\")\r\n   */\n\n\n  _createClass(RelativeTimeFormat, [{\n    key: \"format\",\n    value: function format() {\n      var _parseFormatArgs = parseFormatArgs(arguments),\n          _parseFormatArgs2 = _slicedToArray(_parseFormatArgs, 2),\n          number = _parseFormatArgs2[0],\n          unit = _parseFormatArgs2[1];\n\n      return this.getRule(number, unit).replace('{0}', this.formatNumber(Math.abs(number)));\n    }\n    /**\r\n     * Formats time `number` in `units` (either in past or in future).\r\n     * @param {number} number - Time interval value.\r\n     * @param {string} unit - Time interval measurement unit.\r\n     * @return {Object[]} The parts (`{ type, value, unit? }`).\r\n     * @throws {RangeError} If unit is not one of \"second\", \"minute\", \"hour\", \"day\", \"week\", \"month\", \"quarter\".\r\n     * @example\r\n     * // Version 1 (deprecated).\r\n     * // Returns [\r\n     * //   { type: \"literal\", value: \"in \" },\r\n     * //   { type: \"day\", value: \"100\" },\r\n     * //   { type: \"literal\", value: \" days\" }\r\n     * // ]\r\n     * rtf.formatToParts(100, \"day\")\r\n     * //\r\n     * // Version 2.\r\n     * // Returns [\r\n     * //   { type: \"literal\", value: \"in \" },\r\n     * //   { type: \"integer\", value: \"100\", unit: \"day\" },\r\n     * //   { type: \"literal\", value: \" days\" }\r\n     * // ]\r\n     * rtf.formatToParts(100, \"day\")\r\n     */\n\n  }, {\n    key: \"formatToParts\",\n    value: function formatToParts() {\n      var _parseFormatArgs3 = parseFormatArgs(arguments),\n          _parseFormatArgs4 = _slicedToArray(_parseFormatArgs3, 2),\n          number = _parseFormatArgs4[0],\n          unit = _parseFormatArgs4[1];\n\n      var rule = this.getRule(number, unit);\n      var valueIndex = rule.indexOf(\"{0}\"); // \"yesterday\"/\"today\"/\"tomorrow\".\n\n      if (valueIndex < 0) {\n        return [{\n          type: \"literal\",\n          value: rule\n        }];\n      }\n\n      var parts = [];\n\n      if (valueIndex > 0) {\n        parts.push({\n          type: \"literal\",\n          value: rule.slice(0, valueIndex)\n        });\n      }\n\n      parts = parts.concat(this.formatNumberToParts(Math.abs(number)).map(function (part) {\n        return _objectSpread(_objectSpread({}, part), {}, {\n          unit: unit\n        });\n      }));\n\n      if (valueIndex + \"{0}\".length < rule.length - 1) {\n        parts.push({\n          type: \"literal\",\n          value: rule.slice(valueIndex + \"{0}\".length)\n        });\n      }\n\n      return parts;\n    }\n    /**\r\n     * Returns formatting rule for `value` in `units` (either in past or in future).\r\n     * @param {number} value - Time interval value.\r\n     * @param {string} unit - Time interval measurement unit.\r\n     * @return {string}\r\n     * @throws {RangeError} If unit is not one of \"second\", \"minute\", \"hour\", \"day\", \"week\", \"month\", \"quarter\".\r\n     * @example\r\n     * // Returns \"{0} days ago\"\r\n     * getRule(-2, \"day\")\r\n     */\n\n  }, {\n    key: \"getRule\",\n    value: function getRule(value, unit) {\n      // Get locale-specific time interval formatting rules\n      // of a given `style` for the given value of measurement `unit`.\n      //\n      // E.g.:\n      //\n      // ```json\n      // {\n      //  \"past\": {\n      //    \"one\": \"a second ago\",\n      //    \"other\": \"{0} seconds ago\"\n      //  },\n      //  \"future\": {\n      //    \"one\": \"in a second\",\n      //    \"other\": \"in {0} seconds\"\n      //  }\n      // }\n      // ```\n      //\n      var unitMessages = (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__.getLocaleData)(this.locale)[this.style][unit]; // Bundle size optimization technique for styles like\n      // \"tiny\" in `javascript-time-ago`: \"1m\", \"2h\", \"3d\"...\n\n      if (typeof unitMessages === 'string') {\n        return unitMessages;\n      } // Special case for \"yesterday\"/\"today\"/\"tomorrow\".\n\n\n      if (this.numeric === \"auto\") {\n        // \"yesterday\", \"the day before yesterday\", etc.\n        if (value === -2 || value === -1) {\n          var message = unitMessages[\"previous\".concat(value === -1 ? '' : '-' + Math.abs(value))];\n\n          if (message) {\n            return message;\n          }\n        } // \"tomorrow\", \"the day after tomorrow\", etc.\n        else if (value === 1 || value === 2) {\n          var _message = unitMessages[\"next\".concat(value === 1 ? '' : '-' + Math.abs(value))];\n\n          if (_message) {\n            return _message;\n          }\n        } // \"today\"\n        else if (value === 0) {\n          if (unitMessages.current) {\n            return unitMessages.current;\n          }\n        }\n      } // Choose either \"past\" or \"future\" based on time `value` sign.\n      // If there's only \"other\" then it's being collapsed.\n      // (the resulting bundle size optimization technique)\n\n\n      var pluralizedMessages = unitMessages[isNegative(value) ? \"past\" : \"future\"]; // Bundle size optimization technique for styles like \"narrow\"\n      // having messages like \"in {0} d.\" or \"{0} d. ago\".\n\n      if (typeof pluralizedMessages === \"string\") {\n        return pluralizedMessages;\n      } // Quantify `value`.\n      // There seems to be no such locale in CLDR\n      // for which \"plural rules\" function is missing.\n\n\n      var quantifier = this.pluralRules && this.pluralRules.select(Math.abs(value)) || 'other'; // \"other\" rule is supposed to be always present.\n      // If only \"other\" rule is present then \"rules\" is not an object and is a string.\n\n      return pluralizedMessages[quantifier] || pluralizedMessages.other;\n    }\n    /**\r\n     * Formats a number into a string.\r\n     * Uses `Intl.NumberFormat` when available.\r\n     * @param  {number} number\r\n     * @return {string}\r\n     */\n\n  }, {\n    key: \"formatNumber\",\n    value: function formatNumber(number) {\n      return this.numberFormat ? this.numberFormat.format(number) : String(number);\n    }\n    /**\r\n     * Formats a number into a list of parts.\r\n     * Uses `Intl.NumberFormat` when available.\r\n     * @param  {number} number\r\n     * @return {object[]}\r\n     */\n\n  }, {\n    key: \"formatNumberToParts\",\n    value: function formatNumberToParts(number) {\n      // `Intl.NumberFormat.formatToParts()` is not present, for example,\n      // in Node.js 8.x while `Intl.NumberFormat` itself is present.\n      return this.numberFormat && this.numberFormat.formatToParts ? this.numberFormat.formatToParts(number) : [{\n        type: \"integer\",\n        value: this.formatNumber(number)\n      }];\n    }\n    /**\r\n     * Returns a new object with properties reflecting the locale and date and time formatting options computed during initialization of this DateTimeFormat object.\r\n     * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DateTimeFormat/resolvedOptions\r\n     * @return {Object}\r\n     */\n\n  }, {\n    key: \"resolvedOptions\",\n    value: function resolvedOptions() {\n      return {\n        locale: this.locale,\n        style: this.style,\n        numeric: this.numeric,\n        numberingSystem: this.numberingSystem\n      };\n    }\n  }]);\n\n  return RelativeTimeFormat;\n}();\n/**\r\n * Returns an array containing those of the provided locales\r\n * that are supported in collation without having to fall back\r\n * to the runtime's default locale.\r\n * @param {(string|string[])} locale - A string with a BCP 47 language tag, or an array of such strings. For the general form of the locales argument, see the Intl page.\r\n * @param {Object} [options] - An object that may have the following property:\r\n * @param {string} [options.localeMatcher=\"lookup\"] - The locale matching algorithm to use. Possible values are \"lookup\" and \"best fit\". Currently only \"lookup\" is supported.\r\n * @return {string[]} An array of strings representing a subset of the given locale tags that are supported in collation without having to fall back to the runtime's default locale.\r\n * @example\r\n * var locales = ['ban', 'id-u-co-pinyin', 'es-PY']\r\n * var options = { localeMatcher: 'lookup' }\r\n * // Returns [\"id\", \"es-PY\"]\r\n * Intl.RelativeTimeFormat.supportedLocalesOf(locales, options)\r\n */\n\n\n\n\nRelativeTimeFormat.supportedLocalesOf = function (locales) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  // Convert `locales` to an array.\n  if (typeof locales === 'string') {\n    locales = [locales];\n  } else if (!Array.isArray(locales)) {\n    throw new TypeError('Invalid \"locales\" argument');\n  }\n\n  return locales.filter(function (locale) {\n    return (0,_resolveLocale_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(locale, options);\n  });\n};\n/**\r\n * Adds locale data for a specific locale.\r\n * @param {Object} localeData\r\n */\n\n\nRelativeTimeFormat.addLocale = _LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__.addLocaleData;\n/**\r\n * Sets default locale.\r\n * @param  {string} locale\r\n */\n\nRelativeTimeFormat.setDefaultLocale = _LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__.setDefaultLocale;\n/**\r\n * Gets default locale.\r\n * @return  {string} locale\r\n */\n\nRelativeTimeFormat.getDefaultLocale = _LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__.getDefaultLocale;\n/**\r\n * Export `Intl.PluralRules` just in case it's used somewhere else.\r\n */\n\nRelativeTimeFormat.PluralRules = _PluralRules_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]; // The specification allows units to be in plural form.\n// Convert plural to singular.\n// Example: \"seconds\" -> \"second\".\n\nvar UNIT_ERROR = 'Invalid \"unit\" argument';\n\nfunction parseUnit(unit) {\n  if (_typeof(unit) === 'symbol') {\n    throw new TypeError(UNIT_ERROR);\n  }\n\n  if (typeof unit !== 'string') {\n    throw new RangeError(\"\".concat(UNIT_ERROR, \": \").concat(unit));\n  }\n\n  if (unit[unit.length - 1] === 's') {\n    unit = unit.slice(0, unit.length - 1);\n  }\n\n  if (UNITS.indexOf(unit) < 0) {\n    throw new RangeError(\"\".concat(UNIT_ERROR, \": \").concat(unit));\n  }\n\n  return unit;\n} // Converts `value` to a `Number`.\n// The specification allows value to be a non-number.\n// For example, \"-0\" is supposed to be treated as `-0`.\n// Also checks if `value` is a finite number.\n\n\nvar NUMBER_ERROR = 'Invalid \"number\" argument';\n\nfunction parseNumber(value) {\n  value = Number(value);\n\n  if (Number.isFinite) {\n    if (!Number.isFinite(value)) {\n      throw new RangeError(\"\".concat(NUMBER_ERROR, \": \").concat(value));\n    }\n  }\n\n  return value;\n}\n/**\r\n * Tells `0` from `-0`.\r\n * https://stackoverflow.com/questions/7223359/are-0-and-0-the-same\r\n * @param  {number} number\r\n * @return {Boolean}\r\n * @example\r\n * isNegativeZero(0); // false\r\n * isNegativeZero(-0); // true\r\n */\n\n\nfunction isNegativeZero(number) {\n  return 1 / number === -Infinity;\n}\n\nfunction isNegative(number) {\n  return number < 0 || number === 0 && isNegativeZero(number);\n}\n\nfunction parseFormatArgs(args) {\n  if (args.length < 2) {\n    throw new TypeError(\"\\\"unit\\\" argument is required\");\n  }\n\n  return [parseNumber(args[0]), parseUnit(args[1])];\n}\n//# sourceMappingURL=RelativeTimeFormat.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/relative-time-format/modules/RelativeTimeFormat.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/relative-time-format/modules/getPluralRulesLocale.js":
/*!***************************************************************************!*\
  !*** ./node_modules/relative-time-format/modules/getPluralRulesLocale.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ getPluralRulesLocale)\n/* harmony export */ });\n/**\r\n * Returns a `locale` for which a function exists in `./PluralRuleFunctions.js`.\r\n * @param  {string} locale\r\n * @return {string}\r\n * @example\r\n * getPluralRulesLocale(\"ru-RU-Cyrl\") // Returns \"ru\".\r\n */\nfunction getPluralRulesLocale(locale) {\n  // \"pt\" language is the only one having different pluralization rules\n  // for the one (\"pt\") (Portuguese) locale and the other (\"pt-PT\") (European Portuguese).\n  // http://www.unicode.org/cldr/charts/latest/supplemental/language_plural_rules.html\n  // (see the entries for \"pt\" and \"pt_PT\" there)\n  if (locale === 'pt-PT') {\n    return locale;\n  }\n\n  return getLanguageFromLanguageTag(locale);\n}\n/**\r\n * Extracts language from an IETF BCP 47 language tag.\r\n * @param {string} languageTag - IETF BCP 47 language tag.\r\n * @return {string}\r\n * @example\r\n * // Returns \"he\"\r\n * getLanguageFromLanguageTag(\"he-IL-u-ca-hebrew-tz-jeruslm\")\r\n * // Returns \"ar\"\r\n * getLanguageFromLanguageTag(\"ar-u-nu-latn\")\r\n */\n\nvar LANGUAGE_REG_EXP = /^([a-z0-9]+)/i;\n\nfunction getLanguageFromLanguageTag(languageTag) {\n  var match = languageTag.match(LANGUAGE_REG_EXP);\n\n  if (!match) {\n    throw new TypeError(\"Invalid locale: \".concat(languageTag));\n  }\n\n  return match[1];\n}\n//# sourceMappingURL=getPluralRulesLocale.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/relative-time-format/modules/getPluralRulesLocale.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/relative-time-format/modules/resolveLocale.js":
/*!********************************************************************!*\
  !*** ./node_modules/relative-time-format/modules/resolveLocale.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ resolveLocale),\n/* harmony export */   resolveLocaleLookup: () => (/* binding */ resolveLocaleLookup)\n/* harmony export */ });\n/* harmony import */ var _LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./LocaleDataStore.js */ \"(ssr)/./node_modules/relative-time-format/modules/LocaleDataStore.js\");\n\n/**\r\n * Resolves a locale to a supported one (if any).\r\n * @param  {string} locale\r\n * @param {Object} [options] - An object that may have the following property:\r\n * @param {string} [options.localeMatcher=\"lookup\"] - The locale matching algorithm to use. Possible values are \"lookup\" and \"best fit\". Currently only \"lookup\" is supported.\r\n * @return {string} [locale]\r\n * @example\r\n * // Returns \"sr\"\r\n * resolveLocale(\"sr-Cyrl-BA\")\r\n * // Returns `undefined`\r\n * resolveLocale(\"xx-Latn\")\r\n */\n\nfunction resolveLocale(locale) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var localeMatcher = options.localeMatcher || 'lookup';\n\n  switch (localeMatcher) {\n    case 'lookup':\n      return resolveLocaleLookup(locale);\n    // \"best fit\" locale matching is not supported.\n    // https://github.com/catamphetamine/relative-time-format/issues/2\n\n    case 'best fit':\n      // return resolveLocaleBestFit(locale)\n      return resolveLocaleLookup(locale);\n\n    default:\n      throw new RangeError(\"Invalid \\\"localeMatcher\\\" option: \".concat(localeMatcher));\n  }\n}\n/**\r\n * Resolves a locale to a supported one (if any).\r\n * Starts from the most specific locale and gradually\r\n * falls back to less specific ones.\r\n * This is a basic implementation of the \"lookup\" algorithm.\r\n * https://tools.ietf.org/html/rfc4647#section-3.4\r\n * @param  {string} locale\r\n * @return {string} [locale]\r\n * @example\r\n * // Returns \"sr\"\r\n * resolveLocaleLookup(\"sr-Cyrl-BA\")\r\n * // Returns `undefined`\r\n * resolveLocaleLookup(\"xx-Latn\")\r\n */\n\nfunction resolveLocaleLookup(locale) {\n  var resolvedLocale = (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__.resolveLocale)(locale);\n\n  if (resolvedLocale) {\n    return resolvedLocale;\n  } // `sr-Cyrl-BA` -> `sr-Cyrl` -> `sr`.\n\n\n  var parts = locale.split('-');\n\n  while (locale.length > 1) {\n    parts.pop();\n    locale = parts.join('-');\n\n    var _resolvedLocale = (0,_LocaleDataStore_js__WEBPACK_IMPORTED_MODULE_0__.resolveLocale)(locale);\n\n    if (_resolvedLocale) {\n      return _resolvedLocale;\n    }\n  }\n}\n//# sourceMappingURL=resolveLocale.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/relative-time-format/modules/resolveLocale.js\n");

/***/ })

};
;