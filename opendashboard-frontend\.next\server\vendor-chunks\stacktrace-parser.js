"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stacktrace-parser";
exports.ids = ["vendor-chunks/stacktrace-parser"];
exports.modules = {

/***/ "(ssr)/./node_modules/stacktrace-parser/dist/stack-trace-parser.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/stacktrace-parser/dist/stack-trace-parser.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\nvar UNKNOWN_FUNCTION = '<unknown>';\n/**\n * This parses the different stack traces and puts them into one format\n * This borrows heavily from TraceKit (https://github.com/csnover/TraceKit)\n */\n\nfunction parse(stackString) {\n  var lines = stackString.split('\\n');\n  return lines.reduce(function (stack, line) {\n    var parseResult = parseChrome(line) || parseWinjs(line) || parseGecko(line) || parseNode(line) || parseJSC(line);\n\n    if (parseResult) {\n      stack.push(parseResult);\n    }\n\n    return stack;\n  }, []);\n}\nvar chromeRe = /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\\/|[a-z]:\\\\|\\\\\\\\).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\nvar chromeEvalRe = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\n\nfunction parseChrome(line) {\n  var parts = chromeRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  var isNative = parts[2] && parts[2].indexOf('native') === 0; // start of line\n\n  var isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n\n  var submatch = chromeEvalRe.exec(parts[2]);\n\n  if (isEval && submatch != null) {\n    // throw out eval line/column and use top-most line/column number\n    parts[2] = submatch[1]; // url\n\n    parts[3] = submatch[2]; // line\n\n    parts[4] = submatch[3]; // column\n  }\n\n  return {\n    file: !isNative ? parts[2] : null,\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: isNative ? [parts[2]] : [],\n    lineNumber: parts[3] ? +parts[3] : null,\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\nvar winjsRe = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\n\nfunction parseWinjs(line) {\n  var parts = winjsRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[2],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[3],\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\nvar geckoRe = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\nvar geckoEvalRe = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\n\nfunction parseGecko(line) {\n  var parts = geckoRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  var isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n  var submatch = geckoEvalRe.exec(parts[3]);\n\n  if (isEval && submatch != null) {\n    // throw out eval line/column and use top-most line number\n    parts[3] = submatch[1];\n    parts[4] = submatch[2];\n    parts[5] = null; // no column when eval\n  }\n\n  return {\n    file: parts[3],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: parts[2] ? parts[2].split(',') : [],\n    lineNumber: parts[4] ? +parts[4] : null,\n    column: parts[5] ? +parts[5] : null\n  };\n}\n\nvar javaScriptCoreRe = /^\\s*(?:([^@]*)(?:\\((.*?)\\))?@)?(\\S.*?):(\\d+)(?::(\\d+))?\\s*$/i;\n\nfunction parseJSC(line) {\n  var parts = javaScriptCoreRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[3],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[4],\n    column: parts[5] ? +parts[5] : null\n  };\n}\n\nvar nodeRe = /^\\s*at (?:((?:\\[object object\\])?[^\\\\/]+(?: \\[as \\S+\\])?) )?\\(?(.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\n\nfunction parseNode(line) {\n  var parts = nodeRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[2],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[3],\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/stacktrace-parser/dist/stack-trace-parser.esm.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/stacktrace-parser/dist/stack-trace-parser.esm.js":
/*!***********************************************************************!*\
  !*** ./node_modules/stacktrace-parser/dist/stack-trace-parser.esm.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\nvar UNKNOWN_FUNCTION = '<unknown>';\n/**\n * This parses the different stack traces and puts them into one format\n * This borrows heavily from TraceKit (https://github.com/csnover/TraceKit)\n */\n\nfunction parse(stackString) {\n  var lines = stackString.split('\\n');\n  return lines.reduce(function (stack, line) {\n    var parseResult = parseChrome(line) || parseWinjs(line) || parseGecko(line) || parseNode(line) || parseJSC(line);\n\n    if (parseResult) {\n      stack.push(parseResult);\n    }\n\n    return stack;\n  }, []);\n}\nvar chromeRe = /^\\s*at (.*?) ?\\(((?:file|https?|blob|chrome-extension|native|eval|webpack|rsc|<anonymous>|\\/|[a-z]:\\\\|\\\\\\\\).*?)(?::(\\d+))?(?::(\\d+))?\\)?\\s*$/i;\nvar chromeEvalRe = /\\((\\S*)(?::(\\d+))(?::(\\d+))\\)/;\n\nfunction parseChrome(line) {\n  var parts = chromeRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  var isNative = parts[2] && parts[2].indexOf('native') === 0; // start of line\n\n  var isEval = parts[2] && parts[2].indexOf('eval') === 0; // start of line\n\n  var submatch = chromeEvalRe.exec(parts[2]);\n\n  if (isEval && submatch != null) {\n    // throw out eval line/column and use top-most line/column number\n    parts[2] = submatch[1]; // url\n\n    parts[3] = submatch[2]; // line\n\n    parts[4] = submatch[3]; // column\n  }\n\n  return {\n    file: !isNative ? parts[2] : null,\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: isNative ? [parts[2]] : [],\n    lineNumber: parts[3] ? +parts[3] : null,\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\nvar winjsRe = /^\\s*at (?:((?:\\[object object\\])?.+) )?\\(?((?:file|ms-appx|https?|webpack|rsc|blob):.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\n\nfunction parseWinjs(line) {\n  var parts = winjsRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[2],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[3],\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\nvar geckoRe = /^\\s*(.*?)(?:\\((.*?)\\))?(?:^|@)((?:file|https?|blob|chrome|webpack|rsc|resource|\\[native).*?|[^@]*bundle)(?::(\\d+))?(?::(\\d+))?\\s*$/i;\nvar geckoEvalRe = /(\\S+) line (\\d+)(?: > eval line \\d+)* > eval/i;\n\nfunction parseGecko(line) {\n  var parts = geckoRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  var isEval = parts[3] && parts[3].indexOf(' > eval') > -1;\n  var submatch = geckoEvalRe.exec(parts[3]);\n\n  if (isEval && submatch != null) {\n    // throw out eval line/column and use top-most line number\n    parts[3] = submatch[1];\n    parts[4] = submatch[2];\n    parts[5] = null; // no column when eval\n  }\n\n  return {\n    file: parts[3],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: parts[2] ? parts[2].split(',') : [],\n    lineNumber: parts[4] ? +parts[4] : null,\n    column: parts[5] ? +parts[5] : null\n  };\n}\n\nvar javaScriptCoreRe = /^\\s*(?:([^@]*)(?:\\((.*?)\\))?@)?(\\S.*?):(\\d+)(?::(\\d+))?\\s*$/i;\n\nfunction parseJSC(line) {\n  var parts = javaScriptCoreRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[3],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[4],\n    column: parts[5] ? +parts[5] : null\n  };\n}\n\nvar nodeRe = /^\\s*at (?:((?:\\[object object\\])?[^\\\\/]+(?: \\[as \\S+\\])?) )?\\(?(.*?):(\\d+)(?::(\\d+))?\\)?\\s*$/i;\n\nfunction parseNode(line) {\n  var parts = nodeRe.exec(line);\n\n  if (!parts) {\n    return null;\n  }\n\n  return {\n    file: parts[2],\n    methodName: parts[1] || UNKNOWN_FUNCTION,\n    arguments: [],\n    lineNumber: +parts[3],\n    column: parts[4] ? +parts[4] : null\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/stacktrace-parser/dist/stack-trace-parser.esm.js\n");

/***/ })

};
;