"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/tabbable";
exports.ids = ["vendor-chunks/tabbable"];
exports.modules = {

/***/ "(ssr)/./node_modules/tabbable/dist/index.esm.js":
/*!*************************************************!*\
  !*** ./node_modules/tabbable/dist/index.esm.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusable: () => (/* binding */ focusable),\n/* harmony export */   getTabIndex: () => (/* binding */ getTabIndex),\n/* harmony export */   isFocusable: () => (/* binding */ isFocusable),\n/* harmony export */   isTabbable: () => (/* binding */ isTabbable),\n/* harmony export */   tabbable: () => (/* binding */ tabbable)\n/* harmony export */ });\n/*!\n* tabbable 6.2.0\n* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE\n*/\n// NOTE: separate `:not()` selectors has broader browser support than the newer\n//  `:not([inert], [inert] *)` (Feb 2023)\n// CAREFUL: JSDom does not support `:not([inert] *)` as a selector; using it causes\n//  the entire query to fail, resulting in no nodes found, which will break a lot\n//  of things... so we have to rely on JS to identify nodes inside an inert container\nvar candidateSelectors = ['input:not([inert])', 'select:not([inert])', 'textarea:not([inert])', 'a[href]:not([inert])', 'button:not([inert])', '[tabindex]:not(slot):not([inert])', 'audio[controls]:not([inert])', 'video[controls]:not([inert])', '[contenteditable]:not([contenteditable=\"false\"]):not([inert])', 'details>summary:first-of-type:not([inert])', 'details:not([inert])'];\nvar candidateSelector = /* #__PURE__ */candidateSelectors.join(',');\nvar NoElement = typeof Element === 'undefined';\nvar matches = NoElement ? function () {} : Element.prototype.matches || Element.prototype.msMatchesSelector || Element.prototype.webkitMatchesSelector;\nvar getRootNode = !NoElement && Element.prototype.getRootNode ? function (element) {\n  var _element$getRootNode;\n  return element === null || element === void 0 ? void 0 : (_element$getRootNode = element.getRootNode) === null || _element$getRootNode === void 0 ? void 0 : _element$getRootNode.call(element);\n} : function (element) {\n  return element === null || element === void 0 ? void 0 : element.ownerDocument;\n};\n\n/**\n * Determines if a node is inert or in an inert ancestor.\n * @param {Element} [node]\n * @param {boolean} [lookUp] If true and `node` is not inert, looks up at ancestors to\n *  see if any of them are inert. If false, only `node` itself is considered.\n * @returns {boolean} True if inert itself or by way of being in an inert ancestor.\n *  False if `node` is falsy.\n */\nvar isInert = function isInert(node, lookUp) {\n  var _node$getAttribute;\n  if (lookUp === void 0) {\n    lookUp = true;\n  }\n  // CAREFUL: JSDom does not support inert at all, so we can't use the `HTMLElement.inert`\n  //  JS API property; we have to check the attribute, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's an active element\n  var inertAtt = node === null || node === void 0 ? void 0 : (_node$getAttribute = node.getAttribute) === null || _node$getAttribute === void 0 ? void 0 : _node$getAttribute.call(node, 'inert');\n  var inert = inertAtt === '' || inertAtt === 'true';\n\n  // NOTE: this could also be handled with `node.matches('[inert], :is([inert] *)')`\n  //  if it weren't for `matches()` not being a function on shadow roots; the following\n  //  code works for any kind of node\n  // CAREFUL: JSDom does not appear to support certain selectors like `:not([inert] *)`\n  //  so it likely would not support `:is([inert] *)` either...\n  var result = inert || lookUp && node && isInert(node.parentNode); // recursive\n\n  return result;\n};\n\n/**\n * Determines if a node's content is editable.\n * @param {Element} [node]\n * @returns True if it's content-editable; false if it's not or `node` is falsy.\n */\nvar isContentEditable = function isContentEditable(node) {\n  var _node$getAttribute2;\n  // CAREFUL: JSDom does not support the `HTMLElement.isContentEditable` API so we have\n  //  to use the attribute directly to check for this, which can either be empty or 'true';\n  //  if it's `null` (not specified) or 'false', it's a non-editable element\n  var attValue = node === null || node === void 0 ? void 0 : (_node$getAttribute2 = node.getAttribute) === null || _node$getAttribute2 === void 0 ? void 0 : _node$getAttribute2.call(node, 'contenteditable');\n  return attValue === '' || attValue === 'true';\n};\n\n/**\n * @param {Element} el container to check in\n * @param {boolean} includeContainer add container to check\n * @param {(node: Element) => boolean} filter filter candidates\n * @returns {Element[]}\n */\nvar getCandidates = function getCandidates(el, includeContainer, filter) {\n  // even if `includeContainer=false`, we still have to check it for inertness because\n  //  if it's inert, all its children are inert\n  if (isInert(el)) {\n    return [];\n  }\n  var candidates = Array.prototype.slice.apply(el.querySelectorAll(candidateSelector));\n  if (includeContainer && matches.call(el, candidateSelector)) {\n    candidates.unshift(el);\n  }\n  candidates = candidates.filter(filter);\n  return candidates;\n};\n\n/**\n * @callback GetShadowRoot\n * @param {Element} element to check for shadow root\n * @returns {ShadowRoot|boolean} ShadowRoot if available or boolean indicating if a shadowRoot is attached but not available.\n */\n\n/**\n * @callback ShadowRootFilter\n * @param {Element} shadowHostNode the element which contains shadow content\n * @returns {boolean} true if a shadow root could potentially contain valid candidates.\n */\n\n/**\n * @typedef {Object} CandidateScope\n * @property {Element} scopeParent contains inner candidates\n * @property {Element[]} candidates list of candidates found in the scope parent\n */\n\n/**\n * @typedef {Object} IterativeOptions\n * @property {GetShadowRoot|boolean} getShadowRoot true if shadow support is enabled; falsy if not;\n *  if a function, implies shadow support is enabled and either returns the shadow root of an element\n *  or a boolean stating if it has an undisclosed shadow root\n * @property {(node: Element) => boolean} filter filter candidates\n * @property {boolean} flatten if true then result will flatten any CandidateScope into the returned list\n * @property {ShadowRootFilter} shadowRootFilter filter shadow roots;\n */\n\n/**\n * @param {Element[]} elements list of element containers to match candidates from\n * @param {boolean} includeContainer add container list to check\n * @param {IterativeOptions} options\n * @returns {Array.<Element|CandidateScope>}\n */\nvar getCandidatesIteratively = function getCandidatesIteratively(elements, includeContainer, options) {\n  var candidates = [];\n  var elementsToCheck = Array.from(elements);\n  while (elementsToCheck.length) {\n    var element = elementsToCheck.shift();\n    if (isInert(element, false)) {\n      // no need to look up since we're drilling down\n      // anything inside this container will also be inert\n      continue;\n    }\n    if (element.tagName === 'SLOT') {\n      // add shadow dom slot scope (slot itself cannot be focusable)\n      var assigned = element.assignedElements();\n      var content = assigned.length ? assigned : element.children;\n      var nestedCandidates = getCandidatesIteratively(content, true, options);\n      if (options.flatten) {\n        candidates.push.apply(candidates, nestedCandidates);\n      } else {\n        candidates.push({\n          scopeParent: element,\n          candidates: nestedCandidates\n        });\n      }\n    } else {\n      // check candidate element\n      var validCandidate = matches.call(element, candidateSelector);\n      if (validCandidate && options.filter(element) && (includeContainer || !elements.includes(element))) {\n        candidates.push(element);\n      }\n\n      // iterate over shadow content if possible\n      var shadowRoot = element.shadowRoot ||\n      // check for an undisclosed shadow\n      typeof options.getShadowRoot === 'function' && options.getShadowRoot(element);\n\n      // no inert look up because we're already drilling down and checking for inertness\n      //  on the way down, so all containers to this root node should have already been\n      //  vetted as non-inert\n      var validShadowRoot = !isInert(shadowRoot, false) && (!options.shadowRootFilter || options.shadowRootFilter(element));\n      if (shadowRoot && validShadowRoot) {\n        // add shadow dom scope IIF a shadow root node was given; otherwise, an undisclosed\n        //  shadow exists, so look at light dom children as fallback BUT create a scope for any\n        //  child candidates found because they're likely slotted elements (elements that are\n        //  children of the web component element (which has the shadow), in the light dom, but\n        //  slotted somewhere _inside_ the undisclosed shadow) -- the scope is created below,\n        //  _after_ we return from this recursive call\n        var _nestedCandidates = getCandidatesIteratively(shadowRoot === true ? element.children : shadowRoot.children, true, options);\n        if (options.flatten) {\n          candidates.push.apply(candidates, _nestedCandidates);\n        } else {\n          candidates.push({\n            scopeParent: element,\n            candidates: _nestedCandidates\n          });\n        }\n      } else {\n        // there's not shadow so just dig into the element's (light dom) children\n        //  __without__ giving the element special scope treatment\n        elementsToCheck.unshift.apply(elementsToCheck, element.children);\n      }\n    }\n  }\n  return candidates;\n};\n\n/**\n * @private\n * Determines if the node has an explicitly specified `tabindex` attribute.\n * @param {HTMLElement} node\n * @returns {boolean} True if so; false if not.\n */\nvar hasTabIndex = function hasTabIndex(node) {\n  return !isNaN(parseInt(node.getAttribute('tabindex'), 10));\n};\n\n/**\n * Determine the tab index of a given node.\n * @param {HTMLElement} node\n * @returns {number} Tab order (negative, 0, or positive number).\n * @throws {Error} If `node` is falsy.\n */\nvar getTabIndex = function getTabIndex(node) {\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (node.tabIndex < 0) {\n    // in Chrome, <details/>, <audio controls/> and <video controls/> elements get a default\n    // `tabIndex` of -1 when the 'tabindex' attribute isn't specified in the DOM,\n    // yet they are still part of the regular tab order; in FF, they get a default\n    // `tabIndex` of 0; since Chrome still puts those elements in the regular tab\n    // order, consider their tab index to be 0.\n    // Also browsers do not return `tabIndex` correctly for contentEditable nodes;\n    // so if they don't have a tabindex attribute specifically set, assume it's 0.\n    if ((/^(AUDIO|VIDEO|DETAILS)$/.test(node.tagName) || isContentEditable(node)) && !hasTabIndex(node)) {\n      return 0;\n    }\n  }\n  return node.tabIndex;\n};\n\n/**\n * Determine the tab index of a given node __for sort order purposes__.\n * @param {HTMLElement} node\n * @param {boolean} [isScope] True for a custom element with shadow root or slot that, by default,\n *  has tabIndex -1, but needs to be sorted by document order in order for its content to be\n *  inserted into the correct sort position.\n * @returns {number} Tab order (negative, 0, or positive number).\n */\nvar getSortOrderTabIndex = function getSortOrderTabIndex(node, isScope) {\n  var tabIndex = getTabIndex(node);\n  if (tabIndex < 0 && isScope && !hasTabIndex(node)) {\n    return 0;\n  }\n  return tabIndex;\n};\nvar sortOrderedTabbables = function sortOrderedTabbables(a, b) {\n  return a.tabIndex === b.tabIndex ? a.documentOrder - b.documentOrder : a.tabIndex - b.tabIndex;\n};\nvar isInput = function isInput(node) {\n  return node.tagName === 'INPUT';\n};\nvar isHiddenInput = function isHiddenInput(node) {\n  return isInput(node) && node.type === 'hidden';\n};\nvar isDetailsWithSummary = function isDetailsWithSummary(node) {\n  var r = node.tagName === 'DETAILS' && Array.prototype.slice.apply(node.children).some(function (child) {\n    return child.tagName === 'SUMMARY';\n  });\n  return r;\n};\nvar getCheckedRadio = function getCheckedRadio(nodes, form) {\n  for (var i = 0; i < nodes.length; i++) {\n    if (nodes[i].checked && nodes[i].form === form) {\n      return nodes[i];\n    }\n  }\n};\nvar isTabbableRadio = function isTabbableRadio(node) {\n  if (!node.name) {\n    return true;\n  }\n  var radioScope = node.form || getRootNode(node);\n  var queryRadios = function queryRadios(name) {\n    return radioScope.querySelectorAll('input[type=\"radio\"][name=\"' + name + '\"]');\n  };\n  var radioSet;\n  if (typeof window !== 'undefined' && typeof window.CSS !== 'undefined' && typeof window.CSS.escape === 'function') {\n    radioSet = queryRadios(window.CSS.escape(node.name));\n  } else {\n    try {\n      radioSet = queryRadios(node.name);\n    } catch (err) {\n      // eslint-disable-next-line no-console\n      console.error('Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s', err.message);\n      return false;\n    }\n  }\n  var checked = getCheckedRadio(radioSet, node.form);\n  return !checked || checked === node;\n};\nvar isRadio = function isRadio(node) {\n  return isInput(node) && node.type === 'radio';\n};\nvar isNonTabbableRadio = function isNonTabbableRadio(node) {\n  return isRadio(node) && !isTabbableRadio(node);\n};\n\n// determines if a node is ultimately attached to the window's document\nvar isNodeAttached = function isNodeAttached(node) {\n  var _nodeRoot;\n  // The root node is the shadow root if the node is in a shadow DOM; some document otherwise\n  //  (but NOT _the_ document; see second 'If' comment below for more).\n  // If rootNode is shadow root, it'll have a host, which is the element to which the shadow\n  //  is attached, and the one we need to check if it's in the document or not (because the\n  //  shadow, and all nodes it contains, is never considered in the document since shadows\n  //  behave like self-contained DOMs; but if the shadow's HOST, which is part of the document,\n  //  is hidden, or is not in the document itself but is detached, it will affect the shadow's\n  //  visibility, including all the nodes it contains). The host could be any normal node,\n  //  or a custom element (i.e. web component). Either way, that's the one that is considered\n  //  part of the document, not the shadow root, nor any of its children (i.e. the node being\n  //  tested).\n  // To further complicate things, we have to look all the way up until we find a shadow HOST\n  //  that is attached (or find none) because the node might be in nested shadows...\n  // If rootNode is not a shadow root, it won't have a host, and so rootNode should be the\n  //  document (per the docs) and while it's a Document-type object, that document does not\n  //  appear to be the same as the node's `ownerDocument` for some reason, so it's safer\n  //  to ignore the rootNode at this point, and use `node.ownerDocument`. Otherwise,\n  //  using `rootNode.contains(node)` will _always_ be true we'll get false-positives when\n  //  node is actually detached.\n  // NOTE: If `nodeRootHost` or `node` happens to be the `document` itself (which is possible\n  //  if a tabbable/focusable node was quickly added to the DOM, focused, and then removed\n  //  from the DOM as in https://github.com/focus-trap/focus-trap-react/issues/905), then\n  //  `ownerDocument` will be `null`, hence the optional chaining on it.\n  var nodeRoot = node && getRootNode(node);\n  var nodeRootHost = (_nodeRoot = nodeRoot) === null || _nodeRoot === void 0 ? void 0 : _nodeRoot.host;\n\n  // in some cases, a detached node will return itself as the root instead of a document or\n  //  shadow root object, in which case, we shouldn't try to look further up the host chain\n  var attached = false;\n  if (nodeRoot && nodeRoot !== node) {\n    var _nodeRootHost, _nodeRootHost$ownerDo, _node$ownerDocument;\n    attached = !!((_nodeRootHost = nodeRootHost) !== null && _nodeRootHost !== void 0 && (_nodeRootHost$ownerDo = _nodeRootHost.ownerDocument) !== null && _nodeRootHost$ownerDo !== void 0 && _nodeRootHost$ownerDo.contains(nodeRootHost) || node !== null && node !== void 0 && (_node$ownerDocument = node.ownerDocument) !== null && _node$ownerDocument !== void 0 && _node$ownerDocument.contains(node));\n    while (!attached && nodeRootHost) {\n      var _nodeRoot2, _nodeRootHost2, _nodeRootHost2$ownerD;\n      // since it's not attached and we have a root host, the node MUST be in a nested shadow DOM,\n      //  which means we need to get the host's host and check if that parent host is contained\n      //  in (i.e. attached to) the document\n      nodeRoot = getRootNode(nodeRootHost);\n      nodeRootHost = (_nodeRoot2 = nodeRoot) === null || _nodeRoot2 === void 0 ? void 0 : _nodeRoot2.host;\n      attached = !!((_nodeRootHost2 = nodeRootHost) !== null && _nodeRootHost2 !== void 0 && (_nodeRootHost2$ownerD = _nodeRootHost2.ownerDocument) !== null && _nodeRootHost2$ownerD !== void 0 && _nodeRootHost2$ownerD.contains(nodeRootHost));\n    }\n  }\n  return attached;\n};\nvar isZeroArea = function isZeroArea(node) {\n  var _node$getBoundingClie = node.getBoundingClientRect(),\n    width = _node$getBoundingClie.width,\n    height = _node$getBoundingClie.height;\n  return width === 0 && height === 0;\n};\nvar isHidden = function isHidden(node, _ref) {\n  var displayCheck = _ref.displayCheck,\n    getShadowRoot = _ref.getShadowRoot;\n  // NOTE: visibility will be `undefined` if node is detached from the document\n  //  (see notes about this further down), which means we will consider it visible\n  //  (this is legacy behavior from a very long way back)\n  // NOTE: we check this regardless of `displayCheck=\"none\"` because this is a\n  //  _visibility_ check, not a _display_ check\n  if (getComputedStyle(node).visibility === 'hidden') {\n    return true;\n  }\n  var isDirectSummary = matches.call(node, 'details>summary:first-of-type');\n  var nodeUnderDetails = isDirectSummary ? node.parentElement : node;\n  if (matches.call(nodeUnderDetails, 'details:not([open]) *')) {\n    return true;\n  }\n  if (!displayCheck || displayCheck === 'full' || displayCheck === 'legacy-full') {\n    if (typeof getShadowRoot === 'function') {\n      // figure out if we should consider the node to be in an undisclosed shadow and use the\n      //  'non-zero-area' fallback\n      var originalNode = node;\n      while (node) {\n        var parentElement = node.parentElement;\n        var rootNode = getRootNode(node);\n        if (parentElement && !parentElement.shadowRoot && getShadowRoot(parentElement) === true // check if there's an undisclosed shadow\n        ) {\n          // node has an undisclosed shadow which means we can only treat it as a black box, so we\n          //  fall back to a non-zero-area test\n          return isZeroArea(node);\n        } else if (node.assignedSlot) {\n          // iterate up slot\n          node = node.assignedSlot;\n        } else if (!parentElement && rootNode !== node.ownerDocument) {\n          // cross shadow boundary\n          node = rootNode.host;\n        } else {\n          // iterate up normal dom\n          node = parentElement;\n        }\n      }\n      node = originalNode;\n    }\n    // else, `getShadowRoot` might be true, but all that does is enable shadow DOM support\n    //  (i.e. it does not also presume that all nodes might have undisclosed shadows); or\n    //  it might be a falsy value, which means shadow DOM support is disabled\n\n    // Since we didn't find it sitting in an undisclosed shadow (or shadows are disabled)\n    //  now we can just test to see if it would normally be visible or not, provided it's\n    //  attached to the main document.\n    // NOTE: We must consider case where node is inside a shadow DOM and given directly to\n    //  `isTabbable()` or `isFocusable()` -- regardless of `getShadowRoot` option setting.\n\n    if (isNodeAttached(node)) {\n      // this works wherever the node is: if there's at least one client rect, it's\n      //  somehow displayed; it also covers the CSS 'display: contents' case where the\n      //  node itself is hidden in place of its contents; and there's no need to search\n      //  up the hierarchy either\n      return !node.getClientRects().length;\n    }\n\n    // Else, the node isn't attached to the document, which means the `getClientRects()`\n    //  API will __always__ return zero rects (this can happen, for example, if React\n    //  is used to render nodes onto a detached tree, as confirmed in this thread:\n    //  https://github.com/facebook/react/issues/9117#issuecomment-284228870)\n    //\n    // It also means that even window.getComputedStyle(node).display will return `undefined`\n    //  because styles are only computed for nodes that are in the document.\n    //\n    // NOTE: THIS HAS BEEN THE CASE FOR YEARS. It is not new, nor is it caused by tabbable\n    //  somehow. Though it was never stated officially, anyone who has ever used tabbable\n    //  APIs on nodes in detached containers has actually implicitly used tabbable in what\n    //  was later (as of v5.2.0 on Apr 9, 2021) called `displayCheck=\"none\"` mode -- essentially\n    //  considering __everything__ to be visible because of the innability to determine styles.\n    //\n    // v6.0.0: As of this major release, the default 'full' option __no longer treats detached\n    //  nodes as visible with the 'none' fallback.__\n    if (displayCheck !== 'legacy-full') {\n      return true; // hidden\n    }\n    // else, fallback to 'none' mode and consider the node visible\n  } else if (displayCheck === 'non-zero-area') {\n    // NOTE: Even though this tests that the node's client rect is non-zero to determine\n    //  whether it's displayed, and that a detached node will __always__ have a zero-area\n    //  client rect, we don't special-case for whether the node is attached or not. In\n    //  this mode, we do want to consider nodes that have a zero area to be hidden at all\n    //  times, and that includes attached or not.\n    return isZeroArea(node);\n  }\n\n  // visible, as far as we can tell, or per current `displayCheck=none` mode, we assume\n  //  it's visible\n  return false;\n};\n\n// form fields (nested) inside a disabled fieldset are not focusable/tabbable\n//  unless they are in the _first_ <legend> element of the top-most disabled\n//  fieldset\nvar isDisabledFromFieldset = function isDisabledFromFieldset(node) {\n  if (/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(node.tagName)) {\n    var parentNode = node.parentElement;\n    // check if `node` is contained in a disabled <fieldset>\n    while (parentNode) {\n      if (parentNode.tagName === 'FIELDSET' && parentNode.disabled) {\n        // look for the first <legend> among the children of the disabled <fieldset>\n        for (var i = 0; i < parentNode.children.length; i++) {\n          var child = parentNode.children.item(i);\n          // when the first <legend> (in document order) is found\n          if (child.tagName === 'LEGEND') {\n            // if its parent <fieldset> is not nested in another disabled <fieldset>,\n            // return whether `node` is a descendant of its first <legend>\n            return matches.call(parentNode, 'fieldset[disabled] *') ? true : !child.contains(node);\n          }\n        }\n        // the disabled <fieldset> containing `node` has no <legend>\n        return true;\n      }\n      parentNode = parentNode.parentElement;\n    }\n  }\n\n  // else, node's tabbable/focusable state should not be affected by a fieldset's\n  //  enabled/disabled state\n  return false;\n};\nvar isNodeMatchingSelectorFocusable = function isNodeMatchingSelectorFocusable(options, node) {\n  if (node.disabled ||\n  // we must do an inert look up to filter out any elements inside an inert ancestor\n  //  because we're limited in the type of selectors we can use in JSDom (see related\n  //  note related to `candidateSelectors`)\n  isInert(node) || isHiddenInput(node) || isHidden(node, options) ||\n  // For a details element with a summary, the summary element gets the focus\n  isDetailsWithSummary(node) || isDisabledFromFieldset(node)) {\n    return false;\n  }\n  return true;\n};\nvar isNodeMatchingSelectorTabbable = function isNodeMatchingSelectorTabbable(options, node) {\n  if (isNonTabbableRadio(node) || getTabIndex(node) < 0 || !isNodeMatchingSelectorFocusable(options, node)) {\n    return false;\n  }\n  return true;\n};\nvar isValidShadowRootTabbable = function isValidShadowRootTabbable(shadowHostNode) {\n  var tabIndex = parseInt(shadowHostNode.getAttribute('tabindex'), 10);\n  if (isNaN(tabIndex) || tabIndex >= 0) {\n    return true;\n  }\n  // If a custom element has an explicit negative tabindex,\n  // browsers will not allow tab targeting said element's children.\n  return false;\n};\n\n/**\n * @param {Array.<Element|CandidateScope>} candidates\n * @returns Element[]\n */\nvar sortByOrder = function sortByOrder(candidates) {\n  var regularTabbables = [];\n  var orderedTabbables = [];\n  candidates.forEach(function (item, i) {\n    var isScope = !!item.scopeParent;\n    var element = isScope ? item.scopeParent : item;\n    var candidateTabindex = getSortOrderTabIndex(element, isScope);\n    var elements = isScope ? sortByOrder(item.candidates) : element;\n    if (candidateTabindex === 0) {\n      isScope ? regularTabbables.push.apply(regularTabbables, elements) : regularTabbables.push(element);\n    } else {\n      orderedTabbables.push({\n        documentOrder: i,\n        tabIndex: candidateTabindex,\n        item: item,\n        isScope: isScope,\n        content: elements\n      });\n    }\n  });\n  return orderedTabbables.sort(sortOrderedTabbables).reduce(function (acc, sortable) {\n    sortable.isScope ? acc.push.apply(acc, sortable.content) : acc.push(sortable.content);\n    return acc;\n  }, []).concat(regularTabbables);\n};\nvar tabbable = function tabbable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorTabbable.bind(null, options),\n      flatten: false,\n      getShadowRoot: options.getShadowRoot,\n      shadowRootFilter: isValidShadowRootTabbable\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorTabbable.bind(null, options));\n  }\n  return sortByOrder(candidates);\n};\nvar focusable = function focusable(container, options) {\n  options = options || {};\n  var candidates;\n  if (options.getShadowRoot) {\n    candidates = getCandidatesIteratively([container], options.includeContainer, {\n      filter: isNodeMatchingSelectorFocusable.bind(null, options),\n      flatten: true,\n      getShadowRoot: options.getShadowRoot\n    });\n  } else {\n    candidates = getCandidates(container, options.includeContainer, isNodeMatchingSelectorFocusable.bind(null, options));\n  }\n  return candidates;\n};\nvar isTabbable = function isTabbable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, candidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorTabbable(options, node);\n};\nvar focusableCandidateSelector = /* #__PURE__ */candidateSelectors.concat('iframe').join(',');\nvar isFocusable = function isFocusable(node, options) {\n  options = options || {};\n  if (!node) {\n    throw new Error('No node provided');\n  }\n  if (matches.call(node, focusableCandidateSelector) === false) {\n    return false;\n  }\n  return isNodeMatchingSelectorFocusable(options, node);\n};\n\n\n//# sourceMappingURL=index.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/tabbable/dist/index.esm.js\n");

/***/ })

};
;