"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-indexeddb";
exports.ids = ["vendor-chunks/y-indexeddb"];
exports.modules = {

/***/ "(ssr)/./node_modules/y-indexeddb/src/y-indexeddb.js":
/*!*****************************************************!*\
  !*** ./node_modules/y-indexeddb/src/y-indexeddb.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IndexeddbPersistence: () => (/* binding */ IndexeddbPersistence),\n/* harmony export */   PREFERRED_TRIM_SIZE: () => (/* binding */ PREFERRED_TRIM_SIZE),\n/* harmony export */   clearDocument: () => (/* binding */ clearDocument),\n/* harmony export */   fetchUpdates: () => (/* binding */ fetchUpdates),\n/* harmony export */   storeState: () => (/* binding */ storeState)\n/* harmony export */ });\n/* harmony import */ var yjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! yjs */ \"(ssr)/./node_modules/yjs/dist/yjs.mjs\");\n/* harmony import */ var lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/indexeddb */ \"(ssr)/./node_modules/lib0/indexeddb.js\");\n/* harmony import */ var lib0_promise__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lib0/promise */ \"(ssr)/./node_modules/lib0/promise.js\");\n/* harmony import */ var lib0_observable__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/observable */ \"(ssr)/./node_modules/lib0/observable.js\");\n\n\n\n\n\nconst customStoreName = 'custom'\nconst updatesStoreName = 'updates'\n\nconst PREFERRED_TRIM_SIZE = 500\n\n/**\n * @param {IndexeddbPersistence} idbPersistence\n * @param {function(IDBObjectStore):void} [beforeApplyUpdatesCallback]\n * @param {function(IDBObjectStore):void} [afterApplyUpdatesCallback]\n */\nconst fetchUpdates = (idbPersistence, beforeApplyUpdatesCallback = () => {}, afterApplyUpdatesCallback = () => {}) => {\n  const [updatesStore] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(/** @type {IDBDatabase} */ (idbPersistence.db), [updatesStoreName]) // , 'readonly')\n  return lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.getAll(updatesStore, lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.createIDBKeyRangeLowerBound(idbPersistence._dbref, false)).then(updates => {\n    if (!idbPersistence._destroyed) {\n      beforeApplyUpdatesCallback(updatesStore)\n      yjs__WEBPACK_IMPORTED_MODULE_1__.transact(idbPersistence.doc, () => {\n        updates.forEach(val => yjs__WEBPACK_IMPORTED_MODULE_1__.applyUpdate(idbPersistence.doc, val))\n      }, idbPersistence, false)\n      afterApplyUpdatesCallback(updatesStore)\n    }\n  })\n    .then(() => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.getLastKey(updatesStore).then(lastKey => { idbPersistence._dbref = lastKey + 1 }))\n    .then(() => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.count(updatesStore).then(cnt => { idbPersistence._dbsize = cnt }))\n    .then(() => updatesStore)\n}\n\n/**\n * @param {IndexeddbPersistence} idbPersistence\n * @param {boolean} forceStore\n */\nconst storeState = (idbPersistence, forceStore = true) =>\n  fetchUpdates(idbPersistence)\n    .then(updatesStore => {\n      if (forceStore || idbPersistence._dbsize >= PREFERRED_TRIM_SIZE) {\n        lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.addAutoKey(updatesStore, yjs__WEBPACK_IMPORTED_MODULE_1__.encodeStateAsUpdate(idbPersistence.doc))\n          .then(() => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.del(updatesStore, lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.createIDBKeyRangeUpperBound(idbPersistence._dbref, true)))\n          .then(() => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.count(updatesStore).then(cnt => { idbPersistence._dbsize = cnt }))\n      }\n    })\n\n/**\n * @param {string} name\n */\nconst clearDocument = name => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.deleteDB(name)\n\n/**\n * @extends Observable<string>\n */\nclass IndexeddbPersistence extends lib0_observable__WEBPACK_IMPORTED_MODULE_2__.Observable {\n  /**\n   * @param {string} name\n   * @param {Y.Doc} doc\n   */\n  constructor (name, doc) {\n    super()\n    this.doc = doc\n    this.name = name\n    this._dbref = 0\n    this._dbsize = 0\n    this._destroyed = false\n    /**\n     * @type {IDBDatabase|null}\n     */\n    this.db = null\n    this.synced = false\n    this._db = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.openDB(name, db =>\n      lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.createStores(db, [\n        ['updates', { autoIncrement: true }],\n        ['custom']\n      ])\n    )\n    /**\n     * @type {Promise<IndexeddbPersistence>}\n     */\n    this.whenSynced = lib0_promise__WEBPACK_IMPORTED_MODULE_3__.create(resolve => this.on('synced', () => resolve(this)))\n\n    this._db.then(db => {\n      this.db = db\n      /**\n       * @param {IDBObjectStore} updatesStore\n       */\n      const beforeApplyUpdatesCallback = (updatesStore) => lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.addAutoKey(updatesStore, yjs__WEBPACK_IMPORTED_MODULE_1__.encodeStateAsUpdate(doc))\n      const afterApplyUpdatesCallback = () => {\n        if (this._destroyed) return this\n        this.synced = true\n        this.emit('synced', [this])\n      }\n      fetchUpdates(this, beforeApplyUpdatesCallback, afterApplyUpdatesCallback)\n    })\n    /**\n     * Timeout in ms untill data is merged and persisted in idb.\n     */\n    this._storeTimeout = 1000\n    /**\n     * @type {any}\n     */\n    this._storeTimeoutId = null\n    /**\n     * @param {Uint8Array} update\n     * @param {any} origin\n     */\n    this._storeUpdate = (update, origin) => {\n      if (this.db && origin !== this) {\n        const [updatesStore] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(/** @type {IDBDatabase} */ (this.db), [updatesStoreName])\n        lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.addAutoKey(updatesStore, update)\n        if (++this._dbsize >= PREFERRED_TRIM_SIZE) {\n          // debounce store call\n          if (this._storeTimeoutId !== null) {\n            clearTimeout(this._storeTimeoutId)\n          }\n          this._storeTimeoutId = setTimeout(() => {\n            storeState(this, false)\n            this._storeTimeoutId = null\n          }, this._storeTimeout)\n        }\n      }\n    }\n    doc.on('update', this._storeUpdate)\n    this.destroy = this.destroy.bind(this)\n    doc.on('destroy', this.destroy)\n  }\n\n  destroy () {\n    if (this._storeTimeoutId) {\n      clearTimeout(this._storeTimeoutId)\n    }\n    this.doc.off('update', this._storeUpdate)\n    this.doc.off('destroy', this.destroy)\n    this._destroyed = true\n    return this._db.then(db => {\n      db.close()\n    })\n  }\n\n  /**\n   * Destroys this instance and removes all data from indexeddb.\n   *\n   * @return {Promise<void>}\n   */\n  clearData () {\n    return this.destroy().then(() => {\n      lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.deleteDB(this.name)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @return {Promise<String | number | ArrayBuffer | Date | any>}\n   */\n  get (key) {\n    return this._db.then(db => {\n      const [custom] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(db, [customStoreName], 'readonly')\n      return lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.get(custom, key)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @param {String | number | ArrayBuffer | Date} value\n   * @return {Promise<String | number | ArrayBuffer | Date>}\n   */\n  set (key, value) {\n    return this._db.then(db => {\n      const [custom] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(db, [customStoreName])\n      return lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.put(custom, value, key)\n    })\n  }\n\n  /**\n   * @param {String | number | ArrayBuffer | Date} key\n   * @return {Promise<undefined>}\n   */\n  del (key) {\n    return this._db.then(db => {\n      const [custom] = lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.transact(db, [customStoreName])\n      return lib0_indexeddb__WEBPACK_IMPORTED_MODULE_0__.del(custom, key)\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-indexeddb/src/y-indexeddb.js\n");

/***/ })

};
;