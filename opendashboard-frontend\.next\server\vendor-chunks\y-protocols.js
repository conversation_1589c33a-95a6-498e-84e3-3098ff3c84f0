"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/y-protocols";
exports.ids = ["vendor-chunks/y-protocols"];
exports.modules = {

/***/ "(ssr)/./node_modules/y-protocols/awareness.js":
/*!***********************************************!*\
  !*** ./node_modules/y-protocols/awareness.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Awareness: () => (/* binding */ Awareness),\n/* harmony export */   applyAwarenessUpdate: () => (/* binding */ applyAwarenessUpdate),\n/* harmony export */   encodeAwarenessUpdate: () => (/* binding */ encodeAwarenessUpdate),\n/* harmony export */   modifyAwarenessUpdate: () => (/* binding */ modifyAwarenessUpdate),\n/* harmony export */   outdatedTimeout: () => (/* binding */ outdatedTimeout),\n/* harmony export */   removeAwarenessStates: () => (/* binding */ removeAwarenessStates)\n/* harmony export */ });\n/* harmony import */ var lib0_encoding__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lib0/encoding */ \"(ssr)/./node_modules/lib0/encoding.js\");\n/* harmony import */ var lib0_decoding__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lib0/decoding */ \"(ssr)/./node_modules/lib0/decoding.js\");\n/* harmony import */ var lib0_time__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lib0/time */ \"(ssr)/./node_modules/lib0/time.js\");\n/* harmony import */ var lib0_math__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lib0/math */ \"(ssr)/./node_modules/lib0/math.js\");\n/* harmony import */ var lib0_observable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lib0/observable */ \"(ssr)/./node_modules/lib0/observable.js\");\n/* harmony import */ var lib0_function__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lib0/function */ \"(ssr)/./node_modules/lib0/function.js\");\n/**\n * @module awareness-protocol\n */\n\n\n\n\n\n\n\n // eslint-disable-line\n\nconst outdatedTimeout = 30000\n\n/**\n * @typedef {Object} MetaClientState\n * @property {number} MetaClientState.clock\n * @property {number} MetaClientState.lastUpdated unix timestamp\n */\n\n/**\n * The Awareness class implements a simple shared state protocol that can be used for non-persistent data like awareness information\n * (cursor, username, status, ..). Each client can update its own local state and listen to state changes of\n * remote clients. Every client may set a state of a remote peer to `null` to mark the client as offline.\n *\n * Each client is identified by a unique client id (something we borrow from `doc.clientID`). A client can override\n * its own state by propagating a message with an increasing timestamp (`clock`). If such a message is received, it is\n * applied if the known state of that client is older than the new state (`clock < newClock`). If a client thinks that\n * a remote client is offline, it may propagate a message with\n * `{ clock: currentClientClock, state: null, client: remoteClient }`. If such a\n * message is received, and the known clock of that client equals the received clock, it will override the state with `null`.\n *\n * Before a client disconnects, it should propagate a `null` state with an updated clock.\n *\n * Awareness states must be updated every 30 seconds. Otherwise the Awareness instance will delete the client state.\n *\n * @extends {Observable<string>}\n */\nclass Awareness extends lib0_observable__WEBPACK_IMPORTED_MODULE_0__.Observable {\n  /**\n   * @param {Y.Doc} doc\n   */\n  constructor (doc) {\n    super()\n    this.doc = doc\n    /**\n     * @type {number}\n     */\n    this.clientID = doc.clientID\n    /**\n     * Maps from client id to client state\n     * @type {Map<number, Object<string, any>>}\n     */\n    this.states = new Map()\n    /**\n     * @type {Map<number, MetaClientState>}\n     */\n    this.meta = new Map()\n    this._checkInterval = /** @type {any} */ (setInterval(() => {\n      const now = lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n      if (this.getLocalState() !== null && (outdatedTimeout / 2 <= now - /** @type {{lastUpdated:number}} */ (this.meta.get(this.clientID)).lastUpdated)) {\n        // renew local clock\n        this.setLocalState(this.getLocalState())\n      }\n      /**\n       * @type {Array<number>}\n       */\n      const remove = []\n      this.meta.forEach((meta, clientid) => {\n        if (clientid !== this.clientID && outdatedTimeout <= now - meta.lastUpdated && this.states.has(clientid)) {\n          remove.push(clientid)\n        }\n      })\n      if (remove.length > 0) {\n        removeAwarenessStates(this, remove, 'timeout')\n      }\n    }, lib0_math__WEBPACK_IMPORTED_MODULE_2__.floor(outdatedTimeout / 10)))\n    doc.on('destroy', () => {\n      this.destroy()\n    })\n    this.setLocalState({})\n  }\n\n  destroy () {\n    this.emit('destroy', [this])\n    this.setLocalState(null)\n    super.destroy()\n    clearInterval(this._checkInterval)\n  }\n\n  /**\n   * @return {Object<string,any>|null}\n   */\n  getLocalState () {\n    return this.states.get(this.clientID) || null\n  }\n\n  /**\n   * @param {Object<string,any>|null} state\n   */\n  setLocalState (state) {\n    const clientID = this.clientID\n    const currLocalMeta = this.meta.get(clientID)\n    const clock = currLocalMeta === undefined ? 0 : currLocalMeta.clock + 1\n    const prevState = this.states.get(clientID)\n    if (state === null) {\n      this.states.delete(clientID)\n    } else {\n      this.states.set(clientID, state)\n    }\n    this.meta.set(clientID, {\n      clock,\n      lastUpdated: lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n    })\n    const added = []\n    const updated = []\n    const filteredUpdated = []\n    const removed = []\n    if (state === null) {\n      removed.push(clientID)\n    } else if (prevState == null) {\n      if (state != null) {\n        added.push(clientID)\n      }\n    } else {\n      updated.push(clientID)\n      if (!lib0_function__WEBPACK_IMPORTED_MODULE_3__.equalityDeep(prevState, state)) {\n        filteredUpdated.push(clientID)\n      }\n    }\n    if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n      this.emit('change', [{ added, updated: filteredUpdated, removed }, 'local'])\n    }\n    this.emit('update', [{ added, updated, removed }, 'local'])\n  }\n\n  /**\n   * @param {string} field\n   * @param {any} value\n   */\n  setLocalStateField (field, value) {\n    const state = this.getLocalState()\n    if (state !== null) {\n      this.setLocalState({\n        ...state,\n        [field]: value\n      })\n    }\n  }\n\n  /**\n   * @return {Map<number,Object<string,any>>}\n   */\n  getStates () {\n    return this.states\n  }\n}\n\n/**\n * Mark (remote) clients as inactive and remove them from the list of active peers.\n * This change will be propagated to remote clients.\n *\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @param {any} origin\n */\nconst removeAwarenessStates = (awareness, clients, origin) => {\n  const removed = []\n  for (let i = 0; i < clients.length; i++) {\n    const clientID = clients[i]\n    if (awareness.states.has(clientID)) {\n      awareness.states.delete(clientID)\n      if (clientID === awareness.clientID) {\n        const curMeta = /** @type {MetaClientState} */ (awareness.meta.get(clientID))\n        awareness.meta.set(clientID, {\n          clock: curMeta.clock + 1,\n          lastUpdated: lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n        })\n      }\n      removed.push(clientID)\n    }\n  }\n  if (removed.length > 0) {\n    awareness.emit('change', [{ added: [], updated: [], removed }, origin])\n    awareness.emit('update', [{ added: [], updated: [], removed }, origin])\n  }\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Array<number>} clients\n * @return {Uint8Array}\n */\nconst encodeAwarenessUpdate = (awareness, clients, states = awareness.states) => {\n  const len = clients.length\n  const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.createEncoder()\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = clients[i]\n    const state = states.get(clientID) || null\n    const clock = /** @type {MetaClientState} */ (awareness.meta.get(clientID)).clock\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clientID)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clock)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarString(encoder, JSON.stringify(state))\n  }\n  return lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.toUint8Array(encoder)\n}\n\n/**\n * Modify the content of an awareness update before re-encoding it to an awareness update.\n *\n * This might be useful when you have a central server that wants to ensure that clients\n * cant hijack somebody elses identity.\n *\n * @param {Uint8Array} update\n * @param {function(any):any} modify\n * @return {Uint8Array}\n */\nconst modifyAwarenessUpdate = (update, modify) => {\n  const decoder = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.createDecoder(update)\n  const encoder = lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.createEncoder()\n  const len = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n  lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, len)\n  for (let i = 0; i < len; i++) {\n    const clientID = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const clock = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const state = JSON.parse(lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarString(decoder))\n    const modifiedState = modify(state)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clientID)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarUint(encoder, clock)\n    lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.writeVarString(encoder, JSON.stringify(modifiedState))\n  }\n  return lib0_encoding__WEBPACK_IMPORTED_MODULE_4__.toUint8Array(encoder)\n}\n\n/**\n * @param {Awareness} awareness\n * @param {Uint8Array} update\n * @param {any} origin This will be added to the emitted change event\n */\nconst applyAwarenessUpdate = (awareness, update, origin) => {\n  const decoder = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.createDecoder(update)\n  const timestamp = lib0_time__WEBPACK_IMPORTED_MODULE_1__.getUnixTime()\n  const added = []\n  const updated = []\n  const filteredUpdated = []\n  const removed = []\n  const len = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n  for (let i = 0; i < len; i++) {\n    const clientID = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    let clock = lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarUint(decoder)\n    const state = JSON.parse(lib0_decoding__WEBPACK_IMPORTED_MODULE_5__.readVarString(decoder))\n    const clientMeta = awareness.meta.get(clientID)\n    const prevState = awareness.states.get(clientID)\n    const currClock = clientMeta === undefined ? 0 : clientMeta.clock\n    if (currClock < clock || (currClock === clock && state === null && awareness.states.has(clientID))) {\n      if (state === null) {\n        // never let a remote client remove this local state\n        if (clientID === awareness.clientID && awareness.getLocalState() != null) {\n          // remote client removed the local state. Do not remote state. Broadcast a message indicating\n          // that this client still exists by increasing the clock\n          clock++\n        } else {\n          awareness.states.delete(clientID)\n        }\n      } else {\n        awareness.states.set(clientID, state)\n      }\n      awareness.meta.set(clientID, {\n        clock,\n        lastUpdated: timestamp\n      })\n      if (clientMeta === undefined && state !== null) {\n        added.push(clientID)\n      } else if (clientMeta !== undefined && state === null) {\n        removed.push(clientID)\n      } else if (state !== null) {\n        if (!lib0_function__WEBPACK_IMPORTED_MODULE_3__.equalityDeep(state, prevState)) {\n          filteredUpdated.push(clientID)\n        }\n        updated.push(clientID)\n      }\n    }\n  }\n  if (added.length > 0 || filteredUpdated.length > 0 || removed.length > 0) {\n    awareness.emit('change', [{\n      added, updated: filteredUpdated, removed\n    }, origin])\n  }\n  if (added.length > 0 || updated.length > 0 || removed.length > 0) {\n    awareness.emit('update', [{\n      added, updated, removed\n    }, origin])\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/y-protocols/awareness.js\n");

/***/ })

};
;