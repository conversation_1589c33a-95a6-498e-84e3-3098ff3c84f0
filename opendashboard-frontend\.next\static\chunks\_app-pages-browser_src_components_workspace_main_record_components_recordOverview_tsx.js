"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_workspace_main_record_components_recordOverview_tsx"],{

/***/ "(app-pages-browser)/./src/components/workspace/main/record/components/imageCropperDialog.tsx":
/*!********************************************************************************!*\
  !*** ./src/components/workspace/main/record/components/imageCropperDialog.tsx ***!
  \********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ImageCropperDialog: function() { return /* binding */ ImageCropperDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _utils_resizeImage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/resizeImage */ \"(app-pages-browser)/./src/utils/resizeImage.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\nconst ImageCropperDialog = (param)=>{\n    let { file, open, onClose, onCrop, type } = param;\n    _s();\n    const [imageUrl, setImageUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [position, setPosition] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const [scale, setScale] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [dragStart, setDragStart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        x: 0,\n        y: 0\n    });\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const imageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // UI dimensions for the cropper dialog (what the user sees in the UI)\n    const uiDimensions = type === \"cover\" ? {\n        width: 500,\n        height: 125\n    } : {\n        width: 200,\n        height: 200\n    };\n    // Final dimensions for the image after processing\n    // Cover: 1200px width with proportional height\n    // Profile: 500x500 square\n    const dimensions = type === \"cover\" ? {\n        width: 1200,\n        height: 300\n    } : {\n        width: 500,\n        height: 500\n    };\n    // Resize options with quality set to 80%\n    const resizeOptions = {\n        quality: 80\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (file && open) {\n            const url = URL.createObjectURL(file);\n            setImageUrl(url);\n            setPosition({\n                x: 0,\n                y: 0\n            });\n            setScale(1);\n            const img = new Image();\n            img.src = url;\n            img.onload = ()=>{\n                imageRef.current = img;\n                drawImage();\n            };\n            return ()=>{\n                URL.revokeObjectURL(url);\n            };\n        }\n    // drawImage is defined in the component body, so it's safe to omit from dependencies\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        file,\n        open\n    ]);\n    const drawImage = ()=>{\n        if (!canvasRef.current || !imageRef.current) return;\n        const canvas = canvasRef.current;\n        const ctx = canvas.getContext(\"2d\");\n        if (!ctx) return;\n        ctx.clearRect(0, 0, canvas.width, canvas.height);\n        const img = imageRef.current;\n        const scaledWidth = img.width * scale;\n        const scaledHeight = img.height * scale;\n        ctx.drawImage(img, position.x, position.y, scaledWidth, scaledHeight);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        drawImage();\n    // drawImage is defined in the component body, so it's safe to omit from dependencies\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        position,\n        scale\n    ]);\n    const handleMouseDown = (e)=>{\n        setIsDragging(true);\n        setDragStart({\n            x: e.clientX - position.x,\n            y: e.clientY - position.y\n        });\n    };\n    const handleMouseMove = (e)=>{\n        if (!isDragging) return;\n        setPosition({\n            x: e.clientX - dragStart.x,\n            y: e.clientY - dragStart.y\n        });\n    };\n    const handleMouseUp = ()=>{\n        setIsDragging(false);\n    };\n    const handleZoomIn = ()=>{\n        setScale((prev)=>Math.min(prev + 0.1, 3));\n    };\n    const handleZoomOut = ()=>{\n        setScale((prev)=>Math.max(prev - 0.1, 0.5));\n    };\n    const handleCrop = async ()=>{\n        if (!canvasRef.current || !file) return;\n        const canvas = canvasRef.current;\n        const cropCanvas = document.createElement(\"canvas\");\n        cropCanvas.width = uiDimensions.width;\n        cropCanvas.height = uiDimensions.height;\n        const cropCtx = cropCanvas.getContext(\"2d\");\n        if (!cropCtx) return;\n        cropCtx.drawImage(canvas, 0, 0, canvas.width, canvas.height, 0, 0, uiDimensions.width, uiDimensions.height);\n        cropCanvas.toBlob(async (blob)=>{\n            if (blob) {\n                const croppedFile = new File([\n                    blob\n                ], file.name, {\n                    type: file.type,\n                    lastModified: Date.now()\n                });\n                const resizedFile = await (0,_utils_resizeImage__WEBPACK_IMPORTED_MODULE_4__.resizeImage)(croppedFile, dimensions, _utils_resizeImage__WEBPACK_IMPORTED_MODULE_4__.ScaleMode.Fill, resizeOptions);\n                onCrop(resizedFile);\n            }\n        }, file.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onClose,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-[550px] max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                        children: [\n                            \"Adjust \",\n                            type === \"cover\" ? \"Cover\" : \"Profile\",\n                            \" Image\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center gap-4 py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative border border-gray-200 overflow-hidden\",\n                            style: {\n                                width: \"\".concat(uiDimensions.width, \"px\"),\n                                height: \"\".concat(uiDimensions.height, \"px\")\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                                ref: canvasRef,\n                                width: uiDimensions.width,\n                                height: uiDimensions.height,\n                                onMouseDown: handleMouseDown,\n                                onMouseMove: handleMouseMove,\n                                onMouseUp: handleMouseUp,\n                                onMouseLeave: handleMouseUp,\n                                className: \"cursor-move\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                                lineNumber: 172,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleZoomOut,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: \"Zoom Out\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleZoomIn,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: \"Zoom In\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Drag to position the image. Use the zoom buttons to resize.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                    lineNumber: 164,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogFooter, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: onClose,\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                            lineNumber: 195,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: handleCrop,\n                            children: \"Apply\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n                    lineNumber: 194,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n            lineNumber: 159,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\imageCropperDialog.tsx\",\n        lineNumber: 158,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ImageCropperDialog, \"0OD4mBhypldDrj9NuBc6JIA0oxw=\");\n_c = ImageCropperDialog;\nvar _c;\n$RefreshReg$(_c, \"ImageCropperDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/record/components/imageCropperDialog.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/record/components/recordImageUploader.tsx":
/*!*********************************************************************************!*\
  !*** ./src/components/workspace/main/record/components/recordImageUploader.tsx ***!
  \*********************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecordImageUploader: function() { return /* binding */ RecordImageUploader; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpTrayIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpTrayIcon,PhotoIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowUpTrayIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowUpTrayIcon,PhotoIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _api_common__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/api/common */ \"(app-pages-browser)/./src/api/common.ts\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _imageCropperDialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./imageCropperDialog */ \"(app-pages-browser)/./src/components/workspace/main/record/components/imageCropperDialog.tsx\");\n/* harmony import */ var _api_workspace__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/api/workspace */ \"(app-pages-browser)/./src/api/workspace.ts\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst RecordImageUploader = (param)=>{\n    let { record, type, onImageUpdated, className = \"\", children } = param;\n    _s();\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isUploading, setIsUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cropperOpen, setCropperOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_3__.useAlert)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const { workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace)();\n    const handleClick = ()=>{\n        var _fileInputRef_current;\n        (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n    };\n    const handleFileChange = (e)=>{\n        const files = e.target.files;\n        if (!files || files.length === 0 || !token) return;\n        const file = files[0];\n        if (!file.type.startsWith(\"image/\")) {\n            toast.error(\"Please select an image file\");\n            return;\n        }\n        setSelectedFile(file);\n        setCropperOpen(true);\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleCropComplete = async (croppedFile)=>{\n        if (!token) return;\n        setIsUploading(true);\n        setCropperOpen(false);\n        const uploadCallback = {\n            onStart: ()=>{},\n            onProgress: ()=>{},\n            onComplete: async (res)=>{\n                var _res_data_data_meta, _res_data_data_meta1;\n                setIsUploading(false);\n                setSelectedFile(null);\n                if (!res.isSuccess) {\n                    console.error(\"Error uploading image:\", res.error);\n                    toast.error(\"Failed to upload \".concat(type, \" image: \").concat(res.error || (0,_api_common__WEBPACK_IMPORTED_MODULE_4__.defaultAPIMessage)()));\n                    return;\n                }\n                const imageUrl = res.data.data.imageUrl || ((_res_data_data_meta = res.data.data.meta) === null || _res_data_data_meta === void 0 ? void 0 : _res_data_data_meta.coverImage) || ((_res_data_data_meta1 = res.data.data.meta) === null || _res_data_data_meta1 === void 0 ? void 0 : _res_data_data_meta1.profileImage) || \"\";\n                toast.success(\"\".concat(type === \"cover\" ? \"Cover\" : \"Profile\", \" image updated\"));\n                onImageUpdated === null || onImageUpdated === void 0 ? void 0 : onImageUpdated(imageUrl);\n            }\n        };\n        if (type === \"cover\") {\n            (0,_api_workspace__WEBPACK_IMPORTED_MODULE_8__.uploadRecordCoverImage)(token.token, workspace.workspace.id, record.id, record.databaseId, croppedFile, uploadCallback);\n        } else {\n            (0,_api_workspace__WEBPACK_IMPORTED_MODULE_8__.uploadRecordProfileImage)(token.token, workspace.workspace.id, record.id, record.databaseId, croppedFile, uploadCallback);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                onClick: handleClick,\n                className: className,\n                children: children || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    variant: \"ghost\",\n                    size: \"sm\",\n                    disabled: isUploading,\n                    className: \"flex items-center gap-1\",\n                    children: isUploading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Uploading...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                        lineNumber: 100,\n                        columnNumber: 29\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: type === \"cover\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpTrayIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 41\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Change cover\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 41\n                                }, undefined)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowUpTrayIcon_PhotoIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 41\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Upload profile image\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 41\n                                }, undefined)\n                            ]\n                        }, void 0, true)\n                    }, void 0, false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                lineNumber: 88,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                type: \"file\",\n                ref: fileInputRef,\n                onChange: handleFileChange,\n                accept: \"image/*\",\n                className: \"hidden\",\n                title: \"Upload \".concat(type, \" image\")\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                lineNumber: 119,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_imageCropperDialog__WEBPACK_IMPORTED_MODULE_7__.ImageCropperDialog, {\n                file: selectedFile,\n                open: cropperOpen,\n                onClose: ()=>setCropperOpen(false),\n                onCrop: handleCropComplete,\n                type: type\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordImageUploader.tsx\",\n                lineNumber: 127,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(RecordImageUploader, \"7mp3dIQOXxS9XIpKpvddDgS6TIU=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_3__.useAlert,\n        _providers_user__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_6__.useWorkspace\n    ];\n});\n_c = RecordImageUploader;\nvar _c;\n$RefreshReg$(_c, \"RecordImageUploader\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/record/components/recordImageUploader.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/workspace/main/record/components/recordOverview.tsx":
/*!****************************************************************************!*\
  !*** ./src/components/workspace/main/record/components/recordOverview.tsx ***!
  \****************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecordOverview: function() { return /* binding */ RecordOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,PencilIcon,PhotoIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,PencilIcon,PhotoIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,PencilIcon,PhotoIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=GlobeAltIcon,PencilIcon,PhotoIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_workspace_main_views_viewIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/viewIcon */ \"(app-pages-browser)/./src/components/workspace/main/views/viewIcon.tsx\");\n/* harmony import */ var _utils_demo_links__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/utils/demo/links */ \"(app-pages-browser)/./src/utils/demo/links.ts\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/switch */ \"(app-pages-browser)/./src/components/ui/switch.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_common_formFieldBody__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/common/formFieldBody */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/common/formFieldBody.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _typings_page__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/typings/page */ \"(app-pages-browser)/./src/typings/page.ts\");\n/* harmony import */ var _utils_environment__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/utils/environment */ \"(app-pages-browser)/./src/utils/environment.ts\");\n/* harmony import */ var _components_workspace_main_emails_sendEmailWrapper__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/emails/sendEmailWrapper */ \"(app-pages-browser)/./src/components/workspace/main/emails/sendEmailWrapper.tsx\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! lodash/debounce */ \"(app-pages-browser)/./node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_16___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_16__);\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _components_workspace_main_record_components_recordImageUploader__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/workspace/main/record/components/recordImageUploader */ \"(app-pages-browser)/./src/components/workspace/main/record/components/recordImageUploader.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* __next_internal_client_entry_do_not_use__ RecordOverview auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst RecordOverview = (props)=>{\n    var _record_meta, _record_meta1, _record_meta2, _record_meta3;\n    _s();\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_12__.usePage)();\n    const { updateRecordValues } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_11__.useViews)();\n    const { recordInfo, database } = (0,_providers_record__WEBPACK_IMPORTED_MODULE_9__.useRecord)();\n    const { members } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_19__.useWorkspace)();\n    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_17__.getDatabaseTitleCol)(database);\n    const title = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_17__.getRecordTitle)(recordInfo.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n    const canBeEnriched = false;\n    const isPublished = true;\n    const disabled = !accessLevel || ![\n        _typings_page__WEBPACK_IMPORTED_MODULE_13__.AccessLevel.Full,\n        _typings_page__WEBPACK_IMPORTED_MODULE_13__.AccessLevel.Edit\n    ].includes(accessLevel);\n    const { record } = recordInfo;\n    const toSaveRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(null);\n    const debounceRef = (0,react__WEBPACK_IMPORTED_MODULE_2__.useRef)(lodash_debounce__WEBPACK_IMPORTED_MODULE_16___default()(async ()=>{\n        const values = toSaveRef.current;\n        if (values) {\n            await updateRecordValues(record.databaseId, [\n                record.id\n            ], values, {});\n            toSaveRef.current = null;\n        }\n    }, 100));\n    const updateValues = async (u)=>{\n        // debounce update values\n        toSaveRef.current = u;\n        debounceRef.current();\n    };\n    const updateTitleFormat = async (titleFormat)=>{\n        await updateRecordValues(record.databaseId, [\n            record.id\n        ], {}, {\n            titleFormat\n        });\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        document.title = title;\n    }, [\n        title\n    ]);\n    const coverImage = typeof ((_record_meta = record.meta) === null || _record_meta === void 0 ? void 0 : _record_meta.coverImage) === \"object\" ? record.meta.coverImage.url : ((_record_meta1 = record.meta) === null || _record_meta1 === void 0 ? void 0 : _record_meta1.coverImage) || \"\";\n    const profileImage = typeof ((_record_meta2 = record.meta) === null || _record_meta2 === void 0 ? void 0 : _record_meta2.profileImage) === \"object\" ? record.meta.profileImage.url : ((_record_meta3 = record.meta) === null || _record_meta3 === void 0 ? void 0 : _record_meta3.profileImage) || \"\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"hidden lg:block \".concat(props.isTabbed ? \"!block\" : \"w-96 border-r\", \" h-full border-neutral-300 rOV\"),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex flex-col overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-32 bg-cover bg-center bg-gray-100 relative\",\n                        style: {\n                            backgroundImage: coverImage ? \"url(\".concat(coverImage, \")\") : undefined\n                        },\n                        children: !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_record_components_recordImageUploader__WEBPACK_IMPORTED_MODULE_18__.RecordImageUploader, {\n                            record: record,\n                            type: \"cover\",\n                            className: \"absolute bottom-2 right-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"secondary\",\n                                size: \"sm\",\n                                className: \"bg-white/80 backdrop-blur-sm hover:bg-white/90\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 mr-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    coverImage ? \"Change Cover\" : \"Add Cover\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 29\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                            lineNumber: 90,\n                            columnNumber: 25\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-3 border-b flex flex-col gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.Avatar, {\n                                                className: \"mr-1 size-12\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarImage, {\n                                                        src: profileImage,\n                                                        alt: title,\n                                                        className: \"!rounded-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                        lineNumber: 110,\n                                                        columnNumber: 33\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_1__.AvatarFallback, {\n                                                        className: \"!rounded\",\n                                                        children: title[0]\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 33\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 29\n                                            }, undefined),\n                                            !disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_record_components_recordImageUploader__WEBPACK_IMPORTED_MODULE_18__.RecordImageUploader, {\n                                                record: record,\n                                                type: \"profile\",\n                                                className: \"absolute -bottom-1 -right-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"secondary\",\n                                                    size: \"icon\",\n                                                    className: \"rounded-full h-6 w-6 bg-white/80 backdrop-blur-sm hover:bg-white/90\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-3 w-3\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 41\n                                                    }, undefined)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                lineNumber: 118,\n                                                columnNumber: 33\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                        lineNumber: 108,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"font-semibold text-black flex-1 truncate overflow-hidden\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    (0,_utils_environment__WEBPACK_IMPORTED_MODULE_14__.isLocal)() && isPublished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        onClick: (e)=>{\n                                            // setCollapse(!collapse)\n                                            e.stopPropagation();\n                                            e.preventDefault();\n                                            alert(\"Show a popup with the fields and a publish button; if published show an input with the link\");\n                                        },\n                                        className: \"size-6 p-1 rounded-full items-center hover:bg-neutral-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                            className: \"size-4 transition-transform text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 54\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 21\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-1 items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_emails_sendEmailWrapper__WEBPACK_IMPORTED_MODULE_15__.SendEmailWrapper, {\n                                    isRecordPage: true,\n                                    database: database,\n                                    recordIds: [\n                                        record.id\n                                    ]\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                    lineNumber: 150,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 21\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 overflow-hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_3__.ScrollArea, {\n                            className: \"h-full w-full overflow-x-hidden !max-w-full\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"block max-w-full overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-3 pb-28 flex flex-col gap-2 max-w-full\",\n                                    children: [\n                                        database.definition.columnIds.map((id)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_form_components_common_formFieldBody__WEBPACK_IMPORTED_MODULE_10__.FormFieldBody, {\n                                                columnsMap: database.definition.columnsMap,\n                                                columnsPropMap: {},\n                                                updateFieldProps: ()=>{},\n                                                activeField: \"\",\n                                                setActiveField: ()=>{},\n                                                values: record.recordValues,\n                                                processedRecordValues: recordInfo.processedRecord.processedRecordValues,\n                                                updateValues: updateValues,\n                                                isEditing: false,\n                                                disabled: disabled,\n                                                databaseId: database.id,\n                                                id: id\n                                            }, id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 74\n                                            }, undefined)),\n                                        (0,_utils_environment__WEBPACK_IMPORTED_MODULE_14__.isLocal)() && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 my-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    variant: \"outline\",\n                                                    size: \"sm\",\n                                                    children: \"New field\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 37\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: \"Choose fields\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                            lineNumber: 214,\n                                                            columnNumber: 41\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                            className: \"w-56 p-0 rounded-none\",\n                                                            align: \"start\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col h-auto max-h-96\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2 border-b flex-1\",\n                                                                        children: _utils_demo_links__WEBPACK_IMPORTED_MODULE_7__.subLinks.map((link)=>{\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                                variant: \"ghost\",\n                                                                                className: \"text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_viewIcon__WEBPACK_IMPORTED_MODULE_6__.ViewIcon, {\n                                                                                        type: link.type,\n                                                                                        className: \"size-4\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                        lineNumber: 224,\n                                                                                        columnNumber: 61\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1 overflow-hidden truncate text-left\",\n                                                                                        children: link.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                        lineNumber: 225,\n                                                                                        columnNumber: 61\n                                                                                    }, undefined),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_8__.Switch, {\n                                                                                        className: \"h-4 w-8\",\n                                                                                        thumbClassName: \"!size-3\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                        lineNumber: 228,\n                                                                                        columnNumber: 61\n                                                                                    }, undefined)\n                                                                                ]\n                                                                            }, link.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                lineNumber: 221,\n                                                                                columnNumber: 64\n                                                                            }, undefined);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                        lineNumber: 219,\n                                                                        columnNumber: 49\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"p-2\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                            variant: \"ghost\",\n                                                                            className: \"text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start\",\n                                                                            onClick: ()=>{\n                                                                            // setOpen(false)\n                                                                            // props.requestNewView()\n                                                                            },\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_GlobeAltIcon_PencilIcon_PhotoIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"size-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                                    lineNumber: 239,\n                                                                                    columnNumber: 57\n                                                                                }, undefined),\n                                                                                \"New View\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                            lineNumber: 233,\n                                                                            columnNumber: 53\n                                                                        }, undefined)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                        lineNumber: 232,\n                                                                        columnNumber: 49\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 45\n                                                            }, undefined)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 41\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 37\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 47\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 29\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordOverview.tsx\",\n            lineNumber: 83,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s(RecordOverview, \"iYwp0IbCU3agsnPt7KFLPOlVMFM=\", false, function() {\n    return [\n        _providers_page__WEBPACK_IMPORTED_MODULE_12__.usePage,\n        _providers_views__WEBPACK_IMPORTED_MODULE_11__.useViews,\n        _providers_record__WEBPACK_IMPORTED_MODULE_9__.useRecord,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_19__.useWorkspace\n    ];\n});\n_c = RecordOverview;\nvar _c;\n$RefreshReg$(_c, \"RecordOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/record/components/recordOverview.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/demo/links.ts":
/*!*********************************!*\
  !*** ./src/utils/demo/links.ts ***!
  \*********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   databaseLinkGroup: function() { return /* binding */ databaseLinkGroup; },\n/* harmony export */   pageLinkGroup: function() { return /* binding */ pageLinkGroup; },\n/* harmony export */   privatePageLinkGroup: function() { return /* binding */ privatePageLinkGroup; },\n/* harmony export */   subLinks: function() { return /* binding */ subLinks; }\n/* harmony export */ });\nconst subLinks = [];\nconst links = [\n    {\n        id: \"contacts\",\n        emoji: \"\\uD83D\\uDCD5\",\n        deletable: false,\n        title: \"Contacts\",\n        editable: false,\n        url: \"/space/databases/contacts\",\n        subLinks\n    },\n    {\n        id: \"companies\",\n        emoji: \"\\uD83D\\uDCBC\",\n        deletable: false,\n        title: \"Companies\",\n        editable: false,\n        url: \"/space/companies\",\n        subLinks\n    },\n    {\n        id: \"properties\",\n        emoji: \"\\uD83C\\uDFE0\",\n        deletable: true,\n        title: \"Properties\",\n        editable: true,\n        url: \"/space/properties\",\n        subLinks\n    },\n    {\n        id: \"leases\",\n        emoji: \"\\uD83D\\uDCC4\",\n        deletable: true,\n        title: \"Leases\",\n        editable: true,\n        url: \"/space/leases\",\n        subLinks\n    }\n];\nconst databaseLinkGroup = {\n    id: \"databases\",\n    links,\n    title: \"Databases\"\n};\nconst pageLinkGroup = {\n    id: \"pages\",\n    links,\n    title: \"Shared\"\n};\nconst privatePageLinkGroup = {\n    id: \"pages\",\n    links,\n    title: \"Private\"\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/demo/links.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/utils/resizeImage.ts":
/*!**********************************!*\
  !*** ./src/utils/resizeImage.ts ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ScaleMode: function() { return /* binding */ ScaleMode; },\n/* harmony export */   resizeImage: function() { return /* binding */ resizeImage; }\n/* harmony export */ });\nvar ScaleMode;\n(function(ScaleMode) {\n    ScaleMode[\"Fit\"] = \"fit\";\n    ScaleMode[\"Fill\"] = \"fill\";\n})(ScaleMode || (ScaleMode = {}));\nfunction resizeImage(file, dimension, scaleMode, options) {\n    return new Promise((resolve, reject)=>{\n        const img = new Image();\n        img.src = URL.createObjectURL(file);\n        img.onload = ()=>{\n            const canvas = document.createElement(\"canvas\");\n            const { width, height } = dimension;\n            canvas.width = width;\n            canvas.height = height;\n            const ctx = canvas.getContext(\"2d\");\n            let sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight;\n            const imgAspectRatio = img.width / img.height;\n            const canvasAspectRatio = width / height;\n            if (imgAspectRatio > canvasAspectRatio && scaleMode === \"fit\" || imgAspectRatio < canvasAspectRatio && scaleMode === \"fill\") {\n                // Adjust based on height\n                sHeight = img.height;\n                sWidth = img.height * canvasAspectRatio;\n                sx = (img.width - sWidth) / 2;\n                sy = 0;\n                dx = dy = 0;\n                dWidth = width;\n                dHeight = height;\n            } else {\n                // Adjust based on width\n                sWidth = img.width;\n                sHeight = img.width / canvasAspectRatio;\n                sx = 0;\n                sy = (img.height - sHeight) / 2;\n                dx = dy = 0;\n                dWidth = width;\n                dHeight = height;\n            }\n            ctx.drawImage(img, sx, sy, sWidth, sHeight, dx, dy, dWidth, dHeight);\n            // Use quality parameter if provided (default is 0.92 in most browsers)\n            const quality = (options === null || options === void 0 ? void 0 : options.quality) ? options.quality / 100 : undefined;\n            canvas.toBlob((blob)=>{\n                if (blob) {\n                    const resizedFile = new File([\n                        blob\n                    ], file.name, {\n                        type: blob.type,\n                        lastModified: new Date().getTime()\n                    });\n                    resolve(resizedFile);\n                } else {\n                    reject(new Error(\"Canvas toBlob failed\"));\n                }\n            }, file.type, quality);\n        };\n        img.onerror = (error)=>{\n            reject(error);\n        };\n    });\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/resizeImage.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js ***!
  \*************************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowUpTrayIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5m-13.5-9L12 3m0 0 4.5 4.5M12 3v13.5\"\n    }));\n}\n_c = ArrowUpTrayIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowUpTrayIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowUpTrayIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpTrayIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js":
/*!**********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js ***!
  \**********************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction GlobeAltIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418\"\n    }));\n}\n_c = GlobeAltIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(GlobeAltIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"GlobeAltIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0dsb2JlQWx0SWNvbi5qcyIsIm1hcHBpbmdzIjoiOztBQUErQjtBQUMvQixTQUFTQyxhQUFhLEtBSXJCLEVBQUVDLE1BQU07UUFKYSxFQUNwQkMsS0FBSyxFQUNMQyxPQUFPLEVBQ1AsR0FBR0MsT0FDSixHQUpxQjtJQUtwQixPQUFPLFdBQVcsR0FBRUwsZ0RBQW1CLENBQUMsT0FBT08sT0FBT0MsTUFBTSxDQUFDO1FBQzNEQyxPQUFPO1FBQ1BDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxhQUFhO1FBQ2JDLFFBQVE7UUFDUixlQUFlO1FBQ2YsYUFBYTtRQUNiQyxLQUFLWjtRQUNMLG1CQUFtQkU7SUFDckIsR0FBR0MsUUFBUUYsUUFBUSxXQUFXLEdBQUVILGdEQUFtQixDQUFDLFNBQVM7UUFDM0RlLElBQUlYO0lBQ04sR0FBR0QsU0FBUyxNQUFNLFdBQVcsR0FBRUgsZ0RBQW1CLENBQUMsUUFBUTtRQUN6RGdCLGVBQWU7UUFDZkMsZ0JBQWdCO1FBQ2hCQyxHQUFHO0lBQ0w7QUFDRjtLQXRCU2pCO0FBdUJULE1BQU1rQixhQUFhLFdBQVcsR0FBR25CLDZDQUFnQixDQUFDQzs7QUFDbEQsK0RBQWVrQixVQUFVQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9AaGVyb2ljb25zL3JlYWN0LzI0L291dGxpbmUvZXNtL0dsb2JlQWx0SWNvbi5qcz9iZjMyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gR2xvYmVBbHRJY29uKHtcbiAgdGl0bGUsXG4gIHRpdGxlSWQsXG4gIC4uLnByb3BzXG59LCBzdmdSZWYpIHtcbiAgcmV0dXJuIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwic3ZnXCIsIE9iamVjdC5hc3NpZ24oe1xuICAgIHhtbG5zOiBcImh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnXCIsXG4gICAgZmlsbDogXCJub25lXCIsXG4gICAgdmlld0JveDogXCIwIDAgMjQgMjRcIixcbiAgICBzdHJva2VXaWR0aDogMS41LFxuICAgIHN0cm9rZTogXCJjdXJyZW50Q29sb3JcIixcbiAgICBcImFyaWEtaGlkZGVuXCI6IFwidHJ1ZVwiLFxuICAgIFwiZGF0YS1zbG90XCI6IFwiaWNvblwiLFxuICAgIHJlZjogc3ZnUmVmLFxuICAgIFwiYXJpYS1sYWJlbGxlZGJ5XCI6IHRpdGxlSWRcbiAgfSwgcHJvcHMpLCB0aXRsZSA/IC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwidGl0bGVcIiwge1xuICAgIGlkOiB0aXRsZUlkXG4gIH0sIHRpdGxlKSA6IG51bGwsIC8qI19fUFVSRV9fKi9SZWFjdC5jcmVhdGVFbGVtZW50KFwicGF0aFwiLCB7XG4gICAgc3Ryb2tlTGluZWNhcDogXCJyb3VuZFwiLFxuICAgIHN0cm9rZUxpbmVqb2luOiBcInJvdW5kXCIsXG4gICAgZDogXCJNMTIgMjFhOS4wMDQgOS4wMDQgMCAwIDAgOC43MTYtNi43NDdNMTIgMjFhOS4wMDQgOS4wMDQgMCAwIDEtOC43MTYtNi43NDdNMTIgMjFjMi40ODUgMCA0LjUtNC4wMyA0LjUtOVMxNC40ODUgMyAxMiAzbTAgMThjLTIuNDg1IDAtNC41LTQuMDMtNC41LTlTOS41MTUgMyAxMiAzbTAgMGE4Ljk5NyA4Ljk5NyAwIDAgMSA3Ljg0MyA0LjU4Mk0xMiAzYTguOTk3IDguOTk3IDAgMCAwLTcuODQzIDQuNTgybTE1LjY4NiAwQTExLjk1MyAxMS45NTMgMCAwIDEgMTIgMTAuNWMtMi45OTggMC01Ljc0LTEuMS03Ljg0My0yLjkxOG0xNS42ODYgMEE4Ljk1OSA4Ljk1OSAwIDAgMSAyMSAxMmMwIC43NzgtLjA5OSAxLjUzMy0uMjg0IDIuMjUzbTAgMEExNy45MTkgMTcuOTE5IDAgMCAxIDEyIDE2LjVjLTMuMTYyIDAtNi4xMzMtLjgxNS04LjcxNi0yLjI0N20wIDBBOS4wMTUgOS4wMTUgMCAwIDEgMyAxMmMwLTEuNjA1LjQyLTMuMTEzIDEuMTU3LTQuNDE4XCJcbiAgfSkpO1xufVxuY29uc3QgRm9yd2FyZFJlZiA9IC8qI19fUFVSRV9fKi8gUmVhY3QuZm9yd2FyZFJlZihHbG9iZUFsdEljb24pO1xuZXhwb3J0IGRlZmF1bHQgRm9yd2FyZFJlZjsiXSwibmFtZXMiOlsiUmVhY3QiLCJHbG9iZUFsdEljb24iLCJzdmdSZWYiLCJ0aXRsZSIsInRpdGxlSWQiLCJwcm9wcyIsImNyZWF0ZUVsZW1lbnQiLCJPYmplY3QiLCJhc3NpZ24iLCJ4bWxucyIsImZpbGwiLCJ2aWV3Qm94Iiwic3Ryb2tlV2lkdGgiLCJzdHJva2UiLCJyZWYiLCJpZCIsInN0cm9rZUxpbmVjYXAiLCJzdHJva2VMaW5lam9pbiIsImQiLCJGb3J3YXJkUmVmIiwiZm9yd2FyZFJlZiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js ***!
  \*******************************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction PhotoIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"m2.25 15.75 5.159-5.159a2.25 2.25 0 0 1 3.182 0l5.159 5.159m-1.5-1.5 1.409-1.409a2.25 2.25 0 0 1 3.182 0l2.909 2.909m-18 3.75h16.5a1.5 1.5 0 0 0 1.5-1.5V6a1.5 1.5 0 0 0-1.5-1.5H3.75A1.5 1.5 0 0 0 2.25 6v12a1.5 1.5 0 0 0 1.5 1.5Zm10.5-11.25h.008v.008h-.008V8.25Zm.375 0a.375.375 0 1 1-.75 0 .375.375 0 0 1 .75 0Z\"\n    }));\n}\n_c = PhotoIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(PhotoIcon);\n_c1 = ForwardRef;\n/* harmony default export */ __webpack_exports__[\"default\"] = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"PhotoIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PhotoIcon.js\n"));

/***/ })

}]);