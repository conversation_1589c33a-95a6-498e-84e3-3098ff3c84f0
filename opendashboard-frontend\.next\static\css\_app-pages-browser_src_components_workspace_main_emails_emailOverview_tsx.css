/*!*********************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/components/workspace/main/emails/emails.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************/
.allEmails > div {
        height: 100%;
        overflow: hidden;
    }

.allEmails {
    --size-5: 1.25rem;
    --size-7: 1.75rem;
    --size-8: 2rem;
    --size-20: 5rem;
    --size-24: 6rem;
    --size-48: 12rem;
    --size-xs: 20rem;
    --size-sm: 24rem;
    --size-md: 28rem;
    --size-lg: 32rem;
    --size-xl: 36rem;
    --size-2xl: 42rem;
    --size-3xl: 48rem;
}

/** @type {import('tailwindcss').Config} */

/*module.exports = {*/

/*    theme: {*/

/*    spacing: {*/

/*    px: '1px',*/

/*    0: '0',*/

/*    0.5: '0.125rem',*/

/*    1: '0.25rem',*/

/*    1.5: '0.375rem',*/

/*    2: '0.5rem',*/

/*    2.5: '0.625rem',*/

/*    3: '0.75rem',*/

/*    3.5: '0.875rem',*/

/*    4: '1rem',*/

/*    5: '1.25rem',*/

/*    6: '1.5rem',*/

/*    7: '1.75rem',*/

/*    8: '2rem',*/

/*    9: '2.25rem',*/

/*    10: '2.5rem',*/

/*    11: '2.75rem',*/

/*    12: '3rem',*/

/*    14: '3.5rem',*/

/*    16: '4rem',*/

/*    20: '5rem',*/

/*    24: '6rem',*/

/*    28: '7rem',*/

/*    32: '8rem',*/

/*    36: '9rem',*/

/*    40: '10rem',*/

/*    44: '11rem',*/

/*    48: '12rem',*/

/*    52: '13rem',*/

/*    56: '14rem',*/

/*    60: '15rem',*/

/*    64: '16rem',*/

/*    72: '18rem',*/

/*    80: '20rem',*/

/*    96: '24rem',*/

/*},*/

/*}*/

/*}*/

.allEmails .rowGrid {
    /*display: grid;*/
    /*grid-template-columns: var(--size-8) minmax(var(--size-48), var(--size-2xl)) repeat(8, minmax(0, var(--size-24))) minmax(var(--size-7), 1fr);*/
    /*place-items: center start;*/
    min-height: 0; /* NEW */
    min-width: 0; /* NEW; needed for Firefox */
    /*text-align: left;*/
    /*padding-block-start: var(--space-4);*/
    /*padding-block-end: var(--space-4);*/
    /*padding-inline-end: var(--space-4);*/
    /*gap: var(--space-3);*/
    /*border-color: var(--colors-border);*/
    /*border-block-width: 1px;*/
    /*margin-block-start: calc(var(--space-px)*-1);*/
    display: flex;
    align-items: center;
    gap: 0.75rem}

.allEmails .rowGrid > div {
    /*overflow: hidden;*/
    /*min-width: 0;*/
    width: 6rem;
    overflow: hidden;
}

.allEmails .rowGrid > div.check {
        width: 2rem;
}

.allEmails .rowGrid > div.fluid {
        flex: 1 1 0%;
        padding-left: 0px;
}
