"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_emails_newEmail_tsx-_app-pages-browser_src_c-589fae",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx":
/*!*************************************************************!*\
  !*** ./src/components/workspace/main/views/table/index.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TableView: function() { return /* binding */ TableView; },\n/* harmony export */   filterAndSortRecords: function() { return /* binding */ filterAndSortRecords; },\n/* harmony export */   searchFilteredRecords: function() { return /* binding */ searchFilteredRecords; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _table_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./table.css */ \"(app-pages-browser)/./src/components/workspace/main/views/table/table.css\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var react_data_grid_lib_styles_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-data-grid/lib/styles.css */ \"(app-pages-browser)/./node_modules/react-data-grid/lib/styles.css\");\n/* harmony import */ var react_data_grid__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! react-data-grid */ \"(app-pages-browser)/./node_modules/react-data-grid/lib/bundle.js\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/header */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/header.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/checkbox */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/checkbox.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/date */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/date.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/person */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/person.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/files */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/files.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/addColumn */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/addColumn.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_selectRow__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/selectRow */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/selectRow.tsx\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/ai */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/ai.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/select */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/select.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/linked.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! opendb-app-db-utils/lib/utils/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/utils/db.js\");\n/* harmony import */ var _components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/workspace/main/views/common/contentLocked */ \"(app-pages-browser)/./src/components/workspace/main/views/common/contentLocked.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/summarize */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/summarize.tsx\");\n/* harmony import */ var _barrel_optimize_names_ExclamationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ExclamationCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_workspace_main_database_importRecords__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/workspace/main/database/importRecords */ \"(app-pages-browser)/./src/components/workspace/main/database/importRecords.tsx\");\n/* harmony import */ var _providers_broadcast__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/providers/broadcast */ \"(app-pages-browser)/./src/providers/broadcast.tsx\");\n/* harmony import */ var _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/custom-ui/forceRender */ \"(app-pages-browser)/./src/components/custom-ui/forceRender.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/scannableCode */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/scannableCode.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// DerivedFormulaEditor is used in HeaderRenderer, imported there directly\n\n\nconst TableView = (props)=>{\n    var _containerEle_current;\n    _s();\n    const { databaseStore, databaseErrorStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_3__.useWorkspace)();\n    const { definition } = props;\n    const { cache } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_22__.usePage)();\n    const { registerListener } = (0,_providers_broadcast__WEBPACK_IMPORTED_MODULE_26__.useBroadcast)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_21__.useMaybeShared)();\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_28__.useMaybeTemplate)();\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { selectedIds, setSelectedIds, sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerEle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    let scrollToRowId = -1;\n    const editColIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    let editColumnTrigger = false;\n    const getColDefs = ()=>{\n        const columns = [];\n        // const autoEdit: {\n        //     databaseId: string,\n        //     columnId: string\n        // } | null = cache.getCache(DatabaseConstants.NewlyCreatedColumnKey)\n        if (!database) return columns;\n        const dbDefinition = database.database.definition;\n        if (!dbDefinition) return columns;\n        let { columnsOrder, columnPropsMap } = definition;\n        columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : [];\n        columnPropsMap = columnPropsMap || {};\n        for (const key of dbDefinition.columnIds){\n            if (!columnsOrder.includes(key)) columnsOrder.push(key);\n            if (!columnPropsMap[key]) columnPropsMap[key] = {};\n        }\n        for (const id of columnsOrder){\n            // {key: dataPoint.key, name: dataPoint.key, field: dataPoint.key}\n            const dbCol = dbDefinition.columnsMap[id];\n            if (!dbCol) continue;\n            if (columnPropsMap[id].isHidden) continue;\n            const triggerEdit = editColIdRef.current === dbCol.id;\n            // const triggerEdit = !!(autoEdit && autoEdit.databaseId === definition.databaseId && autoEdit.columnId === dbCol.id)\n            const __meta__ = {\n                databaseId: definition.databaseId,\n                column: dbCol,\n                triggerEdit: triggerEdit,\n                headerLocked: !canEditStructure,\n                contentLocked: isPublishedView || definition.lockContent\n            };\n            // if (triggerEdit) scrollToColId = columns.length\n            if (triggerEdit) editColumnTrigger = true;\n            const column = {\n                key: id,\n                name: dbCol.title,\n                renderEditCell: _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextEditor,\n                renderCell: _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextRenderer,\n                renderHeaderCell: _components_workspace_main_views_table_renderer_common_header__WEBPACK_IMPORTED_MODULE_6__.HeaderRenderer,\n                width: 200,\n                editable: true,\n                __meta__\n            };\n            switch(dbCol.type){\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.AI:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_15__.AIRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UUID:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.UUIDRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Number:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Derived:\n                    if (dbCol.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text && dbCol.isLong) column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    if (dbCol.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Derived) {\n                        column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    }\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__.LinkedRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__.LinkedEditor;\n                    column.editable = !(!dbCol.databaseId || databaseErrorStore[dbCol.databaseId] || !databaseStore[dbCol.databaseId]);\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Summarize:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_23__.SummarizeRenderer;\n                    column.editable = false;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Select:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__.SelectRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__.SelectEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Checkbox:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_8__.CheckboxRenderer;\n                    // column.editable = true\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Date:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.CreatedAt:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UpdatedAt:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Person:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.CreatedBy:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UpdatedBy:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Files:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__.FileRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__.FileEditor;\n                    column.editable = true;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ScannableCode:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_30__.ScannableCodeRenderer;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ButtonGroup:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_29__.ButtonGroupRenderer;\n                    // ButtonGroup is read-only, so we don't need an editor\n                    column.editable = false;\n                    break;\n                default:\n                    continue;\n            }\n            column.editable = column.editable && canEditData;\n            columns.push(column);\n        }\n        if (canEditStructure) columns.push((0,_components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.getNewColumnProps)(database.database.id));\n        columns.unshift((0,_components_workspace_main_views_table_renderer_common_selectRow__WEBPACK_IMPORTED_MODULE_13__.getSelectColumnProps)(columns.length, canEditStructure));\n        return columns;\n    };\n    const colIds = cache.getCache(_components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.DatabaseConstants.NewlyCreatedRecordsKey);\n    const createdColIds = colIds && Array.isArray(colIds) ? colIds : [];\n    const cols = getColDefs();\n    const getProcessedRows = ()=>{\n        if (!database) return [];\n        const sortOptions = [];\n        if (sorts.length > 0) {\n            sortOptions.push(...sorts);\n        } else if (definition.sorts.length > 0) {\n            sortOptions.push(...definition.sorts);\n        }\n        if (sortOptions.length === 0) sortOptions.push({\n            columnId: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.MagicColumn.CreatedAt,\n            order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Sort.Asc\n        });\n        const { rows } = filterAndSortRecords(database, members, databaseStore, definition.filter, filter, sortOptions, workspace.workspaceMember.userId);\n        return rows;\n    };\n    const filteredRows = getProcessedRows();\n    const rows = searchFilteredRecords(search, filteredRows);\n    console.log(rows);\n    function rowKeyGetter(row) {\n        return row.id;\n    }\n    // console.log({scrollToColId, scrollToRowId})\n    // console.log({sorts, filter, rows, createdAts: rows.map(r => r.record.createdAt)})\n    const { forceRender } = (0,_components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__[\"default\"])();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!gridRef.current) return;\n        // if (scrollToColId > -1) {\n        //     gridRef.current?.scrollToCell({idx: scrollToColId + 2});\n        //\n        //     // console.log(\"Clearing New Col Key\", {scrollToColId})\n        //     // cache.clearCache(DatabaseConstants.NewlyCreatedColumnKey)\n        // }\n        if (scrollToRowId > -1) {\n            var _gridRef_current;\n            (_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : _gridRef_current.scrollToCell({\n                rowIdx: scrollToRowId + 1\n            });\n        // console.log(\"Clearing New Row Key\", {scrollToRowId})\n        // cache.clearCache(DatabaseConstants.NewlyCreatedRecordsKey)\n        }\n        if (editColIdRef.current && editColumnTrigger) {\n            // setEditColumnId(\"\")\n            editColIdRef.current = \"\";\n        // forceRenderRef.current()\n        }\n    // }, [cache, scrollToColId, scrollToRowId]);\n    }, [\n        cache,\n        editColumnTrigger,\n        scrollToRowId\n    ]);\n    // }, [cache, editColumnId, editColumnTrigger, scrollToRowId]);\n    // }, [cache, scrollToColId, scrollToRowId]);\n    // const setEditColIdRef = useRef(setEditColumnId)\n    // setEditColIdRef.current = setEditColumnId\n    // console.log(\"Render\", {editColumnId: editColIdRef.current, editColumnTrigger, cols})\n    const forceRenderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(forceRender);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const listener = (data)=>{\n            const { columnId } = data;\n            // setEditColIdRef.current(columnId)\n            // setEditColIdRef.current(columnId)\n            // setEditColumnId(columnId)\n            editColIdRef.current = columnId;\n            forceRenderRef.current();\n            console.log(\"New column listener called:\", {\n                columnId\n            });\n        };\n        const unregister = registerListener(\"d:\".concat(databaseId), _components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.DatabaseConstants.NewlyCreatedColumnKey, listener);\n        return ()=>{\n            unregister();\n        };\n    }, [\n        databaseId,\n        registerListener\n    ]);\n    // useEffect(() => {\n    //     const createdColIds: string[] = colIds && Array.isArray(colIds) ? colIds : []\n    //     if (createdColIds.length > 0) {\n    //         setTimeout(() => {\n    //             cache.clearCache(DatabaseConstants.NewlyCreatedRecordsKey)\n    //         }, 3000)\n    //     }\n    // }, [colIds, cache])\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            !definition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_14__.PageLoader, {\n                size: \"full\",\n                error: \"Unable to load database\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 352,\n                columnNumber: 29\n            }, undefined),\n            definition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"size-full flex flex-col\",\n                children: [\n                    !isPublishedView && definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_20__.ContentLocked, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex-1 overflow-hidden table-view bg-white relative\",\n                        ref: containerEle,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_data_grid__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            ref: gridRef,\n                            columns: cols,\n                            rows: rows,\n                            // rows={demoDatabase.rows}\n                            rowHeight: 40,\n                            headerRowHeight: 40,\n                            summaryRowHeight: 40,\n                            renderers: {\n                                noRowsFallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmptyRowsRenderer, {\n                                    width: (_containerEle_current = containerEle.current) === null || _containerEle_current === void 0 ? void 0 : _containerEle_current.clientWidth,\n                                    database: database.database\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 45\n                                }, void 0)\n                            },\n                            className: \"w-full h-full rdg-light\",\n                            // defaultColumnOptions={{\n                            //     sortable: true,\n                            //     resizable: true\n                            // }}\n                            rowKeyGetter: rowKeyGetter,\n                            selectedRows: new Set(selectedIds),\n                            onSelectedRowsChange: (ids)=>setSelectedIds(Array.from(ids)),\n                            rowClass: (row, index)=>{\n                                // row.id.includes('7') || index === 0 ? highlightClassname : undefined\n                                return createdColIds.includes(row.id) ? \"bg-neutral-100 animate-pulse\" : undefined;\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 354,\n                columnNumber: 28\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TableView, \"lkqYHWO0TTxPYaLnF4z8pOFnEuk=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_3__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews,\n        _providers_page__WEBPACK_IMPORTED_MODULE_22__.usePage,\n        _providers_broadcast__WEBPACK_IMPORTED_MODULE_26__.useBroadcast,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_21__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_28__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews,\n        _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__[\"default\"]\n    ];\n});\n_c = TableView;\nconst EmptyRowsRenderer = (param)=>{\n    let { database, width } = param;\n    _s1();\n    const { createRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const addRow = async ()=>{\n        const rS = await createRecords(database.id, [\n            {}\n        ]);\n    };\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            textAlign: \"center\",\n            width: width,\n            minWidth: width,\n            overflow: \"hidden\"\n        },\n        className: \"content-center sticky left-0 top-2 w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8 flex flex-col items-center min-h-72\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExclamationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        width: 48,\n                        height: 48,\n                        className: \"inline\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: \"Database has no records\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4 flex justify-center items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                            variant: \"ghost\",\n                            className: \"text-xs h-8 font-semibold\",\n                            onClick: addRow,\n                            children: \"Add Row\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                            className: \"text-xs px-4 h-8 text-center items-center font-semibold\",\n                            onClick: ()=>setOpen(true),\n                            children: \"Import from file\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n            lineNumber: 403,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n        lineNumber: 402,\n        columnNumber: 21\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_database_importRecords__WEBPACK_IMPORTED_MODULE_25__.ImportRecords, {\n                database: database,\n                close: ()=>setOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 419,\n                columnNumber: 18\n            }, undefined),\n            width && width > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: content\n            }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        gridColumn: \"1/-1\",\n                        overflow: \"hidden\"\n                    },\n                    className: \"content-center\",\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 14\n                }, undefined)\n            }, void 0, false)\n        ]\n    }, void 0, true);\n};\n_s1(EmptyRowsRenderer, \"rC5mgFaRT9QarQc0IyDaBINFj1o=\", false, function() {\n    return [\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews\n    ];\n});\n_c1 = EmptyRowsRenderer;\nconst filterAndSortRecords = function(database, members, databaseStore, definitionFilter, filter, sortOptions, currentUserId) {\n    let currentObjectId = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"\";\n    var _processedRecords_;\n    const rows = [];\n    if (!database) return {\n        rows\n    };\n    const linkedDatabaseId = database ? Object.values(database.database.definition.columnsMap).filter((c)=>c.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked && c.databaseId).map((c)=>c.databaseId) : [];\n    const linkedDatabases = {};\n    for (const id of linkedDatabaseId){\n        const db = databaseStore[id];\n        if (db) {\n            linkedDatabases[id] = {\n                id,\n                definition: db.database.definition,\n                srcPackageName: db.database.srcPackageName,\n                recordsMap: {}\n            };\n            for (let r of Object.values(db.recordsIdMap)){\n                linkedDatabases[id].recordsMap[r.record.id] = r.record;\n            }\n        }\n    }\n    const persons = (0,_components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.membersToPersons)(members);\n    const records = Object.values(database.recordsIdMap).map((r)=>r.record);\n    console.log(\"\\uD83D\\uDD0D DEBUG - Processing records with titleFormat:\", {\n        titleFormat: database.database.definition.titleFormat,\n        recordCount: records.length,\n        databaseId: database.database.id\n    });\n    const processedRecords = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.transformRawRecords)(database.database.definition, records, persons, linkedDatabases);\n    console.log(\"\\uD83D\\uDD0D DEBUG - Processed records sample:\", {\n        firstRecordTitle: (_processedRecords_ = processedRecords[0]) === null || _processedRecords_ === void 0 ? void 0 : _processedRecords_.title,\n        totalRecords: processedRecords.length\n    });\n    let filtered = {\n        processedRecords,\n        records\n    };\n    if (definitionFilter && definitionFilter.conditions.length > 0) {\n        filtered = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.filterRecords)(filtered.records, filtered.processedRecords, definitionFilter, database.database.definition, currentUserId, currentObjectId);\n    }\n    if (filter && filter.conditions.length > 0) {\n        filtered = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.filterRecords)(filtered.records, filtered.processedRecords, filter, database.database.definition, currentUserId, currentObjectId);\n    }\n    if (sortOptions.length === 0) sortOptions.push({\n        columnId: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.MagicColumn.CreatedAt,\n        order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Sort.Asc\n    });\n    const sorted = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.sortRecords)(filtered.records, filtered.processedRecords, sortOptions, database.database.definition);\n    for(let i = 0; i < sorted.records.length; i++){\n        const record = sorted.records[i];\n        const processedRecord = sorted.processedRecords[i];\n        const row = {\n            id: record.id,\n            record: database.recordsIdMap[record.id].record,\n            updatedAt: new Date(record.updatedAt).toISOString(),\n            processedRecord\n        };\n        rows.push(row);\n    }\n    return {\n        rows\n    };\n};\nconst searchFilteredRecords = (search, filteredRows)=>{\n    if (!search || !search.trim()) return filteredRows;\n    return filteredRows.filter((r)=>r.processedRecord.valuesText.toLowerCase().includes(search.trim().toLowerCase()));\n};\nvar _c, _c1;\n$RefreshReg$(_c, \"TableView\");\n$RefreshReg$(_c1, \"EmptyRowsRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\n"));

/***/ })

});