"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_board_board_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx":
/*!*************************************************************!*\
  !*** ./src/components/workspace/main/views/table/index.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TableView: function() { return /* binding */ TableView; },\n/* harmony export */   filterAndSortRecords: function() { return /* binding */ filterAndSortRecords; },\n/* harmony export */   searchFilteredRecords: function() { return /* binding */ searchFilteredRecords; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _table_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./table.css */ \"(app-pages-browser)/./src/components/workspace/main/views/table/table.css\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var react_data_grid_lib_styles_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-data-grid/lib/styles.css */ \"(app-pages-browser)/./node_modules/react-data-grid/lib/styles.css\");\n/* harmony import */ var react_data_grid__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! react-data-grid */ \"(app-pages-browser)/./node_modules/react-data-grid/lib/bundle.js\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/header */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/header.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/checkbox */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/checkbox.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/date */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/date.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/person */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/person.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/files */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/files.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/addColumn */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/addColumn.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_selectRow__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/selectRow */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/selectRow.tsx\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/ai */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/ai.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/select */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/select.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/linked.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! opendb-app-db-utils/lib/utils/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/utils/db.js\");\n/* harmony import */ var _components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/workspace/main/views/common/contentLocked */ \"(app-pages-browser)/./src/components/workspace/main/views/common/contentLocked.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/summarize */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/summarize.tsx\");\n/* harmony import */ var _barrel_optimize_names_ExclamationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ExclamationCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_workspace_main_database_importRecords__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/workspace/main/database/importRecords */ \"(app-pages-browser)/./src/components/workspace/main/database/importRecords.tsx\");\n/* harmony import */ var _providers_broadcast__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/providers/broadcast */ \"(app-pages-browser)/./src/providers/broadcast.tsx\");\n/* harmony import */ var _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/custom-ui/forceRender */ \"(app-pages-browser)/./src/components/custom-ui/forceRender.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/scannableCode */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/scannableCode.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// DerivedFormulaEditor is used in HeaderRenderer, imported there directly\n\n\nconst TableView = (props)=>{\n    var _containerEle_current;\n    _s();\n    const { databaseStore, databaseErrorStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_3__.useWorkspace)();\n    const { definition } = props;\n    const { cache } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_22__.usePage)();\n    const { registerListener } = (0,_providers_broadcast__WEBPACK_IMPORTED_MODULE_26__.useBroadcast)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_21__.useMaybeShared)();\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_28__.useMaybeTemplate)();\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { selectedIds, setSelectedIds, sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerEle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    let scrollToRowId = -1;\n    const editColIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    let editColumnTrigger = false;\n    const getColDefs = ()=>{\n        const columns = [];\n        // const autoEdit: {\n        //     databaseId: string,\n        //     columnId: string\n        // } | null = cache.getCache(DatabaseConstants.NewlyCreatedColumnKey)\n        if (!database) return columns;\n        const dbDefinition = database.database.definition;\n        if (!dbDefinition) return columns;\n        let { columnsOrder, columnPropsMap } = definition;\n        columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : [];\n        columnPropsMap = columnPropsMap || {};\n        for (const key of dbDefinition.columnIds){\n            if (!columnsOrder.includes(key)) columnsOrder.push(key);\n            if (!columnPropsMap[key]) columnPropsMap[key] = {};\n        }\n        for (const id of columnsOrder){\n            // {key: dataPoint.key, name: dataPoint.key, field: dataPoint.key}\n            const dbCol = dbDefinition.columnsMap[id];\n            if (!dbCol) continue;\n            if (columnPropsMap[id].isHidden) continue;\n            const triggerEdit = editColIdRef.current === dbCol.id;\n            // const triggerEdit = !!(autoEdit && autoEdit.databaseId === definition.databaseId && autoEdit.columnId === dbCol.id)\n            const __meta__ = {\n                databaseId: definition.databaseId,\n                column: dbCol,\n                triggerEdit: triggerEdit,\n                headerLocked: !canEditStructure,\n                contentLocked: isPublishedView || definition.lockContent\n            };\n            // if (triggerEdit) scrollToColId = columns.length\n            if (triggerEdit) editColumnTrigger = true;\n            const column = {\n                key: id,\n                name: dbCol.title,\n                renderEditCell: _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextEditor,\n                renderCell: _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextRenderer,\n                renderHeaderCell: _components_workspace_main_views_table_renderer_common_header__WEBPACK_IMPORTED_MODULE_6__.HeaderRenderer,\n                width: 200,\n                editable: true,\n                __meta__\n            };\n            switch(dbCol.type){\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.AI:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_15__.AIRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UUID:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.UUIDRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Number:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Derived:\n                    if (dbCol.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text && dbCol.isLong) column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    if (dbCol.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Derived) {\n                        column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    }\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__.LinkedRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__.LinkedEditor;\n                    column.editable = !(!dbCol.databaseId || databaseErrorStore[dbCol.databaseId] || !databaseStore[dbCol.databaseId]);\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Summarize:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_23__.SummarizeRenderer;\n                    column.editable = false;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Select:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__.SelectRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__.SelectEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Checkbox:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_8__.CheckboxRenderer;\n                    // column.editable = true\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Date:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.CreatedAt:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UpdatedAt:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Person:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.CreatedBy:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UpdatedBy:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Files:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__.FileRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__.FileEditor;\n                    column.editable = true;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ScannableCode:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_30__.ScannableCodeRenderer;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ButtonGroup:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_29__.ButtonGroupRenderer;\n                    // ButtonGroup is read-only, so we don't need an editor\n                    column.editable = false;\n                    break;\n                default:\n                    continue;\n            }\n            column.editable = column.editable && canEditData;\n            columns.push(column);\n        }\n        if (canEditStructure) columns.push((0,_components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.getNewColumnProps)(database.database.id));\n        columns.unshift((0,_components_workspace_main_views_table_renderer_common_selectRow__WEBPACK_IMPORTED_MODULE_13__.getSelectColumnProps)(columns.length, canEditStructure));\n        return columns;\n    };\n    const colIds = cache.getCache(_components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.DatabaseConstants.NewlyCreatedRecordsKey);\n    const createdColIds = colIds && Array.isArray(colIds) ? colIds : [];\n    const cols = getColDefs();\n    const getProcessedRows = ()=>{\n        if (!database) return [];\n        const sortOptions = [];\n        if (sorts.length > 0) {\n            sortOptions.push(...sorts);\n        } else if (definition.sorts.length > 0) {\n            sortOptions.push(...definition.sorts);\n        }\n        if (sortOptions.length === 0) sortOptions.push({\n            columnId: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.MagicColumn.CreatedAt,\n            order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Sort.Asc\n        });\n        const { rows } = filterAndSortRecords(database, members, databaseStore, definition.filter, filter, sortOptions, workspace.workspaceMember.userId);\n        return rows;\n    };\n    const filteredRows = getProcessedRows();\n    const rows = searchFilteredRecords(search, filteredRows);\n    console.log(rows);\n    function rowKeyGetter(row) {\n        return row.id;\n    }\n    // console.log({scrollToColId, scrollToRowId})\n    // console.log({sorts, filter, rows, createdAts: rows.map(r => r.record.createdAt)})\n    const { forceRender } = (0,_components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__[\"default\"])();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!gridRef.current) return;\n        // if (scrollToColId > -1) {\n        //     gridRef.current?.scrollToCell({idx: scrollToColId + 2});\n        //\n        //     // console.log(\"Clearing New Col Key\", {scrollToColId})\n        //     // cache.clearCache(DatabaseConstants.NewlyCreatedColumnKey)\n        // }\n        if (scrollToRowId > -1) {\n            var _gridRef_current;\n            (_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : _gridRef_current.scrollToCell({\n                rowIdx: scrollToRowId + 1\n            });\n        // console.log(\"Clearing New Row Key\", {scrollToRowId})\n        // cache.clearCache(DatabaseConstants.NewlyCreatedRecordsKey)\n        }\n        if (editColIdRef.current && editColumnTrigger) {\n            // setEditColumnId(\"\")\n            editColIdRef.current = \"\";\n        // forceRenderRef.current()\n        }\n    // }, [cache, scrollToColId, scrollToRowId]);\n    }, [\n        cache,\n        editColumnTrigger,\n        scrollToRowId\n    ]);\n    // }, [cache, editColumnId, editColumnTrigger, scrollToRowId]);\n    // }, [cache, scrollToColId, scrollToRowId]);\n    // const setEditColIdRef = useRef(setEditColumnId)\n    // setEditColIdRef.current = setEditColumnId\n    // console.log(\"Render\", {editColumnId: editColIdRef.current, editColumnTrigger, cols})\n    const forceRenderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(forceRender);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const listener = (data)=>{\n            const { columnId } = data;\n            // setEditColIdRef.current(columnId)\n            // setEditColIdRef.current(columnId)\n            // setEditColumnId(columnId)\n            editColIdRef.current = columnId;\n            forceRenderRef.current();\n            console.log(\"New column listener called:\", {\n                columnId\n            });\n        };\n        const unregister = registerListener(\"d:\".concat(databaseId), _components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.DatabaseConstants.NewlyCreatedColumnKey, listener);\n        return ()=>{\n            unregister();\n        };\n    }, [\n        databaseId,\n        registerListener\n    ]);\n    // useEffect(() => {\n    //     const createdColIds: string[] = colIds && Array.isArray(colIds) ? colIds : []\n    //     if (createdColIds.length > 0) {\n    //         setTimeout(() => {\n    //             cache.clearCache(DatabaseConstants.NewlyCreatedRecordsKey)\n    //         }, 3000)\n    //     }\n    // }, [colIds, cache])\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            !definition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_14__.PageLoader, {\n                size: \"full\",\n                error: \"Unable to load database\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 352,\n                columnNumber: 29\n            }, undefined),\n            definition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"size-full flex flex-col\",\n                children: [\n                    !isPublishedView && definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_20__.ContentLocked, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex-1 overflow-hidden table-view bg-white relative\",\n                        ref: containerEle,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_data_grid__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            ref: gridRef,\n                            columns: cols,\n                            rows: rows,\n                            // rows={demoDatabase.rows}\n                            rowHeight: 40,\n                            headerRowHeight: 40,\n                            summaryRowHeight: 40,\n                            renderers: {\n                                noRowsFallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmptyRowsRenderer, {\n                                    width: (_containerEle_current = containerEle.current) === null || _containerEle_current === void 0 ? void 0 : _containerEle_current.clientWidth,\n                                    database: database.database\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 45\n                                }, void 0)\n                            },\n                            className: \"w-full h-full rdg-light\",\n                            // defaultColumnOptions={{\n                            //     sortable: true,\n                            //     resizable: true\n                            // }}\n                            rowKeyGetter: rowKeyGetter,\n                            selectedRows: new Set(selectedIds),\n                            onSelectedRowsChange: (ids)=>setSelectedIds(Array.from(ids)),\n                            rowClass: (row, index)=>{\n                                // row.id.includes('7') || index === 0 ? highlightClassname : undefined\n                                return createdColIds.includes(row.id) ? \"bg-neutral-100 animate-pulse\" : undefined;\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 354,\n                columnNumber: 28\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TableView, \"lkqYHWO0TTxPYaLnF4z8pOFnEuk=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_3__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews,\n        _providers_page__WEBPACK_IMPORTED_MODULE_22__.usePage,\n        _providers_broadcast__WEBPACK_IMPORTED_MODULE_26__.useBroadcast,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_21__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_28__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews,\n        _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__[\"default\"]\n    ];\n});\n_c = TableView;\nconst EmptyRowsRenderer = (param)=>{\n    let { database, width } = param;\n    _s1();\n    const { createRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const addRow = async ()=>{\n        const rS = await createRecords(database.id, [\n            {}\n        ]);\n    };\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            textAlign: \"center\",\n            width: width,\n            minWidth: width,\n            overflow: \"hidden\"\n        },\n        className: \"content-center sticky left-0 top-2 w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8 flex flex-col items-center min-h-72\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExclamationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        width: 48,\n                        height: 48,\n                        className: \"inline\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: \"Database has no records\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4 flex justify-center items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                            variant: \"ghost\",\n                            className: \"text-xs h-8 font-semibold\",\n                            onClick: addRow,\n                            children: \"Add Row\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                            className: \"text-xs px-4 h-8 text-center items-center font-semibold\",\n                            onClick: ()=>setOpen(true),\n                            children: \"Import from file\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n            lineNumber: 403,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n        lineNumber: 402,\n        columnNumber: 21\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_database_importRecords__WEBPACK_IMPORTED_MODULE_25__.ImportRecords, {\n                database: database,\n                close: ()=>setOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 419,\n                columnNumber: 18\n            }, undefined),\n            width && width > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: content\n            }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        gridColumn: \"1/-1\",\n                        overflow: \"hidden\"\n                    },\n                    className: \"content-center\",\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 14\n                }, undefined)\n            }, void 0, false)\n        ]\n    }, void 0, true);\n};\n_s1(EmptyRowsRenderer, \"rC5mgFaRT9QarQc0IyDaBINFj1o=\", false, function() {\n    return [\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews\n    ];\n});\n_c1 = EmptyRowsRenderer;\nconst filterAndSortRecords = function(database, members, databaseStore, definitionFilter, filter, sortOptions, currentUserId) {\n    let currentObjectId = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"\";\n    var _processedRecords_;\n    const rows = [];\n    if (!database) return {\n        rows\n    };\n    const linkedDatabaseId = database ? Object.values(database.database.definition.columnsMap).filter((c)=>c.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked && c.databaseId).map((c)=>c.databaseId) : [];\n    const linkedDatabases = {};\n    for (const id of linkedDatabaseId){\n        const db = databaseStore[id];\n        if (db) {\n            linkedDatabases[id] = {\n                id,\n                definition: db.database.definition,\n                srcPackageName: db.database.srcPackageName,\n                recordsMap: {}\n            };\n            for (let r of Object.values(db.recordsIdMap)){\n                linkedDatabases[id].recordsMap[r.record.id] = r.record;\n            }\n        }\n    }\n    const persons = (0,_components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.membersToPersons)(members);\n    const records = Object.values(database.recordsIdMap).map((r)=>r.record);\n    console.log(\"\\uD83D\\uDD0D DEBUG - Processing records with titleFormat:\", {\n        titleFormat: database.database.definition.titleFormat,\n        recordCount: records.length,\n        databaseId: database.database.id\n    });\n    const processedRecords = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.transformRawRecords)(// const processedRecords = debugTransformRawRecords(\n    database.database.definition, records, persons, linkedDatabases);\n    console.log(\"\\uD83D\\uDD0D DEBUG - Processed records sample:\", {\n        firstRecordTitle: (_processedRecords_ = processedRecords[0]) === null || _processedRecords_ === void 0 ? void 0 : _processedRecords_.title,\n        totalRecords: processedRecords.length\n    });\n    let filtered = {\n        processedRecords,\n        records\n    };\n    if (definitionFilter && definitionFilter.conditions.length > 0) {\n        filtered = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.filterRecords)(filtered.records, filtered.processedRecords, definitionFilter, database.database.definition, currentUserId, currentObjectId);\n    }\n    if (filter && filter.conditions.length > 0) {\n        filtered = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.filterRecords)(filtered.records, filtered.processedRecords, filter, database.database.definition, currentUserId, currentObjectId);\n    }\n    if (sortOptions.length === 0) sortOptions.push({\n        columnId: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.MagicColumn.CreatedAt,\n        order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Sort.Asc\n    });\n    const sorted = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.sortRecords)(filtered.records, filtered.processedRecords, sortOptions, database.database.definition);\n    for(let i = 0; i < sorted.records.length; i++){\n        const record = sorted.records[i];\n        const processedRecord = sorted.processedRecords[i];\n        const row = {\n            id: record.id,\n            record: database.recordsIdMap[record.id].record,\n            updatedAt: new Date(record.updatedAt).toISOString(),\n            processedRecord\n        };\n        rows.push(row);\n    }\n    return {\n        rows\n    };\n};\nconst searchFilteredRecords = (search, filteredRows)=>{\n    if (!search || !search.trim()) return filteredRows;\n    return filteredRows.filter((r)=>r.processedRecord.valuesText.toLowerCase().includes(search.trim().toLowerCase()));\n};\nvar _c, _c1;\n$RefreshReg$(_c, \"TableView\");\n$RefreshReg$(_c1, \"EmptyRowsRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\n"));

/***/ })

});