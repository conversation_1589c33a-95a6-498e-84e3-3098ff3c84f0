"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_form_form_tsx",{

/***/ "(app-pages-browser)/./src/providers/workspaceSocket.tsx":
/*!*******************************************!*\
  !*** ./src/providers/workspaceSocket.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SocketTimeoutMS: function() { return /* binding */ SocketTimeoutMS; },\n/* harmony export */   WorkspaceSocketProvider: function() { return /* binding */ WorkspaceSocketProvider; },\n/* harmony export */   offlineMessage: function() { return /* binding */ offlineMessage; },\n/* harmony export */   useWorkspaceSocket: function() { return /* binding */ useWorkspaceSocket; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var socket_io_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! socket.io-client */ \"(app-pages-browser)/./node_modules/socket.io-client/build/esm/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _api_common__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/api/common */ \"(app-pages-browser)/./src/api/common.ts\");\n/* harmony import */ var _typings_workspace__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/typings/workspace */ \"(app-pages-browser)/./src/typings/workspace.ts\");\n/* harmony import */ var _typings_socket__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/typings/socket */ \"(app-pages-browser)/./src/typings/socket.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! opendb-app-db-utils/lib */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/index.js\");\n/* harmony import */ var opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_9__);\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst offlineMessage = ()=>{\n    return \"You are offline, editing is disabled until a connection is established\";\n};\nconst SocketTimeoutMS = 60000; // I minute\nconst WorkspaceSocketContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_2__.createContext)(undefined);\nconst WorkspaceSocketProvider = (props)=>{\n    _s();\n    const [isConnected, setConnected] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(\"\");\n    const [socket, setSocket] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)();\n    const { token, workspaces, isAuthenticated } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { workspace, pagesId, databasePagesId, pageStore, databasePageStore, databaseStore, updateDatabaseStore, updatePageStore, updateDatabasePageStore, updatePageViews, updateWorkspace, updateMemberSettings, url, deleteDatabase, deletePage, addDatabase, addPage, addPagePermissions, updatePagePermission, deletePagePermissions, updateMembers, members, updateDatabaseRecordValues, refreshPagesAndDatabases } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const { id } = workspace.workspace;\n    const { role } = workspace.workspaceMember;\n    const tokenStr = (token === null || token === void 0 ? void 0 : token.token) || \"\";\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const s = (0,socket_io_client__WEBPACK_IMPORTED_MODULE_1__.io)(\"\".concat(String((0,_api_common__WEBPACK_IMPORTED_MODULE_5__.apiUrl)()).replace(\"/api/v1\", \"\"), \"/workspace/\").concat(id), {\n            path: \"/ws/\",\n            auth: {\n                token: tokenStr\n            },\n            withCredentials: true,\n            transports: [\n                \"websocket\"\n            ]\n        });\n        setSocket(s);\n        s.on(\"connect_error\", (err)=>{\n            console.log(\"Connection error: \", err); // prints the message associated with the error\n            console.log(err.message); // not authorized\n            console.log(err[\"data\"]);\n            setError(err.message);\n            setConnected(false);\n        });\n        // client-side\n        s.on(\"connect\", ()=>{\n            console.log(\"Socket connected:\", s.connected); // true\n            console.log(s.id); // x8WIv7-mJelg7on_ALbx\n            setConnected(true);\n            setError(\"\");\n        });\n        //\n        s.on(\"disconnect\", ()=>{\n            console.log(\"Socket disconnected:\", s.connected); // false\n            console.log(s.id); // undefined\n            setConnected(false);\n        });\n        return ()=>{\n            s.disconnect();\n        };\n    }, [\n        isAuthenticated,\n        id,\n        tokenStr\n    ]);\n    const subscriptions = (0,react__WEBPACK_IMPORTED_MODULE_2__.useMemo)(()=>{\n        const subscriptions = [];\n        if (role !== _typings_workspace__WEBPACK_IMPORTED_MODULE_6__.WorkspaceMemberRole.Collaborator) subscriptions.push(\"shared\");\n        subscriptions.push(\"public\");\n        subscriptions.push(\"user:\".concat(workspace.workspaceMember.userId));\n        subscriptions.push(\"u:\".concat(workspace.workspaceMember.userId));\n        for (const id of pagesId)subscriptions.push(\"page:\".concat(id));\n        for (const id of databasePagesId){\n            const permissible = databasePageStore[id];\n            if (!permissible) continue;\n            subscriptions.push(\"page:\".concat(permissible.page.id));\n            subscriptions.push(\"database:\".concat(permissible.page.databaseId));\n        }\n        return subscriptions;\n    }, [\n        role,\n        workspace.workspaceMember.userId,\n        pagesId,\n        databasePagesId,\n        databasePageStore\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!socket || !isConnected) {\n            console.log(\"Socket is not connected\");\n            return;\n        }\n        socket.emit(_typings_socket__WEBPACK_IMPORTED_MODULE_7__.WorkspaceHandlerEvent.BatchSubscribe, {\n            subscriptions\n        }, function(responseData) {\n            console.log(\"Batch subscription completed with response:\", responseData);\n        });\n        console.log(\"Socket is connected, setting up subscriptions\");\n    }, [\n        isConnected,\n        socket,\n        subscriptions\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!socket || !isConnected) {\n            console.log(\"Socket is not connected\");\n            return;\n        }\n        console.log(\"Socket is connected, setting up page listeners\");\n        socket.on(\"page-updated\", (d)=>{\n            console.log(\"Page updated\", d);\n            const { id, databaseId, update } = d;\n            if (databaseId && databasePageStore[databaseId]) {\n                const { page } = databasePageStore[databaseId];\n                updateDatabasePageStore(databaseId, {\n                    page: {\n                        ...page,\n                        ...update\n                    }\n                }, true);\n            } else if (pageStore[id]) {\n                const { page } = pageStore[id];\n                updatePageStore(id, {\n                    page: {\n                        ...page,\n                        ...update\n                    }\n                }, true);\n            }\n        });\n        socket.on(\"page-view-updated\", (d)=>{\n            console.log(\"Page view updated\", d);\n            const { id, databaseId, pageId, update } = d;\n            if (databaseId && databasePageStore[databaseId]) {\n                const { views } = databasePageStore[databaseId];\n                const updated = [\n                    ...views\n                ];\n                for(let i = 0; i < updated.length; i++){\n                    const view = updated[i];\n                    if (view.id === id) {\n                        updated[i] = {\n                            ...view,\n                            ...update\n                        };\n                        break;\n                    }\n                }\n                updateDatabasePageStore(databaseId, {\n                    views: updated\n                }, true);\n            } else if (pageStore[pageId]) {\n                const { views } = pageStore[pageId];\n                const updated = [\n                    ...views\n                ];\n                for(let i = 0; i < updated.length; i++){\n                    const view = updated[i];\n                    if (view.id === id) {\n                        updated[i] = {\n                            ...view,\n                            ...update\n                        };\n                        break;\n                    }\n                }\n                updatePageStore(pageId, {\n                    views: updated\n                }, true);\n            }\n        });\n        socket.on(\"page-view-deleted\", (d)=>{\n            console.log(\"Page view deleted\", d);\n            const { id, databaseId, pageId, update } = d;\n            let parentLink = url(\"\");\n            let cb = null;\n            if (databaseId && databasePageStore[databaseId]) {\n                const { views } = databasePageStore[databaseId];\n                const updated = views.filter((v)=>v.id !== id);\n                cb = ()=>updateDatabasePageStore(databaseId, {\n                        views: updated\n                    }, true);\n                parentLink = url(\"/databases/\".concat(databaseId));\n            } else if (pageStore[pageId]) {\n                const { views } = pageStore[pageId];\n                const updated = views.filter((v)=>v.id !== id);\n                cb = ()=>updatePageStore(pageId, {\n                        views: updated\n                    }, true);\n                parentLink = url(\"/\".concat(pageId));\n            } else return;\n            const viewLink = \"\".concat(parentLink, \"/views/\").concat(id);\n            if (window.location.href.includes(viewLink)) {\n                router.push(parentLink);\n            }\n            setTimeout(cb, 250);\n        });\n        socket.on(\"page-view-added\", (d)=>{\n            console.log(\"Page view added\", d);\n            const { id, databaseId, viewsOrder, view } = d;\n            if (databaseId && databasePageStore[databaseId]) {\n                const { views } = databasePageStore[databaseId];\n                const updated = [\n                    ...views,\n                    view\n                ];\n                updatePageViews(\"database\", databaseId, viewsOrder, updated);\n            } else if (pageStore[id]) {\n                const { views } = pageStore[id];\n                const updated = [\n                    ...views,\n                    view\n                ];\n                updatePageViews(\"page\", id, viewsOrder, updated);\n            }\n        });\n        socket.on(\"page-deleted\", (d)=>{\n            console.log(\"Page deleted\", d);\n            const { id, databaseId } = d;\n            let link = url(\"\");\n            let cb = null;\n            if (databaseId) {\n                cb = ()=>deleteDatabase(databaseId);\n                link = url(\"/databases/\".concat(databaseId));\n            } else if (pageStore[id]) {\n                cb = ()=>deletePage(id);\n                link = url(\"/\".concat(id));\n            } else return;\n            if (window.location.href.includes(link)) {\n                console.log(\"Routing to workspace\");\n                router.replace(url());\n            }\n            setTimeout(cb, 400);\n        });\n        socket.on(\"database-created\", (d)=>{\n            console.log(\"Database created\", d);\n            const { database } = d;\n            addDatabase(database);\n        });\n        socket.on(\"page-created\", (d)=>{\n            console.log(\"Page created\", d);\n            const { page } = d;\n            addPage(page);\n        });\n        socket.on(\"page-permission-added\", (d)=>{\n            console.log(\"Page permission added\", d);\n            const { id, databaseId, permissions } = d;\n            addPagePermissions(id, databaseId, permissions);\n        });\n        socket.on(\"page-permission-updated\", (d)=>{\n            console.log(\"Page permission updated\", d);\n            const { id, databaseId, data } = d;\n            updatePagePermission(id, databaseId, data);\n        });\n        socket.on(\"page-permission-deleted\", (d)=>{\n            console.log(\"Page permission deleted\", d);\n            const { id, databaseId, data } = d;\n            deletePagePermissions(id, databaseId, data);\n        });\n        socket.on(\"workspace-members-added\", (d)=>{\n            console.log(\"Workspace members added\", d);\n            const newMembers = [\n                ...members,\n                ...d.members\n            ];\n            const keyMap = {};\n            for (const m of newMembers){\n                keyMap[m.user.id] = m;\n            }\n            updateMembers(Object.values(keyMap));\n        });\n        socket.on(\"column-added-or-updated\", (d)=>{\n            console.log(\"column added or updated\", d);\n            const { column, databaseId } = d;\n            if (!column.id) return;\n            const db = databaseStore[databaseId];\n            if (!db) return;\n            const database = db.database;\n            const definition = database.definition;\n            definition.columnsMap[column.id] = column;\n            if (!definition.columnIds.includes(column.id)) definition.columnIds.push(column.id);\n            updateDatabaseStore(databaseId, {\n                definition\n            }, {});\n        });\n        socket.on(\"column-deleted\", (d)=>{\n            console.log(\"column deleted\", d);\n            const { columnId, databaseId } = d;\n            const db = databaseStore[databaseId];\n            if (!db) return;\n            const database = db.database;\n            const definition = database.definition;\n            if (!definition.columnsMap[columnId]) return;\n            delete definition.columnsMap[columnId];\n            definition.columnIds = (0,opendb_app_db_utils_lib__WEBPACK_IMPORTED_MODULE_9__.removeAllArrayItem)(definition.columnIds, columnId);\n            if (definition.uniqueColumnId === columnId) definition.uniqueColumnId = \"\";\n            updateDatabaseStore(databaseId, {\n                definition\n            }, {});\n        });\n        socket.on(\"column-made-unique\", (d)=>{\n            console.log(\"column made unique\", d);\n            const { data, databaseId } = d;\n            const { columnId, unique } = data;\n            const db = databaseStore[databaseId];\n            if (!db) return;\n            const database = db.database;\n            const definition = database.definition;\n            if (unique) definition.uniqueColumnId = columnId;\n            else definition.uniqueColumnId = \"\";\n            updateDatabaseStore(databaseId, {\n                definition\n            }, {});\n        });\n        socket.on(\"database-definition-updated\", (d)=>{\n            console.log(\"\\uD83D\\uDD0D DEBUG - definition updated\", d);\n            const { data, databaseId, extra } = d;\n            const db = databaseStore[databaseId];\n            if (!db) {\n                console.log(\"\\uD83D\\uDD0D DEBUG - No database found in store for ID:\", databaseId);\n                return;\n            }\n            const database = db.database;\n            const oldDefinition = database.definition;\n            const definition = {\n                ...database.definition,\n                ...data\n            };\n            console.log(\"\\uD83D\\uDD0D DEBUG - Updating database definition:\", {\n                databaseId,\n                oldTitleFormat: oldDefinition.titleFormat,\n                newTitleFormat: definition.titleFormat,\n                updateData: data,\n                extra\n            });\n            updateDatabaseStore(databaseId, {\n                definition,\n                ...extra || {}\n            }, {});\n            console.log(\"\\uD83D\\uDD0D DEBUG - Database store updated\");\n        });\n        socket.on(\"records-updated\", (d)=>{\n            console.log(\"column values updated\", d);\n            const { databaseId, recordIds, values, meta, recordExtra } = d;\n            updateDatabaseRecordValues(databaseId, recordIds, values, meta, recordExtra, true);\n        });\n        socket.on(\"records-created\", (d)=>{\n            console.log(\"record created\", d);\n            const { records, databaseId } = d;\n            const recordsIdMap = {};\n            for (const record of records){\n                recordsIdMap[record.id] = recordsIdMap[record.id] || {};\n                // @Todo: calculate process record\n                recordsIdMap[record.id].record = record;\n            }\n            updateDatabaseStore(databaseId, {}, recordsIdMap);\n        });\n        socket.on(\"records-deleted\", (d)=>{\n            console.log(\"record deleted\", d);\n            const { ids, databaseId } = d;\n            const db = databaseStore[databaseId];\n            if (!db) return;\n            const recordsIdMap = db.recordsIdMap;\n            for (const id of ids){\n                delete recordsIdMap[id];\n            }\n            updateDatabaseStore(databaseId, {}, recordsIdMap);\n        });\n        socket.on(\"template-installed\", (d)=>{\n            console.log(\"template-installed\", d);\n            const { install } = d;\n            refreshPagesAndDatabases();\n        });\n        return ()=>{\n            console.log(\"Removing page and database listeners\");\n            socket.off(\"page-updated\");\n            socket.off(\"page-view-updated\");\n            socket.off(\"page-view-deleted\");\n            socket.off(\"page-view-added\");\n            socket.off(\"page-deleted\");\n            socket.off(\"database-created\");\n            socket.off(\"page-created\");\n            socket.off(\"page-permission-added\");\n            socket.off(\"page-permission-updated\");\n            socket.off(\"page-permission-deleted\");\n            socket.off(\"workspace-members-added\");\n            socket.off(\"column-added-or-updated\");\n            socket.off(\"column-deleted\");\n            socket.off(\"column-made-unique\");\n            socket.off(\"records-updated\");\n            socket.off(\"records-deleted\");\n            socket.off(\"records-created\");\n            socket.off(\"template-installed\");\n        };\n    // }, [addDatabase, addPage, addPagePermissions, databasePageStore, databaseStore, deleteDatabase, deletePage, deletePagePermissions, isConnected, members, pageStore, router, socket, updateDatabasePageStore, updateDatabaseRecordValues, updateDatabaseStore, updateMembers, updatePagePermission, updatePageStore, updatePageViews, url]);\n    }, [\n        socket,\n        isConnected\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        if (!socket || !isConnected) {\n            console.log(\"Socket is not connected\");\n            return;\n        }\n        console.log(\"Socket is connected, setting up workspace change & member settings listeners\");\n        socket.on(\"member-settings-updated\", (d)=>{\n            console.log(\"Member settings updated\", d);\n            updateMemberSettings(d.settings);\n        });\n        socket.on(\"workspace-updated\", (d)=>{\n            console.log(\"Workspace updated\", d);\n            updateWorkspace(d.update);\n        });\n        return ()=>{\n            socket.off(\"member-settings-updated\");\n            socket.off(\"workspace-updated\");\n        };\n    // }, [isConnected, socket, updateMemberSettings, updateWorkspace]);\n    }, [\n        socket,\n        isConnected\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WorkspaceSocketContext.Provider, {\n            value: {\n                socket,\n                error,\n                isConnected\n            },\n            children: props.children\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\providers\\\\workspaceSocket.tsx\",\n            lineNumber: 539,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s(WorkspaceSocketProvider, \"Fvj+I6aY6rESDnHlNbQee+YEav0=\", false, function() {\n    return [\n        _providers_user__WEBPACK_IMPORTED_MODULE_3__.useAuth,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_4__.useWorkspace,\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter\n    ];\n});\n_c = WorkspaceSocketProvider;\nconst useWorkspaceSocket = ()=>{\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_2__.useContext)(WorkspaceSocketContext);\n    if (!context) throw new Error(\"useWorkspaceSocket must be used within a WorkspaceSocketProvider\");\n    return context;\n};\n_s1(useWorkspaceSocket, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"WorkspaceSocketProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/providers/workspaceSocket.tsx\n"));

/***/ })

});