"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_list_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx":
/*!************************************************************!*\
  !*** ./src/components/workspace/main/views/list/index.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListView: function() { return /* binding */ ListView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_timeAgo__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/timeAgo */ \"(app-pages-browser)/./src/utils/timeAgo.ts\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/parseISO/index.js\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! opendb-app-db-utils/lib/methods/date */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/methods/date.js\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var _list_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list.css */ \"(app-pages-browser)/./src/components/workspace/main/views/list/list.css\");\n/* __next_internal_client_entry_do_not_use__ ListView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListView = (props)=>{\n    _s();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const columnPropsMap = definition.columnPropsMap || {};\n    const database = databaseStore[definition.databaseId];\n    const [selectedRecordId, setSelectedRecordId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { filter, search, setPeekRecordId } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews)();\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 33,\n        columnNumber: 27\n    }, undefined);\n    const getRows = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, [\n            {\n                columnId: \"createdAt\",\n                order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Sort.Desc\n            }\n        ], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        return (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__.searchFilteredRecords)(search, rows);\n    };\n    const rows = getRows();\n    // titleColOpts no longer needed since we use processedRecord.title\n    const handleRecordClick = (recordId)=>{\n        setSelectedRecordId(recordId);\n        setPeekRecordId(recordId);\n    };\n    let columnsOrder = [\n        ...definition.columnsOrder || []\n    ];\n    if (database.database.definition.columnIds) {\n        for (const key of database.database.definition.columnIds){\n            if (!columnsOrder.includes(key)) {\n                columnsOrder.push(key);\n            }\n        }\n    }\n    const allFields = columnsOrder.filter((id)=>{\n        var _columnPropsMap_id;\n        return !((_columnPropsMap_id = columnPropsMap[id]) === null || _columnPropsMap_id === void 0 ? void 0 : _columnPropsMap_id.isHidden) && database.database.definition.columnsMap[id];\n    }).map((id)=>database.database.definition.columnsMap[id]);\n    const fieldsToDisplay = allFields.filter((field)=>field.type !== opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files && field.type !== opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup);\n    const formatFieldValue = (row, field)=>{\n        if (row.processedRecord && row.processedRecord.processedRecordValues && row.processedRecord.processedRecordValues[field.id] !== undefined) {\n            const processedValue = row.processedRecord.processedRecordValues[field.id];\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Person || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((person)=>{\n                        if (typeof person === \"object\" && person !== null && \"title\" in person) {\n                            return person.title;\n                        }\n                        return String(person);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Select) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((option)=>{\n                        if (typeof option === \"object\" && option !== null && \"title\" in option) {\n                            return option.title;\n                        }\n                        return String(option);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Linked) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((linked)=>{\n                        if (typeof linked === \"object\" && linked !== null && \"title\" in linked) {\n                            return linked.title;\n                        }\n                        return String(linked);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.AI) {\n                if (processedValue) {\n                    return String(processedValue).substring(0, 50) + (String(processedValue).length > 50 ? \"...\" : \"\");\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup) {\n                const buttonGroupColumn = field;\n                const buttons = buttonGroupColumn.buttons || [];\n                if (buttons.length === 0) {\n                    return \"No actions\";\n                } else if (buttons.length === 1) {\n                    return buttons[0].label || \"Action\";\n                } else {\n                    return \"\".concat(buttons.length, \" actions\");\n                }\n            }\n            // Handle Files fields\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files) {\n                if (Array.isArray(processedValue)) {\n                    return \"\".concat(processedValue.length, \" file\").concat(processedValue.length !== 1 ? \"s\" : \"\");\n                }\n                return \"No files\";\n            }\n            if (processedValue !== null && processedValue !== undefined) {\n                if (typeof processedValue === \"object\") {\n                    try {\n                        return JSON.stringify(processedValue);\n                    } catch (e) {\n                        return \"[Object]\";\n                    }\n                }\n                return String(processedValue);\n            }\n        }\n        if (!row.record.recordValues || row.record.recordValues[field.id] === undefined) {\n            return \"-\";\n        }\n        const value = row.record.recordValues[field.id];\n        if (value === null || value === undefined) {\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Text) {\n            const formattedValue = (0,_components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__.getTextBasedColFormattedValue)(field, String(value));\n            return formattedValue.displayValue || String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UUID) {\n            return String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Checkbox || typeof value === \"boolean\") {\n            return value ? \"Yes\" : \"No\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Date || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedAt || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedAt) {\n            try {\n                let date;\n                if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedAt) {\n                    date = new Date(row.record.createdAt);\n                } else if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedAt) {\n                    date = new Date(row.record.updatedAt);\n                } else if (typeof value === \"string\" || typeof value === \"number\") {\n                    date = new Date(value);\n                } else {\n                    return \"-\";\n                }\n                if ((0,opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__.isDateObjValid)(date)) {\n                    return (0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])((0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(date.toISOString()), \"MMM d, yyyy\");\n                }\n            } catch (e) {\n                return String(value);\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Person) {\n            if (typeof value === \"string\" || typeof value === \"number\") {\n                const personId = String(value);\n                const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === personId);\n                return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : personId;\n            }\n            if (Array.isArray(value)) {\n                return value.map((personId)=>{\n                    const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === String(personId));\n                    return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : String(personId);\n                }).join(\", \");\n            }\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n            let userId;\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy) {\n                userId = row.record.createdById;\n            } else if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n                userId = row.record.updatedById;\n            } else if (typeof value === \"string\" || typeof value === \"number\") {\n                userId = String(value);\n            } else if (value && typeof value === \"object\" && \"userId\" in value) {\n                userId = String(value.userId);\n            }\n            if (userId) {\n                const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === userId);\n                return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : userId;\n            }\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Select) {\n            if (Array.isArray(value)) {\n                if (field.options && Array.isArray(field.options)) {\n                    return value.map((v)=>{\n                        const option = field.options.find((opt)=>opt.value === v);\n                        return option ? option.label : v;\n                    }).join(\", \");\n                }\n                return value.join(\", \");\n            }\n            if (field.options && Array.isArray(field.options)) {\n                const option = field.options.find((opt)=>opt.value === value);\n                return option ? option.label : value;\n            }\n            return String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Linked) {\n            if (Array.isArray(value)) {\n                if (field.linkedDatabase && databaseStore[field.linkedDatabase]) {\n                    const linkedDb = databaseStore[field.linkedDatabase];\n                    if (linkedDb.recordsIdMap) {\n                        return value.map((id)=>{\n                            const recordData = linkedDb.recordsIdMap[id];\n                            if (!recordData) return String(id);\n                            const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getDatabaseTitleCol)(linkedDb.database);\n                            return (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getRecordTitle)(recordData.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n                        }).join(\", \");\n                    }\n                }\n                return value.map((id)=>String(id)).join(\", \");\n            }\n            if (field.linkedDatabase && databaseStore[field.linkedDatabase]) {\n                const linkedDb = databaseStore[field.linkedDatabase];\n                if (linkedDb.recordsIdMap && value) {\n                    const recordData = linkedDb.recordsIdMap[value];\n                    if (!recordData) return String(value);\n                    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getDatabaseTitleCol)(linkedDb.database);\n                    return (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getRecordTitle)(recordData.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n                }\n            }\n            return value ? String(value) : \"-\";\n        }\n        // Handle Number fields\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Number) {\n            const numValue = Number(value);\n            if (!isNaN(numValue)) {\n                if (field.format === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.NumberColumnFormat.Currency && field.currency) {\n                    return (0,_components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__.formatCurrency)(numValue, field.currency);\n                }\n                if (field.format === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.NumberColumnFormat.Percentage) {\n                    return \"\".concat(numValue, \"%\");\n                }\n                return numValue.toString();\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.AI) {\n            if (value) {\n                return String(value).substring(0, 50) + (String(value).length > 50 ? \"...\" : \"\");\n            }\n            const recordMeta = row.record.meta || {};\n            const recordColMeta = recordMeta[\"column:\".concat(field.id)];\n            if (recordColMeta && recordColMeta.value) {\n                return String(recordColMeta.value).substring(0, 50) + (String(recordColMeta.value).length > 50 ? \"...\" : \"\");\n            }\n            return \"AI content\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup) {\n            const buttonGroupColumn = field;\n            const buttons = buttonGroupColumn.buttons || [];\n            if (buttons.length === 0) {\n                return \"No actions\";\n            } else if (buttons.length === 1) {\n                return buttons[0].label || \"Action\";\n            } else {\n                return \"\".concat(buttons.length, \" actions\");\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files) {\n            if (Array.isArray(value)) {\n                return \"\".concat(value.length, \" file\").concat(value.length !== 1 ? \"s\" : \"\");\n            }\n            return \"No files\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Derived) {\n            if (value) {\n                return String(value);\n            }\n            return \"Derived value\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Summarize) {\n            if (value) {\n                return String(value);\n            }\n            return \"Summary\";\n        }\n        if (Array.isArray(value)) {\n            return value.map((item)=>item !== null && item !== undefined ? String(item) : \"-\").join(\", \");\n        }\n        if (typeof value === \"object\" && value !== null) {\n            try {\n                return JSON.stringify(value);\n            } catch (e) {\n                return \"[Object]\";\n            }\n        }\n        return value !== null && value !== undefined ? String(value) : \"-\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full overflow-hidden listView\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-hidden size-full flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                    className: \"w-full h-full scrollBlockChild\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scroll-container pb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b rowGrid border-neutral-200 header-row\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-black font-semibold bg-white check !w-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-black font-semibold bg-white fluid\",\n                                        children: \"Title\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black font-semibold bg-white\",\n                                            children: field.title\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 37\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 29\n                            }, undefined),\n                            rows.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: \"No records found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 37\n                            }, undefined) : rows.map((row)=>{\n                                // Use the processed title from transformRawRecords instead of getRecordTitle\n                                const title = row.processedRecord.title || \"Untitled\";\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"rowGrid border-b hover:bg-neutral-100 cursor-pointer\", selectedRecordId === row.id ? \"bg-blue-50\" : \"\"),\n                                    onClick: ()=>handleRecordClick(row.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs check !w-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 45\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs flex flex-col gap-1 fluid\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"title-text text-xs font-semibold\",\n                                                    children: title || \"Untitled\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 text-xs text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: database.database.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: (0,_utils_timeAgo__WEBPACK_IMPORTED_MODULE_10__.timeAgo)(new Date(row.updatedAt))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 45\n                                        }, undefined),\n                                        fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs truncate\",\n                                                children: formatFieldValue(row, field)\n                                            }, field.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 49\n                                            }, undefined))\n                                    ]\n                                }, row.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 41\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                lineNumber: 385,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n            lineNumber: 384,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 383,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ListView, \"OmGhde1YkSXjrGzkJNmblhKb9Kw=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews\n    ];\n});\n_c = ListView;\nvar _c;\n$RefreshReg$(_c, \"ListView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx\n"));

/***/ })

});