"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_list_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx":
/*!************************************************************!*\
  !*** ./src/components/workspace/main/views/list/index.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListView: function() { return /* binding */ ListView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_timeAgo__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/timeAgo */ \"(app-pages-browser)/./src/utils/timeAgo.ts\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/parseISO/index.js\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! opendb-app-db-utils/lib/methods/date */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/methods/date.js\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var _list_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list.css */ \"(app-pages-browser)/./src/components/workspace/main/views/list/list.css\");\n/* __next_internal_client_entry_do_not_use__ ListView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListView = (props)=>{\n    _s();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const columnPropsMap = definition.columnPropsMap || {};\n    const database = databaseStore[definition.databaseId];\n    const [selectedRecordId, setSelectedRecordId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { filter, search, setPeekRecordId } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews)();\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 33,\n        columnNumber: 27\n    }, undefined);\n    const getRows = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, [\n            {\n                columnId: \"createdAt\",\n                order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Sort.Desc\n            }\n        ], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        return (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__.searchFilteredRecords)(search, rows);\n    };\n    const rows = getRows();\n    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getDatabaseTitleCol)(database.database);\n    const handleRecordClick = (recordId)=>{\n        setSelectedRecordId(recordId);\n        setPeekRecordId(recordId);\n    };\n    let columnsOrder = [\n        ...definition.columnsOrder || []\n    ];\n    if (database.database.definition.columnIds) {\n        for (const key of database.database.definition.columnIds){\n            if (!columnsOrder.includes(key)) {\n                columnsOrder.push(key);\n            }\n        }\n    }\n    const allFields = columnsOrder.filter((id)=>{\n        var _columnPropsMap_id;\n        return !((_columnPropsMap_id = columnPropsMap[id]) === null || _columnPropsMap_id === void 0 ? void 0 : _columnPropsMap_id.isHidden) && database.database.definition.columnsMap[id];\n    }).map((id)=>database.database.definition.columnsMap[id]);\n    const fieldsToDisplay = allFields.filter((field)=>field.type !== opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files && field.type !== opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup);\n    const formatFieldValue = (row, field)=>{\n        if (row.processedRecord && row.processedRecord.processedRecordValues && row.processedRecord.processedRecordValues[field.id] !== undefined) {\n            const processedValue = row.processedRecord.processedRecordValues[field.id];\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Person || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((person)=>{\n                        if (typeof person === \"object\" && person !== null && \"title\" in person) {\n                            return person.title;\n                        }\n                        return String(person);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Select) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((option)=>{\n                        if (typeof option === \"object\" && option !== null && \"title\" in option) {\n                            return option.title;\n                        }\n                        return String(option);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Linked) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((linked)=>{\n                        if (typeof linked === \"object\" && linked !== null && \"title\" in linked) {\n                            return linked.title;\n                        }\n                        return String(linked);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.AI) {\n                if (processedValue) {\n                    return String(processedValue).substring(0, 50) + (String(processedValue).length > 50 ? \"...\" : \"\");\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup) {\n                const buttonGroupColumn = field;\n                const buttons = buttonGroupColumn.buttons || [];\n                if (buttons.length === 0) {\n                    return \"No actions\";\n                } else if (buttons.length === 1) {\n                    return buttons[0].label || \"Action\";\n                } else {\n                    return \"\".concat(buttons.length, \" actions\");\n                }\n            }\n            // Handle Files fields\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files) {\n                if (Array.isArray(processedValue)) {\n                    return \"\".concat(processedValue.length, \" file\").concat(processedValue.length !== 1 ? \"s\" : \"\");\n                }\n                return \"No files\";\n            }\n            if (processedValue !== null && processedValue !== undefined) {\n                if (typeof processedValue === \"object\") {\n                    try {\n                        return JSON.stringify(processedValue);\n                    } catch (e) {\n                        return \"[Object]\";\n                    }\n                }\n                return String(processedValue);\n            }\n        }\n        if (!row.record.recordValues || row.record.recordValues[field.id] === undefined) {\n            return \"-\";\n        }\n        const value = row.record.recordValues[field.id];\n        if (value === null || value === undefined) {\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Text) {\n            const formattedValue = (0,_components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__.getTextBasedColFormattedValue)(field, String(value));\n            return formattedValue.displayValue || String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UUID) {\n            return String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Checkbox || typeof value === \"boolean\") {\n            return value ? \"Yes\" : \"No\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Date || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedAt || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedAt) {\n            try {\n                let date;\n                if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedAt) {\n                    date = new Date(row.record.createdAt);\n                } else if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedAt) {\n                    date = new Date(row.record.updatedAt);\n                } else if (typeof value === \"string\" || typeof value === \"number\") {\n                    date = new Date(value);\n                } else {\n                    return \"-\";\n                }\n                if ((0,opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__.isDateObjValid)(date)) {\n                    return (0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])((0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(date.toISOString()), \"MMM d, yyyy\");\n                }\n            } catch (e) {\n                return String(value);\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Person) {\n            if (typeof value === \"string\" || typeof value === \"number\") {\n                const personId = String(value);\n                const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === personId);\n                return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : personId;\n            }\n            if (Array.isArray(value)) {\n                return value.map((personId)=>{\n                    const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === String(personId));\n                    return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : String(personId);\n                }).join(\", \");\n            }\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n            let userId;\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy) {\n                userId = row.record.createdById;\n            } else if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n                userId = row.record.updatedById;\n            } else if (typeof value === \"string\" || typeof value === \"number\") {\n                userId = String(value);\n            } else if (value && typeof value === \"object\" && \"userId\" in value) {\n                userId = String(value.userId);\n            }\n            if (userId) {\n                const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === userId);\n                return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : userId;\n            }\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Select) {\n            if (Array.isArray(value)) {\n                if (field.options && Array.isArray(field.options)) {\n                    return value.map((v)=>{\n                        const option = field.options.find((opt)=>opt.value === v);\n                        return option ? option.label : v;\n                    }).join(\", \");\n                }\n                return value.join(\", \");\n            }\n            if (field.options && Array.isArray(field.options)) {\n                const option = field.options.find((opt)=>opt.value === value);\n                return option ? option.label : value;\n            }\n            return String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Linked) {\n            if (Array.isArray(value)) {\n                if (field.linkedDatabase && databaseStore[field.linkedDatabase]) {\n                    const linkedDb = databaseStore[field.linkedDatabase];\n                    if (linkedDb.recordsIdMap) {\n                        return value.map((id)=>{\n                            const recordData = linkedDb.recordsIdMap[id];\n                            if (!recordData) return String(id);\n                            const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getDatabaseTitleCol)(linkedDb.database);\n                            return (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getRecordTitle)(recordData.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n                        }).join(\", \");\n                    }\n                }\n                return value.map((id)=>String(id)).join(\", \");\n            }\n            if (field.linkedDatabase && databaseStore[field.linkedDatabase]) {\n                const linkedDb = databaseStore[field.linkedDatabase];\n                if (linkedDb.recordsIdMap && value) {\n                    const recordData = linkedDb.recordsIdMap[value];\n                    if (!recordData) return String(value);\n                    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getDatabaseTitleCol)(linkedDb.database);\n                    return (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getRecordTitle)(recordData.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n                }\n            }\n            return value ? String(value) : \"-\";\n        }\n        // Handle Number fields\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Number) {\n            const numValue = Number(value);\n            if (!isNaN(numValue)) {\n                if (field.format === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.NumberColumnFormat.Currency && field.currency) {\n                    return (0,_components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__.formatCurrency)(numValue, field.currency);\n                }\n                if (field.format === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.NumberColumnFormat.Percentage) {\n                    return \"\".concat(numValue, \"%\");\n                }\n                return numValue.toString();\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.AI) {\n            if (value) {\n                return String(value).substring(0, 50) + (String(value).length > 50 ? \"...\" : \"\");\n            }\n            const recordMeta = row.record.meta || {};\n            const recordColMeta = recordMeta[\"column:\".concat(field.id)];\n            if (recordColMeta && recordColMeta.value) {\n                return String(recordColMeta.value).substring(0, 50) + (String(recordColMeta.value).length > 50 ? \"...\" : \"\");\n            }\n            return \"AI content\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup) {\n            const buttonGroupColumn = field;\n            const buttons = buttonGroupColumn.buttons || [];\n            if (buttons.length === 0) {\n                return \"No actions\";\n            } else if (buttons.length === 1) {\n                return buttons[0].label || \"Action\";\n            } else {\n                return \"\".concat(buttons.length, \" actions\");\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files) {\n            if (Array.isArray(value)) {\n                return \"\".concat(value.length, \" file\").concat(value.length !== 1 ? \"s\" : \"\");\n            }\n            return \"No files\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Derived) {\n            if (value) {\n                return String(value);\n            }\n            return \"Derived value\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Summarize) {\n            if (value) {\n                return String(value);\n            }\n            return \"Summary\";\n        }\n        if (Array.isArray(value)) {\n            return value.map((item)=>item !== null && item !== undefined ? String(item) : \"-\").join(\", \");\n        }\n        if (typeof value === \"object\" && value !== null) {\n            try {\n                return JSON.stringify(value);\n            } catch (e) {\n                return \"[Object]\";\n            }\n        }\n        return value !== null && value !== undefined ? String(value) : \"-\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full overflow-hidden listView\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-hidden size-full flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                    className: \"w-full h-full scrollBlockChild\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scroll-container pb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b rowGrid border-neutral-200 header-row\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-black font-semibold bg-white check !w-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-black font-semibold bg-white fluid\",\n                                        children: \"Title\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black font-semibold bg-white\",\n                                            children: field.title\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 37\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 29\n                            }, undefined),\n                            rows.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: \"No records found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 37\n                            }, undefined) : rows.map((row)=>{\n                                // Use the processed title from transformRawRecords instead of getRecordTitle\n                                const title = row.processedRecord.title || \"Untitled\";\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"rowGrid border-b hover:bg-neutral-100 cursor-pointer\", selectedRecordId === row.id ? \"bg-blue-50\" : \"\"),\n                                    onClick: ()=>handleRecordClick(row.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs check !w-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 45\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs flex flex-col gap-1 fluid\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"title-text text-xs font-semibold\",\n                                                    children: title || \"Untitled\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 text-xs text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: database.database.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: (0,_utils_timeAgo__WEBPACK_IMPORTED_MODULE_10__.timeAgo)(new Date(row.updatedAt))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 45\n                                        }, undefined),\n                                        fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs truncate\",\n                                                children: formatFieldValue(row, field)\n                                            }, field.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 49\n                                            }, undefined))\n                                    ]\n                                }, row.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 41\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                lineNumber: 385,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n            lineNumber: 384,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 383,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ListView, \"OmGhde1YkSXjrGzkJNmblhKb9Kw=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews\n    ];\n});\n_c = ListView;\nvar _c;\n$RefreshReg$(_c, \"ListView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx\n"));

/***/ })

});