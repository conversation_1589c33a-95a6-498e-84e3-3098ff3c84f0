"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_list_index_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx":
/*!************************************************************!*\
  !*** ./src/components/workspace/main/views/list/index.tsx ***!
  \************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListView: function() { return /* binding */ ListView; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var _components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table */ \"(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\");\n/* harmony import */ var _components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/scroll-area */ \"(app-pages-browser)/./src/components/ui/scroll-area.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _utils_timeAgo__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/utils/timeAgo */ \"(app-pages-browser)/./src/utils/timeAgo.ts\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/format/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/esm/parseISO/index.js\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! opendb-app-db-utils/lib/methods/date */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/methods/date.js\");\n/* harmony import */ var opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var _list_css__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./list.css */ \"(app-pages-browser)/./src/components/workspace/main/views/list/list.css\");\n/* __next_internal_client_entry_do_not_use__ ListView auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ListView = (props)=>{\n    _s();\n    const { databaseStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace)();\n    const { definition } = props;\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const columnPropsMap = definition.columnPropsMap || {};\n    const database = databaseStore[definition.databaseId];\n    const [selectedRecordId, setSelectedRecordId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { filter, search, setPeekRecordId } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews)();\n    if (!database) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_4__.PageLoader, {\n        size: \"full\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 33,\n        columnNumber: 27\n    }, undefined);\n    const getRows = ()=>{\n        var _workspace_workspaceMember, _database_database;\n        if (!database) return [];\n        const { rows } = (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__.filterAndSortRecords)(database, members, databaseStore, definition.filter || {\n            match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Match.All,\n            conditions: []\n        }, filter, [\n            {\n                columnId: \"createdAt\",\n                order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.Sort.Desc\n            }\n        ], (workspace === null || workspace === void 0 ? void 0 : (_workspace_workspaceMember = workspace.workspaceMember) === null || _workspace_workspaceMember === void 0 ? void 0 : _workspace_workspaceMember.userId) || \"\", (database === null || database === void 0 ? void 0 : (_database_database = database.database) === null || _database_database === void 0 ? void 0 : _database_database.id) || \"\");\n        return (0,_components_workspace_main_views_table__WEBPACK_IMPORTED_MODULE_6__.searchFilteredRecords)(search, rows);\n    };\n    const rows = getRows();\n    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getDatabaseTitleCol)(database.database);\n    const handleRecordClick = (recordId)=>{\n        setSelectedRecordId(recordId);\n        setPeekRecordId(recordId);\n    };\n    let columnsOrder = [\n        ...definition.columnsOrder || []\n    ];\n    if (database.database.definition.columnIds) {\n        for (const key of database.database.definition.columnIds){\n            if (!columnsOrder.includes(key)) {\n                columnsOrder.push(key);\n            }\n        }\n    }\n    const allFields = columnsOrder.filter((id)=>{\n        var _columnPropsMap_id;\n        return !((_columnPropsMap_id = columnPropsMap[id]) === null || _columnPropsMap_id === void 0 ? void 0 : _columnPropsMap_id.isHidden) && database.database.definition.columnsMap[id];\n    }).map((id)=>database.database.definition.columnsMap[id]);\n    const fieldsToDisplay = allFields.filter((field)=>field.type !== opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files && field.type !== opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup);\n    const formatFieldValue = (row, field)=>{\n        if (row.processedRecord && row.processedRecord.processedRecordValues && row.processedRecord.processedRecordValues[field.id] !== undefined) {\n            const processedValue = row.processedRecord.processedRecordValues[field.id];\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Person || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((person)=>{\n                        if (typeof person === \"object\" && person !== null && \"title\" in person) {\n                            return person.title;\n                        }\n                        return String(person);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Select) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((option)=>{\n                        if (typeof option === \"object\" && option !== null && \"title\" in option) {\n                            return option.title;\n                        }\n                        return String(option);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Linked) {\n                if (Array.isArray(processedValue)) {\n                    return processedValue.map((linked)=>{\n                        if (typeof linked === \"object\" && linked !== null && \"title\" in linked) {\n                            return linked.title;\n                        }\n                        return String(linked);\n                    }).join(\", \");\n                } else if (processedValue && typeof processedValue === \"object\" && \"title\" in processedValue) {\n                    return processedValue.title;\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.AI) {\n                if (processedValue) {\n                    return String(processedValue).substring(0, 50) + (String(processedValue).length > 50 ? \"...\" : \"\");\n                }\n            }\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup) {\n                const buttonGroupColumn = field;\n                const buttons = buttonGroupColumn.buttons || [];\n                if (buttons.length === 0) {\n                    return \"No actions\";\n                } else if (buttons.length === 1) {\n                    return buttons[0].label || \"Action\";\n                } else {\n                    return \"\".concat(buttons.length, \" actions\");\n                }\n            }\n            // Handle Files fields\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files) {\n                if (Array.isArray(processedValue)) {\n                    return \"\".concat(processedValue.length, \" file\").concat(processedValue.length !== 1 ? \"s\" : \"\");\n                }\n                return \"No files\";\n            }\n            if (processedValue !== null && processedValue !== undefined) {\n                if (typeof processedValue === \"object\") {\n                    try {\n                        return JSON.stringify(processedValue);\n                    } catch (e) {\n                        return \"[Object]\";\n                    }\n                }\n                return String(processedValue);\n            }\n        }\n        if (!row.record.recordValues || row.record.recordValues[field.id] === undefined) {\n            return \"-\";\n        }\n        const value = row.record.recordValues[field.id];\n        if (value === null || value === undefined) {\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Text) {\n            const formattedValue = (0,_components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__.getTextBasedColFormattedValue)(field, String(value));\n            return formattedValue.displayValue || String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UUID) {\n            return String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Checkbox || typeof value === \"boolean\") {\n            return value ? \"Yes\" : \"No\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Date || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedAt || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedAt) {\n            try {\n                let date;\n                if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedAt) {\n                    date = new Date(row.record.createdAt);\n                } else if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedAt) {\n                    date = new Date(row.record.updatedAt);\n                } else if (typeof value === \"string\" || typeof value === \"number\") {\n                    date = new Date(value);\n                } else {\n                    return \"-\";\n                }\n                if ((0,opendb_app_db_utils_lib_methods_date__WEBPACK_IMPORTED_MODULE_11__.isDateObjValid)(date)) {\n                    return (0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_14__[\"default\"])((0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_15__[\"default\"])(date.toISOString()), \"MMM d, yyyy\");\n                }\n            } catch (e) {\n                return String(value);\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Person) {\n            if (typeof value === \"string\" || typeof value === \"number\") {\n                const personId = String(value);\n                const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === personId);\n                return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : personId;\n            }\n            if (Array.isArray(value)) {\n                return value.map((personId)=>{\n                    const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === String(personId));\n                    return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : String(personId);\n                }).join(\", \");\n            }\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy || field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n            let userId;\n            if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.CreatedBy) {\n                userId = row.record.createdById;\n            } else if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.UpdatedBy) {\n                userId = row.record.updatedById;\n            } else if (typeof value === \"string\" || typeof value === \"number\") {\n                userId = String(value);\n            } else if (value && typeof value === \"object\" && \"userId\" in value) {\n                userId = String(value.userId);\n            }\n            if (userId) {\n                const person = members === null || members === void 0 ? void 0 : members.find((member)=>member.user.id === userId);\n                return person ? \"\".concat(person.user.firstName, \" \").concat(person.user.lastName).trim() || person.user.email : userId;\n            }\n            return \"-\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Select) {\n            if (Array.isArray(value)) {\n                if (field.options && Array.isArray(field.options)) {\n                    return value.map((v)=>{\n                        const option = field.options.find((opt)=>opt.value === v);\n                        return option ? option.label : v;\n                    }).join(\", \");\n                }\n                return value.join(\", \");\n            }\n            if (field.options && Array.isArray(field.options)) {\n                const option = field.options.find((opt)=>opt.value === value);\n                return option ? option.label : value;\n            }\n            return String(value);\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Linked) {\n            if (Array.isArray(value)) {\n                if (field.linkedDatabase && databaseStore[field.linkedDatabase]) {\n                    const linkedDb = databaseStore[field.linkedDatabase];\n                    if (linkedDb.recordsIdMap) {\n                        return value.map((id)=>{\n                            const recordData = linkedDb.recordsIdMap[id];\n                            if (!recordData) return String(id);\n                            const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getDatabaseTitleCol)(linkedDb.database);\n                            return (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getRecordTitle)(recordData.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n                        }).join(\", \");\n                    }\n                }\n                return value.map((id)=>String(id)).join(\", \");\n            }\n            if (field.linkedDatabase && databaseStore[field.linkedDatabase]) {\n                const linkedDb = databaseStore[field.linkedDatabase];\n                if (linkedDb.recordsIdMap && value) {\n                    const recordData = linkedDb.recordsIdMap[value];\n                    if (!recordData) return String(value);\n                    const titleColOpts = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getDatabaseTitleCol)(linkedDb.database);\n                    return (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_8__.getRecordTitle)(recordData.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts);\n                }\n            }\n            return value ? String(value) : \"-\";\n        }\n        // Handle Number fields\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Number) {\n            const numValue = Number(value);\n            if (!isNaN(numValue)) {\n                if (field.format === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.NumberColumnFormat.Currency && field.currency) {\n                    return (0,_components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_12__.formatCurrency)(numValue, field.currency);\n                }\n                if (field.format === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.NumberColumnFormat.Percentage) {\n                    return \"\".concat(numValue, \"%\");\n                }\n                return numValue.toString();\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.AI) {\n            if (value) {\n                return String(value).substring(0, 50) + (String(value).length > 50 ? \"...\" : \"\");\n            }\n            const recordMeta = row.record.meta || {};\n            const recordColMeta = recordMeta[\"column:\".concat(field.id)];\n            if (recordColMeta && recordColMeta.value) {\n                return String(recordColMeta.value).substring(0, 50) + (String(recordColMeta.value).length > 50 ? \"...\" : \"\");\n            }\n            return \"AI content\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.ButtonGroup) {\n            const buttonGroupColumn = field;\n            const buttons = buttonGroupColumn.buttons || [];\n            if (buttons.length === 0) {\n                return \"No actions\";\n            } else if (buttons.length === 1) {\n                return buttons[0].label || \"Action\";\n            } else {\n                return \"\".concat(buttons.length, \" actions\");\n            }\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Files) {\n            if (Array.isArray(value)) {\n                return \"\".concat(value.length, \" file\").concat(value.length !== 1 ? \"s\" : \"\");\n            }\n            return \"No files\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Derived) {\n            if (value) {\n                return String(value);\n            }\n            return \"Derived value\";\n        }\n        if (field.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_3__.DatabaseFieldDataType.Summarize) {\n            if (value) {\n                return String(value);\n            }\n            return \"Summary\";\n        }\n        if (Array.isArray(value)) {\n            return value.map((item)=>item !== null && item !== undefined ? String(item) : \"-\").join(\", \");\n        }\n        if (typeof value === \"object\" && value !== null) {\n            try {\n                return JSON.stringify(value);\n            } catch (e) {\n                return \"[Object]\";\n            }\n        }\n        return value !== null && value !== undefined ? String(value) : \"-\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-full overflow-hidden listView\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"overflow-hidden size-full flex flex-col\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_scroll_area__WEBPACK_IMPORTED_MODULE_7__.ScrollArea, {\n                    className: \"w-full h-full scrollBlockChild\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"scroll-container pb-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b rowGrid border-neutral-200 header-row\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-black font-semibold bg-white check !w-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-xs text-black font-semibold bg-white fluid\",\n                                        children: \"Title\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 33\n                                    }, undefined),\n                                    fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-black font-semibold bg-white\",\n                                            children: field.title\n                                        }, field.id, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 37\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 29\n                            }, undefined),\n                            rows.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-8 text-gray-500\",\n                                children: \"No records found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 37\n                            }, undefined) : rows.map((row)=>{\n                                // Use the processed title from transformRawRecords instead of getRecordTitle\n                                const title = row.title || \"Untitled\";\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_9__.cn)(\"rowGrid border-b hover:bg-neutral-100 cursor-pointer\", selectedRecordId === row.id ? \"bg-blue-50\" : \"\"),\n                                    onClick: ()=>handleRecordClick(row.id),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs check !w-1\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 45\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs flex flex-col gap-1 fluid\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"title-text text-xs font-semibold\",\n                                                    children: title || \"Untitled\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 49\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 text-xs text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: database.database.name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 424,\n                                                            columnNumber: 53\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: (0,_utils_timeAgo__WEBPACK_IMPORTED_MODULE_10__.timeAgo)(new Date(row.updatedAt))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                            lineNumber: 425,\n                                                            columnNumber: 53\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 49\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 45\n                                        }, undefined),\n                                        fieldsToDisplay.map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs truncate\",\n                                                children: formatFieldValue(row, field)\n                                            }, field.id, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 49\n                                            }, undefined))\n                                    ]\n                                }, row.id, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 41\n                                }, undefined);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 25\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 21\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n                lineNumber: 385,\n                columnNumber: 17\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n            lineNumber: 384,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\list\\\\index.tsx\",\n        lineNumber: 383,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ListView, \"OmGhde1YkSXjrGzkJNmblhKb9Kw=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_2__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_5__.useViews\n    ];\n});\n_c = ListView;\nvar _c;\n$RefreshReg$(_c, \"ListView\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/list/index.tsx\n"));

/***/ })

});