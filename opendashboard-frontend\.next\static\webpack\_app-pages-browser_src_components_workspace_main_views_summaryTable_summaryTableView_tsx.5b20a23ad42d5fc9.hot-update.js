"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_workspace_main_views_summaryTable_summaryTableView_tsx",{

/***/ "(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx":
/*!*************************************************************!*\
  !*** ./src/components/workspace/main/views/table/index.tsx ***!
  \*************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TableView: function() { return /* binding */ TableView; },\n/* harmony export */   filterAndSortRecords: function() { return /* binding */ filterAndSortRecords; },\n/* harmony export */   searchFilteredRecords: function() { return /* binding */ searchFilteredRecords; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _table_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./table.css */ \"(app-pages-browser)/./src/components/workspace/main/views/table/table.css\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var react_data_grid_lib_styles_css__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-data-grid/lib/styles.css */ \"(app-pages-browser)/./node_modules/react-data-grid/lib/styles.css\");\n/* harmony import */ var react_data_grid__WEBPACK_IMPORTED_MODULE_31__ = __webpack_require__(/*! react-data-grid */ \"(app-pages-browser)/./node_modules/react-data-grid/lib/bundle.js\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/text */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/text.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_header__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/header */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/header.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/checkbox */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/checkbox.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/date */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/date.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/person */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/person.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/files */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/files.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/addColumn */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/addColumn.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_common_selectRow__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/common/selectRow */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/common/selectRow.tsx\");\n/* harmony import */ var _components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/custom-ui/loader */ \"(app-pages-browser)/./src/components/custom-ui/loader.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/ai */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/ai.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/select */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/select.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/linked.tsx\");\n/* harmony import */ var _providers_views__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/providers/views */ \"(app-pages-browser)/./src/providers/views.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! opendb-app-db-utils/lib/utils/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/utils/db.js\");\n/* harmony import */ var _components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/components/workspace/main/views/common/contentLocked */ \"(app-pages-browser)/./src/components/workspace/main/views/common/contentLocked.tsx\");\n/* harmony import */ var _providers_shared__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/providers/shared */ \"(app-pages-browser)/./src/providers/shared.tsx\");\n/* harmony import */ var _providers_page__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @/providers/page */ \"(app-pages-browser)/./src/providers/page.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/summarize */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/summarize.tsx\");\n/* harmony import */ var _barrel_optimize_names_ExclamationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_32__ = __webpack_require__(/*! __barrel_optimize__?names=ExclamationCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationCircleIcon.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_workspace_main_database_importRecords__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @/components/workspace/main/database/importRecords */ \"(app-pages-browser)/./src/components/workspace/main/database/importRecords.tsx\");\n/* harmony import */ var _providers_broadcast__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! @/providers/broadcast */ \"(app-pages-browser)/./src/providers/broadcast.tsx\");\n/* harmony import */ var _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! @/components/custom-ui/forceRender */ \"(app-pages-browser)/./src/components/custom-ui/forceRender.tsx\");\n/* harmony import */ var _providers_template__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! @/providers/template */ \"(app-pages-browser)/./src/providers/template.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/buttonGroup */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/buttonGroup.tsx\");\n/* harmony import */ var _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_30__ = __webpack_require__(/*! @/components/workspace/main/views/table/renderer/fields/scannableCode */ \"(app-pages-browser)/./src/components/workspace/main/views/table/renderer/fields/scannableCode.tsx\");\n\nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// DerivedFormulaEditor is used in HeaderRenderer, imported there directly\n\n\nconst TableView = (props)=>{\n    var _containerEle_current;\n    _s();\n    const { databaseStore, databaseErrorStore, members, workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_3__.useWorkspace)();\n    const { definition } = props;\n    const { cache } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const { accessLevel } = (0,_providers_page__WEBPACK_IMPORTED_MODULE_22__.usePage)();\n    const { registerListener } = (0,_providers_broadcast__WEBPACK_IMPORTED_MODULE_26__.useBroadcast)();\n    const maybeShared = (0,_providers_shared__WEBPACK_IMPORTED_MODULE_21__.useMaybeShared)();\n    definition.filter = definition.filter || {\n        conditions: [],\n        match: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Match.All\n    };\n    definition.sorts = definition.sorts || [];\n    const databaseId = definition.databaseId;\n    const database = databaseStore[definition.databaseId];\n    const isPublishedView = !!maybeShared;\n    const editable = !definition.lockContent && !isPublishedView && !!accessLevel;\n    const maybeTemplate = (0,_providers_template__WEBPACK_IMPORTED_MODULE_28__.useMaybeTemplate)();\n    let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);\n    const { selectedIds, setSelectedIds, sorts, filter, search } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const containerEle = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    let scrollToRowId = -1;\n    const editColIdRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(\"\");\n    let editColumnTrigger = false;\n    const getColDefs = ()=>{\n        const columns = [];\n        // const autoEdit: {\n        //     databaseId: string,\n        //     columnId: string\n        // } | null = cache.getCache(DatabaseConstants.NewlyCreatedColumnKey)\n        if (!database) return columns;\n        const dbDefinition = database.database.definition;\n        if (!dbDefinition) return columns;\n        let { columnsOrder, columnPropsMap } = definition;\n        columnsOrder = Array.isArray(columnsOrder) ? columnsOrder : [];\n        columnPropsMap = columnPropsMap || {};\n        for (const key of dbDefinition.columnIds){\n            if (!columnsOrder.includes(key)) columnsOrder.push(key);\n            if (!columnPropsMap[key]) columnPropsMap[key] = {};\n        }\n        for (const id of columnsOrder){\n            // {key: dataPoint.key, name: dataPoint.key, field: dataPoint.key}\n            const dbCol = dbDefinition.columnsMap[id];\n            if (!dbCol) continue;\n            if (columnPropsMap[id].isHidden) continue;\n            const triggerEdit = editColIdRef.current === dbCol.id;\n            // const triggerEdit = !!(autoEdit && autoEdit.databaseId === definition.databaseId && autoEdit.columnId === dbCol.id)\n            const __meta__ = {\n                databaseId: definition.databaseId,\n                column: dbCol,\n                triggerEdit: triggerEdit,\n                headerLocked: !canEditStructure,\n                contentLocked: isPublishedView || definition.lockContent\n            };\n            // if (triggerEdit) scrollToColId = columns.length\n            if (triggerEdit) editColumnTrigger = true;\n            const column = {\n                key: id,\n                name: dbCol.title,\n                renderEditCell: _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextEditor,\n                renderCell: _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextRenderer,\n                renderHeaderCell: _components_workspace_main_views_table_renderer_common_header__WEBPACK_IMPORTED_MODULE_6__.HeaderRenderer,\n                width: 200,\n                editable: true,\n                __meta__\n            };\n            switch(dbCol.type){\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.AI:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_ai__WEBPACK_IMPORTED_MODULE_15__.AIRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UUID:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.UUIDRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Number:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Derived:\n                    if (dbCol.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Text && dbCol.isLong) column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    if (dbCol.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Derived) {\n                        column.renderEditCell = _components_workspace_main_views_table_renderer_fields_text__WEBPACK_IMPORTED_MODULE_5__.TextAreaEditor;\n                    }\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__.LinkedRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_linked__WEBPACK_IMPORTED_MODULE_17__.LinkedEditor;\n                    column.editable = !(!dbCol.databaseId || databaseErrorStore[dbCol.databaseId] || !databaseStore[dbCol.databaseId]);\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Summarize:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_summarize__WEBPACK_IMPORTED_MODULE_23__.SummarizeRenderer;\n                    column.editable = false;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Select:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__.SelectRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_select__WEBPACK_IMPORTED_MODULE_16__.SelectEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Checkbox:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_checkbox__WEBPACK_IMPORTED_MODULE_8__.CheckboxRenderer;\n                    // column.editable = true\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Date:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.CreatedAt:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UpdatedAt:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_date__WEBPACK_IMPORTED_MODULE_9__.DateRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Person:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonEditor;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.CreatedBy:\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.UpdatedBy:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.PersonRenderer;\n                    // @ts-ignore\n                    delete column.renderEditCell;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Files:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__.FileRenderer;\n                    column.renderEditCell = _components_workspace_main_views_table_renderer_fields_files__WEBPACK_IMPORTED_MODULE_11__.FileEditor;\n                    column.editable = true;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ScannableCode:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_scannableCode__WEBPACK_IMPORTED_MODULE_30__.ScannableCodeRenderer;\n                    break;\n                case opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.ButtonGroup:\n                    column.renderCell = _components_workspace_main_views_table_renderer_fields_buttonGroup__WEBPACK_IMPORTED_MODULE_29__.ButtonGroupRenderer;\n                    // ButtonGroup is read-only, so we don't need an editor\n                    column.editable = false;\n                    break;\n                default:\n                    continue;\n            }\n            column.editable = column.editable && canEditData;\n            columns.push(column);\n        }\n        if (canEditStructure) columns.push((0,_components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.getNewColumnProps)(database.database.id));\n        columns.unshift((0,_components_workspace_main_views_table_renderer_common_selectRow__WEBPACK_IMPORTED_MODULE_13__.getSelectColumnProps)(columns.length, canEditStructure));\n        return columns;\n    };\n    const colIds = cache.getCache(_components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.DatabaseConstants.NewlyCreatedRecordsKey);\n    const createdColIds = colIds && Array.isArray(colIds) ? colIds : [];\n    const cols = getColDefs();\n    const getProcessedRows = ()=>{\n        if (!database) return [];\n        const sortOptions = [];\n        if (sorts.length > 0) {\n            sortOptions.push(...sorts);\n        } else if (definition.sorts.length > 0) {\n            sortOptions.push(...definition.sorts);\n        }\n        if (sortOptions.length === 0) sortOptions.push({\n            columnId: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.MagicColumn.CreatedAt,\n            order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Sort.Asc\n        });\n        const { rows } = filterAndSortRecords(database, members, databaseStore, definition.filter, filter, sortOptions, workspace.workspaceMember.userId);\n        return rows;\n    };\n    const filteredRows = getProcessedRows();\n    const rows = searchFilteredRecords(search, filteredRows);\n    console.log(rows);\n    function rowKeyGetter(row) {\n        return row.id;\n    }\n    // console.log({scrollToColId, scrollToRowId})\n    // console.log({sorts, filter, rows, createdAts: rows.map(r => r.record.createdAt)})\n    const { forceRender } = (0,_components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__[\"default\"])();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!gridRef.current) return;\n        // if (scrollToColId > -1) {\n        //     gridRef.current?.scrollToCell({idx: scrollToColId + 2});\n        //\n        //     // console.log(\"Clearing New Col Key\", {scrollToColId})\n        //     // cache.clearCache(DatabaseConstants.NewlyCreatedColumnKey)\n        // }\n        if (scrollToRowId > -1) {\n            var _gridRef_current;\n            (_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : _gridRef_current.scrollToCell({\n                rowIdx: scrollToRowId + 1\n            });\n        // console.log(\"Clearing New Row Key\", {scrollToRowId})\n        // cache.clearCache(DatabaseConstants.NewlyCreatedRecordsKey)\n        }\n        if (editColIdRef.current && editColumnTrigger) {\n            // setEditColumnId(\"\")\n            editColIdRef.current = \"\";\n        // forceRenderRef.current()\n        }\n    // }, [cache, scrollToColId, scrollToRowId]);\n    }, [\n        cache,\n        editColumnTrigger,\n        scrollToRowId\n    ]);\n    // }, [cache, editColumnId, editColumnTrigger, scrollToRowId]);\n    // }, [cache, scrollToColId, scrollToRowId]);\n    // const setEditColIdRef = useRef(setEditColumnId)\n    // setEditColIdRef.current = setEditColumnId\n    // console.log(\"Render\", {editColumnId: editColIdRef.current, editColumnTrigger, cols})\n    const forceRenderRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(forceRender);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const listener = (data)=>{\n            const { columnId } = data;\n            // setEditColIdRef.current(columnId)\n            // setEditColIdRef.current(columnId)\n            // setEditColumnId(columnId)\n            editColIdRef.current = columnId;\n            forceRenderRef.current();\n            console.log(\"New column listener called:\", {\n                columnId\n            });\n        };\n        const unregister = registerListener(\"d:\".concat(databaseId), _components_workspace_main_views_table_renderer_common_addColumn__WEBPACK_IMPORTED_MODULE_12__.DatabaseConstants.NewlyCreatedColumnKey, listener);\n        return ()=>{\n            unregister();\n        };\n    }, [\n        databaseId,\n        registerListener\n    ]);\n    // useEffect(() => {\n    //     const createdColIds: string[] = colIds && Array.isArray(colIds) ? colIds : []\n    //     if (createdColIds.length > 0) {\n    //         setTimeout(() => {\n    //             cache.clearCache(DatabaseConstants.NewlyCreatedRecordsKey)\n    //         }, 3000)\n    //     }\n    // }, [colIds, cache])\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            !definition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_loader__WEBPACK_IMPORTED_MODULE_14__.PageLoader, {\n                size: \"full\",\n                error: \"Unable to load database\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 352,\n                columnNumber: 29\n            }, undefined),\n            definition && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"size-full flex flex-col\",\n                children: [\n                    !isPublishedView && definition.lockContent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_contentLocked__WEBPACK_IMPORTED_MODULE_20__.ContentLocked, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full flex-1 overflow-hidden table-view bg-white relative\",\n                        ref: containerEle,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_data_grid__WEBPACK_IMPORTED_MODULE_31__[\"default\"], {\n                            ref: gridRef,\n                            columns: cols,\n                            rows: rows,\n                            // rows={demoDatabase.rows}\n                            rowHeight: 40,\n                            headerRowHeight: 40,\n                            summaryRowHeight: 40,\n                            renderers: {\n                                noRowsFallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EmptyRowsRenderer, {\n                                    width: (_containerEle_current = containerEle.current) === null || _containerEle_current === void 0 ? void 0 : _containerEle_current.clientWidth,\n                                    database: database.database\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                                    lineNumber: 368,\n                                    columnNumber: 45\n                                }, void 0)\n                            },\n                            className: \"w-full h-full rdg-light\",\n                            // defaultColumnOptions={{\n                            //     sortable: true,\n                            //     resizable: true\n                            // }}\n                            rowKeyGetter: rowKeyGetter,\n                            selectedRows: new Set(selectedIds),\n                            onSelectedRowsChange: (ids)=>setSelectedIds(Array.from(ids)),\n                            rowClass: (row, index)=>{\n                                // row.id.includes('7') || index === 0 ? highlightClassname : undefined\n                                return createdColIds.includes(row.id) ? \"bg-neutral-100 animate-pulse\" : undefined;\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 21\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                        lineNumber: 358,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 354,\n                columnNumber: 28\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_s(TableView, \"lkqYHWO0TTxPYaLnF4z8pOFnEuk=\", false, function() {\n    return [\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_3__.useWorkspace,\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews,\n        _providers_page__WEBPACK_IMPORTED_MODULE_22__.usePage,\n        _providers_broadcast__WEBPACK_IMPORTED_MODULE_26__.useBroadcast,\n        _providers_shared__WEBPACK_IMPORTED_MODULE_21__.useMaybeShared,\n        _providers_template__WEBPACK_IMPORTED_MODULE_28__.useMaybeTemplate,\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews,\n        _components_custom_ui_forceRender__WEBPACK_IMPORTED_MODULE_27__[\"default\"]\n    ];\n});\n_c = TableView;\nconst EmptyRowsRenderer = (param)=>{\n    let { database, width } = param;\n    _s1();\n    const { createRecords } = (0,_providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews)();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const addRow = async ()=>{\n        const rS = await createRecords(database.id, [\n            {}\n        ]);\n    };\n    const content = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        style: {\n            textAlign: \"center\",\n            width: width,\n            minWidth: width,\n            overflow: \"hidden\"\n        },\n        className: \"content-center sticky left-0 top-2 w-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-8 flex flex-col items-center min-h-72\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ExclamationCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_32__[\"default\"], {\n                        width: 48,\n                        height: 48,\n                        className: \"inline\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-sm font-medium\",\n                    children: \"Database has no records\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"my-4 flex justify-center items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                            variant: \"ghost\",\n                            className: \"text-xs h-8 font-semibold\",\n                            onClick: addRow,\n                            children: \"Add Row\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 409,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_24__.Button, {\n                            className: \"text-xs px-4 h-8 text-center items-center font-semibold\",\n                            onClick: ()=>setOpen(true),\n                            children: \"Import from file\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 408,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n            lineNumber: 403,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n        lineNumber: 402,\n        columnNumber: 21\n    }, undefined);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            open && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_database_importRecords__WEBPACK_IMPORTED_MODULE_25__.ImportRecords, {\n                database: database,\n                close: ()=>setOpen(false)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                lineNumber: 419,\n                columnNumber: 18\n            }, undefined),\n            width && width > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: content\n            }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        textAlign: \"center\",\n                        gridColumn: \"1/-1\",\n                        overflow: \"hidden\"\n                    },\n                    className: \"content-center\",\n                    children: content\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\views\\\\table\\\\index.tsx\",\n                    lineNumber: 424,\n                    columnNumber: 14\n                }, undefined)\n            }, void 0, false)\n        ]\n    }, void 0, true);\n};\n_s1(EmptyRowsRenderer, \"rC5mgFaRT9QarQc0IyDaBINFj1o=\", false, function() {\n    return [\n        _providers_views__WEBPACK_IMPORTED_MODULE_18__.useViews\n    ];\n});\n_c1 = EmptyRowsRenderer;\nconst filterAndSortRecords = function(database, members, databaseStore, definitionFilter, filter, sortOptions, currentUserId) {\n    let currentObjectId = arguments.length > 7 && arguments[7] !== void 0 ? arguments[7] : \"\";\n    var _processedRecords_;\n    const rows = [];\n    if (!database) return {\n        rows\n    };\n    const linkedDatabaseId = database ? Object.values(database.database.definition.columnsMap).filter((c)=>c.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.DatabaseFieldDataType.Linked && c.databaseId).map((c)=>c.databaseId) : [];\n    const linkedDatabases = {};\n    for (const id of linkedDatabaseId){\n        const db = databaseStore[id];\n        if (db) {\n            linkedDatabases[id] = {\n                id,\n                definition: db.database.definition,\n                srcPackageName: db.database.srcPackageName,\n                recordsMap: {}\n            };\n            for (let r of Object.values(db.recordsIdMap)){\n                linkedDatabases[id].recordsMap[r.record.id] = r.record;\n            }\n        }\n    }\n    const persons = (0,_components_workspace_main_views_table_renderer_fields_person__WEBPACK_IMPORTED_MODULE_10__.membersToPersons)(members);\n    const records = Object.values(database.recordsIdMap).map((r)=>r.record);\n    console.log(\"\\uD83D\\uDD0D DEBUG - Processing records with titleFormat:\", {\n        titleFormat: database.database.definition.titleFormat,\n        recordCount: records.length,\n        databaseId: database.database.id\n    });\n    const processedRecords = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.transformRawRecords)(// const processedRecords = debugTransformRawRecords(\n    database.database.definition, records, persons, linkedDatabases);\n    console.log(\"\\uD83D\\uDD0D DEBUG - Processed records sample:\", {\n        firstRecordTitle: (_processedRecords_ = processedRecords[0]) === null || _processedRecords_ === void 0 ? void 0 : _processedRecords_.title,\n        totalRecords: processedRecords.length\n    });\n    let filtered = {\n        processedRecords,\n        records\n    };\n    if (definitionFilter && definitionFilter.conditions.length > 0) {\n        filtered = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.filterRecords)(filtered.records, filtered.processedRecords, definitionFilter, database.database.definition, currentUserId, currentObjectId);\n    }\n    if (filter && filter.conditions.length > 0) {\n        filtered = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.filterRecords)(filtered.records, filtered.processedRecords, filter, database.database.definition, currentUserId, currentObjectId);\n    }\n    if (sortOptions.length === 0) sortOptions.push({\n        columnId: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.MagicColumn.CreatedAt,\n        order: opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_7__.Sort.Asc\n    });\n    const sorted = (0,opendb_app_db_utils_lib_utils_db__WEBPACK_IMPORTED_MODULE_19__.sortRecords)(filtered.records, filtered.processedRecords, sortOptions, database.database.definition);\n    for(let i = 0; i < sorted.records.length; i++){\n        const record = sorted.records[i];\n        const processedRecord = sorted.processedRecords[i];\n        const row = {\n            id: record.id,\n            record: database.recordsIdMap[record.id].record,\n            updatedAt: new Date(record.updatedAt).toISOString(),\n            processedRecord,\n            title: processedRecord.title || \"Untitled\" // Add title for easy access\n        };\n        rows.push(row);\n    }\n    return {\n        rows\n    };\n};\nconst searchFilteredRecords = (search, filteredRows)=>{\n    if (!search || !search.trim()) return filteredRows;\n    return filteredRows.filter((r)=>r.processedRecord.valuesText.toLowerCase().includes(search.trim().toLowerCase()));\n};\nvar _c, _c1;\n$RefreshReg$(_c, \"TableView\");\n$RefreshReg$(_c1, \"EmptyRowsRenderer\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/views/table/index.tsx\n"));

/***/ })

});