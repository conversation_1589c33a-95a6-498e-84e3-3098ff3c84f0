"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/database/configureTitleDialog.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/workspace/main/database/configureTitleDialog.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigureTitleDialog: function() { return /* binding */ ConfigureTitleDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _api_database__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/api/database */ \"(app-pages-browser)/./src/api/database.ts\");\n/* harmony import */ var _components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom-ui/mentionInput */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConfigureTitleDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ConfigureTitleDialog = (param)=>{\n    let { database, close } = param;\n    _s();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_5__.useAlert)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    // Get initial title format\n    const getInitialTitleFormat = ()=>{\n        var _database_definition, _database_definition1;\n        if ((database === null || database === void 0 ? void 0 : (_database_definition = database.definition) === null || _database_definition === void 0 ? void 0 : _database_definition.titleFormat) && typeof database.definition.titleFormat === \"string\") {\n            return database.definition.titleFormat;\n        }\n        if ((database === null || database === void 0 ? void 0 : (_database_definition1 = database.definition) === null || _database_definition1 === void 0 ? void 0 : _database_definition1.titleColumnId) && typeof database.definition.titleColumnId === \"string\") {\n            return \"{{\".concat(database.definition.titleColumnId, \"}}\");\n        }\n        return \"\";\n    };\n    const [titleFormat, setTitleFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialTitleFormat());\n    // Create keyMap for MentionInput from available columns\n    const keyMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const map = {};\n        // Filter columns that can be used in title format\n        const availableColumns = Object.values(database.definition.columnsMap).filter((column)=>{\n            // Include columns that display readable text values\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Text) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Number) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Date) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.CreatedAt) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.UpdatedAt) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Select) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Checkbox) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.AI) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Person) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.CreatedBy) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.UpdatedBy) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Summarize) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Derived) return true;\n            // Exclude columns that show IDs or are complex/not user-friendly\n            return false;\n        });\n        // Create keyMap entries for each column\n        availableColumns.forEach((column)=>{\n            map[column.id] = {\n                label: column.title,\n                tag: \"{{\".concat(column.id, \"}}\")\n            };\n        });\n        return map;\n    }, [\n        database.definition.columnsMap\n    ]);\n    const handleSave = async ()=>{\n        try {\n            if (!titleFormat || typeof titleFormat !== \"string\" || !titleFormat.trim()) {\n                toast.error(\"Please configure a title format\");\n                return;\n            }\n            if (!token) {\n                toast.error(\"Authentication required\");\n                return;\n            }\n            // DEBUG: Log what we're sending\n            console.log(\"\\uD83D\\uDD0D DEBUG - Saving title format:\", {\n                titleFormat,\n                workspaceId: workspace.workspace.id,\n                databaseId: database.id,\n                currentDefinition: database.definition\n            });\n            // Use the proper API to update database definition\n            const response = await (0,_api_database__WEBPACK_IMPORTED_MODULE_8__.setTitleFormat)(token.token, workspace.workspace.id, database.id, titleFormat);\n            // DEBUG: Log the response\n            console.log(\"\\uD83D\\uDD0D DEBUG - API Response:\", response);\n            if (response.isSuccess && !response.error) {\n                toast.success(\"Title format updated successfully\");\n                // DEBUG: Check if the database definition was updated\n                console.log(\"\\uD83D\\uDD0D DEBUG - Database definition after save:\", database.definition);\n                close();\n            } else {\n                throw new Error(response.error || \"Failed to update title format\");\n            }\n        } catch (error) {\n            console.error(\"❌ ERROR saving title format:\", error);\n            toast.error(\"Failed to update title format\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: true,\n        onOpenChange: close,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-md md:max-w-lg w-[95vw] max-h-[90vh] overflow-hidden flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    className: \"pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                            children: \"Configure Record Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: \"Type @ to mention columns and create custom record titles\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-grow flex flex-col overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                children: \"Title Format\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_9__.MentionInput, {\n                                keyMap: keyMap,\n                                value: titleFormat,\n                                onChange: setTitleFormat,\n                                placeholder: \"Type @ to mention columns... e.g., @firstName @lastName\",\n                                className: \"w-full min-h-12 text-sm border rounded-md p-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: \"Example: @firstName @lastName - @company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                    className: \"mt-4 flex-shrink-0 sm:justify-between flex flex-col-reverse sm:flex-row gap-2 sm:gap-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            onClick: close,\n                            className: \"sm:mr-2 w-full sm:w-auto\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 146,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: handleSave,\n                            className: \"w-full sm:w-auto\",\n                            disabled: !titleFormat || typeof titleFormat !== \"string\" || !titleFormat.trim(),\n                            children: \"Save\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                    lineNumber: 145,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n            lineNumber: 118,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n        lineNumber: 117,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ConfigureTitleDialog, \"Qz3hyphDfuGYIX8l4pZC4cQUTOg=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_5__.useAlert,\n        _providers_user__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace\n    ];\n});\n_c = ConfigureTitleDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfigureTitleDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/database/configureTitleDialog.tsx\n"));

/***/ })

});