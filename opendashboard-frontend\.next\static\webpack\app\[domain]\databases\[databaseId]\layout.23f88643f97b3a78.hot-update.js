"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/database/configureTitleDialog.tsx":
/*!*************************************************************************!*\
  !*** ./src/components/workspace/main/database/configureTitleDialog.tsx ***!
  \*************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConfigureTitleDialog: function() { return /* binding */ ConfigureTitleDialog; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! opendb-app-db-utils/lib/typings/db */ \"(app-pages-browser)/./node_modules/opendb-app-db-utils/lib/typings/db.js\");\n/* harmony import */ var _providers_alert__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/providers/alert */ \"(app-pages-browser)/./src/providers/alert.tsx\");\n/* harmony import */ var _providers_user__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/providers/user */ \"(app-pages-browser)/./src/providers/user.tsx\");\n/* harmony import */ var _providers_workspace__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/providers/workspace */ \"(app-pages-browser)/./src/providers/workspace.tsx\");\n/* harmony import */ var _api_database__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/api/database */ \"(app-pages-browser)/./src/api/database.ts\");\n/* harmony import */ var _components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/custom-ui/mentionInput */ \"(app-pages-browser)/./src/components/custom-ui/mentionInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConfigureTitleDialog auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst ConfigureTitleDialog = (param)=>{\n    let { database, close } = param;\n    _s();\n    const { toast } = (0,_providers_alert__WEBPACK_IMPORTED_MODULE_5__.useAlert)();\n    const { token } = (0,_providers_user__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const { workspace } = (0,_providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace)();\n    // Get initial title format\n    const getInitialTitleFormat = ()=>{\n        var _database_definition, _database_definition1;\n        if ((database === null || database === void 0 ? void 0 : (_database_definition = database.definition) === null || _database_definition === void 0 ? void 0 : _database_definition.titleFormat) && typeof database.definition.titleFormat === \"string\") {\n            return database.definition.titleFormat;\n        }\n        if ((database === null || database === void 0 ? void 0 : (_database_definition1 = database.definition) === null || _database_definition1 === void 0 ? void 0 : _database_definition1.titleColumnId) && typeof database.definition.titleColumnId === \"string\") {\n            return \"{{\".concat(database.definition.titleColumnId, \"}}\");\n        }\n        return \"\";\n    };\n    const [titleFormat, setTitleFormat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(getInitialTitleFormat());\n    // Create keyMap for MentionInput from available columns\n    const keyMap = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        const map = {};\n        // Filter columns that can be used in title format\n        const availableColumns = Object.values(database.definition.columnsMap).filter((column)=>{\n            // Include columns that display readable text values\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Text) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Number) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Date) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.CreatedAt) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.UpdatedAt) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Select) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Checkbox) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.AI) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Person) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.CreatedBy) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.UpdatedBy) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Summarize) return true;\n            if (column.type === opendb_app_db_utils_lib_typings_db__WEBPACK_IMPORTED_MODULE_4__.DatabaseFieldDataType.Derived) return true;\n            // Exclude columns that show IDs or are complex/not user-friendly\n            return false;\n        });\n        // Create keyMap entries for each column\n        availableColumns.forEach((column)=>{\n            map[column.id] = {\n                label: column.title,\n                tag: \"{{\".concat(column.id, \"}}\")\n            };\n        });\n        return map;\n    }, [\n        database.definition.columnsMap\n    ]);\n    const handleSave = async ()=>{\n        try {\n            if (!titleFormat || typeof titleFormat !== \"string\" || !titleFormat.trim()) {\n                toast.error(\"Please configure a title format\");\n                return;\n            }\n            if (!token) {\n                toast.error(\"Authentication required\");\n                return;\n            }\n            // Clean the title format - remove invisible Unicode characters that MentionInput adds\n            const cleanTitleFormat = titleFormat.replace(/[\\u200B-\\u200D\\uFEFF]/g, \"\") // Remove zero-width spaces and similar\n            .replace(/\\s+/g, \" \") // Normalize multiple spaces to single space\n            .trim();\n            // DEBUG: Log what we're sending\n            console.log(\"\\uD83D\\uDD0D DEBUG - Saving title format:\", {\n                originalTitleFormat: titleFormat,\n                cleanedTitleFormat: cleanTitleFormat,\n                workspaceId: workspace.workspace.id,\n                databaseId: database.id,\n                currentDefinition: database.definition\n            });\n            // Use the proper API to update database definition\n            const response = await (0,_api_database__WEBPACK_IMPORTED_MODULE_8__.setTitleFormat)(token.token, workspace.workspace.id, database.id, cleanTitleFormat);\n            // DEBUG: Log the response\n            console.log(\"\\uD83D\\uDD0D DEBUG - API Response:\", response);\n            if (response.isSuccess && !response.error) {\n                toast.success(\"Title format updated successfully\");\n                // DEBUG: Check if the database definition was updated\n                console.log(\"\\uD83D\\uDD0D DEBUG - Database definition after save:\", database.definition);\n                close();\n            } else {\n                throw new Error(response.error || \"Failed to update title format\");\n            }\n        } catch (error) {\n            console.error(\"❌ ERROR saving title format:\", error);\n            toast.error(\"Failed to update title format\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.Dialog, {\n        open: true,\n        onOpenChange: close,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogContent, {\n            className: \"sm:max-w-md md:max-w-lg w-[95vw] max-h-[90vh] overflow-hidden flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogHeader, {\n                    className: \"pb-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogTitle, {\n                            children: \"Configure Record Title\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xs text-gray-500 mt-1\",\n                            children: \"Type @ to mention columns and create custom record titles\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-grow flex flex-col overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                className: \"text-sm font-medium text-gray-700 mb-2 block\",\n                                children: \"Title Format\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_mentionInput__WEBPACK_IMPORTED_MODULE_9__.MentionInput, {\n                                keyMap: keyMap,\n                                value: titleFormat,\n                                onChange: setTitleFormat,\n                                placeholder: \"Type @ to mention columns... e.g., @firstName @lastName\",\n                                className: \"w-full min-h-12 text-sm border rounded-md p-3\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 25\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-gray-500 mt-1\",\n                                children: \"Example: @firstName @lastName - @company\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 21\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 17\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_3__.DialogFooter, {\n                    className: \"mt-4 flex-shrink-0 sm:justify-between flex flex-col-reverse sm:flex-row gap-2 sm:gap-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outline\",\n                            onClick: close,\n                            className: \"sm:mr-2 w-full sm:w-auto\",\n                            children: \"Cancel\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            onClick: handleSave,\n                            className: \"w-full sm:w-auto\",\n                            disabled: !titleFormat || typeof titleFormat !== \"string\" || !titleFormat.trim(),\n                            children: \"Save\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 17\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n            lineNumber: 125,\n            columnNumber: 13\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\database\\\\configureTitleDialog.tsx\",\n        lineNumber: 124,\n        columnNumber: 9\n    }, undefined);\n};\n_s(ConfigureTitleDialog, \"Qz3hyphDfuGYIX8l4pZC4cQUTOg=\", false, function() {\n    return [\n        _providers_alert__WEBPACK_IMPORTED_MODULE_5__.useAlert,\n        _providers_user__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        _providers_workspace__WEBPACK_IMPORTED_MODULE_7__.useWorkspace\n    ];\n});\n_c = ConfigureTitleDialog;\nvar _c;\n$RefreshReg$(_c, \"ConfigureTitleDialog\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/database/configureTitleDialog.tsx\n"));

/***/ })

});