"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/layout",{

/***/ "(app-pages-browser)/./src/typings/page.ts":
/*!*****************************!*\
  !*** ./src/typings/page.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AccessLevel: function() { return /* binding */ AccessLevel; },\n/* harmony export */   Visibility: function() { return /* binding */ Visibility; }\n/* harmony export */ });\nvar AccessLevel;\n(function(AccessLevel) {\n    AccessLevel[\"View\"] = \"view\";\n    AccessLevel[\"Edit\"] = \"edit\";\n    AccessLevel[\"Full\"] = \"full\";\n})(AccessLevel || (AccessLevel = {}));\nvar Visibility;\n(function(Visibility) {\n    Visibility[\"Private\"] = \"private\";\n    Visibility[\"Open\"] = \"open\";\n})(Visibility || (Visibility = {}));\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/typings/page.ts\n"));

/***/ })

});