"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[domain]/databases/[databaseId]/views/layout",{

/***/ "(app-pages-browser)/./src/components/workspace/main/record/components/recordExtras.tsx":
/*!**************************************************************************!*\
  !*** ./src/components/workspace/main/record/components/recordExtras.tsx ***!
  \**************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RecordExtras: function() { return /* binding */ RecordExtras; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CogIcon_EllipsisHorizontalIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CogIcon,EllipsisHorizontalIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CogIcon_EllipsisHorizontalIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CogIcon,EllipsisHorizontalIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CogIcon_EllipsisHorizontalIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CogIcon,EllipsisHorizontalIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_CogIcon_EllipsisHorizontalIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,CogIcon,EllipsisHorizontalIcon,PlusCircleIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusCircleIcon.js\");\n/* harmony import */ var _components_custom_ui_dndSortable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/custom-ui/dndSortable */ \"(app-pages-browser)/./src/components/custom-ui/dndSortable.tsx\");\n/* harmony import */ var _components_workspace_main_record_components_extra_recordActivities__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/workspace/main/record/components/extra/recordActivities */ \"(app-pages-browser)/./src/components/workspace/main/record/components/extra/recordActivities.tsx\");\n/* harmony import */ var _components_custom_ui_tabView__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/custom-ui/tabView */ \"(app-pages-browser)/./src/components/custom-ui/tabView.tsx\");\n/* harmony import */ var _components_workspace_main_common_workspaceReminders__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/workspace/main/common/workspaceReminders */ \"(app-pages-browser)/./src/components/workspace/main/common/workspaceReminders.tsx\");\n/* harmony import */ var _components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/icons/FontAwesomeRegular */ \"(app-pages-browser)/./src/components/icons/FontAwesomeRegular.tsx\");\n/* harmony import */ var _components_workspace_main_views_common_viewCreator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/workspace/main/views/common/viewCreator */ \"(app-pages-browser)/./src/components/workspace/main/views/common/viewCreator.tsx\");\n/* harmony import */ var _providers_screenSize__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/providers/screenSize */ \"(app-pages-browser)/./src/providers/screenSize.tsx\");\n/* harmony import */ var _components_workspace_main_record_components_extra_recordSummary__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/workspace/main/record/components/extra/recordSummary */ \"(app-pages-browser)/./src/components/workspace/main/record/components/extra/recordSummary.tsx\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* harmony import */ var _components_workspace_main_common_workspaceNotes__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/workspace/main/common/workspaceNotes */ \"(app-pages-browser)/./src/components/workspace/main/common/workspaceNotes.tsx\");\n/* harmony import */ var _providers_record__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/providers/record */ \"(app-pages-browser)/./src/providers/record.tsx\");\n/* harmony import */ var _components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/workspace/main/views/form/components/element/linked */ \"(app-pages-browser)/./src/components/workspace/main/views/form/components/element/linked.tsx\");\n/* harmony import */ var _providers_workspaceSocket__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/providers/workspaceSocket */ \"(app-pages-browser)/./src/providers/workspaceSocket.tsx\");\n/* harmony import */ var _typings_socket__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/typings/socket */ \"(app-pages-browser)/./src/typings/socket.ts\");\n/* harmony import */ var _typings_workspace__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/typings/workspace */ \"(app-pages-browser)/./src/typings/workspace.ts\");\n/* harmony import */ var _utils_environment__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/utils/environment */ \"(app-pages-browser)/./src/utils/environment.ts\");\n/* __next_internal_client_entry_do_not_use__ RecordExtras auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst RecordOverview = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_12__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_src_components_workspace_main_record_components_recordOverview_tsx\").then(__webpack_require__.bind(__webpack_require__, /*! @/components/workspace/main/record/components/recordOverview */ \"(app-pages-browser)/./src/components/workspace/main/record/components/recordOverview.tsx\")).then((module)=>module.RecordOverview), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx -> \" + \"@/components/workspace/main/record/components/recordOverview\"\n        ]\n    },\n    ssr: false\n});\n_c = RecordOverview;\nconst RecordExtras = (param)=>{\n    let { showOverview } = param;\n    _s();\n    const { recordInfo, database } = (0,_providers_record__WEBPACK_IMPORTED_MODULE_14__.useRecord)();\n    const { id, databaseId } = recordInfo.record;\n    const { isMobile } = (0,_providers_screenSize__WEBPACK_IMPORTED_MODULE_10__.useScreenSize)();\n    const { socket, isConnected } = (0,_providers_workspaceSocket__WEBPACK_IMPORTED_MODULE_16__.useWorkspaceSocket)();\n    const [tabsOrder, setTabsOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const { defaultTitle, isContacts, titleColId } = (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_15__.getDatabaseTitleCol)(database);\n    // Use the processed title if available, otherwise fall back to getRecordTitle\n    const title = recordInfo.record.title || (0,_components_workspace_main_views_form_components_element_linked__WEBPACK_IMPORTED_MODULE_15__.getRecordTitle)(recordInfo.record, titleColId, defaultTitle, isContacts);\n    //database, members)\n    // Define the default tabs\n    const defaultTabs = [\n        {\n            id: \"summary\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_8__.BookIcon, {\n                        className: \"size-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden truncate flex-1 font-semibold\",\n                        children: \"Summary\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 51,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true),\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_record_components_extra_recordSummary__WEBPACK_IMPORTED_MODULE_11__.RecordSummary, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                lineNumber: 55,\n                columnNumber: 18\n            }, undefined)\n        },\n        {\n            id: \"activity\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_8__.BoltIcon, {\n                        className: \"size-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden truncate flex-1 font-semibold\",\n                        children: \"Activities\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true),\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_record_components_extra_recordActivities__WEBPACK_IMPORTED_MODULE_5__.RecordActivities, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                lineNumber: 65,\n                columnNumber: 22\n            }, undefined)\n        },\n        {\n            id: \"notes\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_8__.NoteIcon, {\n                        className: \"size-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden truncate flex-1 font-semibold\",\n                        children: \"Notes\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true),\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_workspaceNotes__WEBPACK_IMPORTED_MODULE_13__.WorkspaceNotes, {\n                    recordId: id,\n                    databaseId: databaseId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 24\n                }, undefined)\n            }, void 0, false)\n        },\n        {\n            id: \"reminders\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_8__.ClockThreeIcon, {\n                        className: \"size-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden truncate flex-1 font-semibold\",\n                        children: \"Reminders\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true),\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_common_workspaceReminders__WEBPACK_IMPORTED_MODULE_7__.WorkspaceReminders, {\n                    newReminderTitle: \"Remind me about \".concat(title),\n                    recordId: id,\n                    databaseId: databaseId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 24\n                }, undefined)\n            }, void 0, false)\n        }\n    ];\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)(()=>{\n        const savedOrder = localStorage.getItem(\"recordTabsOrder\");\n        if (savedOrder) {\n            try {\n                const parsedOrder = JSON.parse(savedOrder);\n                if (Array.isArray(parsedOrder)) {\n                    setTabsOrder(parsedOrder);\n                }\n            } catch (e) {\n                console.error(\"Error parsing saved tab order from localStorage:\", e);\n            }\n        }\n        if (socket && isConnected) {\n            socket.emit(_typings_socket__WEBPACK_IMPORTED_MODULE_17__.WorkspaceHandlerEvent.UpdateMemberSettings, {\n                key: _typings_workspace__WEBPACK_IMPORTED_MODULE_18__.WorkspaceMemberSettingsKey.RecordTabsOrder,\n                value: null,\n                action: \"get\"\n            }, (res)=>{\n                if (res && res.status === \"ok\" && res.data && Array.isArray(res.data.value)) {\n                    const backendOrder = res.data.value;\n                    if (backendOrder.length > 0) {\n                        localStorage.setItem(\"recordTabsOrder\", JSON.stringify(backendOrder));\n                        setTabsOrder(backendOrder);\n                    }\n                }\n            });\n        }\n    }, [\n        socket,\n        isConnected\n    ]);\n    const tabs = [\n        ...defaultTabs\n    ];\n    if (isMobile || showOverview) {\n        tabs.unshift({\n            id: \"overview\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_8__.ChartPieIcon, {\n                        className: \"size-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 130,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden truncate flex-1 font-semibold\",\n                        children: \"Overview\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true),\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecordOverview, {\n                isTabbed: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                lineNumber: 135,\n                columnNumber: 22\n            }, undefined)\n        });\n    }\n    if ((0,_utils_environment__WEBPACK_IMPORTED_MODULE_19__.isLocal)()) {\n        tabs.push({\n            id: \"custom-1\",\n            title: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_icons_FontAwesomeRegular__WEBPACK_IMPORTED_MODULE_8__.MessageDotsIcon, {\n                        className: \"size-3\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 17\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"overflow-hidden truncate flex-1 font-semibold\",\n                        children: \"Stories\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 17\n                    }, undefined)\n                ]\n            }, void 0, true),\n            content: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                children: \"Stories\"\n            }, void 0, false)\n        });\n    }\n    if (tabsOrder.length > 0) {\n        const reorderableTabs = tabs.filter((tab)=>tab.id !== \"overview\");\n        const overviewTab = tabs.find((tab)=>tab.id === \"overview\");\n        const tabMap = {};\n        reorderableTabs.forEach((tab)=>{\n            tabMap[tab.id] = tab;\n        });\n        const orderedTabs = [];\n        if (overviewTab) {\n            orderedTabs.push(overviewTab);\n        }\n        tabsOrder.forEach((id)=>{\n            if (tabMap[id]) {\n                orderedTabs.push(tabMap[id]);\n                delete tabMap[id];\n            }\n        });\n        Object.values(tabMap).forEach((tab)=>{\n            orderedTabs.push(tab);\n        });\n        if (orderedTabs.length > 0) {\n            tabs.length = 0;\n            tabs.push(...orderedTabs);\n        }\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_tabView__WEBPACK_IMPORTED_MODULE_6__.TabView, {\n            tabs: tabs,\n            defaultTab: tabs[0].id,\n            tabSwitcherClassName: \"px-3\",\n            className: \"extra rEx\",\n            tabTitleExtra: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(RecordTabOptions, {\n                tabs: tabs,\n                setTabsOrder: setTabsOrder\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                lineNumber: 191,\n                columnNumber: 33\n            }, void 0)\n        }, isMobile ? 1 : 2, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n            lineNumber: 187,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s(RecordExtras, \"maItOOn4X0wZq6acbm23LiLVg5Q=\", false, function() {\n    return [\n        _providers_record__WEBPACK_IMPORTED_MODULE_14__.useRecord,\n        _providers_screenSize__WEBPACK_IMPORTED_MODULE_10__.useScreenSize,\n        _providers_workspaceSocket__WEBPACK_IMPORTED_MODULE_16__.useWorkspaceSocket\n    ];\n});\n_c1 = RecordExtras;\nconst RecordTabOptions = (param)=>{\n    let { tabs, setTabsOrder } = param;\n    _s1();\n    const [open, setOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [newView, setNewView] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const { socket, isConnected } = (0,_providers_workspaceSocket__WEBPACK_IMPORTED_MODULE_16__.useWorkspaceSocket)();\n    const reorderableTabs = tabs.filter((tab)=>tab.id !== \"overview\");\n    const saveTabOrder = (tabIds)=>{\n        localStorage.setItem(\"recordTabsOrder\", JSON.stringify(tabIds));\n        setTabsOrder(tabIds);\n        if (socket && isConnected) {\n            socket.emit(_typings_socket__WEBPACK_IMPORTED_MODULE_17__.WorkspaceHandlerEvent.UpdateMemberSettings, {\n                key: _typings_workspace__WEBPACK_IMPORTED_MODULE_18__.WorkspaceMemberSettingsKey.RecordTabsOrder,\n                value: tabIds\n            }, (res)=>{\n                console.log(\"UpdateMemberSettings with response\", res);\n            });\n        }\n    };\n    const resetTabOrder = ()=>{\n        // Clear localStorage\n        localStorage.removeItem(\"recordTabsOrder\");\n        setTabsOrder([]);\n        if (socket && isConnected) {\n            socket.emit(_typings_socket__WEBPACK_IMPORTED_MODULE_17__.WorkspaceHandlerEvent.UpdateMemberSettings, {\n                key: _typings_workspace__WEBPACK_IMPORTED_MODULE_18__.WorkspaceMemberSettingsKey.RecordTabsOrder,\n                value: []\n            }, (res)=>{\n                console.log(\"UpdateMemberSettings with response\", res);\n            });\n        }\n        setOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"p-2\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_workspace_main_views_common_viewCreator__WEBPACK_IMPORTED_MODULE_9__.ViewCreator, {\n                    context: \"database\",\n                    setOpen: setNewView,\n                    open: newView\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                    lineNumber: 239,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                    open: open,\n                    onOpenChange: setOpen,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                className: \"text-xs rounded-full p-1.5 h-auto gap-2 overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CogIcon_EllipsisHorizontalIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"size-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 25\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                lineNumber: 242,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                            lineNumber: 241,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                            className: \"w-96 p-0 rounded-none\",\n                            align: \"end\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col h-auto max-h-96\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 border-b flex-1\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_custom_ui_dndSortable__WEBPACK_IMPORTED_MODULE_4__.DNDSortable, {\n                                            items: reorderableTabs.map((tab)=>({\n                                                    id: tab.id,\n                                                    data: tab\n                                                })),\n                                            itemRenderer: function(index, item) {\n                                                const tab = item.data;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center w-full gap-2\",\n                                                            children: tab.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 41\n                                                        }, void 0),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                                variant: \"ghost\",\n                                                                onClick: (e)=>{\n                                                                    e.stopPropagation();\n                                                                    e.preventDefault();\n                                                                },\n                                                                className: \"size-6 p-1 rounded-full items-center hover:bg-neutral-300\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CogIcon_EllipsisHorizontalIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 49\n                                                                }, void 0)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                                                lineNumber: 260,\n                                                                columnNumber: 45\n                                                            }, void 0)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                                            lineNumber: 259,\n                                                            columnNumber: 41\n                                                        }, void 0)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                                    lineNumber: 254,\n                                                    columnNumber: 44\n                                                }, void 0);\n                                            },\n                                            onChange: function(items) {\n                                                const tabIds = items.map((item)=>item.id);\n                                                saveTabOrder(tabIds);\n                                            },\n                                            useDragHandle: true,\n                                            handlePosition: \"center\",\n                                            wrapperClassName: (index, item)=>{\n                                                return \"hover:bg-neutral-100 gap-0.5 pl-2\";\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                            lineNumber: 250,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start\",\n                                            onClick: resetTabOrder,\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CogIcon_EllipsisHorizontalIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"size-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"Reset to Default Order\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 25\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-2 border-t\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                            variant: \"ghost\",\n                                            className: \"text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start\",\n                                            onClick: ()=>{\n                                                setOpen(false);\n                                                setNewView(true);\n                                            },\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowPathIcon_CogIcon_EllipsisHorizontalIcon_PlusCircleIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                    className: \"size-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                                    lineNumber: 297,\n                                                    columnNumber: 33\n                                                }, undefined),\n                                                \"New View\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                            lineNumber: 291,\n                                            columnNumber: 29\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                        lineNumber: 290,\n                                        columnNumber: 25\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 21\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n                    lineNumber: 240,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ad\\\\opendashboard-frontend\\\\src\\\\components\\\\workspace\\\\main\\\\record\\\\components\\\\recordExtras.tsx\",\n            lineNumber: 238,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false);\n};\n_s1(RecordTabOptions, \"2cin7Aqx+I/bNMvbcwRXiuI0H2E=\", false, function() {\n    return [\n        _providers_workspaceSocket__WEBPACK_IMPORTED_MODULE_16__.useWorkspaceSocket\n    ];\n});\n_c2 = RecordTabOptions;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"RecordOverview\");\n$RefreshReg$(_c1, \"RecordExtras\");\n$RefreshReg$(_c2, \"RecordTabOptions\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/workspace/main/record/components/recordExtras.tsx\n"));

/***/ })

});