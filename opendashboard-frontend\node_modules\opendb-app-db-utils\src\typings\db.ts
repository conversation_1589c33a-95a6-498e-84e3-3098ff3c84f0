import {WithColor} from "./color";
import {Person} from "./common";
import {CompareOperator} from "../methods/compare";
import {AggregateFunction} from "../utils/db";

export enum DatabaseFieldDataType {
    Text = 'text',
    AI = 'ai',
    UUID = 'uuid',
    Number = 'number',
    Checkbox = 'checkbox',
    Select = 'select',
    Person = 'person',
    CreatedBy = 'created-by',
    UpdatedBy = 'updated-by',
    Linked = 'linked',
    Summarize = 'summarize',
    Files = 'files',
    Date = 'date',
    CreatedAt = 'created-at',
    UpdatedAt = 'updated-at',
    Derived = 'derived',
    ScannableCode = 'scannable-code',
    ButtonGroup = "button-group"
}

export const AutoGeneratedColumnTypes = Object.freeze([
    DatabaseFieldDataType.Summarize,
    DatabaseFieldDataType.Derived,
    DatabaseFieldDataType.UUID,
    DatabaseFieldDataType.CreatedAt,
    DatabaseFieldDataType.UpdatedAt,
    DatabaseFieldDataType.CreatedBy,
    DatabaseFieldDataType.UpdatedBy,
]);

export const CanBeUniqueColumnTypes = Object.freeze([
    DatabaseFieldDataType.Date,
    DatabaseFieldDataType.Number,
    DatabaseFieldDataType.Text,
]);


export const ReadOnlyColumns = Object.freeze([
    ...AutoGeneratedColumnTypes,
    DatabaseFieldDataType.AI,
    DatabaseFieldDataType.ButtonGroup
]);

interface BaseColumn {
    id: string;
    type: DatabaseFieldDataType;
    title: string;
    description?: string;
    meta?: object
}

export enum TextColumnFormat {
    Text = 'text',
    Email = 'email',
    Phone = 'phone',
    Url = 'url',
    Location = 'location',
}

export interface TextColumn extends BaseColumn {
    type: DatabaseFieldDataType.Text
    format?: TextColumnFormat;
    isLong?: boolean
}

export interface AIColumn extends BaseColumn {
    type: DatabaseFieldDataType.AI
    prompt: string,
    maxWordOutput?: number
    attachmentColumnIds?: string[]
}

export interface UUIDColumn extends BaseColumn {
    type: DatabaseFieldDataType.UUID
}

export enum NumberColumnFormat {
    Number = 'Number',
    Percentage = 'percentage',
    Currency = 'currency',
}

export interface NumberColumn extends BaseColumn {
    type: DatabaseFieldDataType.Number
    format?: NumberColumnFormat
    currency?: string
    divideBy?: number
}

export interface CheckboxColumn extends BaseColumn {
    type: DatabaseFieldDataType.Checkbox;
}

export interface SelectOption extends WithColor {
    id: string;
    title: string;
}

export interface SelectColumn extends BaseColumn {
    type: DatabaseFieldDataType.Select;
    optionIds: string[];
    optionsMap: {
        [key: string]: SelectOption;
    };
    isMulti?: boolean
}

export interface PersonColumn extends BaseColumn {
    type: DatabaseFieldDataType.Person;
    isMulti?: boolean
}

export interface CreatedByColumn extends BaseColumn {
    type: DatabaseFieldDataType.CreatedBy;
}

export interface UpdatedByColumn extends BaseColumn {
    type: DatabaseFieldDataType.UpdatedBy;
}

export interface LinkedColumn extends BaseColumn {
    type: DatabaseFieldDataType.Linked;
    databaseId: string
    isMulti?: boolean
}

export interface SummarizeColumn extends BaseColumn, WithDbAggregateFunction {
    type: DatabaseFieldDataType.Summarize;
    targetColumnId: string
    linkedDisplayColumnId: string
    aggregateBy: AggregateFunction
    title: string
    format?: TextColumnFormat | NumberColumnFormat | DateColumnFormat;
    withTime?: boolean
    currency?: string
    divideBy?: number
}

export interface DerivedColumn extends BaseColumn {
    type: DatabaseFieldDataType.Derived;
    derivation?: string;
    format?: TextColumnFormat | NumberColumnFormat | DateColumnFormat;
    returnType?: DatabaseFieldDataType.Text | DatabaseFieldDataType.Number | DatabaseFieldDataType.Checkbox | DatabaseFieldDataType.ButtonGroup | undefined;
}

export namespace ButtonActionDefinition {
    export interface SendEmailProps {
        senderId: string;
        subject: string;
        body: string;
        to: string;
        cc?: string[];
        bcc?: string[];
    }

    export interface OpenUrlProps {
        url: string;
    }

    export interface UpdateRecordProps {
        recordId: string;
        values: Record<string, any>;
    }

    export interface DeleteRecordProps {
        recordId: string;
    }

    export interface ShowConfirmationProps {
        title: string;
        message: string;
        confirmButtonLabel: string;
        cancelButtonLabel: string;
    }

    export interface ShowToastProps {
        title?: string;
        message: string;
        duration?: number;
    }

    export interface CallWorkflowProps {
        workflowId: string;
        input: Record<string, any>;
    }

    export interface ExpandRecordProps {
        recordId: string;
    }

    export interface PeekRecordProps {
        recordId: string;
    }

    export interface SendNotificationProps {
        title: string;
        message: string;
        userId?: string;
    }

    export type ButtonActionProps = SendEmailProps | OpenUrlProps | UpdateRecordProps | DeleteRecordProps | ShowConfirmationProps | ShowToastProps | CallWorkflowProps | ExpandRecordProps | PeekRecordProps | SendNotificationProps;
}

type ButtonActionProps = ButtonActionDefinition.ButtonActionProps;

export interface ButtonAction {
    id: string;
    label: string;
    isReady?: boolean;
    actionType: 'sendEmail' | 'openUrl' | 'updateRecord' | 'deleteRecord' | 'showConfirmation' | 'showToast' | 'callWorkflow' | 'expandRecord' | 'peekRecord' | 'sendNotification';
    props?: ButtonActionProps
}

export interface ActionButton {
    id: string;
    label: string;
    isReady?: boolean;
    actions: ButtonAction[];
}

export interface ButtonGroupColumn extends BaseColumn {
    id: string;
    title: string;
    type: DatabaseFieldDataType.ButtonGroup;
    buttons: ActionButton[];
}

export enum ScannableCodeFormat {
    QR = 'qr',
    Barcode = 'barcode',
}

export interface ScannableCodeColumn extends BaseColumn {
    type: DatabaseFieldDataType.ScannableCode;
    format?: ScannableCodeFormat;
    derivation?: string;
}

export enum DateColumnFormat {
    Relative = 'relative',
    Absolute = 'absolute'
}

export interface DateColumn extends BaseColumn {
    type: DatabaseFieldDataType.Date;
    format?: DateColumnFormat
    withTime?: boolean
}

export interface CreatedAtColumn extends Omit<DateColumn, 'type'> {
    type: DatabaseFieldDataType.CreatedAt;
}

export interface UpdatedAtColumn extends Omit<DateColumn, 'type'> {
    type: DatabaseFieldDataType.UpdatedAt;
}

export interface FilesColumn extends BaseColumn {
    type: DatabaseFieldDataType.Files;
    isMulti?: boolean
}

export type DatabaseColumn =
    | TextColumn
    | NumberColumn
    | AIColumn
    | UUIDColumn
    | LinkedColumn
    | SummarizeColumn
    | SelectColumn
    | CheckboxColumn
    | DateColumn
    | CreatedAtColumn
    | UpdatedAtColumn
    | CreatedByColumn
    | UpdatedByColumn
    | PersonColumn
    | FilesColumn
    | DerivedColumn
    | ButtonGroupColumn
    | ScannableCodeColumn;

export interface DatabaseDefinition {
    uniqueColumnId?: string;
    titleColumnId?: string;
    titleFormat?: string;
    columnIds: string[];
    columnsMap: {
        [key: string]: DatabaseColumn;
    };
}

export type TextColumnDbValue = string;
export type AIColumnRecordMetadata = {
    value: string;
    generatedAt?: Date
    inProgress?: boolean
    error?: string
    contentId?: string
}

export interface FileItem {
    id: string
    type: string
    link: string
    name: string
    size?: number
}

export interface LinkedItem {
    id: string
    title: string
}


export type FilesColumnDbValue = FileItem[]
export type AIColumnDbValue = string;
export type NumberColumnDbValue = number;
export type TagColumnDbValue = string[];
export type CheckboxColumnDbValue = boolean;
export type DateColumnDbValue = string;


export type DatabaseColumnDbValue =
    | TextColumnDbValue
    | NumberColumnDbValue
    | CheckboxColumnDbValue
    | DateColumnDbValue
    | TagColumnDbValue
    | AIColumnDbValue
    | FilesColumnDbValue


export type TextColumnReturnValue = TextColumnDbValue;
export type AIColumnReturnValue = AIColumnDbValue;
export type NumberColumnReturnValue = NumberColumnDbValue;
export type SelectColumnReturnValue = SelectOption[];
export type CheckboxColumnReturnValue = CheckboxColumnDbValue;
export type DateColumnReturnValue = Date;
export type CreatedAtColumnReturnValue = Date;
export type UpdatedAtColumnReturnValue = Date;
export type PersonColumnReturnValue = Person[];
export type CreatedByColumnReturnValue = Person;
export type UpdatedByColumnReturnValue = CreatedByColumnReturnValue;
export type LinkedColumnReturnValue = LinkedItem[];

export type DatabaseColumnReturnValue =
    | TextColumnReturnValue
    | AIColumnReturnValue
    | NumberColumnReturnValue
    | SelectColumnReturnValue
    | CheckboxColumnDbValue
    | DateColumnReturnValue
    | CheckboxColumnReturnValue
    | CreatedAtColumnReturnValue
    | UpdatedAtColumnReturnValue
    | PersonColumnReturnValue
    | CreatedByColumnReturnValue
    | UpdatedByColumnReturnValue
    | FilesColumnDbValue
    | LinkedColumnReturnValue;


export interface RecordValues {
    [key: string]: DatabaseColumnDbValue;
}

export interface RawDbRecord {
    id: string;
    title?: string
    createdAt: Date | string;
    updatedAt: Date | string;
    createdById: string;
    updatedById: string;
    uniqueValue: string;
    recordValues: RecordValues
}


export interface ProcessedRecordValues {
    [key: string]: DatabaseColumnReturnValue;
}

export interface ProcessedDbRecord {
    id: string;
    createdAt: Date | string;
    updatedAt: Date | string;
    createdBy: Person;
    updatedBy: Person;
    uniqueValue: string;
    recordValues: RecordValues
    title: string
    valuesText: string
    processedRecordValues: ProcessedRecordValues;
}

export enum MagicColumn {
    UUID = DatabaseFieldDataType.UUID,
    CreatedAt = DatabaseFieldDataType.CreatedAt,
    UpdatedAt = DatabaseFieldDataType.UpdatedAt,
    CreatedBy = DatabaseFieldDataType.CreatedBy,
    UpdatedBy = DatabaseFieldDataType.UpdatedBy,
}


export interface DbCondition {
    columnId: string;
    op: CompareOperator;
    value: string | string[];
    extra?: string | string[];
}

export enum Match {
    All = 'all',
    Any = 'any',
}

export interface WithDb {
    databaseId: string;
}

export interface DbRecordFilter {
    conditions: DbCondition[];
    match: Match;
}

export interface WithDbRecordFilters {
    filter: DbRecordFilter;
}

export enum Sort {
    Asc = 'asc',
    Desc = 'desc',
}

export interface DbRecordSort {
    columnId: string;
    order: Sort;
}

export interface WithDbRecordSort {
    sorts: DbRecordSort[];
}

export interface WithDbAggregateFunction {
    aggregateBy: AggregateFunction;
}

export const CurrentPerson: Person = Object.freeze({
    image: undefined,
    firstName: 'Current',
    lastName: 'User',
    id: 'current_user',
    title: 'Current User'
})

export const CurrentObject = "current_object"


export interface DbRecordsResolver extends WithDb, WithDbRecordFilters, WithDbRecordSort {
}
