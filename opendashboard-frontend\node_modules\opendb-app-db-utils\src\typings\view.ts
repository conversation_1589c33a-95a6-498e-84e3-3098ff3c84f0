import {DatabaseColumnDbValue, DateColumnFormat, DbRecordFilter, DbRecordsResolver, NumberColumnFormat, TextColumnFormat, WithDb, WithDbRecordFilters} from "./db";
import {Emoji, Icon, Image} from "./common";
import {AggregateFunction} from "../utils/db";
import {CheckboxAggregateFunction, CheckPercentAggregateFunction, CountAggregateFunction, NumberAggregateFunction, PercentAggregateFunction} from "../methods/number";
import {Color} from "./color";

export enum ViewType {
    Table = "table",
    Board = "board",
    Form = "form",
    Document = "document",
    Dashboard = "dashboard",
    SummaryTable = "summary-table",
    ListView = "list-view",
    Calendar = "calendar"
}

export interface ViewDefinition {
    type: ViewType
    lockContent?: boolean

}

export interface ViewColumnCustomization {
    isHidden?: boolean
    color?: Color
}

interface WithColumnCustomization {
    columnsOrder: string[]
    columnPropsMap: {
        [key: string]: ViewColumnCustomization
    }
}

export interface TableViewDefinition extends ViewDefinition, DbRecordsResolver, WithColumnCustomization {
    type: ViewType.Table
}

export interface ListViewDefinition extends Omit<TableViewDefinition, 'type'> {
    type: ViewType.ListView
}

export interface CalendarViewDefinition extends ViewDefinition, DbRecordsResolver, WithColumnCustomization {
    type: ViewType.Calendar;
    databaseId: string;
    eventStartColumnId: string;
    eventEndColumnId?: string;
    defaultDuration: number;
}

export interface BoardGroupItemProps {
    isHidden?: boolean
    itemsOrder?: string[]
}

export interface BoardViewDefinition extends ViewDefinition, DbRecordsResolver, WithColumnCustomization {
    type: ViewType.Board
    groupByIds: string[]
    groupItemsProps?: { [key: string]: BoardGroupItemProps }
    groupOrder: string[]
}

export interface FormColumnCustomization extends ViewColumnCustomization {
    isRequired?: boolean
    defaultValue?: DatabaseColumnDbValue
    title?: string
    allowMultiple?: boolean
    limitToOptions?: boolean
    limitByFilter?: boolean
    selectableOptionsFilter?: DbRecordFilter;
    selectableOptions?: string[]
    min?: number
    max?: number
    selectionLimit?: number
    maxFileSize?: number
    allowedFileTypes?: {
        text?: boolean,
        document?: boolean,
        spreadsheet?: boolean
        presentation?: boolean
        pdf?: boolean
        image?: boolean
    }
}

interface WithFormColumnCustomization {
    columnsOrder: string[]
    columnPropsMap: {
        [key: string]: FormColumnCustomization
    }
}

export interface FormSubmissionWindow {
    startDate?: string
    endDate?: string
}

export interface FormSubmitProps {
    buttonLabel?: string
    message?: string
    redirectUrl?: string
    redirectToUrl?: boolean
    sendEmailOnSubmission?: boolean
    onSubmissionEmails?: string
    acceptNewResponse?: boolean
    protectWithRecaptcha?: boolean
    submissionWindows?: FormSubmissionWindow[]
}

export interface FormViewDefinition extends ViewDefinition, WithFormColumnCustomization {
    type: ViewType.Form
    databaseId: string;
    coverImage?: string
    showLogo?: boolean
    hideBranding?: boolean
    submitProps?: FormSubmitProps
}

export interface DocumentViewDefinition extends ViewDefinition {
    type: ViewType.Document
    itemsOrder?: string[]
}

export interface DashboardViewDefinition extends ViewDefinition {
    type: ViewType.Dashboard
    definition: DashboardDefinition
}


export interface SummaryColumnCustomization {
    id: string
    columnId: string
    aggregateBy: AggregateFunction
    title?: string
    format?: TextColumnFormat | NumberColumnFormat | DateColumnFormat;
    withTime?: boolean
    currency?: string
    divideBy?: number
    isHidden?: boolean
}

interface WithSummarizedCustomization {
    columns: SummaryColumnCustomization[]
}

export interface SummaryViewDefinition extends ViewDefinition, DbRecordsResolver, WithSummarizedCustomization {
    type: ViewType.SummaryTable
    groupByIds: string[]
}

export type Direction = "t" | "l" | "r" | "b" | "";

export enum Position {
    Left = 'left',
    Right = 'right',
}

export enum DashboardElementType {
    Infobox = "infobox",
    LineChart = "lineChart",
    BarChart = "barChart",
    PieChart = "pieChart",
    FunnelChart = "funnelChart",
    Embed = "embed",
    Image = 'image',
    Text = "text",
}

export interface DashboardBaseElement {
    id: string;
    title: string;
    type: DashboardElementType;
    description?: string;
    icon?: Icon | Image | Emoji;
    width?: number;
    height?: number;
    parentId?: string;
    childrenIds?: string[]
    updatedTs?: number
}

export interface EmbedElement extends DashboardBaseElement {
    type: DashboardElementType.Embed;
    embedCode: string;
}

export interface ImageElement extends DashboardBaseElement {
    type: DashboardElementType.Image;
    images: string[];
    autoRotate: boolean
}

export interface TextElement extends DashboardBaseElement {
    type: DashboardElementType.Text;
    content?: string
}

export interface DbValueResolver extends WithDb, WithDbRecordFilters {
    columnId: string
    aggregateBy: CountAggregateFunction | NumberAggregateFunction | CheckboxAggregateFunction | PercentAggregateFunction | CheckPercentAggregateFunction
}

export interface InfoboxElement extends DashboardBaseElement {
    type: DashboardElementType.Infobox;
    iconPosition: Position.Left | Position.Right;
    valueResolve: DbValueResolver;
}

export interface LineChartElement extends DashboardBaseElement, WithColumnCustomization {
    type: DashboardElementType.LineChart
    recordsResolve: DbRecordsResolver;
}

export interface PieChartElement extends DashboardBaseElement {
    type: DashboardElementType.PieChart
    recordsResolve: { groupByIds: string[], titleColId: string } & WithDb & WithDbRecordFilters;
}

export interface DashboardRow {
    id: string
    children: string[]
    updatedTs?: number
}

export type DashboardElement = EmbedElement | ImageElement | TextElement | InfoboxElement | LineChartElement | PieChartElement | DashboardBaseElement

export interface DashboardTransaction {
    action: "updateRow" | 'addRow' | 'deleteRow' | "addElement" | "deleteElement" | "updateElement";
    element?: DashboardElement;
    row?: DashboardRow;
    parentId?: string;
    beforeId?: string;
    afterId?: string;
}

export interface DashboardDefinition {
    elementMap: {
        [id: string]: DashboardElement
    };
    rowsMap: {
        [id: string]: DashboardRow
    }
    children: string[]
}
