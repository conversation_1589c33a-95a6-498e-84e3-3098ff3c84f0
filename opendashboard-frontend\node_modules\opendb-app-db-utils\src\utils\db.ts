import {
    AIColumn,
    AIColumnDbValue,
    AutoGeneratedColumnTypes,
    CheckboxColumn,
    CheckboxColumnReturnValue,
    CreatedAtColumn,
    CreatedByColumn,
    CreatedByColumnReturnValue,
    CurrentObject,
    CurrentPerson,
    DatabaseColumn,
    DatabaseColumnDbValue,
    DatabaseColumnReturnValue,
    DatabaseDefinition,
    DatabaseFieldDataType,
    DateColumn,
    DateColumnReturnValue,
    DbCondition,
    DbRecordFilter,
    DbRecordSort,
    DerivedColumn,
    FileItem,
    FilesColumnDbValue,
    LinkedColumnReturnValue,
    MagicColumn,
    Match,
    NumberColumn,
    NumberColumnReturnValue,
    PersonColumn,
    ProcessedDbRecord,
    RawDbRecord,
    RecordValues,
    SelectColumn,
    SelectColumnReturnValue,
    SelectOption,
    Sort,
    TagColumnDbValue,
    TextColumn,
    TextColumnFormat,
    TextColumnReturnValue,
    UpdatedAtColumn,
    UpdatedByColumn,
    UUIDColumn
} from "../typings/db";
import {generateUUID} from "../methods/string";
import {ColorNames} from "../typings/color";
import {ObjectType, Person} from "../typings/common";
import {SubstituteVarData, substituteVars} from "../methods/object";
import {dateToMySQL, isDateObjValid, isDateValid, RelativeDate, resolveRelativeDate} from "../methods/date";
import {compareFields, CompareOperator, SingleOperandOperators} from "../methods/compare";
import {arrayContains, arrayContainsAnyOf, arrayContainsNoneOf, arrayDeDuplicate, arraysHaveSameElements} from "../methods/array";
import {CheckboxAggregateFunction, CheckPercentAggregateFunction, CountAggregateFunction, DateAggregateFunction, NumberAggregateFunction, PercentAggregateFunction, ShowValuesAggregateFunction} from "../methods/number";
import {getCompanyDbDefinition, getCustomerDbDefinition, VersionedDefinition} from "./onboarding";
import {evaluateDerived} from "./derived";


export const constructDb = (): DatabaseDefinition => {
    const textColumn: TextColumn = {
        id: generateUUID(),
        title: 'Text',
        type: DatabaseFieldDataType.Text,
    };
    const numberColumn: NumberColumn = {
        id: generateUUID(),
        title: 'Number',
        type: DatabaseFieldDataType.Number,
    };

    const option1: SelectOption = {
        color: ColorNames.Caledon,
        id: generateUUID(),
        title: 'Option 1',
    };
    const option2: SelectOption = {
        color: ColorNames.Brown,
        id: generateUUID(),
        title: 'Option 2',
    };
    const optionsId = [option1.id, option2.id];
    const optionsMap: {
        [key: string]: SelectOption;
    } = {};
    optionsMap[option1.id] = option1;
    optionsMap[option2.id] = option2;

    const selectColumn: SelectColumn = {
        id: generateUUID(),
        optionIds: optionsId,
        optionsMap: optionsMap,
        title: 'Select',
        type: DatabaseFieldDataType.Select,
    };
    const multiSelectColumn: SelectColumn = {
        id: generateUUID(),
        optionIds: optionsId,
        optionsMap: optionsMap,
        title: 'Multi select',
        type: DatabaseFieldDataType.Select,
        isMulti: true
    };
    const phoneNumberColumn: TextColumn = {
        id: generateUUID(),
        title: 'Phone',
        type: DatabaseFieldDataType.Text,
        format: TextColumnFormat.Phone
    };
    const emailColumn: TextColumn = {
        id: generateUUID(),
        title: 'Email',
        type: DatabaseFieldDataType.Text,
        format: TextColumnFormat.Email
    };
    const aiColumn: AIColumn = {
        id: generateUUID(),
        title: 'AI Intro',
        type: DatabaseFieldDataType.AI,
        prompt: ""
    };
    const checkboxColumn: CheckboxColumn = {
        id: generateUUID(),
        title: 'Checkbox',
        type: DatabaseFieldDataType.Checkbox,
    };
    const dateColumn: DateColumn = {
        id: generateUUID(),
        title: 'Date',
        type: DatabaseFieldDataType.Date,
    };
    const createdAtColumn: CreatedAtColumn = {
        id: generateUUID(),
        title: 'CreatedAt',
        type: DatabaseFieldDataType.CreatedAt,
    };
    const updatedAtColumn: UpdatedAtColumn = {
        id: generateUUID(),
        title: 'Updated At',
        type: DatabaseFieldDataType.UpdatedAt,
    };
    const createdByColumn: CreatedByColumn = {
        id: generateUUID(),
        title: 'Created By',
        type: DatabaseFieldDataType.CreatedBy,
    };
    const updatedByColumn: UpdatedByColumn = {
        id: generateUUID(),
        title: 'Updated By',
        type: DatabaseFieldDataType.UpdatedBy,
    };
    const uuidColumn: UUIDColumn = {
        id: generateUUID(),
        title: 'UUID',
        type: DatabaseFieldDataType.UUID,
    };
    const personColumn: PersonColumn = {
        id: generateUUID(),
        title: 'Person',
        type: DatabaseFieldDataType.Person,
    };
    const derivedColumn: DerivedColumn = {
        description: 'Column',
        // icon,
        id: generateUUID(),
        title: 'Derived',
        type: DatabaseFieldDataType.Derived,
        derivation: ` {{${numberColumn.id}}} + {{${textColumn.id}}}`,
        // returnType: DatabaseFieldDataType.Text,
    };
    const columns: DatabaseColumn[] = [
        uuidColumn,
        textColumn,
        numberColumn,
        selectColumn,
        multiSelectColumn,
        phoneNumberColumn,
        emailColumn,
        aiColumn,
        derivedColumn,
        checkboxColumn,
        dateColumn,
        createdAtColumn,
        updatedAtColumn,
        createdByColumn,
        updatedByColumn,
        personColumn,
    ];

    const dbDefinition: DatabaseDefinition = {
        columnIds: [],
        columnsMap: {},
        uniqueColumnId: emailColumn.id,
        titleFormat: `{{${textColumn.id}}} - {{${numberColumn.id}}}`,
    };
    for (const column of columns) {
        dbDefinition.columnsMap[column.id] = column;
        dbDefinition.columnIds.push(column.id);
    }

    return dbDefinition;
};

export const constructDerivedColumn = (derivation: string): DerivedColumn => {
    const derivedColumn: DerivedColumn = {
        description: 'Column',
        // icon,
        id: generateUUID(),
        title: 'Derived',
        type: DatabaseFieldDataType.Derived,
        derivation: derivation,
        // returnType: DatabaseFieldDataType.Text,
    };

    return derivedColumn
}
export const generatePseudoPerson = (count: number): Person[] => {
    const firstNames = [
        'Alice',
        'Bob',
        'Charlie',
        'David',
        'Eve',
        'Frank',
        'Grace',
        'Henry',
        'Ivy',
        'Jack',
        'Karen',
        'Liam',
        'Mia',
        'Nora',
        'Oliver',
        'Penelope',
        'Quinn',
        'Riley',
        'Samantha',
        'Thomas',
        'Ursula',
        'Victoria',
        'William',
        'Xavier',
        'Yara',
        'Zachary',
    ];
    const lastNames = [
        'Adams',
        'Brown',
        'Clark',
        'Davis',
        'Edwards',
        'Franklin',
        'Garcia',
        'Hill',
        'Ibrahim',
        'Johnson',
        'Kim',
        'Lee',
        'Martinez',
        'Nguyen',
        "O'Brien",
        'Patel',
        'Quinn',
        'Ramirez',
        'Singh',
        'Taylor',
        'Upton',
        'Valdez',
        'Williams',
        'Xu',
        'Yilmaz',
        'Zhang',
    ];

    const persons: Person[] = [];

    for (let i = 0; i < count; i++) {
        const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
        const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];

        const person: Person = {
            firstName,
            lastName,
            id: generateUUID(),
            image: {
                type: ObjectType.Image,
                url: 'https://placehold.co/600x400/EEE/31343C',
            },
            title: `${firstName} ${lastName}`,
        };
        persons.push(person);
    }

    return persons;
};

export const generatePseudoDbRecords = (
    dbDefinition: DatabaseDefinition,
    count = 10,
    persons = generatePseudoPerson(5),
): RawDbRecord[] => {
    const records: RawDbRecord[] = [];

    for (let i = 0; i < count; i++) {
        const id = generateUUID();
        const createdAt = new Date();
        const updatedAt = new Date();
        const createdBy = persons[Math.floor(Math.random() * persons.length)];
        const updatedBy = persons[Math.floor(Math.random() * persons.length)];
        const uniqueValue = `Unique value ${i}`;

        const dataMap: {
            [key: string]: any
        } = {};

        for (const columnId of dbDefinition.columnIds) {
            const column: DatabaseColumn = dbDefinition.columnsMap[columnId];
            let value: DatabaseColumnDbValue;

            const type = column.type;

            if (AutoGeneratedColumnTypes.includes(type as any)) continue;

            switch (type) {
                case DatabaseFieldDataType.Text:
                    value = `${column.title} ${i}`;
                    break;
                case DatabaseFieldDataType.Number:
                    value = i;
                    break;
                case DatabaseFieldDataType.Select:
                    const selCol = column as SelectColumn
                    value = selCol.isMulti ? selCol.optionIds : selCol.optionIds[0];
                    break;
                case DatabaseFieldDataType.Checkbox:
                    value = i % 2 === 0;
                    break;
                case DatabaseFieldDataType.Date:
                    value = new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString();
                    break;
                case DatabaseFieldDataType.AI:
                    const val: AIColumnDbValue = "This is the AI value"
                    value = val
                    break;
                case DatabaseFieldDataType.Linked:
                    value = ["1", "2", "3"];
                    break;
                case DatabaseFieldDataType.CreatedAt:
                    value = dateToMySQL(createdAt);
                    break;
                case DatabaseFieldDataType.UpdatedAt:
                    value = dateToMySQL(updatedAt);
                    break;
                case DatabaseFieldDataType.CreatedBy:
                    value = createdBy.id;
                    break;
                case DatabaseFieldDataType.UpdatedBy:
                    value = updatedBy.id;
                    break;
                case DatabaseFieldDataType.Files:
                    const files: FileItem [] = [
                        {id: generateUUID(), name: 'file1', link: 'https://placehold.co/600x400/EEE/31343C', type: 'image/jpeg'},
                        {id: generateUUID(), name: 'file2', link: 'https://placehold.co/600x400/EEE/31343C', type: 'image/jpeg'},
                        {id: generateUUID(), name: 'file3', link: 'https://placehold.co/600x400/EEE/31343C', type: 'image/jpeg'},
                    ]
                    value = files
                    break;
                case DatabaseFieldDataType.Derived:
                    value = column.derivation === 'i * 2' ? i * 2 : i.toString();
                    break;
                case DatabaseFieldDataType.UUID:
                    value = id;
                    break;
                case DatabaseFieldDataType.Person:
                    value = [
                        persons[Math.floor(Math.random() * persons.length)].id,
                        persons[Math.floor(Math.random() * persons.length)].id,
                        'personId',
                    ];
                    break;
                case DatabaseFieldDataType.ButtonGroup:
                    value = null;
                    break;
                default:

            }
            dataMap[columnId] = value;
        }

        const record: RawDbRecord = {
            id,
            createdAt: createdAt,
            updatedAt: updatedAt,
            createdById: createdBy.id,
            updatedById: updatedBy.id,
            uniqueValue,
            recordValues: dataMap,
        };

        records.push(record);
    }

    return records;
};

// interface RecordIdMap {
//     [id: string]: {
//         record: Record
//         processedRecord?: ProcessedDbRecord
//     }
// }

// interface AdjacentDatabases {
//     [key: string]: {
//         database: Database,
//         recordsMap?: RecordIdMap,
//         error?: string
//     }
// }
export interface LinkedDatabase {
    id: string
    definition: DatabaseDefinition
    srcPackageName: string
    recordsMap: {
        [id: string]: RawDbRecord
    },
}

export type LinkedDatabases = {
    [key: string]: LinkedDatabase
}

// interface


const companyDef = getCompanyDbDefinition()
const contactsDef = getCustomerDbDefinition('')

const companySrcPackageName = getDatabasePackageName(companyDef)
const contactsSrcPackageName = getDatabasePackageName(contactsDef)

export const transformRawRecords = (
    dbDefinition: DatabaseDefinition,
    records: RawDbRecord[],
    persons: Person[],
    linkedDatabases: LinkedDatabases = {},
    isContacts: boolean = false,
    defaultTitle: boolean = false
): ProcessedDbRecord[] => {
    const {columnIds, columnsMap, titleFormat} = dbDefinition;
    const titleColId = dbDefinition.titleColumnId

    const linkedDbsTitleColMap: {
        [id: string]: {
            defaultTitle: boolean, isContacts: boolean, titleColId: string
        }
    } = {}

    for (const linkedDb of Object.values(linkedDatabases)) {
        let defaultTitle = false
        let isContacts = false
        let titleColId = linkedDb.definition.titleColumnId || ''

        const srcPackageName = linkedDb.srcPackageName
        isContacts = srcPackageName === contactsSrcPackageName

        if (srcPackageName === companySrcPackageName && !titleColId) {
            titleColId = 'name'
        } else if (isContacts && !titleColId) {
            titleColId = 'firstName'
            defaultTitle = true
        }
        if (!titleColId) {
            for (const column of Object.values(linkedDb.definition.columnsMap)) {
                if (column.type === DatabaseFieldDataType.Text) {
                    titleColId = column.id
                    break
                }
            }
        }
        linkedDbsTitleColMap[linkedDb.id] = {
            titleColId,
            defaultTitle,
            isContacts
        }
    }

    return records.map((record) => {
        const createdAt = new Date(record.createdAt);
        const updatedAt = new Date(record.updatedAt);
        const createdBy: CreatedByColumnReturnValue =
            persons.find((person) => person.id === record.createdById) || generateUnknownPerson(record.createdById);
        const updatedBy: CreatedByColumnReturnValue =
            persons.find((person) => person.id === record.updatedById) || generateUnknownPerson(record.updatedById);

        const processedRecord: ProcessedDbRecord = {
            createdAt,
            createdBy,
            processedRecordValues: {},
            recordValues: record.recordValues,
            uniqueValue: record.uniqueValue,
            updatedAt,
            updatedBy,
            id: record.id,
            valuesText: '',
            title: getRecordTitle(record, titleColId, defaultTitle, isContacts),
        };
        const textValues: string[] = []

        for (const columnId of columnIds) {
            const column = columnsMap[columnId];
            let rawValue = record.recordValues[columnId];
            let processedValue: DatabaseColumnReturnValue;

            switch (column.type) {
                case DatabaseFieldDataType.Text:
                case DatabaseFieldDataType.AI:
                    processedValue = rawValue as TextColumnReturnValue;
                    break;
                case DatabaseFieldDataType.Linked:
                    const linkedIds: TagColumnDbValue = (Array.isArray(rawValue)
                                                         && rawValue.length > 0
                                                         && typeof rawValue[0] === 'string' ? rawValue : []) as TagColumnDbValue;
                    const linked: LinkedColumnReturnValue = [];

                    if (column.databaseId && linkedDatabases[column.databaseId]) {
                        for (const linkedId of linkedIds) {
                            if (linkedDatabases[column.databaseId].recordsMap[linkedId]) {
                                const {isContacts, defaultTitle, titleColId} = linkedDbsTitleColMap[column.databaseId]
                                const linkedRecord = linkedDatabases[column.databaseId].recordsMap[linkedId]
                                // const title = linkedRecord.title ? linkedRecord.title :
                                //               isContacts && defaultTitle ? `${String(linkedRecord.recordValues['lastName'] || '')} ${String(linkedRecord.recordValues['firstName'] || '')}`.trim() :
                                //               linkedRecord.recordValues[titleColId] && typeof linkedRecord.recordValues[titleColId] === 'string' ? String(linkedRecord.recordValues[titleColId]) :
                                //               'Untitled'
                                const title = getRecordTitle(linkedRecord, titleColId, defaultTitle, isContacts)
                                linked.push(({
                                    id: linkedId,
                                    title
                                }))
                            }
                        }
                    }

                    processedValue = linked;
                    break;
                case DatabaseFieldDataType.Summarize:
                    const {targetColumnId, linkedDisplayColumnId} = column
                    // @todo: Work on this later
                    processedValue = ''
                    break;
                case DatabaseFieldDataType.UUID:
                    processedValue = record.id;
                    rawValue = record.id
                    break;
                case DatabaseFieldDataType.Select:
                    const selectedIds: TagColumnDbValue = (Array.isArray(rawValue)
                                                           && rawValue.length > 0
                                                           && typeof rawValue[0] === 'string' ? rawValue : []) as TagColumnDbValue;
                    const selected: SelectColumnReturnValue = [];
                    for (const selectedId1 of selectedIds) {
                        const option = selectedId1
                                       ? column.optionsMap[selectedId1] || generateUnknownSelectOption(selectedId1)
                                       : null;
                        if (option) selected.push(option);
                    }
                    processedValue = selected;
                    break;
                case DatabaseFieldDataType.Files:
                    const selectedFiles = (Array.isArray(rawValue)
                                           && rawValue.length > 0
                                           && typeof rawValue[0] === 'object' ?
                                           rawValue : []) as FilesColumnDbValue

                    processedValue = selectedFiles
                    break;
                case DatabaseFieldDataType.Number:
                    const numVal = parseFloat(rawValue as string) as NumberColumnReturnValue;
                    processedValue = isNaN(numVal) ? '' : numVal;
                    break;
                case DatabaseFieldDataType.Derived:
                    processedValue = rawValue as TextColumnReturnValue;
                    break;
                case DatabaseFieldDataType.Checkbox:
                    processedValue = !!rawValue as CheckboxColumnReturnValue;
                    break;
                case DatabaseFieldDataType.Date:
                    try {
                        const date = new Date(rawValue as string) as DateColumnReturnValue;
                        processedValue = isDateObjValid(date) ? date.toISOString() : null;
                    } catch (e) {
                        processedValue = null
                    }
                    break;
                case DatabaseFieldDataType.CreatedAt:
                    processedValue = createdAt;
                    rawValue = createdAt.toISOString();
                    break;
                case DatabaseFieldDataType.UpdatedAt:
                    processedValue = updatedAt;
                    rawValue = updatedAt.toISOString();
                    break;
                case DatabaseFieldDataType.CreatedBy:
                    processedValue = createdBy;
                    rawValue = createdBy.id;
                    break;
                case DatabaseFieldDataType.UpdatedBy:
                    processedValue = updatedBy;
                    rawValue = updatedBy.id;
                    break;
                case DatabaseFieldDataType.Person:
                    const personIds = (Array.isArray(rawValue)
                                       && rawValue.length > 0
                                       && typeof rawValue[0] === 'string' ? rawValue : []) as TagColumnDbValue;
                    const ref: Person[] = [];
                    for (const id of personIds) {
                        const person1 = persons.find((person) => person.id === id) || generateUnknownPerson(id);
                        if (person1) ref.push(person1);
                    }
                    processedValue = ref;
                    break;
                default:
                    // We should never get here, but just in case
                    processedValue = null;
            }
            processedRecord.processedRecordValues[columnId] = processedValue;
            processedRecord.recordValues[columnId] = rawValue;
            textValues.push(String(recordValueToText(processedValue)))
        }
        const vars: SubstituteVarData = {};
        for (const key of Object.keys(processedRecord.processedRecordValues)) {
            const val = processedRecord.processedRecordValues[key];
            vars[key] = recordValueToText(val);
        }

        for (const columnId of columnIds) {
            const column = columnsMap[columnId]
            switch (column.type) {
                case DatabaseFieldDataType.Derived:
                    processedRecord.processedRecordValues[columnId] = evaluateDerived(column.derivation || '', vars) as DatabaseColumnReturnValue;
                    vars[columnId] = processedRecord.processedRecordValues[columnId]
                    textValues.push(String(recordValueToText(processedRecord.processedRecordValues[columnId])))
                    break;
                default:

            }
        }
        for (const columnId of columnIds) {
            const column = columnsMap[columnId];
            switch (column.type) {
                case DatabaseFieldDataType.ScannableCode:
                    const value = substituteVars(column.derivation || '', vars, undefined, "curly");
                    processedRecord.processedRecordValues[columnId] = value;
                    vars[columnId] = processedRecord.processedRecordValues[columnId]
                    textValues.push(String(recordValueToText(processedRecord.processedRecordValues[columnId])))
                    break;
                default:
            }
        }
        if (!processedRecord.title) {
            processedRecord.title = titleFormat ? substituteVars(titleFormat, vars, undefined, "curly") || 'Untitled' : 'Untitled';
        }
        processedRecord.valuesText = textValues.join(',')
        return processedRecord;
    });
};

export const getRecordTitle = (record: RawDbRecord, titleColId: string, defaultTitle: boolean, isContacts: boolean) => {
    return record.title ? record.title :
           isContacts && defaultTitle ? `${String(record.recordValues['lastName'] || '')} ${String(record.recordValues['firstName'] || '')}`.trim() :
           record.recordValues[titleColId] && typeof record.recordValues[titleColId] === 'string' ? String(record.recordValues[titleColId]) :
           ''
}

export const generateUnknownPerson = (id: string): Person => {
    return {
        firstName: 'Unknown',
        id,
        image: {
            type: ObjectType.Image,
            url: '',
        },
        lastName: 'Person',
        title: 'Unknown Person'
    };
};

export const generateUnknownSelectOption = (id: string): SelectOption => {
    return {
        color: 'Beige',
        id,
        title: 'Unknown'
    };
};

export const recordValueToText = (value: DatabaseColumnReturnValue): TextColumnReturnValue | NumberColumnReturnValue | CheckboxColumnReturnValue => {
    if (value === null || value === undefined) return '';
    if (value instanceof Date) return dateToMySQL(value);
    if (Array.isArray(value)) {
        return value.map((v) => recordValueToText(v as any)).join(', ');
    }
    const type = typeof value;
    if (type === 'string' || type === 'number' || type === 'boolean') return value as typeof type;

    if (type === 'object') return (value as any).title || (value as any).name || '';
    return '';
};

export interface RecordResponse {
    records: FilterableRecord[],
    processedRecords: ProcessedDbRecord[]
}

export const sortRecords = (records: FilterableRecord[], processedRecords: ProcessedDbRecord[], sorts: DbRecordSort[], definition: DatabaseDefinition): RecordResponse => {
    const indexArray = records.map((_, index) => index);

    indexArray.sort((a, b) => {
        const recordA = processedRecords[a]
        const recordB = processedRecords[b]

        for (const sort of sorts) {
            const {columnId, order} = sort;
            const column = definition.columnsMap[columnId]
            let colType = column?.type

            let aValue = recordValueToText(recordA.processedRecordValues[columnId] || '');
            let bValue = recordValueToText(recordB.processedRecordValues[columnId] || '');

            if (!colType && Object.values(MagicColumn).includes(columnId as unknown as MagicColumn)) {
                switch (columnId as unknown as MagicColumn) {
                    case MagicColumn.UUID:
                        aValue = recordA.id
                        bValue = recordB.id
                        break;
                    case MagicColumn.CreatedAt:
                        aValue = new Date(recordA.createdAt).getTime()
                        bValue = new Date(recordB.createdAt).getTime()
                        break
                    case MagicColumn.UpdatedAt:
                        aValue = new Date(recordA.updatedAt).getTime()
                        bValue = new Date(recordB.updatedAt).getTime()
                        break;
                    case MagicColumn.CreatedBy:
                        aValue = recordValueToText(recordA.createdBy)
                        bValue = recordValueToText(recordB.createdBy)
                        break;
                    case MagicColumn.UpdatedBy:
                        aValue = recordValueToText(recordA.updatedBy)
                        bValue = recordValueToText(recordB.updatedBy)
                        break;
                }
            } else if (colType) {
                switch (colType) {
                    case DatabaseFieldDataType.Number:
                        aValue = Number(aValue)
                        bValue = Number(bValue)
                        break;
                    case DatabaseFieldDataType.Checkbox:
                        aValue = !!aValue
                        bValue = !!bValue
                        break;
                    case DatabaseFieldDataType.Date:
                    case DatabaseFieldDataType.UpdatedAt:
                    case DatabaseFieldDataType.CreatedAt:
                        aValue = new Date(recordA.processedRecordValues[columnId] as (string | Date | number)).getTime()
                        bValue = new Date(recordB.processedRecordValues[columnId] as (string | Date | number)).getTime()
                        break;
                    default:
                }
            } else continue

            if (aValue < bValue) {
                return order === Sort.Asc ? -1 : 1;
            } else if (aValue > bValue) {
                return order === Sort.Asc ? 1 : -1;
            }
        }
        return 0
    });

    const sortedRecords: FilterableRecord[] = []
    const sortedProcessedRecords: ProcessedDbRecord[] = []

    for (let index of indexArray) {
        sortedRecords.push(records[index])
        sortedProcessedRecords.push(processedRecords[index])
    }

    return {
        records: sortedRecords,
        processedRecords: sortedProcessedRecords
    }
}

export const filterRecords = (records: FilterableRecord[], processedRecords: ProcessedDbRecord[], filter: DbRecordFilter, definition: DatabaseDefinition, currentUserId = '', currentObjectId = ''): RecordResponse => {
    const filteredRecords: FilterableRecord[] = []
    const filteredProcessedRecords: ProcessedDbRecord[] = []

    for (let i = 0; i < records.length; i++) {
        const record = records[i]
        const processedRecord = processedRecords[i]

        if (filter.conditions.length === 0) {
            filteredRecords.push(record)
            filteredProcessedRecords.push(processedRecord)

            continue;
        }

        const match = filter.match
        let isConditionMatched = match === Match.All;

        for (const condition of filter.conditions) {
            const isMatched = runFilter(condition, record, processedRecord, definition, currentUserId, currentObjectId);
            if (isMatched && match === Match.Any) {
                isConditionMatched = true;
                break
            }
            if (!isMatched && match === Match.All) {
                isConditionMatched = false;
                break
            }
        }
        if (isConditionMatched) {
            filteredRecords.push(record)
            filteredProcessedRecords.push(processedRecord)
        }
    }
    return {
        records: filteredRecords,
        processedRecords: filteredProcessedRecords
    }
}

type DbColValue = boolean | number | string | string[]

export class FilterableRecord {
    id: string
    databaseId: string
    createdById: string
    updatedById: string
    recordValues: RecordValues
    uniqueValue: string;
    createdAt: Date | string
    updatedAt: Date | string
    deletedAt?: Date | string
}

const runFilter = (condition: DbCondition, record: FilterableRecord, processedRecord: ProcessedDbRecord, definition: DatabaseDefinition, currentUserId: string, currentObjectId: string): boolean => {
    let val1: DbColValue = ''
    let val2: DbColValue = ''
    const op = condition.op

    const defaultVal = true;

    if (!condition.columnId) return defaultVal;
    if (!SingleOperandOperators.includes(op) && (typeof condition.value !== 'number' && !condition.value)) {
        return true;
    }

    let colValue = processedRecord.recordValues[condition.columnId]
    let condValue = condition.value

    const column = definition.columnsMap[condition.columnId]
    let colType: DatabaseFieldDataType = column?.type

    if (!colType && Object.values(MagicColumn).includes(condition.columnId as unknown as MagicColumn)) {
        colType = condition.columnId as DatabaseFieldDataType
        switch (condition.columnId as unknown as MagicColumn) {
            case MagicColumn.UUID:
                colValue = record.id
                break;
            case MagicColumn.CreatedAt:
                colValue = new Date(record.createdAt).toISOString()
                break
            case MagicColumn.UpdatedAt:
                colValue = new Date(record.updatedAt).toISOString()
                break;
            case MagicColumn.CreatedBy:
                colValue = record.createdById
                break;
            case MagicColumn.UpdatedBy:
                colValue = record.updatedById
                break;
        }
    }
    if (!colType) return defaultVal;

    switch (colType) {
        case DatabaseFieldDataType.AI:
        case DatabaseFieldDataType.Text:
        case DatabaseFieldDataType.Derived:
            val1 = String(processedRecord.processedRecordValues[condition.columnId] || '')
            val2 = String(condValue)
            break
        case DatabaseFieldDataType.UUID:
            val1 = String(colValue || '')
            val2 = String(condValue)
            break
        case DatabaseFieldDataType.Person:
        case DatabaseFieldDataType.CreatedBy:
        case DatabaseFieldDataType.UpdatedBy:
            val1 = valToArray(colValue)
            val2 = valToArray(condValue).map(v => {
                if (v === CurrentPerson.id) return currentUserId
                return v
            })
            break
        case DatabaseFieldDataType.Linked:
            val1 = valToArray(colValue)
            val2 = valToArray(condValue).map(v => {
                if (v === CurrentObject) return currentObjectId
                return v
            })
            break
        // val1 = recordValueToText(colValue as FileItem[])
        // val2 = condValue
        // break;
        // case DatabaseFieldDataType.Summarize:
        case DatabaseFieldDataType.Select:
            val1 = valToArray(colValue)
            val2 = valToArray(condValue)
            break
        case DatabaseFieldDataType.Number:
            val1 = Number(colValue)
            val2 = Number(condValue)
            break
        case DatabaseFieldDataType.Checkbox:
            val1 = !!colValue
            val2 = !!condValue
            break
        case DatabaseFieldDataType.CreatedAt:
        case DatabaseFieldDataType.UpdatedAt:
        case DatabaseFieldDataType.Date:
            val1 = typeof colValue === 'string' ? colValue : isDateValid(String(colValue)) ? new Date(colValue as unknown as string).toISOString() : ''
            try {
                val2 = Object.values(RelativeDate).includes(condValue as RelativeDate) ? resolveRelativeDate(condValue as RelativeDate).toISOString() : new Date(condValue as string).toISOString()
            } catch (e) {
                val2 = new Date(0).toISOString();
            }
            break
        default:
            return true

    }
    return matchFilterCondition(colType, val1, op, val2)
}

/*
const textOptions: SelectData[] = [
	CompareOperator.Contains,
	CompareOperator.DoesNotContain,
	CompareOperator.Equals,
	CompareOperator.NotEquals,
	CompareOperator.StartsWith,
	CompareOperator.IsEmpty,
	CompareOperator.IsNotEmpty,
  ].map((o) => valueToSelect(o));

  const numberOptions: SelectData[] = [
	CompareOperator.Equals,
	CompareOperator.NotEquals,
	CompareOperator.GreaterThan,
	CompareOperator.LessThan,
	CompareOperator.IsEmpty,
	CompareOperator.IsNotEmpty,
  ].map((o) => valueToSelect(o));

  const dateOptions: SelectData[] = [
	CompareOperator.IsSameDayAs,
	CompareOperator.IsEarlierThan,
	CompareOperator.IsLaterThan,
	CompareOperator.IsEmpty,
	CompareOperator.IsNotEmpty,
  ].map((o) => valueToSelect(o));

  const tagsOptions: SelectData[] = [
	CompareOperator.Equals,
	CompareOperator.NotEquals,
	CompareOperator.Contains,
	CompareOperator.DoesNotContain,
	CompareOperator.IsEmpty,
	CompareOperator.IsNotEmpty,
  ].map((o) => valueToSelect(o));

  const checkboxOptions: SelectData[] = [
	CompareOperator.IsChecked,
	CompareOperator.IsNotChecked,
  ].map((o) => valueToSelect(o));

  */
const matchFilterCondition = (colType: DatabaseFieldDataType, col1: DbColValue, op: CompareOperator, col2: DbColValue) => {
    switch (colType) {
        case DatabaseFieldDataType.Checkbox:
            return compareFields(col1 ? col1 as string : '', op, '')
        case DatabaseFieldDataType.Text:
        case DatabaseFieldDataType.Derived:
        case DatabaseFieldDataType.UUID:
        case DatabaseFieldDataType.Summarize:
        case DatabaseFieldDataType.AI:
            return compareFields(col1 as string, op, col2 as string)
        case DatabaseFieldDataType.CreatedAt:
        case DatabaseFieldDataType.UpdatedAt:
        case DatabaseFieldDataType.Date:
            return compareFields(col1 as string, op, col2 as string)
        case DatabaseFieldDataType.Number:
            return compareFields(col1 as number, op, col2 as number)
        case DatabaseFieldDataType.Select:
        case DatabaseFieldDataType.Person:
        case DatabaseFieldDataType.CreatedBy:
        case DatabaseFieldDataType.UpdatedBy:
        case DatabaseFieldDataType.Linked:
            switch (op) {
                case CompareOperator.Equals:
                    return arraysHaveSameElements(valToArray(col1), valToArray(col2))
                case CompareOperator.NotEquals:
                    return !arraysHaveSameElements(valToArray(col1), valToArray(col2))
                case CompareOperator.Contains:
                    return arrayContains(valToArray(col1), valToArray(col2))
                case CompareOperator.DoesNotContain:
                    return !arrayContains(valToArray(col1), valToArray(col2))
                case CompareOperator.IsEmpty:
                    return valToArray(col1).length === 0
                case CompareOperator.IsNotEmpty:
                    return valToArray(col1).length > 0
                case CompareOperator.IsAnyOf:
                    return arrayContainsAnyOf(valToArray(col1), valToArray(col2));
                case CompareOperator.IsNoneOf:
                    return arrayContainsNoneOf(valToArray(col1), valToArray(col2));
                default:
                    return false
            }
    }
}

const valToArray = (val: DatabaseColumnDbValue): string[] => {
    if (Array.isArray(val)) {
        return val.map(v => String(v))
    }
    return val ? [String(val)] : []
}


export type AggregateFunction = CountAggregateFunction | NumberAggregateFunction | CheckboxAggregateFunction | ShowValuesAggregateFunction
    | PercentAggregateFunction | DateAggregateFunction | CheckPercentAggregateFunction

export const resolveColumnValuesAggregation = (values: DatabaseColumnDbValue[], aggregateFunction: AggregateFunction) => {
    let isArray = false;
    const tagValues: (string | number | boolean | FileItem)[] = []

    let emptyCount = 0
    for (const value of values) {
        const isValArray = Array.isArray(value)
        isArray = isArray || isValArray
        if (isValArray) {
            tagValues.push(...value)
        } else tagValues.push(value as (string | number | boolean | FileItem))
        if (value === undefined || value === null || value === false || !String(value).trim()) {
            emptyCount += 1
        } else if (isValArray && Array.isArray(value) && value.length === 0) {
            emptyCount += 1
        }
    }
    const numericValues = values.map((value) => Number(value)).filter((value) => !isNaN(value));

    const uniqueValues = isArray ? arrayDeDuplicate(tagValues) : arrayDeDuplicate(values)


    switch (aggregateFunction) {
        case ShowValuesAggregateFunction.ShowOriginal:
            return values
        case ShowValuesAggregateFunction.ShowUnique:
            return uniqueValues;
        case CountAggregateFunction.CountAll:
            return values.length;
        case CountAggregateFunction.CountValues:
            return isArray ? tagValues.length : values.length;
        case CountAggregateFunction.CountUnique:
            return uniqueValues.length;
        case CountAggregateFunction.CountEmpty:
            return emptyCount
        case CountAggregateFunction.CountNotEmpty:
            return values.length - emptyCount
        case  PercentAggregateFunction.PercentEmpty:
            return emptyCount / values.length * 100;
        case  PercentAggregateFunction.PercentNotEmpty:
            return (values.length - emptyCount) / values.length * 100;
        case CheckboxAggregateFunction.CountChecked:
            return values.length - emptyCount
        case CheckboxAggregateFunction.CountUnchecked:
            return emptyCount
        case  CheckPercentAggregateFunction.PercentChecked:
            return (values.length - emptyCount) / values.length * 100;
        case  CheckPercentAggregateFunction.PercentNotChecked:
            return emptyCount / values.length * 100;
        case DateAggregateFunction.DateRange:
        case DateAggregateFunction.EarliestDate:
        case DateAggregateFunction.LatestDate:
            // const dates: Date[] = []
            let earliestDate: Date
            let latestDate: Date
            for (const value of values) {
                if (!value) continue
                const date = new Date(String(value))
                if (isDateObjValid(date)) {
                    // dates.push(date)
                    if (!earliestDate || date < earliestDate) earliestDate = date
                    if (!latestDate || date > latestDate) latestDate = date
                }
            }
            if (aggregateFunction === DateAggregateFunction.DateRange && earliestDate && latestDate) return [earliestDate, latestDate]
            if (aggregateFunction === DateAggregateFunction.EarliestDate && earliestDate) return [earliestDate]
            if (aggregateFunction === DateAggregateFunction.LatestDate && latestDate) return [latestDate]
            return null
        case NumberAggregateFunction.Sum:
            return numericValues.reduce((sum, value) => sum + value, 0);
        case NumberAggregateFunction.Min:
            return Math.min(...numericValues);
        case NumberAggregateFunction.Max:
            return Math.max(...numericValues);
        case NumberAggregateFunction.Average:
            if (numericValues.length === 0) {
                return 0; // Handle division by zero
            }
            const sum = numericValues.reduce((acc, value) => acc + value, 0);
            return sum / numericValues.length;
        case NumberAggregateFunction.Range:
            if (numericValues.length === 0) return 0
            const min = Math.min(...numericValues)
            const max = Math.max(...numericValues)
            return max - min
        default:
            throw new Error(`Unsupported aggregate function: ${aggregateFunction}`);
    }

}


function getDatabasePackageName(versioned: VersionedDefinition) {
    const {domain, versionName, templateName, versionNumber} = versioned
    return `${domain}.${templateName}`
}




