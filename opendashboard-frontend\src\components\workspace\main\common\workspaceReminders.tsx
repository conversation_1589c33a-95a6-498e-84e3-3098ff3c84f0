import React, {useEffect, useRef, useState} from "react";
import {ScrollArea} from "@/components/ui/scroll-area";
import {Checkbox} from "@/components/ui/checkbox";
import {DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {Button} from "@/components/ui/button";
import {ChevronDownIcon, EllipsisVerticalIcon} from "@heroicons/react/24/outline";
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import {CheckIcon} from "@radix-ui/react-icons";
import {Dialog, DialogContent, DialogHeader, DialogTitle} from "@/components/ui/dialog";
import {Input} from "@/components/ui/input";
import {Label} from "@/components/ui/label";
import {Textarea} from "@/components/ui/textarea";
import {timeAgo} from "@/utils/timeAgo";
import {useWorkspace} from "@/providers/workspace";
import {useAuth} from "@/providers/user";
import {useAlert} from "@/providers/alert";
import {ItemDataLoad} from "@/api/common";
import {MyWorkspaceMember, WorkspaceReminder} from "@/typings/workspace";
import {createReminder, deleteReminder, GetReminderParams, getReminders, resolveReminder, updateReminder} from "@/api/workspace";
import {PageLoader} from "@/components/custom-ui/loader";
import {ClockThreeIcon, XmarkIcon} from "@/components/icons/FontAwesomeRegular";
import {DatePickerUI} from "@/components/custom-ui/datePicker";
import {arrayRemoveElementAtIndex, datePlusDays, datePlusMonths} from "opendb-app-db-utils/lib";
import {PersonSelect} from "@/components/workspace/main/common/personSelect";
import {getDatabaseTitleCol, getRecordTitle} from "@/components/workspace/main/views/form/components/element/linked";
import {useWorkspaceSocket} from "@/providers/workspaceSocket";

export interface WorkspaceReminderCallback {
    saveReminder: (reminder?: WorkspaceReminder) => void
}

interface WorkspaceRemindersProps {
    recordId?: string | null
    databaseId?: string | null
    newReminderTitle?: string
    isNotificationRender?: boolean
    hideTitle?: boolean
    filter?: 'open' | 'resolved'
    onReady?: (callback: WorkspaceReminderCallback) => void // atleast first page is loaded
    onResolved?: (reminder: WorkspaceReminder, resolved: boolean) => void
}

export const WorkspaceReminders = (props: WorkspaceRemindersProps) => {
    const {workspace, membersMap} = useWorkspace()
    const {token} = useAuth()
    const {toast} = useAlert()

    const perPage = 24
    const [page, setPage] = useState(1)
    const [load, setLoad] = useState<ItemDataLoad<{ reminders: WorkspaceReminder[] }>>({})
    const [hasMore, setHasMore] = useState(true)
    const [isCreating, setIsCreating] = useState(false)
    const [stateFilter, setFilter] = useState<'open' | 'resolved' | undefined>()
    const filter = props.filter || stateFilter

    const [focus, setFocus] = useState<WorkspaceReminder>()

    const loadReminders = async (page = 1) => {
        if (!token) return
        setLoad({isLoading: true, error: undefined})

        const params: GetReminderParams = {
            perPage,
            page
        }
        if (props.recordId && props.databaseId) {
            params.type = 'record'
            params.recordId = props.recordId
            params.databaseId = props.databaseId
        } else {
            params.type = 'user'
        }
        params.filter = filter
        const res = await getReminders(token.token, workspace.workspace.id, params)
        if (res.error) {
            setLoad({isLoading: false, error: res.error})
            if (page > 1) toast.error(res.error)
            return
        }
        const oldData = load?.data || {reminders: []}
        if (page === 1) oldData.reminders = []
        setLoad({isLoading: false, data: {reminders: [...oldData.reminders, ...res.data.data.reminders]}})
        setPage(page)

        if (res.data.data.reminders.length === 0 || (page === 1 && res.data.data.reminders.length < perPage)) setHasMore(false)
    }

    const newReminder = () => {
        setIsCreating(true)
    }

    const loadMore = () => {
        loadReminders(page + 1).then()
    }

    const onDialogClose = (reminder?: WorkspaceReminder) => {
        setFocus(undefined)
        setIsCreating(false)
        if (!reminder) return

        if (!load.data) return

        setLoad(load => {
            const reminders = [...(load.data?.reminders || [])]
            let isNew = true
            for (let i = 0; i < reminders.length; i++) {
                let reminder1 = reminders[i];
                if (reminder1.reminder.id === reminder.reminder.id) {
                    reminders[i] = reminder
                    isNew = false
                    break
                }
            }
            if (isNew) {
                reminders.unshift(reminder)
            }
            return {...load, data: {reminders}}
        })
    }

    const onResolve = (reminder: WorkspaceReminder, resolve: boolean) => {
        if (!load.data) return
        const reminders = [...load.data.reminders]
        for (let reminder1 of reminders) {
            if (reminder1.reminder.id === reminder.reminder.id) {
                reminder1.reminder.isResolved = resolve
                if (resolve) {
                    reminder1.reminder.resolvedAt = new Date().toISOString()
                }
            }
        }
        setLoad({data: {reminders}})
        props.onResolved?.(reminder, resolve)
    }

    const onDelete = (id: string) => {
        if (!load.data) return
        const reminders = [...load.data.reminders].filter(r => r.reminder.id !== id)
        setLoad({data: {reminders}})
        setFocus(undefined)
    }

    useEffect(() => {
        if (load.data && props.onReady) {
            const cb: WorkspaceReminderCallback = {
                saveReminder: onDialogClose
            }
            props.onReady(cb)
        }
    })

    useEffect(() => {
        setLoad({data: undefined})
        loadReminders(1).then()
    }, [filter]);

    const {socket, isConnected} = useWorkspaceSocket()

    const loadRef = useRef(load)
    loadRef.current = load
    useEffect(() => {
        if (!socket || !isConnected) return

        socket.on("new-reminder", (d: {
            reminder: WorkspaceReminder
        }) => {
            console.log("New reminder callback", d)
            if (!loadRef.current.data) return

            const matchCurrent = d.reminder.reminder.recordId === props.recordId && d.reminder.reminder.databaseId === props.databaseId
            if (!matchCurrent)  return


            const reminders = [...(loadRef.current.data?.reminders || [])]
            const index = reminders.findIndex(r => r.reminder.id === d.reminder.reminder.id)
            if (index === -1) {
                reminders.unshift(d.reminder)
            } else {
                reminders[index].reminder = d.reminder.reminder
            }
            setLoad({...loadRef.current, data: {reminders}})
        })

        socket.on("deleted-reminder", (d: {
            reminder: WorkspaceReminder
        }) => {
            console.log("Deleted reminder callback", d)
            if (!loadRef.current.data) return
            const matchCurrent = d.reminder.reminder.recordId === props.recordId && d.reminder.reminder.databaseId === props.databaseId
            if (!matchCurrent) return

            const reminders = [...(loadRef.current.data?.reminders || [])].filter(r => r.reminder.id !== d.reminder.reminder.id)
            setLoad({...loadRef.current, data: {reminders}})
        })

        console.log("Reminders listener defined")
        return () => {
            if (!socket) return
            socket.off('new-reminder')
            socket.off('deleted-reminder')

            console.log("Reminders listener cleared")

        }
    }, [isConnected, socket])

    return <>
        <div className="h-full w-full flex flex-col">
            {!props.isNotificationRender && !props.hideTitle && <div className="flex p-4 gap-2">
                <h2 className="font-semibold flex-1">Reminders</h2>
                {load.data && <>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="rounded-full h-auto p-2 px-3 text-xs gap-2">
                                <span className='capitalize'>{filter || 'Filter'}</span> <ChevronDownIcon className="size-3"/>
                            </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-56  rounded-none text-neutral-800 font-semibold" align="end">
                            <DropdownMenuGroup className="p-1">
                                <DropdownMenuItem
                                    onClick={() => setFilter(undefined)}
                                    className="text-xs rounded-none p-2 flex gap-2">
                                    <div className="flex-1">All</div>
                                    {!filter && <CheckIcon className="size-4"/>}
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => setFilter('open')}
                                    className="text-xs rounded-none p-2 flex gap-2">
                                    <div className="flex-1">Open</div>
                                    {filter === 'open' && <CheckIcon className="size-4"/>}
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => setFilter('resolved')}
                                    className="text-xs rounded-none p-2 flex gap-2">
                                    <div className="flex-1">Resolved</div>
                                    {filter === 'resolved' && <CheckIcon className="size-4"/>}
                                </DropdownMenuItem>
                            </DropdownMenuGroup>
                        </DropdownMenuContent>
                    </DropdownMenu>
                    <Button
                        onClick={newReminder}
                        className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                        New reminder
                    </Button>
                </>}
            </div>}

            <div className="flex-1 overflow-hidden">
                {page === 1 && (load.isLoading || load.error || !load.data) && <>
                    <PageLoader
                        error={load.error}
                        size='full'
                        cta={load.error ? {'label': 'Retry', onClick: loadReminders} : undefined}/>
                </>}
                {load.data && load.data.reminders.length === 0 && <>
                    <PageLoader
                        error={"It's empty here"}
                        size='full'
                        icon={<ClockThreeIcon className="size-12"/>}
                        cta={{'label': 'Create New', onClick: newReminder}}/>
                </>}
                {load.data && load.data.reminders.length > 0 && <>
                    <ScrollArea className="w-full h-full scrollBlockChild">
                        <div className={`p-4 pt-0 pb-12 w-full overflow-x-hidden ${props.isNotificationRender && '!p-0'}`}>
                            {props.isNotificationRender ? <>
                                <div className="w-full flex flex-col pb-10 overflow-x-hidden">
                                    {load.data.reminders.map((r, i) => <ReminderRender
                                        membersMap={membersMap}
                                        onResolved={resolve => onResolve(r, resolve)}
                                        onClick={() => setFocus(r)}
                                        onDelete={() => onDelete(r.reminder.id)}
                                        isNotificationRender={props.isNotificationRender}
                                        reminder={r} key={r.reminder.id}/>)}
                                </div>
                            </> : <>
                                 <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                     {load.data.reminders.map((r, i) => <ReminderRender
                                         membersMap={membersMap}
                                         onResolved={resolve => onResolve(r, resolve)}
                                         onClick={() => setFocus(r)}
                                         onDelete={() => onDelete(r.reminder.id)}
                                         isNotificationRender={props.isNotificationRender}
                                         reminder={r} key={r.reminder.id}/>)}
                                 </div>
                             </>}
                            {hasMore && <>
                                <div className='flex justify-center my-8 pb-16'>
                                    <Button
                                        variant="link"
                                        disabled={load.isLoading}
                                        onClick={loadMore}
                                        className="text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                                        {load.isLoading ? 'Loading...' : 'Load More'}
                                    </Button>
                                </div>
                            </>}
                        </div>
                    </ScrollArea>
                </>}
                {(focus || isCreating) && <EditReminderDialog
                    reminder={focus}
                    initialize={{title: props.newReminderTitle, databaseId: props.databaseId, recordId: props.recordId}}
                    onClose={onDialogClose}/>}
            </div>
        </div>

    </>
}

interface ReminderRenderProps {
    onClick: () => void
    onDelete: () => void
    reminder: WorkspaceReminder
    onResolved: (resolve: boolean) => void
    membersMap: { [p: string]: MyWorkspaceMember }
    isNotificationRender?: boolean
}

const ReminderRender = (props: ReminderRenderProps) => {
    let recordTitle = ''
    let recordImage = ''
    if (props.reminder.database && props.reminder.record) {
        const {defaultTitle, titleColId, isContacts} = getDatabaseTitleCol(props.reminder.database)
        recordTitle = getRecordTitle(props.reminder.record, titleColId, defaultTitle, isContacts)
    }
    const assignedToUserIds = (props.reminder?.reminder.assignedToUserIds || '').split(',');

    const member: MyWorkspaceMember | undefined = props.membersMap[assignedToUserIds[0]]
    const memberName = `${member?.user?.firstName || ''} ${member?.user?.lastName || ''}`.trim() || 'Unknown member'

    const {confirm, toast} = useAlert()
    const {token, user} = useAuth()

    const [isResolving, setIsResolving] = useState(false)

    const resolve = async (resolved: boolean) => {
        if (isResolving || !token) return
        setIsResolving(true)
        const res = await resolveReminder(token.token, props.reminder.reminder.workspaceId, props.reminder.reminder.id, {resolved})
        setIsResolving(false)
        if (res.error) {
            toast.error("Error resolving reminder:" + res.error)
            return
        }
        props.onResolved?.(resolved)
    }

    const [isDeleting, setIsDeleting] = useState(false)

    const doDelete = async () => {
        if (isDeleting || !token) return
        setIsDeleting(true)
        const res = await deleteReminder(token.token, props.reminder.reminder.workspaceId, {id: props.reminder.reminder.id})
        setIsDeleting(false)
        if (res.error) {
            toast.error("Error deleting reminder:" + res.error)
            return
        }
        props.onDelete?.()
    }

    const confirmDelete = () => {
        confirm("Delete reminder?", "This cannot be reversed", async () => {
            doDelete().then()
        })
    }

    return (
        <>
            {props.isNotificationRender ? <>

                <div
                    onClick={props.onClick}
                    className="flex gap-1 h-auto text-left p-2 pr-4 border-b border-neutral-200 items-center select-none cursor-pointer text-black overflow-hidden">
                    <Avatar className="size-8 mx-1">
                        <AvatarImage className="size-full" src={member?.user?.profilePhoto}/>
                        <AvatarFallback className='text-xs text-[10px]'>{memberName[0]}</AvatarFallback>
                    </Avatar>
                    <div className={`flex-1 text-xs flex flex-col gap-1.5 overflow-hidden ${props.reminder.reminder.isResolved && 'opacity-65'}`}>
                        <div className={`truncate font-semibold ${props.reminder.reminder.isResolved && 'line-through'}`}>
                            {props.reminder.reminder.title}
                        </div>
                        <div className={`truncate text-muted-foreground text-[11px] ${props.reminder.reminder.isResolved && 'line-through'}`}>
                            {props.reminder.reminder.description}
                        </div>
                        <div className="text-xs text-[10px] font-medium">
                            {props.reminder.reminder.isResolved && props.reminder.reminder.resolvedAt ? <>Resolved {timeAgo(new Date(props.reminder.reminder.resolvedAt))}</> : <>Created {timeAgo(new Date(props.reminder.reminder.createdAt))}</>}
                        </div>
                    </div>
                    <div className="ml-1">
                        <Checkbox
                            onClick={e => e.stopPropagation()}
                            disabled={isResolving}
                            onCheckedChange={resolve}
                            checked={props.reminder.reminder.isResolved}/>
                    </div>

                </div>
            </> : <>
                 <div
                     onClick={props.onClick}
                     className="overflow-hidden p-3 border border-neutral-200 flex flex-col gap-2 hover:border-black transition-all relative select-none">
                     <div className='flex gap-2'>
                         <Button className='!p-0.5 gap-2 text-xs text-[10px] !h-auto hover:bg-neutral-200 truncate' variant='ghost'>
                             <Avatar className="size-4">
                                 <AvatarImage className="size-full" src={recordImage}/>
                                 <AvatarFallback className='text-xs text-[10px]'>{(recordTitle || 'Untitled')[0]}</AvatarFallback>
                             </Avatar>
                             <span className='text-xs text-[10px] font-semibold underline decoration-dashed underline-offset-4 decoration-neutral-300'>{recordTitle}</span>
                         </Button>
                         <div className='flex-1'></div>
                     </div>
                     <div className='absolute right-2 top-2'>
                         <DropdownMenu>
                             <DropdownMenuTrigger asChild>
                                 <Button variant="ghost" className="rounded-full h-auto p-1.5 size-6 text-xs gap-2">
                                     <EllipsisVerticalIcon className="size-full"/>
                                 </Button>
                             </DropdownMenuTrigger>
                             <DropdownMenuContent className="w-28 rounded-none text-neutral-800 font-semibold"
                                                  align="end">
                                 <DropdownMenuItem
                                     disabled={isDeleting}
                                     onClick={(e) => {
                                         e.preventDefault()
                                         e.stopPropagation()
                                         confirmDelete()
                                     }} className="text-xs rounded-none p-2 flex gap-2">
                                     Delete
                                 </DropdownMenuItem>
                             </DropdownMenuContent>
                         </DropdownMenu>
                     </div>

                     <div className="flex gap-2 items-center">
                         <div className="flex-1 overflow-hidden text-xs font-semibold">
                             <div className={`text-xs font-semibold truncate ${props.reminder.reminder.isResolved && 'line-through'}`}>
                                 {props.reminder.reminder.title}
                             </div>
                         </div>
                         <Checkbox
                             onClick={e => e.stopPropagation()}
                             disabled={isResolving}
                             onCheckedChange={resolve}
                             checked={props.reminder.reminder.isResolved}/>
                     </div>
                     <div className={`text-xs text-muted-foreground font-medium text-[10px] h-16 overflow-hidden ${props.reminder.reminder.isResolved && 'line-through'}`}>
                         {props.reminder.reminder.description}
                     </div>
                     <div className='flex gap-2 text-xs text-muted-foreground font-medium text-[10px] border-t pt-2 overflow-hidden'>
                         <div className='flex-1 overflow-hidden'>
                             <div className='!p-0 gap-2 text-xs text-[10px] flex !h-auto'>
                                 <Avatar className="size-4">
                                     <AvatarImage className="size-full" src={member?.user?.profilePhoto}/>
                                     <AvatarFallback className='text-xs text-[10px]'>{memberName[0]}</AvatarFallback>
                                 </Avatar>
                                 <span className='text-xs text-[10px] truncate'>{memberName}</span>
                                 {assignedToUserIds.length > 1 && <span className='whitespace-nowrap'>+{assignedToUserIds.length - 1}</span>}
                             </div>
                         </div>
                         <div>
                             {props.reminder.reminder.isResolved && props.reminder.reminder.resolvedAt ? <>Resolved {timeAgo(new Date(props.reminder.reminder.resolvedAt))}</> : <>Created {timeAgo(new Date(props.reminder.reminder.createdAt))}</>}
                         </div>
                     </div>
                 </div>
             </>}

        </>
    )
}


interface EditReminderDialogProps {
    isCompleted?: boolean;
    initialize?: {
        title?: string
        recordId?: string | null
        databaseId?: string | null
    }
    reminder?: WorkspaceReminder
    onClose: (reminder?: WorkspaceReminder) => void
}

export const MaxNotifyAt = 3

export const EditReminderDialog = (props: EditReminderDialogProps) => {
    const {token, user} = useAuth()
    const {workspace} = useWorkspace()
    const {toast} = useAlert()

    const defaultNotifyDates = () => {
        const dates = props.reminder?.reminder.notifyDates || []
        if (dates.length === 0) {
            const _1day = new Date(datePlusDays(new Date(), 1).setMinutes(0, 0, 0))
            dates.push(_1day.toISOString())
        }
        return dates
    }

    const defaultTitle = () => {
        if (props.reminder) return props.reminder.reminder.title
        return props.initialize?.title || 'Remind me about ...'
    }

    const defaultAssignedTo = () => {
        if (props.reminder) return (props.reminder.reminder.assignedToUserIds || '').split(',').filter(id => !!id.trim())
        return [workspace.workspaceMember.userId]
    }

    const [title, setTitle] = useState(defaultTitle())
    const [description, setDescription] = useState(props.reminder?.reminder.description || '')
    const [assignedToUserIds, setAssignedToUserIds] = useState<string[]>(defaultAssignedTo())
    const [notifyDates, setNotifyDates] = useState<string[]>(defaultNotifyDates())

    const [saving, setSaving] = useState(false)

    const saveDate = (index: number, date: Date | null) => {
        if (!date) return
        const dates = [...notifyDates]
        dates[index] = date.toISOString()
        setNotifyDates(dates)
    }

    const removeDate = (index: number) => {
        const dates = arrayRemoveElementAtIndex(notifyDates, index)
        setNotifyDates(dates)
    }

    const addDate = (date: Date) => {
        date = new Date(date.setMinutes(0, 0, 0))
        const dates = [...notifyDates]
        dates.push(date.toISOString())
        setNotifyDates(dates)
    }

    const saveReminder = async () => {
        if (!token) return
        if (!props.reminder) {
            return doCreateReminder()
        }
        setSaving(true)
        const res = await updateReminder(token.token, workspace.workspace.id, {
            id: props.reminder.reminder.id,
            title,
            assignedToUserIds,
            description,
            recordId: props.reminder?.reminder.recordId,
            databaseId: props.reminder?.reminder.databaseId,
            notifyDates
        })
        setSaving(false)
        if (res.error) {
            toast.error('Error saving title: ' + res.error)
            return
        }
        props.onClose(res.data.data.reminder)
    }

    const doCreateReminder = async () => {
        if (!token) return
        setSaving(true)
        const res = await createReminder(token.token, workspace.workspace.id, {
            title,
            assignedToUserIds,
            description,
            recordId: props.initialize?.recordId,
            databaseId: props.initialize?.databaseId,
            notifyDates
        })
        setSaving(false)
        if (res.error) {
            toast.error('Error saving title: ' + res.error)
            return
        }
        props.onClose(res.data.data.reminder)
    }

    const isReady = !!title.trim() && assignedToUserIds.length > 0
    return (<>
        <Dialog open onOpenChange={() => props.onClose()}>
            <DialogContent className="max-w-[600px] !rounded-none p-4">
                <DialogHeader>
                    <DialogTitle className="font-bold"> {props.reminder ? "Edit" : "New"} Reminder</DialogTitle>
                </DialogHeader>
                <div className="flex flex-col gap-2 py-2">
                    <div className="flex flex-col flex-1 gap-0.5">
                        <Label htmlFor='name' className="block text-xs font-medium leading-6 text-gray-900">Title</Label>
                        <Input
                            id="name"
                            value={title}
                            onChange={e => setTitle(e.target.value)}
                            type="text"
                            autoCapitalize="none"
                            autoComplete="name"
                            autoCorrect="off"
                            className="rounded-none text-xs"
                            disabled={saving}
                        />
                    </div>
                    <div className="flex flex-col flex-1 gap-0.5">
                        <Label htmlFor='description' className="block text-xs font-medium leading-6 text-gray-900">Description</Label>
                        <Textarea
                            id="description"
                            autoCapitalize="none"
                            rows={3}
                            value={description}
                            onChange={e => setDescription(e.target.value)}
                            className="rounded-none text-xs"
                        />
                    </div>
                    <div className="flex flex-col flex-1 gap-0.5">
                        <Label className="block text-xs font-medium leading-6 text-gray-900">Notify At (Up to {MaxNotifyAt})</Label>
                        <div className='flex flex-col gap-2'>
                            {notifyDates.map((n, i) => {
                                return <div className='flex gap-2 items-center' key={i}>
                                    <div className='flex-1'>
                                        <DatePickerUI
                                            startDate={new Date()}
                                            showTimeSelect
                                            value={n}
                                            dateFormat={"yyyy-MM-dd HH:mm"}
                                            onChange={d => saveDate(i, d)}/>
                                    </div>
                                    {notifyDates.length > 1 && <Button
                                        onClick={() => removeDate(i)}
                                        variant="ghost" className='size-6 p-1.5 rounded-full'>
                                        <XmarkIcon className='size-full'/>
                                    </Button>}
                                </div>
                            })}
                        </div>
                    </div>
                    {notifyDates.length < MaxNotifyAt && <div className="flex gap-0.5">
                        {/*<Button*/}
                        {/*    onClick={() => addDate(datePlusHours(new Date(), 2))}*/}
                        {/*    variant="ghost"*/}
                        {/*    className="text-xs p-2 px-3 h-auto w-auto rounded-full font-medium gap-1">*/}
                        {/*    In 2 hours*/}
                        {/*</Button>*/}
                        <Button
                            onClick={() => addDate(datePlusDays(new Date(), 1))}
                            variant="ghost"
                            className="text-xs p-2 px-3 h-auto w-auto rounded-full font-medium gap-1">
                            Tomorrow
                        </Button>
                        <Button
                            onClick={() => addDate(datePlusDays(new Date(), 7))}
                            variant="ghost"
                            className="text-xs p-2 px-3 h-auto w-auto rounded-full font-medium gap-1">
                            In a week
                        </Button>
                        <Button
                            onClick={() => addDate(datePlusMonths(new Date(), 1))}
                            variant="ghost"
                            className="text-xs p-2 px-3 h-auto w-auto rounded-full font-medium gap-1">
                            In a month
                        </Button>
                        <Button
                            onClick={() => addDate(datePlusMonths(new Date(), 6))}
                            variant="ghost"
                            className="text-xs p-2 px-3 h-auto w-auto rounded-full font-medium gap-1">
                            In 6 months
                        </Button>
                        <Button
                            onClick={() => addDate(datePlusMonths(new Date(), 12))}
                            variant="ghost"
                            className="text-xs p-2 px-3 h-auto w-auto rounded-full font-medium gap-1">
                            In a year
                        </Button>
                    </div>}
                    <div className="flex flex-col flex-1 gap-0.5">
                        <Label className="block text-xs font-medium leading-6 text-gray-900">Who</Label>
                        <PersonSelect
                            className='h-8'
                            selectedIds={assignedToUserIds}
                            onChange={setAssignedToUserIds}
                            isMulti
                        />
                    </div>
                </div>
                <div>
                    <Button
                        onClick={saveReminder}
                        disabled={!isReady || saving}
                        className="mr-2 text-xs p-2 px-3 h-auto w-auto rounded-full font-semibold gap-1">
                        {props.reminder ? "Save" : "Create"} Reminder
                    </Button>
                </div>
            </DialogContent>
        </Dialog>

    </>)
}
