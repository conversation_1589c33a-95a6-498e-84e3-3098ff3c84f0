"use client";

import React, { useState, useMemo } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog";
import { Database } from "@/typings/database";
import { DatabaseFieldDataType } from "opendb-app-db-utils/lib/typings/db";
import { useAlert } from "@/providers/alert";
import { useAuth } from "@/providers/user";
import { useWorkspace } from "@/providers/workspace";
import { setTitleFormat as setTitleFormatAPI } from "@/api/database";
import { MentionInput, MentionInfo } from "@/components/custom-ui/mentionInput";

interface ConfigureTitleDialogProps {
    database: Database;
    close: () => void;
}

export const ConfigureTitleDialog: React.FC<ConfigureTitleDialogProps> = ({database, close}) => {
    const { toast } = useAlert();
    const { token } = useAuth();
    const { workspace } = useWorkspace();

    // Get initial title format
    const getInitialTitleFormat = (): string => {
        if (database?.definition?.titleFormat && typeof database.definition.titleFormat === 'string') {
            return database.definition.titleFormat;
        }
        if (database?.definition?.titleColumnId && typeof database.definition.titleColumnId === 'string') {
            return `{{${database.definition.titleColumnId}}}`;
        }
        return '';
    };

    const [titleFormat, setTitleFormat] = useState<string>(getInitialTitleFormat());

    // Create keyMap for MentionInput from available columns
    const keyMap = useMemo((): Record<string, MentionInfo> => {
        const map: Record<string, MentionInfo> = {};

        // Filter columns that can be used in title format
        const availableColumns = Object.values(database.definition.columnsMap)
            .filter(column => {
                // Include columns that display readable text values
                if (column.type === DatabaseFieldDataType.Text) return true;
                if (column.type === DatabaseFieldDataType.Number) return true;
                if (column.type === DatabaseFieldDataType.Date) return true;
                if (column.type === DatabaseFieldDataType.CreatedAt) return true;
                if (column.type === DatabaseFieldDataType.UpdatedAt) return true;
                if (column.type === DatabaseFieldDataType.Select) return true;
                if (column.type === DatabaseFieldDataType.Checkbox) return true;
                if (column.type === DatabaseFieldDataType.AI) return true;
                if (column.type === DatabaseFieldDataType.Person) return true;
                if (column.type === DatabaseFieldDataType.CreatedBy) return true;
                if (column.type === DatabaseFieldDataType.UpdatedBy) return true;
                if (column.type === DatabaseFieldDataType.Summarize) return true;
                if (column.type === DatabaseFieldDataType.Derived) return true;

                // Exclude columns that show IDs or are complex/not user-friendly
                return false;
            });

        // Create keyMap entries for each column
        availableColumns.forEach(column => {
            map[column.id] = {
                label: column.title,
                tag: `{{${column.id}}}`
            };
        });

        return map;
    }, [database.definition.columnsMap]);

    const handleSave = async () => {
        try {
            if (!titleFormat || typeof titleFormat !== 'string' || !titleFormat.trim()) {
                toast.error("Please configure a title format");
                return;
            }

            if (!token) {
                toast.error("Authentication required");
                return;
            }

            // Clean the title format - remove invisible Unicode characters that MentionInput adds
            const cleanTitleFormat = titleFormat
                .replace(/[\u200B-\u200D\uFEFF]/g, '') // Remove zero-width spaces and similar
                .replace(/\s+/g, ' ') // Normalize multiple spaces to single space
                .trim();

            // DEBUG: Log what we're sending
            console.log("🔍 DEBUG - Saving title format:", {
                originalTitleFormat: titleFormat,
                cleanedTitleFormat: cleanTitleFormat,
                workspaceId: workspace.workspace.id,
                databaseId: database.id,
                currentDefinition: database.definition
            });

            // Use the proper API to update database definition
            const response = await setTitleFormatAPI(token.token, workspace.workspace.id, database.id, cleanTitleFormat);

            // DEBUG: Log the response
            console.log("🔍 DEBUG - API Response:", response);

            if (response.isSuccess && !response.error) {
                toast.success("Title format updated successfully");

                // DEBUG: Check if the database definition was updated
                console.log("🔍 DEBUG - Database definition after save:", database.definition);

                close();
            } else {
                throw new Error(response.error || 'Failed to update title format');
            }
        } catch (error) {
            console.error("❌ ERROR saving title format:", error);
            toast.error("Failed to update title format");
        }
    };

    return (
        <Dialog open={true} onOpenChange={close}>
            <DialogContent className="sm:max-w-md md:max-w-lg w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
                <DialogHeader className="pb-2">
                    <DialogTitle>Configure Record Title</DialogTitle>
                    <p className="text-xs text-gray-500 mt-1">
                        Type @ to mention columns and create custom record titles
                    </p>
                </DialogHeader>

                <div className="flex-grow flex flex-col overflow-hidden">
                    {/* MentionInput for title format */}
                    <div className="mb-4">
                        <label className="text-sm font-medium text-gray-700 mb-2 block">
                            Title Format
                        </label>
                        <MentionInput
                            keyMap={keyMap}
                            value={titleFormat}
                            onChange={setTitleFormat}
                            placeholder="Type @ to mention columns... e.g., @firstName @lastName"
                            className="w-full min-h-12 text-sm border rounded-md p-3"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                            Example: @firstName @lastName - @company
                        </p>
                    </div>
                </div>

                <DialogFooter className="mt-4 flex-shrink-0 sm:justify-between flex flex-col-reverse sm:flex-row gap-2 sm:gap-0">
                    <Button
                        variant="outline"
                        onClick={close}
                        className="sm:mr-2 w-full sm:w-auto"
                    >
                        Cancel
                    </Button>
                    <Button
                        onClick={handleSave}
                        className="w-full sm:w-auto"
                        disabled={!titleFormat || typeof titleFormat !== 'string' || !titleFormat.trim()}
                    >
                        Save
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
};
