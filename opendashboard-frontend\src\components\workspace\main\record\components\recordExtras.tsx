"use client";

import {<PERSON><PERSON>} from "@/components/ui/button";
import {ViewIcon} from "@/components/workspace/main/views/viewIcon";
import {subLinks} from "@/utils/demo/links";
import React, {useEffect, useState} from "react";
import {DropdownMenu, DropdownMenuContent, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {ArrowPathIcon, CogIcon, EllipsisHorizontalIcon, GlobeAltIcon, PlusCircleIcon} from "@heroicons/react/24/outline";
import Link from "next/link";
import {DNDSortable, SortItem} from "@/components/custom-ui/dndSortable";
import {SubLink} from "@/components/workspace/main/common/navLinks";
import {RecordActivities} from "@/components/workspace/main/record/components/extra/recordActivities";
import {Tab, TabView} from "@/components/custom-ui/tabView";
import {WorkspaceReminders} from "@/components/workspace/main/common/workspaceReminders";
import {BoltIcon, BookIcon, ChartPieIcon, ClockThreeIcon, MessageDotsIcon, NoteIcon} from "@/components/icons/FontAwesomeRegular";
import {ViewCreator} from "@/components/workspace/main/views/common/viewCreator";
import {useScreenSize} from "@/providers/screenSize";
import {RecordSummary} from "@/components/workspace/main/record/components/extra/recordSummary";
import dynamic from "next/dynamic";
import {WorkspaceNotes} from "@/components/workspace/main/common/workspaceNotes";
import {useRecord} from "@/providers/record";
import {getDatabaseTitleCol, getRecordTitle} from "@/components/workspace/main/views/form/components/element/linked";
import {useWorkspaceSocket} from "@/providers/workspaceSocket";
import {WorkspaceHandlerEvent} from "@/typings/socket";
import {WorkspaceMemberSettingsKey} from "@/typings/workspace";
import {isLocal} from "@/utils/environment";

const RecordOverview = dynamic(
    () => import('@/components/workspace/main/record/components/recordOverview').then((module) => module.RecordOverview),
    {ssr: false}
    // { loading: () => <p>Loading...</p> }
);

export const RecordExtras = ({showOverview}: {showOverview?: boolean}) => {
    const {recordInfo, database} = useRecord()
    const {id, databaseId} = recordInfo.record
    const {isMobile} = useScreenSize()
    const {socket, isConnected} = useWorkspaceSocket()
    const [tabsOrder, setTabsOrder] = useState<string[]>([])

    const {defaultTitle, isContacts, titleColId} = getDatabaseTitleCol(database)
    // Use the processed title if available, otherwise fall back to getRecordTitle
    const title = recordInfo.record.title || getRecordTitle(recordInfo.record, titleColId, defaultTitle, isContacts)
    //database, members)

    // Define the default tabs
    const defaultTabs: Tab[] = [{
        id: "summary",
        title: <>
            <BookIcon className="size-3"/>
            <div className="overflow-hidden truncate flex-1 font-semibold">
                Summary
            </div>
        </>,
        content: <RecordSummary/>
    },
        {
            id: "activity",
            title: <>
                <BoltIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Activities
                </div>
            </>,
            content: <RecordActivities/>
        },
        {
            id: "notes",
            title: <>
                <NoteIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Notes
                </div>
            </>,
            content: <><WorkspaceNotes recordId={id} databaseId={databaseId}/></>
        },
        {
            id: "reminders",
            title: <>
                <ClockThreeIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Reminders
                </div>
            </>,
            content: <><WorkspaceReminders
                newReminderTitle={`Remind me about ${title}`}
                recordId={id} databaseId={databaseId}/></>
        }]




    useEffect(() => {
        const savedOrder = localStorage.getItem('recordTabsOrder')
        if (savedOrder) {
            try {
                const parsedOrder = JSON.parse(savedOrder)
                if (Array.isArray(parsedOrder)) {
                    setTabsOrder(parsedOrder)
                }
            } catch (e) {
                console.error('Error parsing saved tab order from localStorage:', e)
            }
        }


        if (socket && isConnected) {
            socket.emit(WorkspaceHandlerEvent.UpdateMemberSettings, {
                key: WorkspaceMemberSettingsKey.RecordTabsOrder,
                value: null,
                action: 'get'
            }, (res: any) => {
                if (res && res.status === 'ok' && res.data && Array.isArray(res.data.value)) {
                    const backendOrder = res.data.value
                    if (backendOrder.length > 0) {
                        localStorage.setItem('recordTabsOrder', JSON.stringify(backendOrder))
                        setTabsOrder(backendOrder)
                    }
                }
            })
        }
    }, [socket, isConnected])

    const tabs: Tab[] = [...defaultTabs]

    if (isMobile || showOverview) {
        tabs.unshift({
            id: "overview",
            title: <>
                <ChartPieIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Overview
                </div>
            </>,
            content: <RecordOverview isTabbed/>
        },)
    }

    if (isLocal()) {
        tabs.push({
            id: "custom-1",
            title: <>
                <MessageDotsIcon className="size-3"/>
                <div className="overflow-hidden truncate flex-1 font-semibold">
                    Stories
                </div>
            </>,
            content: <>Stories</>
        })
    }


    if (tabsOrder.length > 0) {
        const reorderableTabs = tabs.filter(tab => tab.id !== "overview")
        const overviewTab = tabs.find(tab => tab.id === "overview")

        const tabMap: {[key: string]: Tab} = {}
        reorderableTabs.forEach(tab => {
            tabMap[tab.id] = tab
        })

        const orderedTabs: Tab[] = []


        if (overviewTab) {
            orderedTabs.push(overviewTab)
        }

        tabsOrder.forEach(id => {
            if (tabMap[id]) {
                orderedTabs.push(tabMap[id])
                delete tabMap[id]
            }
        })

        Object.values(tabMap).forEach(tab => {
            orderedTabs.push(tab)
        })

        if (orderedTabs.length > 0) {
            tabs.length = 0
            tabs.push(...orderedTabs)
        }
    }

    return <>
        <TabView tabs={tabs} defaultTab={tabs[0].id}
                 key={isMobile ? 1 : 2}
                 tabSwitcherClassName="px-3"
                 className="extra rEx"
                 tabTitleExtra={<RecordTabOptions tabs={tabs} setTabsOrder={setTabsOrder} />}/>
    </>
}

interface RecordTabOptionsProps {
    tabs: Tab[];
    setTabsOrder: (order: string[]) => void;
}

const RecordTabOptions = ({ tabs, setTabsOrder }: RecordTabOptionsProps) => {
    const [open, setOpen] = useState(false)
    const [newView, setNewView] = useState(false)
    const {socket, isConnected} = useWorkspaceSocket()


    const reorderableTabs = tabs.filter(tab => tab.id !== "overview")

    const saveTabOrder = (tabIds: string[]) => {
        localStorage.setItem('recordTabsOrder', JSON.stringify(tabIds))
        setTabsOrder(tabIds)
        if (socket && isConnected) {
            socket.emit(WorkspaceHandlerEvent.UpdateMemberSettings, {
                key: WorkspaceMemberSettingsKey.RecordTabsOrder,
                value: tabIds
            }, (res: any) => {
                console.log('UpdateMemberSettings with response', res);
            })
        }
    }


    const resetTabOrder = () => {
        // Clear localStorage
        localStorage.removeItem('recordTabsOrder');
        setTabsOrder([])
        if (socket && isConnected) {
            socket.emit(WorkspaceHandlerEvent.UpdateMemberSettings, {
                key: WorkspaceMemberSettingsKey.RecordTabsOrder,
                value: []
            }, (res: any) => {
                console.log('UpdateMemberSettings with response', res);
            })
        }
        setOpen(false)
    }

    return <>
        <div className="p-2">
            <ViewCreator context='database' setOpen={setNewView} open={newView}/>
            <DropdownMenu open={open} onOpenChange={setOpen}>
                <DropdownMenuTrigger asChild>
                    <Button variant="ghost"
                            className="text-xs rounded-full p-1.5 h-auto gap-2 overflow-hidden">
                        <CogIcon className="size-4"/>
                    </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-96 p-0 rounded-none" align="end">
                    <div className="flex flex-col h-auto max-h-96">
                        <div className="p-2 border-b flex-1">
                            <DNDSortable
                                items={reorderableTabs.map(tab => ({id: tab.id, data: tab}))}
                                itemRenderer={function (index: number, item: SortItem<Tab>): React.ReactNode {
                                    const tab = item.data
                                    return <Button variant="ghost"
                                                   className="text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden">
                                        <div className="flex items-center w-full gap-2">
                                            {tab.title}
                                        </div>
                                        <div className="flex items-center">
                                            <Button variant="ghost"
                                                    onClick={(e) => {
                                                        e.stopPropagation()
                                                        e.preventDefault()
                                                    }}
                                                    className="size-6 p-1 rounded-full items-center hover:bg-neutral-300">
                                                <EllipsisHorizontalIcon className="h-3 w-3"/>
                                            </Button>
                                        </div>
                                    </Button>
                                }}
                                onChange={function (items: SortItem<Tab>[]): void {
                                    const tabIds = items.map(item => item.id)
                                    saveTabOrder(tabIds)
                                }}
                                useDragHandle
                                handlePosition={"center"}
                                wrapperClassName={(index, item) => {
                                    return `hover:bg-neutral-100 gap-0.5 pl-2`
                                }}
                            />
                        </div>
                        <div className="p-2">
                            <Button variant="ghost"
                                    className="text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start"
                                    onClick={resetTabOrder}>
                                <ArrowPathIcon className="size-4"/>
                                Reset to Default Order
                            </Button>
                        </div>
                        <div className="p-2 border-t">
                            <Button variant="ghost"
                                    className="text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start"
                                    onClick={() => {
                                        setOpen(false)
                                        setNewView(true)
                                    }}>
                                <PlusCircleIcon className="size-4"/>
                                New View
                            </Button>
                        </div>
                    </div>
                </DropdownMenuContent>
            </DropdownMenu>
        </div>
    </>
}