"use client";

import {Ava<PERSON>, AvatarFallback, AvatarImage} from "@/components/ui/avatar";
import * as React from "react";
import {useEffect, useRef} from "react";
import {ScrollArea} from "@/components/ui/scroll-area";
import {But<PERSON>} from "@/components/ui/button";
import {GlobeAltIcon, PlusCircleIcon, PencilIcon, PhotoIcon} from "@heroicons/react/24/outline";
import {DropdownMenu, DropdownMenuContent, DropdownMenuTrigger} from "@/components/ui/dropdown-menu";
import {ViewIcon} from "@/components/workspace/main/views/viewIcon";
import {subLinks} from "@/utils/demo/links";
import {Switch} from "@/components/ui/switch";
import {useRecord} from "@/providers/record";
import {RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {FormFieldBody} from "@/components/workspace/main/views/form/components/common/formFieldBody";
import {useViews} from "@/providers/views";
import {usePage} from "@/providers/page";
import {AccessLevel} from "@/typings/page";
import {isLocal} from "@/utils/environment";
import {SendEmailWrapper} from "@/components/workspace/main/emails/sendEmailWrapper";
import debounce from "lodash/debounce";
import {getDatabaseTitleCol, getRecordTitle} from "@/components/workspace/main/views/form/components/element/linked";
import {RecordImageUploader} from "@/components/workspace/main/record/components/recordImageUploader";
import {useWorkspace} from "@/providers/workspace";


export interface RecordOverviewProps {
    isTabbed?: boolean
}

export const RecordOverview = (props: RecordOverviewProps) => {
 const {accessLevel} = usePage()
    const {updateRecordValues} = useViews()
    const {recordInfo, database} = useRecord()
    const {members} = useWorkspace()


    const titleColOpts = getDatabaseTitleCol(database)
    const title = getRecordTitle(recordInfo.record, titleColOpts.titleColId, titleColOpts.defaultTitle, titleColOpts.isContacts)


    const canBeEnriched = false;
    const isPublished = true;
    const disabled = !accessLevel || ![AccessLevel.Full, AccessLevel.Edit].includes(accessLevel)

    const {record} = recordInfo

    const toSaveRef = useRef<RecordValues | null>(null)

    const debounceRef = useRef(debounce(async () => {
        const values = toSaveRef.current
        if (values) {
            await updateRecordValues(record.databaseId, [record.id], values, {})
            toSaveRef.current = null;
        }
    }, 100))

    const updateValues = async (u: RecordValues) => {
        // debounce update values
        toSaveRef.current = u
        debounceRef.current()
    }

    const updateTitleFormat = async (titleFormat: any[]) => {
        await updateRecordValues(record.databaseId, [record.id], {}, { titleFormat });
    }

    useEffect(() => {
        document.title = title
    }, [title])



    const coverImage = typeof record.meta?.coverImage === 'object'
        ? (record.meta.coverImage as any).url
        : record.meta?.coverImage || '';

    const profileImage = typeof record.meta?.profileImage === 'object'
        ? (record.meta.profileImage as any).url
        : record.meta?.profileImage || '';

    return <>
        <div className={`hidden lg:block ${props.isTabbed ? '!block' : 'w-96 border-r'} h-full border-neutral-300 rOV`}>
            <div className="w-full h-full flex flex-col overflow-hidden">
                <div
                    className="w-full h-32 bg-cover bg-center bg-gray-100 relative"
                    style={{ backgroundImage: coverImage ? `url(${coverImage})` : undefined }}
                >
                    {!disabled && (
                        <RecordImageUploader
                            record={record}
                            type="cover"
                            className="absolute bottom-2 right-2"
                        >
                            <Button
                                variant="secondary"
                                size="sm"
                                className="bg-white/80 backdrop-blur-sm hover:bg-white/90"
                            >
                                <PhotoIcon className="h-4 w-4 mr-1" />
                                {coverImage ? "Change Cover" : "Add Cover"}
                            </Button>
                        </RecordImageUploader>
                    )}
                </div>
                <div className="p-3 border-b flex flex-col gap-3">
                    <div className="flex gap-1 items-center">
                        <div className="relative">
                            <Avatar className="mr-1 size-12">
                                <AvatarImage
                                    src={profileImage}
                                    alt={title}
                                    className="!rounded-sm"
                                />
                                <AvatarFallback className="!rounded">{title[0]}</AvatarFallback>
                            </Avatar>
                            {!disabled && (
                                <RecordImageUploader
                                    record={record}
                                    type="profile"
                                    className="absolute -bottom-1 -right-1"
                                >
                                    <Button
                                        variant="secondary"
                                        size="icon"
                                        className="rounded-full h-6 w-6 bg-white/80 backdrop-blur-sm hover:bg-white/90"
                                    >
                                        <PencilIcon className="h-3 w-3" />
                                    </Button>
                                </RecordImageUploader>
                            )}
                        </div>
                        <div className="font-semibold text-black flex-1 truncate overflow-hidden">
                            {title}
                        </div>
                        {isLocal() && isPublished && <Button variant="ghost"
                                                             onClick={(e) => {
                                                                 // setCollapse(!collapse)
                                                                 e.stopPropagation()
                                                                 e.preventDefault()
                                                                 alert("Show a popup with the fields and a publish button; if published show an input with the link")
                                                             }}
                                                             className="size-6 p-1 rounded-full items-center hover:bg-neutral-300">
                            <GlobeAltIcon className={`size-4 transition-transform text-blue-600`}/>
                        </Button>}
                    </div>

                    <div className="flex gap-1 items-center">
                        {/*<Button variant="outline" size="sm" className="rounded-full">Call</Button>*/}
                        <SendEmailWrapper
                            isRecordPage
                            database={database}
                            recordIds={[record.id]}/>
                        {/*{canBeEnriched && <>*/}
                        {/*    <Button variant="outline" size="sm" className="rounded-full">Send email</Button>*/}
                        {/*    <Button variant="outline" size="sm" className="rounded-full">Enrich</Button>*/}
                        {/*</>}*/}
                        {/*<DropdownMenu>*/}
                        {/*    <DropdownMenuTrigger asChild>*/}
                        {/*        <Button variant="outline" size="sm" className="rounded-full">*/}
                        {/*            <ChevronDownIcon className="size-3"/>*/}
                        {/*        </Button>*/}
                        {/*    </DropdownMenuTrigger>*/}
                        {/*    <DropdownMenuContent className="w-56  rounded-none text-neutral-800 font-semibold"*/}
                        {/*                         align="end">*/}
                        {/*        <DropdownMenuGroup className="p-1">*/}
                        {/*            <InputWithEnter*/}
                        {/*                wrapperClassname=""*/}
                        {/*                inputClassname=""*/}
                        {/*                shortEnter*/}
                        {/*                onChange={title => {*/}
                        {/*                    // props.updateField(props.id, {title})*/}
                        {/*                    // setD1(false)*/}
                        {/*                }}*/}
                        {/*                value="Record title"/>*/}
                        {/*        </DropdownMenuGroup>*/}
                        {/*        <DropdownMenuGroup className="p-1">*/}
                        {/*            <DropdownMenuItem className="text-xs rounded-none p-2">*/}
                        {/*                Option 2*/}
                        {/*            </DropdownMenuItem>*/}
                        {/*            <DropdownMenuItem className="text-xs rounded-none p-2">*/}
                        {/*                Option 3*/}
                        {/*            </DropdownMenuItem>*/}

                        {/*        </DropdownMenuGroup>*/}
                        {/*    </DropdownMenuContent>*/}
                        {/*</DropdownMenu>*/}
                    </div>

                </div>
                <div className="flex-1 overflow-hidden">
                    <ScrollArea className="h-full w-full overflow-x-hidden !max-w-full">
                        <div className="block max-w-full overflow-hidden">
                            <div className="p-3 pb-28 flex flex-col gap-2 max-w-full">
                                {database.definition.columnIds.map(id => <FormFieldBody
                                    key={id}
                                    columnsMap={database.definition.columnsMap}
                                    columnsPropMap={{}}
                                    updateFieldProps={() => {}}
                                    activeField={''}
                                    setActiveField={() => {}}
                                    values={record.recordValues}
                                    processedRecordValues={recordInfo.processedRecord.processedRecordValues}
                                    updateValues={updateValues}
                                    isEditing={false}
                                    disabled={disabled}
                                    databaseId={database.id}
                                    id={id}/>)}

                                {isLocal() && <div className="flex gap-2 my-8">
                                    <Button variant="outline" size="sm">New field</Button>

                                    <DropdownMenu>
                                        <DropdownMenuTrigger asChild>
                                            <Button variant="outline" size="sm">Choose fields</Button>
                                        </DropdownMenuTrigger>
                                        <DropdownMenuContent className="w-56 p-0 rounded-none" align="start">
                                            <div className="flex flex-col h-auto max-h-96">
                                                <div className="p-2 border-b flex-1">
                                                    {subLinks.map(link => {
                                                        return <Button variant="ghost"
                                                                       key={link.id}
                                                                       className="text-xs rounded-none p-1.5 px-2 h-auto gap-2 w-full justify-start overflow-hidden">
                                                            <ViewIcon type={link.type} className="size-4"/>
                                                            <div
                                                                className="flex-1 overflow-hidden truncate text-left">{link.title}
                                                            </div>
                                                            <Switch className="h-4 w-8" thumbClassName="!size-3"/>
                                                        </Button>
                                                    })}
                                                </div>
                                                <div className="p-2">
                                                    <Button variant="ghost"
                                                            className="text-xs rounded-none p-1.5 h-auto gap-2 w-full justify-start"
                                                            onClick={() => {
                                                                // setOpen(false)
                                                                // props.requestNewView()
                                                            }}>
                                                        <PlusCircleIcon className="size-4"/>
                                                        New View
                                                    </Button>
                                                </div>
                                            </div>
                                            {/*<ViewSwitcher requestNewView={() => setNewView(true)}/>*/}
                                        </DropdownMenuContent>
                                    </DropdownMenu>
                                </div>}

                            </div>
                        </div>
                    </ScrollArea>
                </div>
            </div>
        </div>
    </>
}

