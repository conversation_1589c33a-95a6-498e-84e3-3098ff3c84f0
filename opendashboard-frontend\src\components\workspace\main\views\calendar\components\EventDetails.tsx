import React from 'react';
import { format } from 'date-fns';
import { Sheet, SheetContent } from '@/components/ui/sheet';
import { Button } from '@/components/ui/button';
import { XMarkIcon } from '@heroicons/react/24/outline';
import { RecordProvider } from '@/providers/record';
import { RecordOverview } from '@/components/workspace/main/record/components/recordOverview';
import { RecordExtras } from '@/components/workspace/main/record/components/recordExtras';
import { CalendarEvent } from '@/typings/page';
import { Database } from '@/typings/database';

interface EventDetailsProps {
  selectedEvent: string | null;
  setSelectedEvent: (id: string | null) => void;
  events: CalendarEvent[];
  database: Database;
  openEditEventDialog: (event: CalendarEvent) => void;
  canEditData: boolean;
  viewType: string;
  savedScrollTop: React.MutableRefObject<number>;
}

export const EventDetails: React.FC<EventDetailsProps> = ({
  selectedEvent,
  setSelectedEvent,
  events,
  database,
  openEditEventDialog,
  canEditData,
  viewType,
  savedScrollTop
}) => {
  const event = events.find(e => e.id === selectedEvent);

  if (!event) return null;

  return (
    <Sheet open={!!selectedEvent} onOpenChange={(open) => {
      if (!open) {
        const containerId = viewType === 'day' ? 'day-view-container' : 'week-view-container';
        const container = document.getElementById(containerId);
        if (container) {
          savedScrollTop.current = container.scrollTop;
        }
      }
      setSelectedEvent(null);
    }}>
      <SheetContent className="w-full sm:max-w-2xl p-0 flex flex-col overflow-hidden">
        <div className="flex justify-between items-center p-4 border-b">
          <h3 className="font-semibold">Event Details</h3>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSelectedEvent(null)}
          >
            <XMarkIcon className="h-5 w-5" />
          </Button>
        </div>
        <div className="flex-1 overflow-hidden">
          <RecordProvider
            database={database}
            recordInfo={{
              record: event.record,
              processedRecord: event.processedRecord
            }}
          >
            <div className="p-4 border-b bg-gray-50">
              <div className="text-lg font-semibold">
                {event.title}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                {format(event.start, "PPP")}
                {" at "}
                {format(event.start, "p")}
                {" to "}
                {format(event.end, "p")}
              </div>
              {canEditData && (
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={() => {
                    openEditEventDialog(event);
                    setSelectedEvent(null);
                  }}
                >
                  Edit Event
                </Button>
              )}
            </div>
            <RecordOverview />
            <div className="flex-1 overflow-hidden">
              <RecordExtras />
            </div>
          </RecordProvider>
        </div>
      </SheetContent>
    </Sheet>
  );
};