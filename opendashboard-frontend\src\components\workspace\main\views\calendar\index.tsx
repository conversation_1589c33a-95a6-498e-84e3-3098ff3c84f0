import React, { useState, useRef, useEffect, useCallback } from "react";
import { useWorkspace } from "@/providers/workspace";
import { Match} from "opendb-app-db-utils/lib/typings/db";
import { PageLoader } from "@/components/custom-ui/loader";
import { usePage } from "@/providers/page";
import { useMaybeShared } from "@/providers/shared";
import { useMaybeTemplate } from "@/providers/template";
import { useViews } from "@/providers/views";
import { filterAndSortRecords, searchFilteredRecords } from "@/components/workspace/main/views/table";
import { Calendar } from "@/components/ui/calendar";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { AngleLeftIcon, AngleRightIcon, PlusIcon, MagnifyingGlassIcon } from "@/components/icons/FontAwesomeRegular";
import { format, addDays, addMonths, subMonths, addWeeks, subWeeks } from "date-fns";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { useScreenSize } from "@/providers/screenSize";
import { DayView } from "./components/DayView";
import { WeekView } from "./components/WeekView";
import { MonthView } from "./components/MonthView";
import { getDatabaseTitleCol, getRecordTitle } from '@/components/workspace/main/views/form/components/element/linked';
import { CalendarViewRenderProps, CalendarEvent, CalendarViewType } from '@/typings/page';

export const CalendarView = (props: CalendarViewRenderProps) => {
  const { databaseStore, members, workspace } = useWorkspace();
  const { definition } = props;
  const { accessLevel } = usePage();

  const maybeShared = useMaybeShared();
  const maybeTemplate = useMaybeTemplate();
  const { isMobile } = useScreenSize();

  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [viewType, setViewType] = useState<CalendarViewType>("week");
  const [selectedEvent, setSelectedEvent] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [showSideCalendar, setShowSideCalendar] = useState(!isMobile);

  const savedScrollTop = useRef(0);

  definition.filter = definition.filter || { conditions: [], match: Match.All };
  definition.sorts = definition.sorts || [];

  // const databaseId = definition.databaseId;
  const database = databaseStore[definition.databaseId];

  const isPublishedView = !!maybeShared;
  const editable = !definition.lockContent && !isPublishedView && !!accessLevel;

  // let canEditStructure = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);
  let canEditData = !!(!maybeTemplate && !maybeShared && !isPublishedView && !definition.lockContent && accessLevel && editable);

  const {  sorts, filter, search, createRecords, updateRecordValues, setPeekRecordId, peekRecordId, refreshDatabase } = useViews();

  useEffect(() => {
    const containerId = viewType === 'day' ? 'day-view-container' : 'week-view-container';
    const container = document.getElementById(containerId);

    if (container) {
      requestAnimationFrame(() => {
        container.scrollTop = savedScrollTop.current;
      });
    }
  }, [selectedEvent, viewType]);

  useEffect(() => {
    setShowSideCalendar(!isMobile);
  }, [isMobile]);


  useEffect(() => {
    if (peekRecordId) {
      console.log("Peek view opened for record:", peekRecordId);
    } else {
      console.log("Peek view closed, refreshing calendar data");
      const refreshData = async () => {
        await refreshDatabase(definition.databaseId);
      };
      refreshData();
    }
  }, [peekRecordId, definition.databaseId, refreshDatabase]);

  if (!database) return <PageLoader size="full" />;

  const getEvents = (): CalendarEvent[] => {
    if (!database) return [];

    const { rows } = filterAndSortRecords(
      database,
      members,
      databaseStore,
      definition.filter || { match: Match.All, conditions: [] },
      filter,
      sorts.length ? sorts : (definition.sorts || []),
      workspace?.workspaceMember?.userId || '',
      database?.database?.id || ''
    );

    const filteredRows = searchFilteredRecords(search || "", rows);
    const titleColOpts = getDatabaseTitleCol(database.database);

    return filteredRows.map(row => {
      const startValue = row.processedRecord.processedRecordValues[definition.eventStartColumnId];
      let startDate: Date;

      if (startValue && typeof startValue === 'string') {
        startDate = new Date(startValue);
      } else {
        startDate = new Date();
      }

      let endDate: Date;
      if (definition.eventEndColumnId) {
        const endValue = row.processedRecord.processedRecordValues[definition.eventEndColumnId];
        if (endValue && typeof endValue === 'string') {
          endDate = new Date(endValue);
        } else {
          endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);
        }
      } else {
        endDate = new Date(startDate.getTime() + (definition.defaultDuration || 30) * 60000);
      }

      const title = getRecordTitle(
        row.record,
        titleColOpts.titleColId,
        titleColOpts.defaultTitle,
        titleColOpts.isContacts
      );

      return {
        id: row.id,
        title,
        start: startDate,
        end: endDate,
        record: row.record,
        processedRecord: row.processedRecord
      };
    });
  };

  const getFilteredEvents = () => {
    const baseEvents = getEvents();

    if (!searchTerm.trim()) {
      return baseEvents;
    }

    return baseEvents.filter(event => {
      const searchLower = searchTerm.toLowerCase();
      return event.title.toLowerCase().includes(searchLower);
    });
  };


  const events = getFilteredEvents();

  const goToToday = () => setSelectedDate(new Date());

  const goToPrevious = () => {
    switch (viewType) {
      case "day":
        setSelectedDate(prevDate => addDays(prevDate, -1));
        break;
      case "week":
        setSelectedDate(prevDate => subWeeks(prevDate, 1));
        break;
      case "month":
        setSelectedDate(prevDate => subMonths(prevDate, 1));
        break;
    }
  };

  const goToNext = () => {
    switch (viewType) {
      case "day":
        setSelectedDate(prevDate => addDays(prevDate, 1));
        break;
      case "week":
        setSelectedDate(prevDate => addWeeks(prevDate, 1));
        break;
      case "month":
        setSelectedDate(prevDate => addMonths(prevDate, 1));
        break;
    }
  };

  const handleCreateEvent = async (date: Date = new Date()) => {
    if (!canEditData) return;

    const startTime = new Date(date);
    const endTime = new Date(startTime.getTime() + (definition.defaultDuration || 30) * 60000);
    const titleColOpts = getDatabaseTitleCol(database.database);

    try {
      const recordValues: any = {
        [definition.eventStartColumnId]: startTime.toISOString(),
        ...(definition.eventEndColumnId && { [definition.eventEndColumnId]: endTime.toISOString() })
      };

      if (titleColOpts.titleColId) {
        recordValues[titleColOpts.titleColId] = "New Event";
      }

      const result = await createRecords(definition.databaseId, [recordValues]);

      if (result && result.records && result.records.length > 0) {
        const newRecordId = result.records[0].id;

        if (newRecordId) {
          await refreshDatabase(definition.databaseId);
          setPeekRecordId(newRecordId);
          toast.success("New event created");
        } else {
          toast.error("Error accessing the new event");
        }
      } else {
        toast.error("Failed to create event properly");
      }
    } catch (error) {
      toast.error("Failed to create event");
    }
  };

  const handleEventClick = (event: CalendarEvent) => {
    if (event && event.id) {
      const containerId = viewType === 'day' ? 'day-view-container' : 'week-view-container';
      const container = document.getElementById(containerId);
      if (container) {
        savedScrollTop.current = container.scrollTop;
      }


      setPeekRecordId(event.id);
      setSelectedEvent(event.id);
    }
  };

  const getHeaderDateDisplay = () => {
    switch (viewType) {
      case "day":
        return format(selectedDate, 'MMMM d, yyyy');
      case "week":
        return `${format(addDays(selectedDate, -selectedDate.getDay()), 'MMM d')} - ${format(addDays(selectedDate, 6-selectedDate.getDay()), 'MMM d, yyyy')}`;
      case "month":
        return format(selectedDate, 'MMMM yyyy');
      default:
        return format(selectedDate, 'MMMM d, yyyy');
    }
  };

  const handleEventDrop = async (event: CalendarEvent, newDate: Date) => {
    if (!canEditData) return;

    try {
      const newStart = new Date(newDate);

      const eventDuration = event.end
        ? new Date(event.end).getTime() - new Date(event.start).getTime()
        : 60 * 60 * 1000;

      const newEnd = new Date(newStart.getTime() + eventDuration);

      const values = {
        [definition.eventStartColumnId]: newStart.toISOString(),
        ...(definition.eventEndColumnId && {
          [definition.eventEndColumnId]: newEnd.toISOString()
        })
      };

      const updated = await updateRecordValues(
        definition.databaseId,
        [event.id],
        values
      );

      if (updated) {
        await refreshDatabase(definition.databaseId);
        setPeekRecordId(event.id);
        toast.success("Event updated successfully");
      }
    } catch (error) {
      console.error("Error updating event:", error);
      toast.error("Failed to update event");
    }
  };

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Enhanced Header */}
      <div className="border-b bg-white shadow-sm">
        {isMobile ? (
          /* Mobile Header Layout */
          <div className="p-4">
            {/* Top row - Date and Calendar button */}
            <div className="flex items-center justify-between mb-3">
              <h1 className="text-lg font-semibold text-gray-900 truncate flex-1 mr-3">
                {getHeaderDateDisplay()}
              </h1>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSideCalendar(!showSideCalendar)}
                className="flex-shrink-0"
              >
                Calendar
              </Button>
            </div>

            {/* Bottom row - Navigation and controls */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={goToToday}
                  className="font-medium text-xs px-2"
                >
                  Today
                </Button>

                <div className="flex items-center space-x-1">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={goToPrevious}
                    className="h-8 w-8 hover:bg-gray-100"
                  >
                    <AngleLeftIcon className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={goToNext}
                    className="h-8 w-8 hover:bg-gray-100"
                  >
                    <AngleRightIcon className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                {/* View Type Selector */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm" className="font-medium text-xs px-2">
                      {viewType === 'day' ? 'Day' : viewType === 'week' ? 'Week' : 'Month'}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setViewType('day')}>Day View</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setViewType('week')}>Week View</DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setViewType('month')}>Month View</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>

                {canEditData && (
                  <Button
                    size="sm"
                    onClick={() => handleCreateEvent(selectedDate)}
                    className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium text-xs px-2"
                  >
                    <PlusIcon className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="flex items-center justify-between px-6 py-4">
            {/* Left section - Navigation */}
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={goToToday}
                className="font-medium"
              >
                Today
              </Button>

              <div className="flex items-center space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={goToPrevious}
                  className="h-8 w-8 hover:bg-gray-100"
                >
                  <AngleLeftIcon className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={goToNext}
                  className="h-8 w-8 hover:bg-gray-100"
                >
                  <AngleRightIcon className="h-4 w-4" />
                </Button>
              </div>

              <h1 className="text-xl font-semibold text-gray-900">
                {getHeaderDateDisplay()}
              </h1>
            </div>

            {/* Right section - Controls */}
            <div className="flex items-center space-x-3">
              <div className="relative">
                <Input
                  placeholder="Search events..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-64 pr-10 h-9"
                />
                <MagnifyingGlassIcon className="h-4 w-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              </div>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="min-w-[80px] font-medium">
                    {viewType === 'day' ? 'Day' : viewType === 'week' ? 'Week' : 'Month'}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => setViewType('day')}>Day View</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setViewType('week')}>Week View</DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setViewType('month')}>Month View</DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {canEditData && (
                <Button
                  size="sm"
                  onClick={() => handleCreateEvent(selectedDate)}
                  className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Event
                </Button>
              )}
            </div>
          </div>
        )}

        {/* Mobile search bar */}
        {isMobile && (
          <div className="px-3 pb-3">
            <div className="relative">
              <Input
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pr-10 h-9"
              />
              <MagnifyingGlassIcon className="h-4 w-4 absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            </div>
          </div>
        )}
      </div>

    <div className="flex flex-1 min-h-0 overflow-hidden">
      {showSideCalendar && (
        <div className={cn(
          "flex-none bg-white",
          isMobile ? "w-full absolute z-50 backdrop-blur-sm h-full shadow-lg" : "w-fit border-r"
        )}>
          <div className="flex justify-between items-center p-2 border-b">
            <h3 className="text-sm font-medium text-gray-700">Calendar</h3>
            {isMobile && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSideCalendar(false)}
                className="h-7 w-7 p-0"
              >
                <span className="sr-only">Close</span>
                ×
              </Button>
            )}
          </div>
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={(date) => {
              if (date) {
                setSelectedDate(date);
                if (isMobile) {
                  setShowSideCalendar(false);
                }
              }
            }}
            className="w-full"
          />
        </div>
      )}

      <div className={cn(
        "flex-1",
        viewType === 'month' ? "overflow-auto" : "overflow-hidden"
      )}>
        {viewType === 'day' && (
          <DayView
            selectedDate={selectedDate}
            events={events}
            selectedEvent={selectedEvent}
            setSelectedEvent={setSelectedEvent}
            openAddEventForm={handleCreateEvent}
            canEditData={canEditData}
            savedScrollTop={savedScrollTop}
            handleEventClick={handleEventClick}
            onEventDrop={handleEventDrop}
          />
        )}
        {viewType === 'week' && (
          <WeekView
            selectedDate={selectedDate}
            events={events}
            selectedEvent={selectedEvent}
            setSelectedEvent={setSelectedEvent}
            setSelectedDate={setSelectedDate}
            openAddEventForm={handleCreateEvent}
            canEditData={canEditData}
            savedScrollTop={savedScrollTop}
            handleEventClick={handleEventClick}
            onEventDrop={handleEventDrop}
          />
        )}
        {viewType === 'month' && (
          <MonthView
            selectedDate={selectedDate}
            events={events}
            selectedEvent={selectedEvent}
            setSelectedEvent={setSelectedEvent}
            setSelectedDate={setSelectedDate}
            openAddEventForm={handleCreateEvent}
            canEditData={canEditData}
            handleEventClick={handleEventClick}
            onEventDrop={handleEventDrop}
          />
        )}
      </div>
    </div>
  </div>
  );
};

