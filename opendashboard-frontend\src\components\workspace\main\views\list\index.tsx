"use client";

import React, {useState} from "react";
import {useWorkspace} from "@/providers/workspace";
import { ButtonGroupColumn,DatabaseFieldDataType, Match,NumberColumnFormat,Sort} from "opendb-app-db-utils/lib/typings/db";
import {PageLoader} from "@/components/custom-ui/loader";
import {useViews} from "@/providers/views";
import { ListViewRenderProps, DataViewRow} from "@/typings/page";
import {filterAndSortRecords, searchFilteredRecords} from "@/components/workspace/main/views/table";
import {ScrollArea} from "@/components/ui/scroll-area";
import {getDatabaseTitleCol, getRecordTitle} from "@/components/workspace/main/views/form/components/element/linked";
import {cn} from "@/lib/utils";
import {timeAgo} from "@/utils/timeAgo";
import {format, parseISO} from "date-fns";
import {isDateObjValid} from "opendb-app-db-utils/lib/methods/date";
import {formatCurrency, getTextBasedColFormattedValue} from "@/components/workspace/main/views/table/renderer/fields/text";
import "./list.css";

export const ListView = (props: ListViewRenderProps) => {
    const {databaseStore, members, workspace} = useWorkspace();
    const {definition} = props;

    definition.filter = definition.filter || {conditions: [], match: Match.All};
    definition.sorts = definition.sorts || [];
    const columnPropsMap = definition.columnPropsMap || {};

    const database = databaseStore[definition.databaseId];

    const [selectedRecordId, setSelectedRecordId] = useState<string | null>(null);

    const {filter, search, setPeekRecordId} = useViews();

    if (!database) return <PageLoader size="full" />;

    const getRows = () => {
        if (!database) return []
        const { rows } = filterAndSortRecords(
            database,
            members,
            databaseStore,
            definition.filter || {match: Match.All, conditions: []},
            filter,
            [{columnId: 'createdAt', order: Sort.Desc}],
            workspace?.workspaceMember?.userId || '',
            database?.database?.id || ''
        )

        return searchFilteredRecords(search, rows)
    }
    const rows = getRows();

    // titleColOpts no longer needed since we use processedRecord.title

    const handleRecordClick = (recordId: string) => {
        setSelectedRecordId(recordId);
        setPeekRecordId(recordId);
    };

    let columnsOrder = [...(definition.columnsOrder || [])];

    if (database.database.definition.columnIds) {
        for (const key of database.database.definition.columnIds) {
            if (!columnsOrder.includes(key)) {
                columnsOrder.push(key);
            }
        }
    }


    const allFields = columnsOrder
        .filter(id => !columnPropsMap[id]?.isHidden && database.database.definition.columnsMap[id])
        .map(id => database.database.definition.columnsMap[id]);

    const fieldsToDisplay = allFields.filter(field =>
        field.type !== DatabaseFieldDataType.Files &&
        field.type !== DatabaseFieldDataType.ButtonGroup
    );

    const formatFieldValue = (row: DataViewRow, field: any) => {
        if (row.processedRecord && row.processedRecord.processedRecordValues &&
            row.processedRecord.processedRecordValues[field.id] !== undefined) {

            const processedValue = row.processedRecord.processedRecordValues[field.id];

            if (field.type === DatabaseFieldDataType.Person ||
                field.type === DatabaseFieldDataType.CreatedBy ||
                field.type === DatabaseFieldDataType.UpdatedBy) {

                if (Array.isArray(processedValue)) {
                    return processedValue.map(person => {
                        if (typeof person === 'object' && person !== null && 'title' in person) {
                            return person.title;
                        }
                        return String(person);
                    }).join(', ');
                } else if (processedValue && typeof processedValue === 'object' && 'title' in processedValue) {
                    return processedValue.title;
                }
            }

            if (field.type === DatabaseFieldDataType.Select) {
                if (Array.isArray(processedValue)) {
                    return processedValue.map(option => {
                        if (typeof option === 'object' && option !== null && 'title' in option) {
                            return option.title;
                        }
                        return String(option);
                    }).join(', ');
                } else if (processedValue && typeof processedValue === 'object' && 'title' in processedValue) {
                    return processedValue.title;
                }
            }

            if (field.type === DatabaseFieldDataType.Linked) {
                if (Array.isArray(processedValue)) {
                    return processedValue.map(linked => {
                        if (typeof linked === 'object' && linked !== null && 'title' in linked) {
                            return linked.title;
                        }
                        return String(linked);
                    }).join(', ');
                } else if (processedValue && typeof processedValue === 'object' && 'title' in processedValue) {
                    return processedValue.title;
                }
            }

            if (field.type === DatabaseFieldDataType.AI) {
                if (processedValue) {
                    return String(processedValue).substring(0, 50) +
                           (String(processedValue).length > 50 ? '...' : '');
                }
            }

            if (field.type === DatabaseFieldDataType.ButtonGroup) {
                const buttonGroupColumn = field as ButtonGroupColumn;
                const buttons = buttonGroupColumn.buttons || [];

                if (buttons.length === 0) {
                    return 'No actions';
                } else if (buttons.length === 1) {
                    return buttons[0].label || 'Action';
                } else {
                    return `${buttons.length} actions`;
                }
            }

            // Handle Files fields
            if (field.type === DatabaseFieldDataType.Files) {
                if (Array.isArray(processedValue)) {
                    return `${processedValue.length} file${processedValue.length !== 1 ? 's' : ''}`;
                }
                return 'No files';
            }

            if (processedValue !== null && processedValue !== undefined) {
                if (typeof processedValue === 'object') {
                    try {
                        return JSON.stringify(processedValue);
                    } catch (e) {
                        return '[Object]';
                    }
                }
                return String(processedValue);
            }
        }

        if (!row.record.recordValues || row.record.recordValues[field.id] === undefined) {
            return '-';
        }

        const value = row.record.recordValues[field.id];

        if (value === null || value === undefined) {
            return '-';
        }

        if (field.type === DatabaseFieldDataType.Text) {
            const formattedValue = getTextBasedColFormattedValue(field, String(value));
            return formattedValue.displayValue || String(value);
        }

        if (field.type === DatabaseFieldDataType.UUID) {
            return String(value);
        }

        if (field.type === DatabaseFieldDataType.Checkbox || typeof value === 'boolean') {
            return value ? 'Yes' : 'No';
        }

        if (field.type === DatabaseFieldDataType.Date ||
            field.type === DatabaseFieldDataType.CreatedAt ||
            field.type === DatabaseFieldDataType.UpdatedAt) {
            try {
                let date: Date;

                if (field.type === DatabaseFieldDataType.CreatedAt) {
                    date = new Date(row.record.createdAt);
                } else if (field.type === DatabaseFieldDataType.UpdatedAt) {
                    date = new Date(row.record.updatedAt);
                } else if (typeof value === 'string' || typeof value === 'number') {
                    date = new Date(value);
                } else {
                    return '-';
                }

                if (isDateObjValid(date)) {
                    return format(parseISO(date.toISOString()), 'MMM d, yyyy');
                }
            } catch (e) {
                return String(value);
            }
        }

        if (field.type === DatabaseFieldDataType.Person) {
            if (typeof value === 'string' || typeof value === 'number') {
                const personId = String(value);
                const person = members?.find(member => member.user.id === personId);
                return person ? `${person.user.firstName} ${person.user.lastName}`.trim() || person.user.email : personId;
            }
            if (Array.isArray(value)) {
                return value.map(personId => {
                    const person = members?.find(member => member.user.id === String(personId));
                    return person ? `${person.user.firstName} ${person.user.lastName}`.trim() || person.user.email : String(personId);
                }).join(', ');
            }
            return '-';
        }

        if (field.type === DatabaseFieldDataType.CreatedBy || field.type === DatabaseFieldDataType.UpdatedBy) {
            let userId: string | undefined;

            if (field.type === DatabaseFieldDataType.CreatedBy) {
                userId = row.record.createdById;
            } else if (field.type === DatabaseFieldDataType.UpdatedBy) {
                userId = row.record.updatedById;
            } else if (typeof value === 'string' || typeof value === 'number') {
                userId = String(value);
            } else if (value && typeof value === 'object' && 'userId' in value) {
                userId = String(value.userId);
            }

            if (userId) {
                const person = members?.find(member => member.user.id === userId);
                return person ? `${person.user.firstName} ${person.user.lastName}`.trim() || person.user.email : userId;
            }

            return '-';
        }

        if (field.type === DatabaseFieldDataType.Select) {
            if (Array.isArray(value)) {
                if (field.options && Array.isArray(field.options)) {
                    return value.map(v => {
                        const option = field.options.find((opt: any) => opt.value === v);
                        return option ? option.label : v;
                    }).join(', ');
                }
                return value.join(', ');
            }
            if (field.options && Array.isArray(field.options)) {
                const option = field.options.find((opt: any) => opt.value === value);
                return option ? option.label : value;
            }
            return String(value);
        }

        if (field.type === DatabaseFieldDataType.Linked) {
            if (Array.isArray(value)) {
                if (field.linkedDatabase && databaseStore[field.linkedDatabase]) {
                    const linkedDb = databaseStore[field.linkedDatabase];
                    if (linkedDb.recordsIdMap) {
                        return value.map(id => {
                            const recordData = linkedDb.recordsIdMap[id as string];
                            if (!recordData) return String(id);
                            const titleColOpts = getDatabaseTitleCol(linkedDb.database);
                            return getRecordTitle(
                                recordData.record,
                                titleColOpts.titleColId,
                                titleColOpts.defaultTitle,
                                titleColOpts.isContacts,
                                // linkedDb.database,
                                // members
                            );
                        }).join(', ');
                    }
                }
                return value.map(id => String(id)).join(', ');
            }
            if (field.linkedDatabase && databaseStore[field.linkedDatabase]) {
                const linkedDb = databaseStore[field.linkedDatabase];
                if (linkedDb.recordsIdMap && value) {
                    const recordData = linkedDb.recordsIdMap[value as string];
                    if (!recordData) return String(value);
                    const titleColOpts = getDatabaseTitleCol(linkedDb.database);
                    return getRecordTitle(
                        recordData.record,
                        titleColOpts.titleColId,
                        titleColOpts.defaultTitle,
                        titleColOpts.isContacts,
                        // linkedDb.database,
                        // members
                    );
                }
            }
            return value ? String(value) : '-';
        }

        // Handle Number fields
        if (field.type === DatabaseFieldDataType.Number) {
            const numValue = Number(value);
            if (!isNaN(numValue)) {
                if (field.format === NumberColumnFormat.Currency && field.currency) {
                    return formatCurrency(numValue, field.currency);
                }
                if (field.format === NumberColumnFormat.Percentage) {
                    return `${numValue}%`;
                }
                return numValue.toString();
            }
        }

        if (field.type === DatabaseFieldDataType.AI) {
            if (value) {
                return String(value).substring(0, 50) + (String(value).length > 50 ? '...' : '');
            }
            const recordMeta = row.record.meta || {};
            const recordColMeta = recordMeta[`column:${field.id}`] as any;
            if (recordColMeta && recordColMeta.value) {
                return String(recordColMeta.value).substring(0, 50) + (String(recordColMeta.value).length > 50 ? '...' : '');
            }
            return 'AI content';
        }

        if (field.type === DatabaseFieldDataType.ButtonGroup) {
            const buttonGroupColumn = field as ButtonGroupColumn;
            const buttons = buttonGroupColumn.buttons || [];

            if (buttons.length === 0) {
                return 'No actions';
            } else if (buttons.length === 1) {
                return buttons[0].label || 'Action';
            } else {
                return `${buttons.length} actions`;
            }
        }

        if (field.type === DatabaseFieldDataType.Files) {
            if (Array.isArray(value)) {
                return `${value.length} file${value.length !== 1 ? 's' : ''}`;
            }
            return 'No files';
        }

        if (field.type === DatabaseFieldDataType.Derived) {
            if (value) {
                return String(value);
            }
            return 'Derived value';
        }

        if (field.type === DatabaseFieldDataType.Summarize) {
            if (value) {
                return String(value);
            }
            return 'Summary';
        }

        if (Array.isArray(value)) {
            return value.map(item => item !== null && item !== undefined ? String(item) : '-').join(', ');
        }

        if (typeof value === 'object' && value !== null) {
            try {
                return JSON.stringify(value);
            } catch (e) {
                return '[Object]';
            }
        }
        return value !== null && value !== undefined ? String(value) : '-';
    };

    return (
        <div className="w-full h-full overflow-hidden listView">
            <div className="overflow-hidden size-full flex flex-col">
                <div className="flex-1 overflow-hidden">
                    <ScrollArea className="w-full h-full scrollBlockChild">
                        <div className="scroll-container pb-20">
                            <div className="border-b rowGrid border-neutral-200 header-row">
                                <div className="text-xs text-black font-semibold bg-white check !w-1">
                                </div>
                                <div className="text-xs text-black font-semibold bg-white fluid">Title</div>
                                {fieldsToDisplay.map((field) => (
                                    <div key={field.id} className="text-xs text-black font-semibold bg-white">
                                        {field.title}
                                    </div>
                                ))}
                            </div>
                                {rows.length === 0 ? (
                                    <div className="text-center py-8 text-gray-500">
                                        No records found
                                    </div>
                                ) : (
                                    rows.map((row) => {
                                    // Use the processed title from transformRawRecords instead of getRecordTitle
                                    const title = row.processedRecord.title || "Untitled";

                                    return (
                                        <div
                                            key={row.id}
                                            className={cn(
                                                "rowGrid border-b hover:bg-neutral-100 cursor-pointer",
                                                selectedRecordId === row.id ? "bg-blue-50" : ""
                                            )}
                                            onClick={() => handleRecordClick(row.id)}
                                        >
                                            <div className="text-xs check !w-1">
                                            </div>
                                            <div className="text-xs flex flex-col gap-1 fluid">
                                                <div className="title-text text-xs font-semibold">
                                                    {title || "Untitled"}
                                                </div>
                                                <div className="flex gap-2 text-xs text-muted-foreground">
                                                    <span className="truncate">{database.database.name}</span>
                                                    <span className="flex-shrink-0">&bull;</span>
                                                    <span className="truncate">{timeAgo(new Date(row.updatedAt))}</span>
                                                </div>
                                            </div>

                                            {fieldsToDisplay.map((field) => (
                                                <div key={field.id} className="text-xs truncate">
                                                    {formatFieldValue(row, field)}
                                                </div>
                                            ))}
                                        </div>
                                    );
                                })
                            )}
                        </div>
                    </ScrollArea>
                </div>
            </div>
        </div>
    );
};