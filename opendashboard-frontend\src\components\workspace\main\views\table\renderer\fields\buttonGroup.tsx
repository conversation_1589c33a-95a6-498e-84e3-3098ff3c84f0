"use client";

import React, { useState } from "react";
import { RenderCellProps } from "react-data-grid";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { CircleExclamationIcon, ChevronDownIcon, EnvelopeIcon, CircleInfoIcon, PenToSquareIcon, CodeMergeIcon, LinkIcon, TrashIcon, ArrowUpRightAndArrowDownLeftFromCenterIcon, CircleCheckIcon, EyeIcon, ListIcon, BellIcon } from "@/components/icons/FontAwesomeRegular";
import { DataViewRow, RGDMeta } from "@/components/workspace/main/views/table";
import { useViews } from "@/providers/views";
import { useAlert } from "@/providers/alert";
import { useWorkspace } from "@/providers/workspace";
import useForceRender from "@/components/custom-ui/forceRender";
import { useAuth } from "@/providers/user";
import { useRouter } from "next/navigation";
import { useBroadcast } from "@/providers/broadcast";
import { GridRender } from "@/components/workspace/main/views/table/renderer/common/gridRender";
import { ButtonGroupColumn, ButtonAction, ActionButton, RecordValues } from "opendb-app-db-utils/lib/typings/db";

import { generateUUID } from "opendb-app-db-utils/lib/methods/string";
import { BroadcastNamespaces } from "@/providers/broadcast";
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { createNotification } from "@/api/workspace";
import { Input } from "@/components/ui/input";
import { getRecordTitle } from "@/components/workspace/main/views/form/components/element/linked";

const NOTIFICATION_FEEDBACK_TIMEOUT = 3000;
const PAGE_RELOAD_TIMEOUT = 2000;
const TOAST_DELAY = 100;


export * from './buttonGroup/configEditor';
export * from './buttonGroup/Editor';

export const getActionIcon = (actionType: string) => {
    switch (actionType) {
        case 'sendEmail':
            return <EnvelopeIcon className="size-3" />;
        case 'openUrl':
            return <LinkIcon className="size-3" />;
        case 'updateRecord':
            return <PenToSquareIcon className="size-3" />;
        case 'deleteRecord':
            return <TrashIcon className="size-3" />;
        case 'showConfirmation':
            return <CircleInfoIcon className="size-3" />;
        case 'showToast':
            return <CircleCheckIcon className="size-3" />;
        case 'sendNotification':
            return <BellIcon className="size-3" />;
        case 'callWorkflow':
            return <CodeMergeIcon className="size-3" />;
        case 'expandRecord':
            return <ArrowUpRightAndArrowDownLeftFromCenterIcon className="size-3" />;
        case 'peekRecord':
            return <EyeIcon className="size-3" />;
        default:
            return null;
    }
};

// Helper function to get the appropriate icon for a button based on its actions
export const getButtonIcon = (button: ActionButton) => {
    if (!button) return <CircleExclamationIcon className="size-3" />;

    if (button.actions?.length > 1) {
        return <ListIcon className="size-3" />;
    } else if (button.actions?.length === 1) {
        return getActionIcon(button.actions[0].actionType);
    } else {
        return <CircleExclamationIcon className="size-3" />;
    }
};


export const getColumnIdByName = (database: any, columnName: string): string | null => {

    for (const [id, column] of Object.entries(database.definition.columnsMap)) {
        if ((column as any).title.toLowerCase() === columnName.toLowerCase()) {
            return id;
        }
    }


    for (const [id, column] of Object.entries(database.definition.columnsMap)) {
        if ((column as any).title.toLowerCase().includes(columnName.toLowerCase())) {
            return id;
        }
    }

    return null;
};


export const getDatabaseTitleCol = (database: any): { titleColId: string, defaultTitle: string, isContacts: boolean } => {
    if (!database || !database.definition) {
        return { titleColId: '', defaultTitle: 'Untitled', isContacts: false };
    }

    const isContacts = database.definition.type === 'contacts';

    let titleColId = '';
    let defaultTitle = 'Untitled';
    if (isContacts) {

        for (const [colId, col] of Object.entries(database.definition.columnsMap)) {
            if ((col as any).type === 'name') {
                titleColId = colId;
                defaultTitle = 'Unnamed Contact';
                break;
            }
        }
    } else {
        for (const [colId, col] of Object.entries(database.definition.columnsMap)) {
            if ((col as any).type === 'text' || (col as any).type === 'title') {
                titleColId = colId;
                defaultTitle = 'Untitled';
                break;
            }
        }
    }

    return { titleColId, defaultTitle, isContacts };
};




export const findRecordIdByTitle = (title: string, linkedDbStore: any, members?: any[]): string | null => {
    if (!linkedDbStore || !linkedDbStore.recordsIdMap) {
        console.warn('Linked database store not available');
        return null;
    }

    const { titleColId, defaultTitle, isContacts } = getDatabaseTitleCol(linkedDbStore.database);
    const searchStr = title.trim().toLowerCase();

    for (const [id, recordData] of Object.entries(linkedDbStore.recordsIdMap)) {
        const recordTitle = getRecordTitle(
            (recordData as any).record,
            titleColId,
            false, // defaultTitle should be boolean
            isContacts
        ).toLowerCase();
        if (recordTitle === searchStr) {
            return id;
        }
    }

    for (const [id, recordData] of Object.entries(linkedDbStore.recordsIdMap)) {
        const recordTitle = getRecordTitle(
            (recordData as any).record,
            titleColId,
            false, // defaultTitle should be boolean
            isContacts
        ).toLowerCase();
        if (recordTitle.includes(searchStr) || searchStr.includes(recordTitle)) {
            return id;
        }
    }


    for (const [id, recordData] of Object.entries(linkedDbStore.recordsIdMap)) {

        for (const [, fieldValue] of Object.entries((recordData as any).record.recordValues)) {
            if (typeof fieldValue === 'string' && fieldValue.toLowerCase().includes(searchStr)) {
                return id;
            }
        }
    }


    const recordIds = Object.keys(linkedDbStore.recordsIdMap);
    if (recordIds.length > 0) {
        console.warn(`No exact match found for "${title}", using first available record as fallback`);
        return recordIds[0];
    }

    console.warn(`No record found matching "${title}"`);
    return null;
};


export const findUserIdByNameOrEmail = (name: string, members: any[]): string | null => {
    if (!name || !members || !Array.isArray(members) || members.length === 0) {
        console.warn("Invalid input for findUserIdByNameOrEmail:", { name, members });
        return null;
    }


    const searchStr = name.trim().toLowerCase();

    for (const member of members) {
        if (member.user && member.user.firstName && member.user.lastName) {
            const fullName = `${member.user.firstName} ${member.user.lastName}`.toLowerCase();
            if (fullName === searchStr) {
                return member.user.id;
            }
        } else if (member.name && typeof member.name === 'string') {
            if (member.name.toLowerCase() === searchStr) {
                return member.id;
            }
        }
    }

    for (const member of members) {
        if (member.user && member.user.email) {
            if (member.user.email.toLowerCase() === searchStr) {
                return member.user.id;
            }
        } else if (member.email && typeof member.email === 'string') {
            if (member.email.toLowerCase() === searchStr) {
                return member.id;
            }
        }
    }

    for (const member of members) {
        if (member.user && member.user.firstName && member.user.lastName) {
            const fullName = `${member.user.firstName} ${member.user.lastName}`.toLowerCase();
            if (fullName.includes(searchStr) || searchStr.includes(fullName)) {
                return member.user.id;
            }
        } else if (member.name && typeof member.name === 'string') {
            if (member.name.toLowerCase().includes(searchStr)) {
                return member.id;
            }
        }
    }


    if (members.length > 0) {
        console.warn(`No exact match found for "${name}", using first available user as fallback`);
        if (members[0].user) {
            return members[0].user.id;
        } else {
            return members[0].id;
        }
    }

    console.warn(`No user found matching "${name}"`);
    return null;
};


export const formatValueForColumnType = (value: any, column: any, _database?: any, members?: any[]): any => {


    switch (column.type) {
        case 'checkbox':
            if (typeof value === 'string') {
                const boolValue = value.toLowerCase() === 'true';
                return boolValue;
            }
            const boolValue = Boolean(value);
            return boolValue;

        case 'number':
            if (typeof value === 'string') {
                const numValue = parseFloat(value);
                return numValue;
            }
            return value;

        case 'select':
            if (typeof value === 'string') {


                if (column.optionsMap) {

                    if (column.optionsMap[value]) {
                        return [value];
                    }


                    let foundOptionId = null;
                    for (const optionId of column.optionIds || Object.keys(column.optionsMap)) {
                        const option = column.optionsMap[optionId];
                        if (option.title.toLowerCase() === value.toLowerCase()) {
                            foundOptionId = optionId;
                            break;
                        }
                    }

                    if (foundOptionId) {
                        return [foundOptionId];
                    }
                }

                else if (column.options && Array.isArray(column.options)) {
                    const option = column.options.find((opt: any) =>
                        opt.value === value || opt.label === value
                    );
                    return option ? [option.value] : [];
                }


                console.warn(`Option "${value}" not found in select column "${column.title}"`);
                return [];
            } else if (Array.isArray(value)) {
                return value;
            }
            return [];

        case 'date':
        case 'createdAt':
        case 'updatedAt':
            if (typeof value === 'string' && !value.includes('T')) {
                try {
                    const date = new Date(value);
                    if (!isNaN(date.getTime())) {
                        const isoString = date.toISOString();
                        return isoString;
                    }
                } catch (e) {
                    console.warn(`Failed to parse date string "${value}":`, e);
                }
            }
            return value;

        case 'person':
        case 'createdBy':
        case 'updatedBy':
            if (typeof value === 'string') {

                if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
                    return [value];
                }


                if (members && members.length > 0) {
                    const userId = findUserIdByNameOrEmail(value, members);
                    if (userId) {
                        return [userId];
                    }
                }

                return [value];
            } else if (Array.isArray(value)) {
                return value;
            }
            return [];

        case 'linked':
            if (typeof value === 'string') {
                if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(value)) {
                    return [value];
                }

                if (_database && column.type === 'linked') {
                    const linkedDbId = column.databaseId;

                    let linkedDbStore = null;

                    if (_database && _database.databaseStore && _database.databaseStore[linkedDbId]) {
                        linkedDbStore = _database.databaseStore[linkedDbId];
                    }

                    if (linkedDbStore) {

                        const recordId = findRecordIdByTitle(value, linkedDbStore, members);
                        if (recordId) {
                            return [recordId];
                        } else {
                            console.warn(`Could not find record with title "${value}" in linked database`);
                        }
                    } else {
                        console.warn(`Linked database store not found for ID: ${linkedDbId}`);
                    }
                }

                return [value];
            } else if (Array.isArray(value)) {
                return value;
            }
            return [];

        default:
            return value;
    }
};


export const convertNameUpdatesToIdUpdates = (database: any, nameUpdates: {[key: string]: any}, members?: any[]): RecordValues => {
    const idUpdates: RecordValues = {};

    for (const [name, value] of Object.entries(nameUpdates)) {
        if (database.definition.columnsMap[name]) {

            const column = database.definition.columnsMap[name];
            idUpdates[name] = formatValueForColumnType(value, column, database, members);
            continue;
        }

        const columnId = getColumnIdByName(database, name);
        if (columnId) {
            const column = database.definition.columnsMap[columnId];
            idUpdates[columnId] = formatValueForColumnType(value, column, database, members);
        } else {
            console.warn(`Column "${name}" not found in database`);
        }
    }

    return idUpdates;
};


export const createButtonAction = (
    actionType: ButtonAction['actionType'],
    label: string
): ButtonAction => {
    // Create default props based on action type
    let props: any;

    switch (actionType) {
        case 'openUrl':
            props = { url: '' };
            break;
        case 'sendEmail':
            props = { to: '', subject: '', body: '' };
            break;
        case 'updateRecord':
            props = { recordId: '', values: {} };
            break;
        case 'deleteRecord':
            props = { recordId: '' };
            break;
        case 'showConfirmation':
            props = { title: '', message: '', confirmButtonLabel: 'Confirm', cancelButtonLabel: 'Cancel' };
            break;
        case 'showToast':
            props = { message: '' };
            break;
        case 'callWorkflow':
            props = { workflowId: '', input: {} };
            break;
        case 'expandRecord':
        case 'peekRecord':
            props = { recordId: '' };
            break;
        case 'sendNotification':
            props = { title: '', message: '' };
            break;
        default:
            props = { message: 'Default action' };
    }

    return {
        id: generateUUID(),
        actionType,
        label,
        isReady: true,
        props
    };
};

const resolveVariables = (text: string, row: any, _meta: any, _database: any, workspace: any, token: any): string => {
    if (!text) return text;

    // Replace column variables
    const columnRegex = /\{\{column\.([^}]+)\}\}/g;
    text = text.replace(columnRegex, (match, columnId) => {
        if (row?.recordValues?.[columnId] !== undefined) {
            return String(row.recordValues[columnId]);
        }
        return match;
    });

    // Replace time/date variables
    const now = new Date();
    text = text.replace(/\{\{time\.now\}\}/g, now.toLocaleTimeString());
    text = text.replace(/\{\{date\.today\}\}/g, now.toLocaleDateString());

    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    text = text.replace(/\{\{date\.tomorrow\}\}/g, tomorrow.toLocaleDateString());

    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    text = text.replace(/\{\{date\.yesterday\}\}/g, yesterday.toLocaleDateString());

    if (token) {
        text = text.replace(/\{\{person\.current\}\}/g, token.userId || '');
        text = text.replace(/\{\{person\.email\}\}/g, token.email || '');
        text = text.replace(/\{\{person\.name\}\}/g, token.name || '');
    }

    if (workspace?.workspace) {
        text = text.replace(/\{\{workspace\.name\}\}/g, workspace.workspace.name || '');
        text = text.replace(/\{\{workspace\.id\}\}/g, workspace.workspace.id || '');
    }

    if (row) {
        text = text.replace(/\{\{record\.id\}\}/g, row.id || '');
    }

    return text;
};

const resolveVariablesInObject = (obj: any, row: any, meta: any, database: any, workspace: any, token: any): any => {
    if (!obj) return obj;

    if (typeof obj === 'string') {
        return resolveVariables(obj, row, meta, database, workspace, token);
    }

    if (Array.isArray(obj)) {
        return obj.map(item => resolveVariablesInObject(item, row, meta, database, workspace, token));
    }

    if (typeof obj === 'object') {
        const result: any = {};
        for (const key in obj) {
            result[key] = resolveVariablesInObject(obj[key], row, meta, database, workspace, token);
        }
        return result;
    }

    return obj;
};

export const handleButtonAction = async (
    button: any, // Using any to bypass type checking for actionType
    row: any,
    meta: any,
    database: any,
    members: any,
    updateRecordValues: any,
    updateDatabaseRecordValues: any,
    deleteRecords: any,
    confirm: any,
    toast: any,
    token: any,
    router: any,
    sendMessage: any,
    forceRender: any,
    showInputDialog: any,
    workspace?: any,
    setPeekRecordId?: (id: string) => void,
    setNotificationSent?: (sent: boolean) => void
) => {
    // Resolve variables in button props
    if (button.props) {
        button.props = resolveVariablesInObject(button.props, row, meta, database, workspace, token);
    }
    switch (button.actionType) {
        case 'openUrl':
            if (!button.props?.url) {
                toast.error("URL action missing URL parameter");
                return;
            }
            try {
                const url = button.props.url;
                window.open(url, '_blank');
            } catch (error) {
                console.error("Error in openUrl action:", error);
                toast.error("Failed to open URL: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        case 'sendEmail':
            if (!button.props?.to) {
                toast.error("Email action missing recipient (to) parameter");
                return;
            }
            try {
                const to = button.props.to;
                const subject = button.props.subject || '';
                const body = button.props.body || '';

                let mailtoUrl = `mailto:${to}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
                if (button.props.cc && Array.isArray(button.props.cc) && button.props.cc.length > 0) {
                    const ccAddresses = button.props.cc.join(',');
                    mailtoUrl += `&cc=${encodeURIComponent(ccAddresses)}`;
                }
                if (button.props.bcc && Array.isArray(button.props.bcc) && button.props.bcc.length > 0) {
                    const bccAddresses = button.props.bcc.join(',');
                    mailtoUrl += `&bcc=${encodeURIComponent(bccAddresses)}`;
                }
                window.open(mailtoUrl, '_blank');
            } catch (error) {
                console.error("Error in sendEmail action:", error);
                toast.error("Failed to open email client: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        case 'updateRecord':
            try {
                let nameUpdates: {[key: string]: any} = {};
                if (button.props?.values && typeof button.props.values === 'object') {
                    const values = button.props.values;

                    for (const [key, value] of Object.entries(values)) {
                        let processedValue = value;
                        nameUpdates[key] = processedValue;
                    }
                } else {
                    toast.error("Update action missing values parameter");
                    return;
                }
                if (Object.keys(nameUpdates).length === 0) {
                    toast.error("No valid updates found");
                    return;
                }
                const idUpdates = convertNameUpdatesToIdUpdates(database.database, nameUpdates, members);
                if (Object.keys(idUpdates).length === 0) {
                    toast.error("No matching columns found for the specified names");
                    return;
                }
                const targetRecordId = row.id;
                let hasChanges = false;
                for (const [key, value] of Object.entries(idUpdates)) {
                    const currentValue = row.recordValues[key];

                    if (Array.isArray(value) && Array.isArray(currentValue)) {
                        if (value.length !== currentValue.length ||
                            !value.every((v, i) => v === currentValue[i])) {
                            hasChanges = true;
                            break;
                        }
                    } else if (typeof value === 'object' && value !== null &&
                               typeof currentValue === 'object' && currentValue !== null) {
                        if (JSON.stringify(value) !== JSON.stringify(currentValue)) {
                            hasChanges = true;
                            break;
                        }
                    } else if (currentValue !== value) {
                        hasChanges = true;
                        break;
                    }
                }

                if (!hasChanges) {
                    toast.info("No changes detected. Record is already up to date.");
                    return;
                }

                updateDatabaseRecordValues(meta.databaseId, [targetRecordId], idUpdates, {}, {}, true);

                forceRender();
                sendMessage(BroadcastNamespaces.DatabaseTableView, "record-updated", {
                    databaseId: meta.databaseId,
                    recordId: targetRecordId
                });
                const toastId = toast.loading("Updating record...");
                const result = await updateRecordValues(meta.databaseId, [targetRecordId], idUpdates);

                if (result) {
                    toast.success("Record updated successfully", { id: toastId });
                } else {
                    toast.error("Failed to update record", { id: toastId });
                    setTimeout(() => {
                        window.location.reload();
                    }, PAGE_RELOAD_TIMEOUT);
                }
            } catch (error) {
                console.error("Failed to apply updates:", error);
                toast.error("Failed to update record: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        case 'deleteRecord':
            if (!meta.databaseId) {
                toast.error("Missing required data for deletion");
                return;
            }
            try {
                const recordId = button.props?.recordId || row.id;
                if (!row || !row.id) {
                    toast.info("Record not found or already deleted.");
                    return;
                }

                await deleteRecords(meta.databaseId, [recordId]);
                toast.success("Record deleted successfully");
            } catch (error) {
                console.error("Failed to delete record:", error);
                toast.error("Failed to delete record: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        case 'showToast':
            if (!button.props?.message) {
                toast.error("Toast action missing message parameter");
                return;
            }

            try {
                const toastType = button.props.type || 'info';
                const toastOptions: any = {};

                const message = button.props.message;
                if (button.props.title) {
                    toastOptions.title = button.props.title;
                }
                if (button.props.duration && typeof button.props.duration === 'number') {
                    toastOptions.duration = button.props.duration;
                }

                switch (toastType) {
                    case 'success':
                        toast.success(message, toastOptions);
                        break;
                    case 'error':
                        toast.error(message, toastOptions);
                        break;
                    case 'warning':
                        toast.warning(message, toastOptions);
                        break;
                    default:
                        toast.info(message, toastOptions);
                        break;
                }
            } catch (error) {
                console.error("Error in showToast action:", error);
                toast.error("Failed to show toast message");
            }
            break;

        case 'showConfirmation':
            try {
                let chainedActions: ButtonAction[] = [];

                if (button.props?.nextAction) {
                    try {
                        const nextAction = typeof button.props.nextAction === 'string'
                            ? JSON.parse(button.props.nextAction)
                            : button.props.nextAction;

                        if (nextAction && nextAction.actionType) {
                            const actionProps = nextAction.props || {};
                            chainedActions.push({
                                id: generateUUID(),
                                label: nextAction.label || "Chained Action",
                                actionType: nextAction.actionType,
                                props: actionProps
                            });
                        }
                    } catch (e) {
                        console.error("Failed to parse nextAction:", e);
                    }
                }

                // Check if there's a chainedActions parameter (multiple actions)
                if (button.props?.chainedActions) {
                    try {
                        const actions = typeof button.props.chainedActions === 'string'
                            ? JSON.parse(button.props.chainedActions)
                            : button.props.chainedActions;

                        if (Array.isArray(actions)) {
                            for (const action of actions) {
                                if (action && action.actionType) {
                                    const actionProps = action.props || {};
                                    chainedActions.push({
                                        id: generateUUID(),
                                        label: action.label || "Chained Action",
                                        actionType: action.actionType,
                                        props: actionProps
                                    });
                                }
                            }
                        }
                    } catch (e) {
                        console.error("Failed to parse chainedActions:", e);
                    }
                }

                const title = button.props?.title || 'Confirmation';
                const message = button.props?.message || 'Please confirm this action.';
                const cancelButtonLabel = button.props?.cancelButtonLabel || 'Cancel';

                confirm(
                    title,
                    message,
                    async () => {
                        if (chainedActions.length > 0) {

                            for (const chainedAction of chainedActions) {
                                await handleButtonAction(
                                    chainedAction,
                                    row,
                                    meta,
                                    database,
                                    members,
                                    updateRecordValues,
                                    updateDatabaseRecordValues,
                                    deleteRecords,
                                    confirm,
                                    toast,
                                    token,
                                    router,
                                    sendMessage,
                                    forceRender,
                                    showInputDialog,
                                    workspace,
                                    setPeekRecordId
                                );
                            }
                        } else {
                            toast.success("Action confirmed");
                        }
                    },
                    cancelButtonLabel ?
                        () => toast.info(`Action cancelled: ${cancelButtonLabel}`) :
                        undefined,
                    {
                        closeOnClickOutside: button.props?.closeOnClickOutside !== "false",
                        closeOnEscape: button.props?.closeOnEscape !== "false"
                    },
                    button.props?.isDangerAction === "true"
                );
            } catch (error) {
                console.error("Error in showConfirmation action:", error);
                toast.error("Failed to show confirmation dialog: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        case 'callWorkflow':
            if (!button.props?.workflowId || !token) {
                toast.error("Workflow action missing workflow ID");
                return;
            }
            try {
                const input = button.props.input || {}
                const processedInput = typeof input === 'string'
                    ? JSON.parse(input)
                    : input;

                localStorage.setItem('workflow_input', JSON.stringify(processedInput));
                router.push(`/workspace/workflows/${button.props.workflowId}?input=${encodeURIComponent(JSON.stringify(processedInput))}`);

                toast.success("Workflow started");
            } catch (error) {
                console.error("Error in callWorkflow action:", error);
                toast.error("Failed to start workflow: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        case 'expandRecord':
            if (!workspace?.workspace?.domain) {
                toast.error("Missing workspace domain");
                return;
            }
            try {
                const recordId = button.props?.recordId || row.id;

                router.push(`/${workspace.workspace.domain}/databases/${meta.databaseId}/records/${recordId}`);
            } catch (error) {
                console.error("Error in expandRecord action:", error);
                toast.error("Failed to expand record: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        case 'peekRecord':
            if (!meta.databaseId) {
                toast.error("Missing required data for peek view");
                return;
            }
            try {
                const recordId = button.props?.recordId || row.id;

                if (typeof setPeekRecordId === 'function') {
                    setPeekRecordId(recordId);
                } else {
                    toast.error("Peek view functionality not available");
                }
            } catch (error) {
                console.error("Error in peekRecord action:", error);
                toast.error("Failed to peek record: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        case 'sendNotification':
            if (!button.props?.title || !button.props?.message) {
                toast.error("Notification action missing title or message");
                return;
            }
            try {
                const title = button.props.title;
                const message = button.props.message;
                const userId = button.props.userId || (token?.userId || '');

                if (!workspace?.workspace?.id || !token?.token) {
                    toast.error("Missing workspace information");
                    return;
                }

                // Use the API to create a notification
                const result = await createNotification(token.token, workspace.workspace.id, {
                    userId: userId,
                    title: title,
                    message: message
                });

                if (result.error) {
                    toast.error(result.error);
                } else {

                    if (typeof setNotificationSent === 'function') {
                        setNotificationSent(true);
                        setTimeout(() => {
                            setNotificationSent(false);
                        }, NOTIFICATION_FEEDBACK_TIMEOUT);
                    }

                    // Add a small delay before showing the toast
                    setTimeout(() => {
                        toast.success("Notification sent successfully", {
                            duration: NOTIFICATION_FEEDBACK_TIMEOUT,
                            id: "notification-sent-" + new Date().getTime()
                        });
                    }, TOAST_DELAY);
                }
            } catch (error) {
                console.error("Error in sendNotification action:", error);
                toast.error("Failed to send notification: " + (error instanceof Error ? error.message : "Unknown error"));
            }
            break;

        default:
            toast.error(`Unknown action type: ${button.actionType}`);
            break;
    }
};

export const ButtonGroupRenderer = <R, SR>(props: RenderCellProps<R, SR>): React.ReactNode => {
    const { updateRecordValues, deleteRecords, setPeekRecordId } = useViews();
    const { confirm, toast } = useAlert();
    const { databaseStore, updateDatabaseRecordValues, members, workspace } = useWorkspace();
    const { forceRender } = useForceRender();
    const { token } = useAuth();
    const router = useRouter();
    const { sendMessage } = useBroadcast();
    const [inputDialogOpen, setInputDialogOpen] = useState(false);
    const [inputDialogTitle, setInputDialogTitle] = useState("");
    const [inputDialogMessage, setInputDialogMessage] = useState("");
    const [inputValue, setInputValue] = useState("");
    const [inputCallback, setInputCallback] = useState<((value: string) => void) | null>(null);

    // This state is used by the handleButtonAction function for notification feedback
    // The notificationSent state is not directly used in rendering but is updated by the handleButtonAction function
    const [, setNotificationSent] = useState(false);

    const { column } = props;
    const rowData = props.row as DataViewRow;
    const row = rowData.record;

    // The __meta__ property is added by the table component at runtime
    const meta = (column as any)['__meta__'] as RGDMeta;
    const dbColumn = meta.column as ButtonGroupColumn;
    const buttons = dbColumn.buttons || [];

    const database = databaseStore?.[meta.databaseId];
    if (!database) {
        return null;
    }

    const showInputDialog = (title: string, message: string, callback: (value: string) => void) => {
        setInputDialogTitle(title);
        setInputDialogMessage(message);
        setInputValue("");
        setInputCallback(() => callback);
        setInputDialogOpen(true);
    };

    const handleInputSubmit = () => {
        if (inputCallback) {
            inputCallback(inputValue);
        }
        setInputDialogOpen(false);
    };

    const handleButtonClick = async (button: ActionButton) => {
        if (button?.actions?.length > 0) {
            for (const action of button.actions) {
                await handleButtonAction(
                    action,
                    row,
                    meta,
                    database,
                    members,
                    updateRecordValues,
                    updateDatabaseRecordValues,
                    deleteRecords,
                    confirm,
                    toast,
                    token,
                    router,
                    sendMessage,
                    forceRender,
                    showInputDialog,
                    workspace,
                    setPeekRecordId,
                    setNotificationSent
                );
            }
        } else {
            toast.info("This button has no actions configured");
        }
    };

    return (
        <>
            <GridRender rowId={rowData.id}>
                <div className="r-text r-button-group text-xs h-full flex items-center group overflow-hidden button-group-container">
                {buttons.length > 0 && (
                    buttons.length === 1 ? (
                        <Button
                            className="rounded-full p-1.5 h-auto text-xs font-semibold gap-1 flex items-center border bg-white hover:bg-gray-50 text-black w-full sm:w-auto"
                            onClick={() => handleButtonClick(buttons[0])}
                        >
                            {getButtonIcon(buttons[0])}
                            <span className="truncate">{buttons[0].label || 'Action'}</span>
                        </Button>
                    ) : (
                        <div className="flex items-center gap-1 flex-wrap sm:flex-nowrap w-full">
                            <Button
                                className="rounded-full p-1.5 h-auto text-xs font-semibold gap-1 flex items-center border bg-white hover:bg-gray-50 text-black w-full sm:w-auto"
                                onClick={() => handleButtonClick(buttons[0])}
                            >
                                {getButtonIcon(buttons[0])}
                                <span className="truncate">{buttons[0].label || 'Action'}</span>
                            </Button>

                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button
                                        className="rounded-full p-1.5 h-auto border bg-white hover:bg-gray-50 text-black w-full sm:w-auto mt-1 sm:mt-0"
                                    >
                                        <ChevronDownIcon className="size-3" />
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-48 z-[9999]">
                                    {buttons.slice(1).map((button) => (
                                        <DropdownMenuItem
                                            key={button.id}
                                            onClick={() => handleButtonClick(button)}
                                            className="text-xs p-2 gap-2 flex items-center"
                                        >
                                            {getButtonIcon(button)}
                                            <span className="truncate">{button.label || 'Action'}</span>
                                        </DropdownMenuItem>
                                    ))}
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </div>
                    )
                )}
                </div>
            </GridRender>

            <Dialog open={inputDialogOpen} onOpenChange={setInputDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>{inputDialogTitle}</DialogTitle>
                    </DialogHeader>
                    <div className="py-4">
                        <p className="text-sm text-gray-500 mb-4">{inputDialogMessage}</p>
                        <Input
                            value={inputValue}
                            onChange={(e) => setInputValue(e.target.value)}
                            placeholder="Enter your input here"
                        />
                    </div>
                    <DialogFooter>
                        <Button className="border bg-white hover:bg-gray-50 text-black" onClick={() => setInputDialogOpen(false)}>Cancel</Button>
                        <Button className="border bg-white hover:bg-gray-50 text-black" onClick={handleInputSubmit}>Submit</Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </>
    );
};

// Use ButtonGroupRenderer directly instead of this wrapper
export const ButtonGroupEditor = ButtonGroupRenderer;
