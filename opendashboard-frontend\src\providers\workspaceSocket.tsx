import {io, Socket} from "socket.io-client";
import {createContext, PropsWithChildren, useContext, useEffect, useMemo, useState} from "react";
import {useAuth} from "@/providers/user";
import {useWorkspace} from "@/providers/workspace";
import {apiUrl} from "@/api/common";
import {KeyValueStore, MyWorkspaceMember, Workspace, WorkspaceMemberRole, WorkspaceMemberSettings} from "@/typings/workspace";
import {AccessLevel, Page, PagePermission, PermissiblePageWithPermissions, View} from "@/typings/page";
import {WorkspaceHandlerEvent} from "@/typings/socket";
import {useRouter} from "next/navigation";
import {Database, PermissibleDatabaseWithPermissions, Record} from "@/typings/database";
import {DatabaseColumn, DatabaseDefinition, RecordValues} from "opendb-app-db-utils/lib/typings/db";
import {removeAllArrayItem} from "opendb-app-db-utils/lib";
import {RecordsIdMap} from "@/typings/utilities";
import {TemplateInstall} from "@/typings/creator";

interface WorkspaceSocketContextProps {
    isConnected: boolean;
    error: string
    socket?: Socket
}

export const offlineMessage = () => {
    return 'You are offline, editing is disabled until a connection is established'
}

export const SocketTimeoutMS = 60000; // I minute

const WorkspaceSocketContext = createContext<WorkspaceSocketContextProps | undefined>(
    undefined
);

export const WorkspaceSocketProvider = (
    props: PropsWithChildren
) => {
    const [isConnected, setConnected] = useState(false);
    const [error, setError] = useState("");
    const [socket, setSocket] = useState<Socket | undefined>();
    const {token, workspaces, isAuthenticated} = useAuth();
    const {
        workspace, pagesId, databasePagesId, pageStore, databasePageStore,
        databaseStore,
        updateDatabaseStore,
        updatePageStore, updateDatabasePageStore,
        updatePageViews,
        updateWorkspace, updateMemberSettings, url,
        deleteDatabase,
        deletePage,
        addDatabase,
        addPage,
        addPagePermissions,
        updatePagePermission,
        deletePagePermissions,
        updateMembers,
        members,
        updateDatabaseRecordValues,
        refreshPagesAndDatabases
    } = useWorkspace()
    const router = useRouter()

    const {id} = workspace.workspace
    const {role} = workspace.workspaceMember

    const tokenStr = token?.token || ''


    useEffect(() => {
        const s = io(`${String(apiUrl()).replace('/api/v1', '')}/workspace/${id}`, {
            path: "/ws/",
            auth: {
                token: tokenStr,
            },
            withCredentials: true,
            transports: ["websocket"]
        });
        setSocket(s);
        s.on("connect_error", (err) => {
            console.log("Connection error: ", err); // prints the message associated with the error
            console.log(err.message); // not authorized
            console.log((err as any)['data']);

            setError(err.message)
            setConnected(false)
        });
        // client-side
        s.on("connect", () => {
            console.log('Socket connected:', s.connected); // true
            console.log(s.id); // x8WIv7-mJelg7on_ALbx
            setConnected(true)
            setError('')
        });
        //
        s.on("disconnect", () => {
            console.log('Socket disconnected:', s.connected); // false
            console.log(s.id); // undefined
            setConnected(false)
        });
        return () => {
            s.disconnect();
        };
    }, [isAuthenticated, id, tokenStr]);

    const subscriptions = useMemo(() => {
        const subscriptions: string[] = [];

        if (role !== WorkspaceMemberRole.Collaborator) subscriptions.push("shared")
        subscriptions.push("public")
        subscriptions.push(`user:${workspace.workspaceMember.userId}`)
        subscriptions.push(`u:${workspace.workspaceMember.userId}`)

        for (const id of pagesId) subscriptions.push(`page:${id}`)
        for (const id of databasePagesId) {
            const permissible = databasePageStore[id]
            if (!permissible) continue
            subscriptions.push(`page:${permissible.page.id}`)
            subscriptions.push(`database:${permissible.page.databaseId}`)
        }
        return subscriptions
    }, [role, workspace.workspaceMember.userId, pagesId, databasePagesId, databasePageStore])

    useEffect(() => {
        if (!socket || !isConnected) {
            console.log("Socket is not connected")
            return
        }
        socket.emit(WorkspaceHandlerEvent.BatchSubscribe, {
            subscriptions
        }, function (responseData: any) {
            console.log('Batch subscription completed with response:', responseData);
        });
        console.log("Socket is connected, setting up subscriptions")
    }, [isConnected, socket, subscriptions]);

    useEffect(() => {
        if (!socket || !isConnected) {
            console.log("Socket is not connected")
            return
        }
        console.log("Socket is connected, setting up page listeners")

        socket.on("page-updated", (d: {
            id: string,
            databaseId: string,
            update: Partial<Page>
        }) => {
            console.log("Page updated", d)
            const {id, databaseId, update} = d
            if (databaseId && databasePageStore[databaseId]) {
                const {page} = databasePageStore[databaseId]
                updateDatabasePageStore(databaseId, {page: {...page, ...update}}, true)
            } else if (pageStore[id]) {
                const {page} = pageStore[id]
                updatePageStore(id, {page: {...page, ...update}}, true)
            }
        })

        socket.on("page-view-updated", (d: {
            id: string,
            pageId: string,
            databaseId: string,
            update: Partial<View>
        }) => {
            console.log("Page view updated", d)
            const {id, databaseId, pageId, update} = d
            if (databaseId && databasePageStore[databaseId]) {
                const {views} = databasePageStore[databaseId]
                const updated = [...views]
                for (let i = 0; i < updated.length; i++) {
                    const view = updated[i];
                    if (view.id === id) {
                        updated[i] = {...view, ...update}
                        break
                    }
                }
                updateDatabasePageStore(databaseId, {views: updated}, true)
            } else if (pageStore[pageId]) {
                const {views} = pageStore[pageId]

                const updated = [...views]
                for (let i = 0; i < updated.length; i++) {
                    const view = updated[i];
                    if (view.id === id) {
                        updated[i] = {...view, ...update}
                        break
                    }
                }
                updatePageStore(pageId, {views: updated}, true)
            }
        })

        socket.on("page-view-deleted", (d: {
            id: string,
            pageId: string,
            databaseId: string,
            update: Partial<View>
        }) => {
            console.log("Page view deleted", d)
            const {id, databaseId, pageId, update} = d
            let parentLink = url(``)

            let cb = null
            if (databaseId && databasePageStore[databaseId]) {
                const {views} = databasePageStore[databaseId]
                const updated = views.filter(v => v.id !== id)

                cb = () => updateDatabasePageStore(databaseId, {views: updated}, true)
                parentLink = url(`/databases/${databaseId}`)
            } else if (pageStore[pageId]) {
                const {views} = pageStore[pageId]
                const updated = views.filter(v => v.id !== id)
                cb = () => updatePageStore(pageId, {views: updated}, true)
                parentLink = url(`/${pageId}`)
            } else return
            const viewLink = `${parentLink}/views/${id}`
            if (window.location.href.includes(viewLink)) {
                router.push(parentLink)
            }
            setTimeout(cb, 250)
        })

        socket.on("page-view-added", (d: {
            id: string,
            databaseId: string,
            viewsOrder: string[],
            view: View
        }) => {
            console.log("Page view added", d)
            const {id, databaseId, viewsOrder, view} = d
            if (databaseId && databasePageStore[databaseId]) {
                const {views} = databasePageStore[databaseId]
                const updated = [...views, view]
                updatePageViews('database', databaseId, viewsOrder, updated)
            } else if (pageStore[id]) {
                const {views} = pageStore[id]
                const updated = [...views, view]
                updatePageViews('page', id, viewsOrder, updated)
            }
        })

        socket.on("page-deleted", (d: {
            id: string,
            databaseId: string,
            viewsOrder: string[],
            view: View
        }) => {
            console.log("Page deleted", d)
            const {id, databaseId} = d

            let link = url(``)

            let cb = null
            if (databaseId) {
                cb = () => deleteDatabase(databaseId)
                link = url(`/databases/${databaseId}`)
            } else if (pageStore[id]) {
                cb = () => deletePage(id)
                link = url(`/${id}`)
            } else return

            if (window.location.href.includes(link)) {
                console.log("Routing to workspace")
                router.replace(url())
            }
            setTimeout(cb, 400)

        })

        socket.on("database-created", (d: {
            database: PermissibleDatabaseWithPermissions
        }) => {
            console.log("Database created", d)
            const {database} = d
            addDatabase(database)
        })

        socket.on("page-created", (d: {
            page: PermissiblePageWithPermissions
        }) => {
            console.log("Page created", d)
            const {page} = d
            addPage(page)
        })

        socket.on("page-permission-added", (d: {
            id: string,
            databaseId: string,
            permissions: PagePermission[]
        }) => {
            console.log("Page permission added", d)
            const {id, databaseId, permissions} = d
            addPagePermissions(id, databaseId, permissions)
        })

        socket.on("page-permission-updated", (d: {
            id: string,
            databaseId: string,
            data: {
                userId: string,
                accessLevel: AccessLevel
            }
        }) => {
            console.log("Page permission updated", d)
            const {id, databaseId, data} = d
            updatePagePermission(id, databaseId, data)
        })

        socket.on("page-permission-deleted", (d: {
            id: string,
            databaseId: string,
            data: {
                userIds: string[]
            }
        }) => {
            console.log("Page permission deleted", d)
            const {id, databaseId, data} = d
            deletePagePermissions(id, databaseId, data)
        })

        socket.on("workspace-members-added", (d: {
            members: MyWorkspaceMember[]
        }) => {
            console.log("Workspace members added", d)

            const newMembers = [...members, ...d.members]
            const keyMap: {
                [memberId: string]: MyWorkspaceMember
            } = {}
            for (const m of newMembers) {
                keyMap[m.user.id] = m
            }
            updateMembers(Object.values(keyMap))
        })

        socket.on("column-added-or-updated", (d: {
            databaseId: string,
            column: DatabaseColumn
        }) => {
            console.log("column added or updated", d)

            const {column, databaseId} = d

            if (!column.id) return
            const db = databaseStore[databaseId]
            if (!db) return
            const database = db.database
            const definition = database.definition

            definition.columnsMap[column.id] = column
            if (!definition.columnIds.includes(column.id)) definition.columnIds.push(column.id)

            updateDatabaseStore(databaseId, {definition}, {})
        })

        socket.on("column-deleted", (d: {
            databaseId: string,
            columnId: string
        }) => {
            console.log("column deleted", d)
            const {columnId, databaseId} = d

            const db = databaseStore[databaseId]
            if (!db) return
            const database = db.database
            const definition = database.definition

            if (!definition.columnsMap[columnId]) return

            delete definition.columnsMap[columnId]
            definition.columnIds = removeAllArrayItem(definition.columnIds, columnId)

            if (definition.uniqueColumnId === columnId) definition.uniqueColumnId = ''
            updateDatabaseStore(databaseId, {definition}, {})
        })

        socket.on("column-made-unique", (d: {
            databaseId: string,
            data: {
                columnId: string,
                unique: boolean
            }
        }) => {
            console.log("column made unique", d)
            const {data, databaseId} = d
            const {columnId, unique} = data

            const db = databaseStore[databaseId]
            if (!db) return
            const database = db.database
            const definition = database.definition

            if (unique) definition.uniqueColumnId = columnId
            else definition.uniqueColumnId = ''

            updateDatabaseStore(databaseId, {definition}, {})
        })

        socket.on("database-definition-updated", (d: {
            databaseId: string,
            data: Partial<DatabaseDefinition>,
            extra?: Partial<Database>
        }) => {
            console.log("🔍 DEBUG - definition updated", d)
            const {data, databaseId, extra} = d

            const db = databaseStore[databaseId]
            if (!db) {
                console.log("🔍 DEBUG - No database found in store for ID:", databaseId)
                return
            }
            const database = db.database
            const oldDefinition = database.definition
            const definition = {...database.definition, ...data,}

            console.log("🔍 DEBUG - Updating database definition:", {
                databaseId,
                oldTitleFormat: oldDefinition.titleFormat,
                newTitleFormat: definition.titleFormat,
                updateData: data,
                extra
            })

            updateDatabaseStore(databaseId, {definition, ...(extra || {})}, {})

            console.log("🔍 DEBUG - Database store updated")
        })

        socket.on("records-updated", (d: {
            databaseId: string,
            recordIds: string[]
            values: RecordValues,
            meta?: KeyValueStore,
            recordExtra: Partial<Record>
        }) => {
            console.log("column values updated", d)
            const {databaseId, recordIds, values, meta, recordExtra} = d
            updateDatabaseRecordValues(databaseId, recordIds, values, meta, recordExtra, true)
        })

        socket.on("records-created", (d: {
            databaseId: string,
            records: Record[]
        }) => {
            console.log("record created", d)
            const {records, databaseId} = d

            const recordsIdMap: RecordsIdMap = {}
            for (const record of records) {
                recordsIdMap[record.id] = recordsIdMap[record.id] || {}

                // @Todo: calculate process record
                recordsIdMap[record.id].record = record
            }
            updateDatabaseStore(databaseId, {}, recordsIdMap)
        })

        socket.on("records-deleted", (d: {
            databaseId: string,
            ids: string[]
        }) => {
            console.log("record deleted", d)
            const {ids, databaseId} = d

            const db = databaseStore[databaseId]
            if (!db) return

            const recordsIdMap: RecordsIdMap = db.recordsIdMap
            for (const id of ids) {
                delete recordsIdMap[id]
            }
            updateDatabaseStore(databaseId, {}, recordsIdMap)
        })

        socket.on("template-installed", (d: {
            install: TemplateInstall
        }) => {
            console.log("template-installed", d)
            const {install} = d

            refreshPagesAndDatabases()

        })

        return () => {
            console.log("Removing page and database listeners")
            socket.off('page-updated')
            socket.off('page-view-updated')
            socket.off('page-view-deleted')
            socket.off('page-view-added')
            socket.off('page-deleted')
            socket.off('database-created')
            socket.off('page-created')
            socket.off('page-permission-added')
            socket.off('page-permission-updated')
            socket.off('page-permission-deleted')
            socket.off('workspace-members-added')
            socket.off('column-added-or-updated')
            socket.off('column-deleted')
            socket.off('column-made-unique')
            socket.off('records-updated')
            socket.off('records-deleted')
            socket.off('records-created')
            socket.off('template-installed')
        }
        // }, [addDatabase, addPage, addPagePermissions, databasePageStore, databaseStore, deleteDatabase, deletePage, deletePagePermissions, isConnected, members, pageStore, router, socket, updateDatabasePageStore, updateDatabaseRecordValues, updateDatabaseStore, updateMembers, updatePagePermission, updatePageStore, updatePageViews, url]);
    }, [socket, isConnected]);

    useEffect(() => {
        if (!socket || !isConnected) {
            console.log("Socket is not connected")
            return
        }
        console.log("Socket is connected, setting up workspace change & member settings listeners")

        socket.on("member-settings-updated", (d: {
            settings: WorkspaceMemberSettings
        }) => {
            console.log("Member settings updated", d)

            updateMemberSettings(d.settings)
        })

        socket.on("workspace-updated", (d: {
            update: Partial<Workspace>
        }) => {
            console.log("Workspace updated", d)
            updateWorkspace(d.update)
        })


        return () => {
            socket.off('member-settings-updated')
            socket.off('workspace-updated')
        }

        // }, [isConnected, socket, updateMemberSettings, updateWorkspace]);
    }, [socket, isConnected]);


    return <>
        <WorkspaceSocketContext.Provider value={{
            socket, error, isConnected
        }}>
            {props.children}
        </WorkspaceSocketContext.Provider>

    </>
}

export const useWorkspaceSocket = () => {
    const context = useContext(WorkspaceSocketContext)
    if (!context) throw new Error('useWorkspaceSocket must be used within a WorkspaceSocketProvider');
    return context as WorkspaceSocketContextProps;
}