import { format, addDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth, isSameDay } from 'date-fns';
import { CalendarEvent } from '@/typings/page';


export const getEventsForRange = (events: CalendarEvent[], start: Date, end: Date): CalendarEvent[] => {
  return events.filter(event => {
    const eventStart = new Date(event.start);
    const eventEnd = new Date(event.end);
    return eventStart <= end && eventEnd >= start;
  });
};

export const getEventDurationInMinutes = (event: CalendarEvent): number => {
  const start = new Date(event.start);
  const end = new Date(event.end);
  return Math.max(30, (end.getTime() - start.getTime()) / (1000 * 60));
};

export const formatDateForHeader = (viewType: string, date: Date): string => {
  switch (viewType) {
    case 'day':
      return format(date, 'MMMM d, yyyy');
    case 'week':
      return `${format(startOfWeek(date, { weekStartsOn: 0 }), 'MMM d')} - ${format(endOfWeek(date, { weekStartsOn: 0 }), 'MMM d, yyyy')}`;
    case 'month':
      return format(date, 'MMMM yyyy');
    default:
      return format(date, 'MMMM d, yyyy');
  }
};

export const getMonthViewDays = (date: Date): Date[] => {
  const monthStart = startOfMonth(date);
  const monthEnd = endOfMonth(date);
  const startDay = startOfWeek(monthStart, { weekStartsOn: 0 });
  const endDay = endOfWeek(monthEnd, { weekStartsOn: 0 });

  const days = [];
  let day = startDay;
  while (day <= endDay) {
    days.push(day);
    day = addDays(day, 1);
  }

  return days;
};

export const getMonthViewWeeks = (date: Date): Date[][] => {
  const days = getMonthViewDays(date);
  const weeks = [];

  for (let i = 0; i < days.length; i += 7) {
    weeks.push(days.slice(i, i + 7));
  }

  return weeks;
};

export const isInCurrentMonth = (date: Date, currentMonth: Date): boolean => {
  return date.getMonth() === currentMonth.getMonth() &&
         date.getFullYear() === currentMonth.getFullYear();
};