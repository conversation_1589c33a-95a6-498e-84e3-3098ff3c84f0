/**
 * Title formatting utilities that integrate with the senior developer's 
 * transformRawRecords system in opendb-app-db-utils
 */

import { DatabaseDefinition } from "opendb-app-db-utils/lib/typings/db";

/**
 * Helper function to validate title format strings
 * The actual formatting is handled by the senior dev's substituteVars function
 * in transformRawRecords from opendb-app-db-utils
 */
export const validateTitleFormat = (titleFormat: string): boolean => {
    if (!titleFormat || typeof titleFormat !== 'string') {
        return false;
    }
    
    // Basic validation - check for valid {{columnId}} patterns
    const regex = /\{\{([^}]+)\}\}/g;
    const matches = titleFormat.match(regex);
    
    if (!matches) {
        // No column references, just text - that's valid
        return titleFormat.trim().length > 0;
    }
    
    // Check that all column references are properly formatted
    for (const match of matches) {
        const columnId = match.slice(2, -2); // Remove {{ and }}
        if (!columnId || columnId.includes('{') || columnId.includes('}')) {
            return false;
        }
    }
    
    return true;
};

/**
 * Helper function to extract column IDs from a title format string
 */
export const extractColumnIds = (titleFormat: string): string[] => {
    if (!titleFormat || typeof titleFormat !== 'string') {
        return [];
    }
    
    const regex = /\{\{([^}]+)\}\}/g;
    const columnIds: string[] = [];
    let match;
    
    while ((match = regex.exec(titleFormat)) !== null) {
        const columnId = match[1];
        if (columnId && !columnIds.includes(columnId)) {
            columnIds.push(columnId);
        }
    }
    
    return columnIds;
};

/**
 * Helper function to validate that all column IDs in a title format exist in the database definition
 */
export const validateTitleFormatColumns = (titleFormat: string, definition: DatabaseDefinition): { isValid: boolean; missingColumns: string[] } => {
    const columnIds = extractColumnIds(titleFormat);
    const missingColumns: string[] = [];
    
    for (const columnId of columnIds) {
        if (!definition.columnsMap[columnId]) {
            missingColumns.push(columnId);
        }
    }
    
    return {
        isValid: missingColumns.length === 0,
        missingColumns
    };
};

/**
 * Helper function to create a sample title format preview
 * This doesn't actually format the title - that's done by the senior dev's system
 * This just shows what the format would look like with sample data
 */
export const previewTitleFormat = (titleFormat: string, definition: DatabaseDefinition): string => {
    if (!validateTitleFormat(titleFormat)) {
        return 'Invalid format';
    }
    
    const { isValid, missingColumns } = validateTitleFormatColumns(titleFormat, definition);
    if (!isValid) {
        return `Missing columns: ${missingColumns.join(', ')}`;
    }
    
    // Replace column references with column titles for preview
    let preview = titleFormat;
    const columnIds = extractColumnIds(titleFormat);
    
    for (const columnId of columnIds) {
        const column = definition.columnsMap[columnId];
        if (column) {
            const pattern = `{{${columnId}}}`;
            preview = preview.replace(new RegExp(pattern.replace(/[{}]/g, '\\$&'), 'g'), `[${column.title}]`);
        }
    }
    
    return preview;
};

/**
 * Helper function to get a default title format for a database
 * Uses the first text column or falls back to the title column
 */
export const getDefaultTitleFormat = (definition: DatabaseDefinition): string => {
    // If there's already a titleFormat, use it
    if (definition.titleFormat && typeof definition.titleFormat === 'string' && definition.titleFormat.trim()) {
        return definition.titleFormat;
    }
    
    // If there's a titleColumnId, use it
    if (definition.titleColumnId && typeof definition.titleColumnId === 'string') {
        return `{{${definition.titleColumnId}}}`;
    }
    
    // Find the first text column
    const textColumns = Object.values(definition.columnsMap).filter(col => 
        col.type === 'text' || col.type === 'ai' || col.type === 'derived'
    );
    
    if (textColumns.length > 0) {
        return `{{${textColumns[0].id}}}`;
    }
    
    // Fallback to first available column
    const allColumns = Object.values(definition.columnsMap);
    if (allColumns.length > 0) {
        return `{{${allColumns[0].id}}}`;
    }
    
    return 'Untitled';
};
